{% extends 'base.html' %}
{% load static %}

{% block title %}Security Test Page - ForgeX{% endblock %}

{% block extra_css %}
<style>
    .test-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    
    .test-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        border: 2px solid #e9ecef;
        border-radius: 8px;
    }
    
    .test-section h3 {
        color: #495057;
        margin-bottom: 1rem;
    }
    
    .test-button {
        margin: 0.5rem;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .test-button.primary {
        background: #007bff;
        color: white;
    }
    
    .test-button.danger {
        background: #dc3545;
        color: white;
    }
    
    .test-button.warning {
        background: #ffc107;
        color: #212529;
    }
    
    .test-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    .log-output {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 1rem;
        margin-top: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-active { background: #28a745; }
    .status-warning { background: #ffc107; }
    .status-error { background: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="test-container">
    <h1><i class="fas fa-shield-alt"></i> Security System Test Page</h1>
    <p class="text-muted">Test the ForgeX security logging system and monitor security events.</p>
    
    <!-- Security Status -->
    <div class="test-section">
        <h3><i class="fas fa-info-circle"></i> Security Status</h3>
        <p>
            <span class="status-indicator status-active"></span>
            <strong>Security System:</strong> <span id="security-status">Loading...</span>
        </p>
        <p>
            <span class="status-indicator status-active"></span>
            <strong>Logging System:</strong> <span id="logging-status">Active</span>
        </p>
        <p>
            <span class="status-indicator status-warning"></span>
            <strong>User Role:</strong> <span id="user-role">Loading...</span>
        </p>
    </div>
    
    <!-- Manual Security Tests -->
    <div class="test-section">
        <h3><i class="fas fa-bug"></i> Manual Security Tests</h3>
        <p>Click these buttons to manually trigger security events and test the logging system:</p>
        
        <button class="test-button primary" onclick="testDevToolsDetection()">
            <i class="fas fa-tools"></i> Test DevTools Detection
        </button>
        
        <button class="test-button warning" onclick="testConsoleAccess()">
            <i class="fas fa-terminal"></i> Test Console Access
        </button>
        
        <button class="test-button danger" onclick="testSecurityThreat()">
            <i class="fas fa-exclamation-triangle"></i> Test Security Threat
        </button>
        
        <button class="test-button primary" onclick="testRightClickBlock()">
            <i class="fas fa-mouse-pointer"></i> Test Right-Click Block
        </button>
        
        <button class="test-button warning" onclick="testKeyboardBlock()">
            <i class="fas fa-keyboard"></i> Test Keyboard Block
        </button>
    </div>
    
    <!-- Automatic Security Tests -->
    <div class="test-section">
        <h3><i class="fas fa-cogs"></i> Automatic Security Tests</h3>
        <p>These tests will automatically trigger various security scenarios:</p>
        
        <button class="test-button primary" onclick="runAllTests()">
            <i class="fas fa-play"></i> Run All Tests
        </button>
        
        <button class="test-button warning" onclick="clearLogs()">
            <i class="fas fa-trash"></i> Clear Log Output
        </button>
        
        <button class="test-button primary" onclick="checkLogFiles()">
            <i class="fas fa-file-alt"></i> Check Log Files
        </button>
    </div>
    
    <!-- Log Output -->
    <div class="test-section">
        <h3><i class="fas fa-list"></i> Security Event Log Output</h3>
        <p>Real-time security events will appear below:</p>
        <div id="log-output" class="log-output">
            <div class="text-muted">Security events will appear here...</div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="test-section">
        <h3><i class="fas fa-question-circle"></i> How to Test</h3>
        <ol>
            <li><strong>Manual Tests:</strong> Click the test buttons above to trigger specific security events</li>
            <li><strong>Browser Tests:</strong> Try pressing F12, Ctrl+Shift+I, or right-clicking outside editor areas</li>
            <li><strong>Check Logs:</strong> View logs at <a href="{% url 'accounts:security_logs' %}" target="_blank">/accounts/security-logs/</a></li>
            <li><strong>File Logs:</strong> Check the <code>logs/</code> directory for text files</li>
        </ol>
    </div>
</div>

<script>
// Security test functions
let logCounter = 0;

function addLogEntry(message, type = 'info') {
    const logOutput = document.getElementById('log-output');
    const timestamp = new Date().toLocaleTimeString();
    const entry = document.createElement('div');
    entry.style.marginBottom = '5px';
    entry.style.color = type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#28a745';
    entry.innerHTML = `[${timestamp}] ${message}`;
    
    if (logCounter === 0) {
        logOutput.innerHTML = '';
    }
    
    logOutput.appendChild(entry);
    logOutput.scrollTop = logOutput.scrollHeight;
    logCounter++;
}

function testDevToolsDetection() {
    addLogEntry('🔧 Testing DevTools detection...', 'info');
    if (window.ForgeXSecurity && window.ForgeXSecurity.testLogging) {
        window.ForgeXSecurity.testLogging();
        addLogEntry('✅ DevTools test event sent', 'info');
    } else {
        // Manual security event
        fetch('/accounts/api/security-log/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
            },
            body: JSON.stringify({
                event_type: 'devtools_detected',
                details: 'Manual DevTools detection test',
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                url: window.location.href,
                page_title: document.title
            })
        }).then(response => {
            if (response.ok) {
                addLogEntry('✅ DevTools detection logged successfully', 'info');
            } else {
                addLogEntry('❌ Failed to log DevTools detection', 'error');
            }
        }).catch(error => {
            addLogEntry(`❌ Error: ${error.message}`, 'error');
        });
    }
}

function testConsoleAccess() {
    addLogEntry('🖥️ Testing console access detection...', 'info');
    fetch('/accounts/api/security-log/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify({
            event_type: 'console_access_attempt',
            details: 'Manual console access test',
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            page_title: document.title
        })
    }).then(response => {
        if (response.ok) {
            addLogEntry('✅ Console access logged successfully', 'info');
        } else {
            addLogEntry('❌ Failed to log console access', 'error');
        }
    });
}

function testSecurityThreat() {
    addLogEntry('⚠️ Testing security threat detection...', 'warning');
    fetch('/accounts/api/security-log/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify({
            event_type: 'security_violation',
            details: 'Manual security threat simulation - HIGH SEVERITY TEST',
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            page_title: document.title
        })
    }).then(response => {
        if (response.ok) {
            addLogEntry('✅ Security threat logged successfully', 'warning');
        } else {
            addLogEntry('❌ Failed to log security threat', 'error');
        }
    });
}

function testRightClickBlock() {
    addLogEntry('🖱️ Testing right-click block...', 'info');
    fetch('/accounts/api/security-log/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify({
            event_type: 'right_click_blocked',
            details: 'Manual right-click block test',
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            page_title: document.title
        })
    }).then(response => {
        if (response.ok) {
            addLogEntry('✅ Right-click block logged successfully', 'info');
        } else {
            addLogEntry('❌ Failed to log right-click block', 'error');
        }
    });
}

function testKeyboardBlock() {
    addLogEntry('⌨️ Testing keyboard shortcut block...', 'info');
    fetch('/accounts/api/security-log/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify({
            event_type: 'keyboard_shortcut_blocked',
            details: 'Manual keyboard shortcut block test (Ctrl+U simulation)',
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            page_title: document.title
        })
    }).then(response => {
        if (response.ok) {
            addLogEntry('✅ Keyboard block logged successfully', 'info');
        } else {
            addLogEntry('❌ Failed to log keyboard block', 'error');
        }
    });
}

function runAllTests() {
    addLogEntry('🚀 Running all security tests...', 'info');
    const tests = [testDevToolsDetection, testConsoleAccess, testRightClickBlock, testKeyboardBlock, testSecurityThreat];
    
    tests.forEach((test, index) => {
        setTimeout(() => {
            test();
        }, index * 1000);
    });
    
    setTimeout(() => {
        addLogEntry('🎉 All tests completed!', 'info');
    }, tests.length * 1000);
}

function clearLogs() {
    document.getElementById('log-output').innerHTML = '<div class="text-muted">Security events will appear here...</div>';
    logCounter = 0;
    addLogEntry('🧹 Log output cleared', 'info');
}

function checkLogFiles() {
    addLogEntry('📁 Checking log files...', 'info');
    addLogEntry('📄 Check these files on the server:', 'info');
    addLogEntry('   - logs/security.log', 'info');
    addLogEntry('   - logs/security_events.log', 'info');
    addLogEntry('   - logs/security_simple.log', 'info');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Check security status
    if (window.ForgeXSecurity) {
        document.getElementById('security-status').textContent = 'Active (v' + window.ForgeXSecurity.version + ')';
        document.getElementById('user-role').textContent = window.ForgeXSecurity.adminMode ? 'Admin' : 'User';
    } else {
        document.getElementById('security-status').textContent = 'Not Detected';
        document.getElementById('user-role').textContent = 'Unknown';
    }
    
    addLogEntry('🛡️ Security test page loaded', 'info');
    addLogEntry('💡 Click the test buttons to generate security events', 'info');
});
</script>
{% endblock %}
