{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Manage Applications - {{ project.title }}{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Back Button -->
    <div style="margin-bottom: 2rem;">
      <a href="{% url 'collaborate:marketplace_detail' marketplace_post.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Marketplace Post
      </a>
    </div>
    
    <!-- Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">📋 Manage Applications</h1>
      <p class="welcome-subtitle">Review and manage applications for your project</p>
    </div>

    <!-- Project Info -->
    <div class="dashboard-section">
      <h2><i class="fas fa-project-diagram"></i> {{ project.title }}</h2>
      <p style="color: rgba(255,255,255,0.8); font-size: 1.1rem;">
        {{ marketplace_post.showcase_description|truncatewords:30 }}
      </p>
    </div>

    <!-- Statistics -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">📝</div>
        <div class="stat-content">
          <h3>{{ applications|length }}</h3>
          <p>Total Applications</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⏳</div>
        <div class="stat-content">
          <h3>{{ applications|filter_by_status:"pending"|length }}</h3>
          <p>Pending Review</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <h3>{{ applications|filter_by_status:"accepted"|length }}</h3>
          <p>Accepted</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">❌</div>
        <div class="stat-content">
          <h3>{{ applications|filter_by_status:"rejected"|length }}</h3>
          <p>Rejected</p>
        </div>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="dashboard-section">
      <h2>📊 Filter Applications</h2>
      <div class="quick-actions">
        <div class="action-grid" style="grid-template-columns: repeat(4, 1fr);">
          <a href="?status=all" class="action-card {% if not request.GET.status or request.GET.status == 'all' %}active{% endif %}">
            <div class="action-icon">📋</div>
            <h3>All Applications</h3>
          </a>
          <a href="?status=pending" class="action-card {% if request.GET.status == 'pending' %}active{% endif %}">
            <div class="action-icon">⏳</div>
            <h3>Pending</h3>
          </a>
          <a href="?status=accepted" class="action-card {% if request.GET.status == 'accepted' %}active{% endif %}">
            <div class="action-icon">✅</div>
            <h3>Accepted</h3>
          </a>
          <a href="?status=rejected" class="action-card {% if request.GET.status == 'rejected' %}active{% endif %}">
            <div class="action-icon">❌</div>
            <h3>Rejected</h3>
          </a>
        </div>
      </div>
    </div>

    <!-- Applications List -->
    {% if applications %}
      <div class="dashboard-section">
        <h2>📨 Applications</h2>
        <div class="mentorship-content">
          {% for application in applications %}
            {% if not request.GET.status or request.GET.status == 'all' or application.status == request.GET.status %}
              <div class="session-card" style="{% if application.status == 'pending' %}border-left: 3px solid #ffc107;{% elif application.status == 'accepted' %}border-left: 3px solid #28a745;{% elif application.status == 'rejected' %}border-left: 3px solid #dc3545;{% endif %}">
                <div class="session-info">
                  <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 1rem;">
                    <h4>{{ application.applicant.username }}</h4>
                    <span class="badge" style="{% if application.status == 'pending' %}background: rgba(255, 193, 7, 0.2); color: #ffc107;{% elif application.status == 'accepted' %}background: rgba(40, 167, 69, 0.2); color: #28a745;{% elif application.status == 'rejected' %}background: rgba(220, 53, 69, 0.2); color: #dc3545;{% endif %} padding: 0.5rem 1rem; border-radius: 20px; font-weight: 600; text-transform: uppercase;">
                      {{ application.get_status_display }}
                    </span>
                  </div>
                  
                  <div class="session-details">
                    <span class="session-date">
                      <i class="fas fa-calendar"></i>
                      Applied {{ application.created_at|date:"M d, Y H:i" }}
                    </span>
                    {% if application.reviewed_at %}
                      <span class="session-date">
                        <i class="fas fa-check"></i>
                        Reviewed {{ application.reviewed_at|date:"M d, Y H:i" }}
                      </span>
                    {% endif %}
                  </div>
                  
                  <div style="margin-top: 1.5rem;">
                    <h5 style="color: #C0ff6b; margin-bottom: 0.5rem;">Why they want to join:</h5>
                    <p style="color: rgba(255, 255, 255, 0.9); background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; border-left: 3px solid #00d4ff;">
                      {{ application.message|linebreaks }}
                    </p>
                  </div>
                  
                  {% if application.skills_offered %}
                    <div style="margin-top: 1rem;">
                      <h5 style="color: #C0ff6b; margin-bottom: 0.5rem;">Skills they can contribute:</h5>
                      <p style="color: rgba(255, 255, 255, 0.9); background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; border-left: 3px solid #00d4ff;">
                        {{ application.skills_offered|linebreaks }}
                      </p>
                    </div>
                  {% endif %}
                  
                  {% if application.portfolio_link %}
                    <div style="margin-top: 1rem;">
                      <h5 style="color: #C0ff6b; margin-bottom: 0.5rem;">Portfolio/GitHub:</h5>
                      <p style="color: rgba(255, 255, 255, 0.9); background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; border-left: 3px solid #00d4ff;">
                        <a href="{{ application.portfolio_link }}" target="_blank" style="color: #C0ff6b; text-decoration: none; font-weight: 600;">
                          <i class="fas fa-external-link-alt"></i>
                          {{ application.portfolio_link }}
                        </a>
                      </p>
                    </div>
                  {% endif %}
                  
                  {% if application.reviewer_notes %}
                    <div style="margin-top: 1rem;">
                      <h5 style="color: #C0ff6b; margin-bottom: 0.5rem;">Review Notes:</h5>
                      <p style="color: rgba(255, 255, 255, 0.9); background: rgba(255, 255, 255, 0.05); padding: 1rem; border-radius: 8px; border-left: 3px solid #00d4ff;">
                        {{ application.reviewer_notes|linebreaks }}
                      </p>
                    </div>
                  {% endif %}
                  
                  {% if application.status == 'pending' %}
                    <div class="mentorship-actions" style="margin-top: 2rem;">
                      <form method="post" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="application_id" value="{{ application.id }}">
                        <input type="hidden" name="action" value="accept">
                        <button type="submit" class="btn btn-primary" style="background: linear-gradient(45deg, #28a745, #20c997);">
                          <i class="fas fa-check"></i> Accept Application
                        </button>
                      </form>
                      
                      <button type="button" class="btn btn-secondary" style="background: linear-gradient(45deg, #dc3545, #c82333);" onclick="toggleRejectForm({{ application.id }})">
                        <i class="fas fa-times"></i> Reject Application
                      </button>
                      
                      <a href="#" class="btn btn-secondary" onclick="viewProfile('{{ application.applicant.username }}')">
                        <i class="fas fa-user"></i> View Profile
                      </a>
                    </div>
                    
                    <!-- Rejection Form -->
                    <div id="rejectForm{{ application.id }}" style="display: none; background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 10px; padding: 1rem; margin-top: 1rem;">
                      <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="application_id" value="{{ application.id }}">
                        <input type="hidden" name="action" value="reject">
                        <div style="margin-bottom: 1rem;">
                          <label style="color: #dc3545; font-weight: 600; margin-bottom: 0.5rem; display: block;">
                            Rejection Notes (optional):
                          </label>
                          <textarea name="notes" style="background: rgba(255, 255, 255, 0.1); border: 1px solid rgba(255, 255, 255, 0.3); border-radius: 8px; padding: 0.8rem; color: white; width: 100%; resize: vertical; min-height: 80px;" 
                                  placeholder="Provide feedback to help the applicant improve..."></textarea>
                        </div>
                        <div style="display: flex; gap: 1rem;">
                          <button type="submit" class="btn btn-secondary" style="background: linear-gradient(45deg, #dc3545, #c82333);">
                            <i class="fas fa-times"></i> Confirm Rejection
                          </button>
                          <button type="button" class="btn btn-secondary" onclick="toggleRejectForm({{ application.id }})">
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          {% endfor %}
        </div>
      </div>
    {% else %}
      <div class="dashboard-section">
        <div class="no-projects-message">
          <div class="no-projects-icon">📨</div>
          <h3>No applications yet</h3>
          <p>When people apply to join your project from the marketplace, they'll appear here.</p>
          <a href="{% url 'collaborate:marketplace_detail' marketplace_post.id %}" class="btn btn-primary">
            <i class="fas fa-eye"></i> View Your Marketplace Post
          </a>
        </div>
      </div>
    {% endif %}
  </div>
</section>

<!-- User Profile Modal -->
<div id="profileModal" class="profile-modal" style="display: none;">
  <div class="profile-modal-overlay" onclick="closeProfileModal()"></div>
  <div class="profile-modal-content">
    <div class="profile-modal-header">
      <h2><i class="fas fa-user"></i> User Profile</h2>
      <button class="profile-modal-close" onclick="closeProfileModal()">×</button>
    </div>
    <div id="profileModalContent" class="profile-modal-body">
      <!-- Profile content will be loaded here -->
    </div>
  </div>
</div>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .stat-card, .session-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds (similar to dashboard)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
  });

function toggleRejectForm(applicationId) {
  const form = document.getElementById('rejectForm' + applicationId);
  if (form.style.display === 'none') {
    // Hide all other rejection forms
    document.querySelectorAll('[id^="rejectForm"]').forEach(f => f.style.display = 'none');
    form.style.display = 'block';
  } else {
    form.style.display = 'none';
  }
}

function viewProfile(username) {
  // Show loading state
  showProfileModal();
  document.getElementById('profileModalContent').innerHTML = `
    <div style="text-align: center; padding: 2rem;">
      <div class="loading-spinner"></div>
      <p style="color: #C0ff6b; margin-top: 1rem;">Loading profile...</p>
    </div>
  `;

  // Fetch user profile data with project context
  const projectId = {{ project.id }};
  fetch(`{% url 'collaborate:user_profile_modal' 'USERNAME' %}`.replace('USERNAME', username) + `?project_id=${projectId}`)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      displayUserProfile(data);
    })
    .catch(error => {
      console.error('Error fetching profile:', error);
      document.getElementById('profileModalContent').innerHTML = `
        <div style="text-align: center; padding: 2rem;">
          <p style="color: #dc3545;">Error loading profile. Please try again.</p>
          <button onclick="closeProfileModal()" class="btn btn-secondary">Close</button>
        </div>
      `;
    });
}

function showProfileModal() {
  document.getElementById('profileModal').style.display = 'flex';
  document.body.style.overflow = 'hidden';
}

function closeProfileModal() {
  document.getElementById('profileModal').style.display = 'none';
  document.body.style.overflow = 'auto';
}

function displayUserProfile(data) {
  const profileContent = document.getElementById('profileModalContent');

  // Build profile picture HTML
  let profilePictureHtml = '';
  if (data.profile_picture) {
    profilePictureHtml = `<img src="${data.profile_picture}" alt="${data.username}'s profile picture" class="profile-modal-picture">`;
  } else {
    profilePictureHtml = `<div class="profile-modal-picture default-avatar"><span>${data.username.charAt(0).toUpperCase()}</span></div>`;
  }

  // Build skills HTML
  let skillsHtml = '';
  if (data.skills && data.skills.length > 0) {
    skillsHtml = data.skills.map(skill => `
      <div class="skill-item-modal">
        <span class="skill-name">${skill.name}</span>
        <span class="skill-level level-${skill.level_value}">${skill.level}</span>
      </div>
    `).join('');
  } else {
    skillsHtml = '<p style="color: #b0b0b0;">No skills listed</p>';
  }

  // Build CV section
  let cvHtml = '';
  if (data.has_cv && data.cv_url) {
    cvHtml = `
      <div class="profile-section">
        <h3><i class="fas fa-file-pdf"></i> CV/Resume</h3>
        <a href="${data.cv_url}" target="_blank" class="btn btn-primary cv-download-btn">
          <i class="fas fa-download"></i> Download CV
        </a>
      </div>
    `;
  } else {
    cvHtml = `
      <div class="profile-section">
        <h3><i class="fas fa-file-pdf"></i> CV/Resume</h3>
        <p style="color: #b0b0b0;">No CV uploaded</p>
      </div>
    `;
  }

  profileContent.innerHTML = `
    <div class="profile-modal-grid">
      <div class="profile-modal-sidebar">
        <div class="profile-modal-user">
          ${profilePictureHtml}
          <h3>${data.full_name}</h3>
          <p class="username">@${data.username}</p>
          ${data.bio ? `<p class="bio">${data.bio}</p>` : ''}
        </div>

        <div class="profile-section">
          <h3><i class="fas fa-clock"></i> Availability</h3>
          <p><strong>Timezone:</strong> ${data.timezone}</p>
          <p><strong>Type:</strong> ${data.availability_type}</p>
          ${data.availability_start ? `<p><strong>Available:</strong> ${data.availability_start} - ${data.availability_end}</p>` : ''}
          ${data.country ? `<p><strong>Country:</strong> ${data.country}</p>` : ''}
          <p><strong>Status:</strong> <span class="availability-status ${data.is_available_now ? 'available' : 'unavailable'}">${data.is_available_now ? 'Available Now' : 'Not Available'}</span></p>
        </div>

        ${cvHtml}

        <div class="profile-section">
          <h3><i class="fas fa-chart-bar"></i> Statistics</h3>
          <p><strong>Projects:</strong> ${data.projects_count}</p>
          ${data.match_score !== undefined ? `<p><strong>Match Score:</strong> ${data.match_score}%</p>` : ''}
        </div>
      </div>

      <div class="profile-modal-main">
        <div class="profile-section">
          <h3><i class="fas fa-tools"></i> Skills & Expertise</h3>
          <div class="skills-grid-modal">
            ${skillsHtml}
          </div>
        </div>

        ${data.description ? `
          <div class="profile-section">
            <h3><i class="fas fa-info-circle"></i> About</h3>
            <p>${data.description}</p>
          </div>
        ` : ''}

        ${data.matching_skills && data.matching_skills.length > 0 ? `
          <div class="profile-section">
            <h3><i class="fas fa-check-circle"></i> Matching Skills for This Project</h3>
            <div class="matching-skills">
              ${data.matching_skills.map(skill => `<span class="matching-skill">${skill}</span>`).join('')}
            </div>
          </div>
        ` : ''}
      </div>
    </div>
  `;
}

// Confirm actions
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('button[type="submit"]').forEach(btn => {
    if (btn.textContent.includes('Accept')) {
      btn.addEventListener('click', function(e) {
        if (!confirm('Are you sure you want to accept this application? This will add the user to your project team.')) {
          e.preventDefault();
        }
      });
    }
  });
  
  document.querySelectorAll('form').forEach(form => {
    const actionInput = form.querySelector('input[name="action"]');
    if (actionInput && actionInput.value === 'reject') {
      form.addEventListener('submit', function(e) {
        if (!confirm('Are you sure you want to reject this application?')) {
          e.preventDefault();
        }
      });
    }
  });
});
</script>

<style>
/* Override collaborate.css with dashboard styling */
:root {
  --color-border: #C0ff6b;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent !important;
  padding: 50px 0 !important;
  min-height: 100vh !important;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Dashboard welcome section */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
  background: linear-gradient(135deg, var(--color-border), #a0e066) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

.welcome-subtitle {
  font-size: 1.2rem !important;
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard stats */
.dashboard-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 40px !important;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 25px !important;
  text-align: center !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
}

.stat-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.stat-icon {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
}

.stat-content h3 {
  font-size: 2rem !important;
  color: var(--color-border) !important;
  margin-bottom: 5px !important;
}

.stat-content p {
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard sections */
.dashboard-section {
  margin-bottom: 40px !important;
}

.dashboard-section h2 {
  color: #ffffff !important;
  margin-bottom: 25px !important;
  text-align: center !important;
  font-size: 1.8rem !important;
}

/* Quick actions */
.quick-actions {
  margin-bottom: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 15px !important;
  padding: 20px !important;
  text-align: center !important;
  text-decoration: none !important;
  color: inherit !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.4s ease !important;
  display: block !important;
}

.action-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
  border-color: rgba(192, 255, 107, 0.5) !important;
  text-decoration: none !important;
  color: inherit !important;
}

.action-icon {
  font-size: 2rem !important;
  margin-bottom: 15px !important;
}

.action-card h3 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

/* Mentorship content */
.mentorship-content {
  margin-bottom: 30px;
}

.session-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 20px !important;
}

.session-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.session-info h4 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

.session-details {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
  margin-bottom: 10px !important;
  font-size: 0.9rem !important;
  color: #b0b0b0 !important;
}

.session-date {
  display: flex !important;
  align-items: center !important;
  gap: 5px !important;
}

/* Mentorship actions */
.mentorship-actions {
  display: flex !important;
  gap: 15px !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
}

/* No projects message */
.no-projects-message {
  text-align: center !important;
  padding: 40px 20px !important;
  background-color: rgba(40, 40, 40, 0.3) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
}

.no-projects-icon {
  font-size: 3rem !important;
  margin-bottom: 20px !important;
  opacity: 0.8 !important;
}

.no-projects-message h3 {
  color: var(--color-border) !important;
  margin-bottom: 15px !important;
  font-size: 1.3rem !important;
}

.no-projects-message p {
  color: #e0e0e0 !important;
  margin-bottom: 25px !important;
  line-height: 1.6 !important;
  max-width: 400px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Profile Modal Styles */
.profile-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: modalFadeIn 0.3s ease-out;
}

.profile-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.profile-modal-content {
  position: relative;
  background: rgba(28, 28, 28, 0.95);
  border-radius: 20px;
  max-width: 90vw;
  max-height: 90vh;
  width: 1000px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.profile-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
  background: rgba(192, 255, 107, 0.1);
}

.profile-modal-header h2 {
  color: var(--color-border);
  margin: 0;
  font-size: 1.5rem;
}

.profile-modal-close {
  background: none;
  border: none;
  color: #ffffff;
  font-size: 2rem;
  cursor: pointer;
  padding: 0;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.profile-modal-close:hover {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.profile-modal-body {
  padding: 2rem;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.profile-modal-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
}

.profile-modal-sidebar {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.profile-modal-user {
  text-align: center;
  padding: 1.5rem;
  background: rgba(40, 40, 40, 0.5);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.profile-modal-picture {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 1rem;
  object-fit: cover;
  border: 3px solid var(--color-border);
}

.profile-modal-picture.default-avatar {
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: bold;
  color: #1a1a2e;
}

.profile-modal-user h3 {
  color: var(--color-border);
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.profile-modal-user .username {
  color: #b0b0b0;
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
}

.profile-modal-user .bio {
  color: #e0e0e0;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0;
}

.profile-section {
  background: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.profile-section h3 {
  color: var(--color-border);
  margin: 0 0 1rem 0;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-section p {
  color: #e0e0e0;
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.availability-status {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.availability-status.available {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.availability-status.unavailable {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.skills-grid-modal {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.skill-item-modal {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.skill-name {
  color: #e0e0e0;
  font-weight: 500;
}

.skill-level {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
}

.skill-level.level-1 { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
.skill-level.level-2 { background: rgba(255, 193, 7, 0.2); color: #ffc107; }
.skill-level.level-3 { background: rgba(40, 167, 69, 0.2); color: #28a745; }
.skill-level.level-4 { background: rgba(0, 123, 255, 0.2); color: #007bff; }
.skill-level.level-5 { background: rgba(102, 16, 242, 0.2); color: #6610f2; }

.cv-download-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(45deg, var(--color-border), #a0e066);
  color: #1a1a2e;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cv-download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
  text-decoration: none;
  color: #1a1a2e;
}

.matching-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.matching-skill {
  background: rgba(192, 255, 107, 0.2);
  color: var(--color-border);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(192, 255, 107, 0.3);
  border-top: 4px solid var(--color-border);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes modalFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes modalSlideIn {
  from { transform: translateY(-50px) scale(0.9); opacity: 0; }
  to { transform: translateY(0) scale(1); opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
  .profile-modal-content {
    width: 95vw;
    max-height: 95vh;
  }

  .profile-modal-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .profile-modal-body {
    padding: 1rem;
  }

  .skills-grid-modal {
    grid-template-columns: 1fr;
  }
}

/* Manage applications specific styles */
.action-card.active {
  background: linear-gradient(45deg, #C0ff6b, #00d4ff) !important;
  color: #1a1a2e !important;
  border-color: transparent !important;
}

.badge {
  display: inline-block;
  font-size: 0.8rem;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  background-color: rgba(40, 40, 40, 0.8);
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 15px;
  border: none;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-info {
  border-left: 4px solid #17a2b8;
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-warning {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-danger {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.alert-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard-style animations */
.stat-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.action-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.action-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.session-card {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.session-card.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Button styling consistency */
.btn {
  background-color: transparent !important;
  color: #ffffff !important;
  border: 2px solid var(--color-border) !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  min-width: 120px !important;
}

.btn:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3) !important;
  transform: translateY(-2px) !important;
  text-decoration: none !important;
}

.btn-primary {
  background-color: rgba(192, 255, 107, 0.2) !important;
  border-color: var(--color-border) !important;
  color: var(--color-border) !important;
}

.btn-primary:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: #ffffff !important;
}

.btn-sm {
  padding: 8px 16px !important;
  font-size: 0.9rem !important;
  min-width: 100px !important;
}

/* Form styling consistency */
.form-control, textarea, input[type="text"], select {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 0.8rem !important;
  color: white !important;
  font-size: 1rem !important;
  width: 100% !important;
  resize: vertical !important;
}

.form-control:focus, textarea:focus, input[type="text"]:focus, select:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--color-border) !important;
  box-shadow: 0 0 0 0.2rem rgba(192, 255, 107, 0.25) !important;
  outline: none !important;
}

.form-control::placeholder, textarea::placeholder, input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Application status styling */
.session-card[style*="border-left: 3px solid #ffc107"] {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(40, 40, 40, 0.6));
}

.session-card[style*="border-left: 3px solid #28a745"] {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(40, 40, 40, 0.6));
}

.session-card[style*="border-left: 3px solid #dc3545"] {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(40, 40, 40, 0.6));
}

/* Button styling improvements */
.btn {
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Rejection form styling */
#rejectForm {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 10px;
  padding: 1rem;
  margin-top: 1rem;
}

/* Portfolio link styling */
a[href*="http"] {
  color: #C0ff6b;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

a[href*="http"]:hover {
  color: #a0e066;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .mentorship-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .mentorship-actions .btn {
    width: 100%;
  }

  .session-info h4 {
    font-size: 1rem;
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.3rem 0.8rem;
  }
}

@media (max-width: 480px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }
}
</style>
{% endblock %}
