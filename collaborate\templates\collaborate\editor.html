{% extends "base.html" %} {% load static %} {% block extra_css %}

<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
/>

<!-- xterm.js for VS Code-like terminal -->
<link rel="stylesheet" href="https://unpkg.com/xterm@5.3.0/css/xterm.css" />


{% endblock %} {% block content %}
{% csrf_token %}
<div class="editor-container">
  <div class="editor-header">
    <h1>{{ project.title }} - Editor</h1>
    <div class="editor-controls">
      <select
        id="language-selector"
        name="language"
        class="language-selector"
        title="Select Programming Language"
        style="display: none;"
      >
        <option value="python">Python</option>
      </select>
      <button id="run-code-btn" class="btn-primary">
        <i class="fas fa-play"></i> Run
      </button>
      <button id="preview-btn" class="btn-info" style="display: none;">
        <i class="fas fa-external-link-alt"></i> Preview
      </button>
      <button id="save-btn" class="btn-success" style="display: none;">
        <i class="fas fa-save"></i> Save
      </button>
      <button id="download-btn" class="btn-info" style="display: none;">
        <i class="fas fa-download"></i> Download
      </button>
      <button id="init-collab-btn" class="btn-warning" style="display: none;">
        <i class="fas fa-sync"></i> Initialize Collaboration
      </button>
      <button id="debug-collab-btn" class="btn-info" style="display: none;">
        <i class="fas fa-bug"></i> Debug Collaboration
      </button>
      <button id="simulate-user-btn" class="btn-warning" style="display: none;">
        <i class="fas fa-user-plus"></i> Simulate User
      </button>
    </div>
  </div>

  <div class="editor-main">
    <!-- Top section with file explorer and editor -->
    <div class="editor-top-section">
      <div class="file-explorer">
        <!-- Sidebar Navigation Tabs -->
        <div class="sidebar-tabs">
          <button class="sidebar-tab active" data-panel="explorer" title="Explorer">
            <i class="fas fa-folder"></i>
          </button>
          <button class="sidebar-tab" data-panel="extensions" title="Extensions">
            <i class="fas fa-puzzle-piece"></i>
          </button>
        </div>

        <!-- Explorer Panel -->
        <div id="explorer-panel" class="sidebar-panel active">
          <div class="file-explorer-header">
            <h3>Project Files</h3>
            <div class="file-controls">
              <input
                type="text"
                id="file-search"
                placeholder="Search files..."
                class="file-search"
              />
              <button id="new-file-btn" class="btn-sm btn-primary" title="New File">
                <i class="fas fa-plus"></i><i class="fas fa-file"></i>
              </button>
              <button
                id="new-folder-btn"
                class="btn-sm btn-primary"
                title="New Folder"
              >
                <i class="fas fa-plus"></i><i class="fas fa-folder"></i>
              </button>
              <button
                id="upload-file-btn"
                class="btn-sm btn-primary"
                title="Upload Files"
              >
                <i class="fas fa-upload"></i>
              </button>
              <input
                type="file"
                id="file-upload-input"
                multiple
                style="display: none;"
              />
            </div>
          </div>
          <div id="file-tree" class="file-tree"></div>
          <div id="file-tabs" class="file-tabs"></div>
        </div>

        <!-- Extensions Panel -->
        <div id="extensions-panel" class="sidebar-panel extensions-panel">
          <div class="extensions-header">
            <span>Extensions</span>
            <button id="refresh-extensions-btn" class="btn-sm" title="Refresh Extensions">
              <i class="fas fa-sync"></i>
            </button>
          </div>

          <!-- Extension Items -->
          <div class="extension-item" data-extension="blackbox">
            <div class="extension-icon blackbox">
              <i class="fas fa-robot"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">Blackbox AI</div>
              <div class="extension-description">AI-powered code completion and generation</div>
            </div>
            <div class="extension-toggle active" data-extension="blackbox"></div>
            <div class="extension-status enabled">Enabled</div>
          </div>

          <div class="extension-item" data-extension="emmet">
            <div class="extension-icon emmet">
              <i class="fas fa-code"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">Emmet</div>
              <div class="extension-description">HTML/CSS abbreviation expansion</div>
            </div>
            <div class="extension-toggle active" data-extension="emmet"></div>
            <div class="extension-status enabled">Enabled</div>
          </div>

          <div class="extension-item" data-extension="prettier">
            <div class="extension-icon prettier">
              <i class="fas fa-magic"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">Prettier</div>
              <div class="extension-description">Code formatter for multiple languages</div>
            </div>
            <div class="extension-toggle" data-extension="prettier"></div>
            <div class="extension-status">Disabled</div>
          </div>

          <div class="extension-item" data-extension="eslint">
            <div class="extension-icon eslint">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">ESLint</div>
              <div class="extension-description">JavaScript linting and error detection</div>
            </div>
            <div class="extension-toggle" data-extension="eslint"></div>
            <div class="extension-status">Disabled</div>
          </div>

          <div class="extension-item" data-extension="typescript">
            <div class="extension-icon typescript">
              <i class="fab fa-js-square"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">TypeScript</div>
              <div class="extension-description">TypeScript language support and IntelliSense</div>
            </div>
            <div class="extension-toggle" data-extension="typescript"></div>
            <div class="extension-status">Disabled</div>
          </div>

          <div class="extension-item" data-extension="intellisense">
            <div class="extension-icon">
              <i class="fas fa-lightbulb"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">IntelliSense</div>
              <div class="extension-description">Advanced code completion and suggestions</div>
            </div>
            <div class="extension-toggle active" data-extension="intellisense"></div>
            <div class="extension-status enabled">Enabled</div>
          </div>

          <div class="extension-item" data-extension="syntax-highlighting">
            <div class="extension-icon">
              <i class="fas fa-palette"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">Syntax Highlighting</div>
              <div class="extension-description">Enhanced syntax highlighting for all languages</div>
            </div>
            <div class="extension-toggle active" data-extension="syntax-highlighting"></div>
            <div class="extension-status enabled">Enabled</div>
          </div>

          <div class="extension-item" data-extension="auto-complete">
            <div class="extension-icon">
              <i class="fas fa-keyboard"></i>
            </div>
            <div class="extension-info">
              <div class="extension-name">Auto Complete</div>
              <div class="extension-description">Smart auto-completion for faster coding</div>
            </div>
            <div class="extension-toggle active" data-extension="auto-complete"></div>
            <div class="extension-status enabled">Enabled</div>
          </div>

          <!-- Extension Commands Panel -->
          <div class="extension-commands" id="extension-commands">
            <div class="commands-header">
              <h4>Extension Commands</h4>
            </div>
            <button class="command-button" id="format-document">Format Document</button>
            <button class="command-button" id="fix-all-problems">Fix All Problems</button>
            <button class="command-button secondary" id="toggle-word-wrap">Toggle Word Wrap</button>
            <button class="command-button secondary" id="toggle-minimap">Toggle Minimap</button>
          </div>
        </div>
      </div>

      <div class="editor-content" id="editor-content">
        <div id="monaco-editor"></div>
        <div class="monaco-logo-overlay">
          <img src="{% static 'img/ForgeX-Logo(Transparant).png' %}" alt="ForgeX Logo" />
        </div>
      </div>
    </div>

    <!-- Bottom section with output panel -->
    <div class="panel-container">
      <div class="panel-tabs">
        <button id="output-tab" class="panel-tab">
          <i class="fas fa-play"></i> Output
        </button>
        <button id="terminal-tab" class="panel-tab active">
          <i class="fas fa-terminal"></i> Terminal
        </button>
        <button id="version-control-tab" class="panel-tab">
          <i class="fas fa-code-branch"></i> Version Control
        </button>

        <div class="panel-controls">
          <button id="toggle-panel-btn" class="btn-sm" title="Toggle Panel">
            <i class="fas fa-chevron-down"></i>
          </button>
        </div>
      </div>

      <div id="output-panel" class="panel-content">
        <div class="panel-header">
          <h3>Execution Output</h3>
          <button id="clear-output-btn" class="btn-sm btn-secondary">
            <i class="fas fa-trash"></i> Clear
          </button>
        </div>
        <div id="output-content"></div>
      </div>

      <div id="terminal-panel" class="panel-content active">
        <div class="panel-header">
          <h3>Terminal</h3>
          <div class="terminal-controls">
            <button id="new-terminal-btn" class="btn-sm btn-primary" title="New Terminal">
              <i class="fas fa-plus"></i> New
            </button>
            <button id="clear-terminal-btn" class="btn-sm btn-secondary" title="Clear Terminal">
              <i class="fas fa-trash"></i> Clear
            </button>
          </div>
        </div>
        <div id="terminal-tabs-container">
          <!-- Terminal tabs will be dynamically added here -->
        </div>
        <div id="terminal-content">
          <!-- Terminal instances will be dynamically added here -->
        </div>
      </div>

      <div id="version-control-panel" class="panel-content">
        <div class="panel-header">
          <h3>Version Control</h3>
          <div class="version-control-controls">
            <button id="commit-btn" class="btn-sm btn-primary" title="Create Commit">
              <i class="fas fa-save"></i> Commit
            </button>
            <button id="history-btn" class="btn-sm btn-secondary" title="View History">
              <i class="fas fa-history"></i> History
            </button>
            <button id="branches-btn" class="btn-sm btn-secondary" title="Manage Branches">
              <i class="fas fa-code-branch"></i> Branches
            </button>
            <button id="debug-vc-btn" class="btn-sm btn-info" title="Debug Version Control">
              <i class="fas fa-bug"></i> Debug
            </button>
          </div>
        </div>
        <div id="version-control-content">
          <div id="vc-status" class="vc-section">
            <h4><i class="fas fa-info-circle"></i> Repository Status</h4>
            <div id="vc-status-content">
              <p class="vc-info">Ready to commit changes</p>
              <div id="vc-branch-info">
                <span class="vc-branch">
                  <i class="fas fa-code-branch"></i>
                  <span id="current-branch">main</span>
                </span>
              </div>
            </div>
          </div>

          <div id="vc-history" class="vc-section" style="display: none;">
            <h4><i class="fas fa-history"></i> Commit History</h4>
            <div id="vc-history-content">
              <div id="commits-list" class="commits-container">
                <!-- Commits will be loaded here -->
              </div>
            </div>
          </div>
        </div>
      </div>




    </div>
  </div>
</div>

<div id="context-menu" class="context-menu">
  <ul>
    <li id="rename-item"><i class="fas fa-edit"></i> Rename</li>
    <li id="duplicate-item"><i class="fas fa-copy"></i> Duplicate</li>
    <li id="delete-item"><i class="fas fa-trash"></i> Delete</li>
    <li id="download-item"><i class="fas fa-download"></i> Download</li>
    <li id="new-file-item"><i class="fas fa-file"></i> New File</li>
    <li id="new-folder-item"><i class="fas fa-folder"></i> New Folder</li>
    <li id="upload-files-item"><i class="fas fa-upload"></i> Upload Files</li>
    <li id="copy-path-item"><i class="fas fa-copy"></i> Copy Path</li>
    <li id="paste-item"><i class="fas fa-paste"></i> Paste</li>
  </ul>
</div>


<div id="file-modal" class="modal">
  <div class="modal-content">
    <span class="close-modal">&times;</span>
    <h2 id="modal-title">Create New File</h2>
    <input type="text" id="file-name-input" placeholder="Enter file name" />
    <div class="modal-buttons">
      <button id="confirm-modal-btn" class="btn-primary">Create</button>
      <button id="cancel-modal-btn" class="btn-secondary">Cancel</button>
    </div>
  </div>
</div>

<!-- Version Control Commit Modal -->
<div id="commit-modal" class="modal">
  <div class="modal-content">
    <span class="close-modal" id="close-commit-modal">&times;</span>
    <h2><i class="fas fa-save"></i> Create Commit</h2>
    <div class="commit-form">
      <label for="commit-message">Commit Message:</label>
      <textarea id="commit-message" placeholder="Describe your changes..." rows="3"></textarea>

      <label for="commit-branch">Branch:</label>
      <select id="commit-branch">
        <option value="main">main</option>
      </select>

      <div class="commit-files-preview">
        <h4>Files to be committed:</h4>
        <div id="commit-files-list">
          <!-- Files will be listed here -->
        </div>
      </div>
    </div>
    <div class="modal-buttons">
      <button id="confirm-commit-btn" class="btn-primary">
        <i class="fas fa-save"></i> Commit Changes
      </button>
      <button id="cancel-commit-btn" class="btn-secondary">Cancel</button>
    </div>
  </div>
</div>

<!-- Version Control History Modal -->
<div id="history-modal" class="modal">
  <div class="modal-content history-modal-content">
    <span class="close-modal" id="close-history-modal">&times;</span>
    <h2><i class="fas fa-history"></i> Commit History</h2>
    <div class="history-controls">
      <select id="history-branch-filter">
        <option value="">All Branches</option>
        <option value="main">main</option>
      </select>
      <button id="refresh-history-btn" class="btn-sm btn-secondary">
        <i class="fas fa-sync"></i> Refresh
      </button>
    </div>
    <div id="history-list" class="history-container">
      <!-- History will be loaded here -->
    </div>
  </div>
</div>







<button id="collabToggle" class="collab-button">🤝</button>
<div id="collabPanel" class="collab-panel hidden">
  <div class="collab-tabs">
    <button class="tab-btn active" data-tab="chat">💬 Chat</button>
    <button class="tab-btn" data-tab="voice">🎤 Voice</button>
    <button class="tab-btn" data-tab="chatbot">🤖 Chatbot</button>
  </div>

  <div class="tab-content active" id="chat-tab">
    <div class="chat-container">
      <div class="chat-messages" id="chatMessages"></div>
      <div class="chat-input-container">
        <input type="text" id="chatInput" placeholder="Type a message..." />
        <button id="sendMessage" class="send-btn">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
    <div class="user-list">
      <h3>👤 Users in Chat</h3>
      <ul id="chat-user-list"></ul>
    </div>
  </div>

  <div class="tab-content" id="voice-tab">
    <div class="voice-container">
      <div class="voice-controls">
        <button id="startCall" class="voice-btn">
          <i class="fas fa-microphone"></i> Start Voice Chat
        </button>
        <button id="endCall" class="voice-btn" style="display: none">
          <i class="fas fa-microphone-slash"></i> End Voice Chat
        </button>
      </div>
      <div class="voice-status">
        <div class="mic-status">
          <span class="status-dot"></span>
          <span class="status-text">Microphone: Off</span>
        </div>
        <div class="connection-status">
          <span class="status-dot"></span>
          <span class="status-text">Connection: Disconnected</span>
        </div>
      </div>
    </div>
    <div class="user-list">
      <h3>🎤 Users in Voice</h3>
      <ul id="voice-user-list"></ul>
    </div>
  </div>

  <div class="tab-content" id="chatbot-tab">
    <div class="chatbot-container">
      <div class="chatbot-header">
        <h3>🤖 Code Assistant</h3>
        <div class="chatbot-controls">
          <button id="clearChatbot" class="btn-sm btn-secondary">
            <i class="fas fa-trash"></i> Clear
          </button>
          <button id="analyzeCode" class="btn-sm btn-primary">
            <i class="fas fa-search"></i> Analyze Current Code
          </button>
        </div>
      </div>
      <div class="chatbot-messages" id="chatbotMessages">
        <div class="chatbot-message system">
          <div class="message-content">
            <p>👋 Hi! I'm your code assistant. I can help you with:</p>
            <ul>
              <li>🐛 Finding and fixing code errors</li>
              <li>💡 Generating code snippets and solutions</li>
              <li>📚 Explaining code concepts</li>
              <li>🔧 Code optimization suggestions</li>
            </ul>
            <p>Just ask me anything about your code!</p>
          </div>
        </div>
      </div>
      <div class="chatbot-input-container">
        <input type="text" id="chatbotInput" placeholder="Ask about your code, request help, or describe what you want to build..." />
        <button id="sendChatbotMessage" class="send-btn">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>
  </div>
</div>


<style>
  /* ===== RESET AND VIEWPORT FITTING ===== */

  /* Reset default margins and padding to eliminate scrollbars */
  * {
    box-sizing: border-box;
  }

  html, body {
    margin: 0 !important;
    padding: 0 !important;
    height: 100vh !important;
    width: 100vw !important;
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
  }

  /* Ensure the Django base template doesn't add any margins */
  .container, .container-fluid {
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
    width: 100% !important;
    height: 100% !important;
  }

  /* ===== MAIN EDITOR LAYOUT IMPROVEMENTS ===== */

  /* Editor Container - Compact VS Code-like layout */
  .editor-container {
    display: flex;
    flex-direction: column;
    height: 91vh !important;
    width: 100vw !important;
    background: #1e1e1e;
    color: #cccccc;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    margin-top: 9vh !important;
  }

  /* Editor Header - Compact VS Code-style toolbar */
  .editor-header {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 6px 12px;
    display: flex !important;
    justify-content: space-between;
    align-items: center;
    z-index: 1000 !important;
    min-height: 47px !important;
    height: 47px !important;
    box-shadow: none;
    position: relative !important;
    width: 100% !important;
    flex-shrink: 0 !important;
  }

  .editor-header h1 {
    color: #cccccc !important;
    font-size: 13px !important;
    font-weight: 400;
    margin: 0 !important;
    letter-spacing: 0;
    text-shadow: none;
    display: block !important;
    visibility: visible !important;
  }

  /* Editor Controls - Compact VS Code-style buttons */
  .editor-controls {
    display: flex !important;
    gap: 4px;
    align-items: center;
    flex-wrap: wrap;
    visibility: visible !important;
  }

  .editor-controls button {
    background: #3c3c3c;
    border: 1px solid #464647;
    color: #cccccc;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 400;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    height: 24px;
  }

  .editor-controls button:hover {
    background: #464647;
    border-color: #6c6c6c;
  }

  .editor-controls button:active {
    background: #094771;
  }

  /* Ensure run button is always visible */
  #run-code-btn {
    display: flex !important;
    visibility: visible !important;
  }

  /* Force hide specific buttons */
  #preview-btn,
  #save-btn,
  #download-btn,
  #debug-collab-btn,
  #simulate-user-btn {
    display: none !important;
  }



  /* Hide output tab and panel - only show terminal tab */
  #output-tab,
  #output-panel {
    display: none !important;
  }

  /* Make terminal tab active by default */
  #terminal-tab.active {
    color: #ffffff !important;
    border-bottom-color: #007acc !important;
    background: #1e1e1e !important;
  }

  /* Make version control tab styling */
  #version-control-tab.active {
    color: #ffffff !important;
    border-bottom-color: #007acc !important;
    background: #1e1e1e !important;
  }

  /* Panel display logic */
  #terminal-panel.active {
    display: flex !important;
  }

  #version-control-panel.active {
    display: flex !important;
  }

  /* Main Editor Layout - Compact VS Code style */
  .editor-main {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: calc(100vh - 47px) !important; /* Account for header height */
    overflow: hidden !important;
    min-height: 0;
  }

  /* Top section containing file explorer and editor */
  .editor-top-section {
    display: flex;
    flex: 1;
    min-height: 0; /* Important for proper flex behavior */
    overflow: hidden;
  }

  /* File Explorer - Compact VS Code sidebar */
  .file-explorer {
    width: 325px;
    min-width: 180px;
    max-width: 350px;
    background: #252526;
    border-right: 1px solid #2d2d30;
    display: flex;
    flex-direction: column;
 
    overflow: hidden;
  }

  .collab-panel {
    position: fixed;
    bottom: 90px;
    right: 20px;
    width: 360px;
    height: 500px;
    background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(40,40,40,0.98) 100%);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    display: flex;
    flex-direction: column;
    z-index: 999;
    color: #fff;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  }

  /* File Explorer Header - Compact VS Code design */
  .file-explorer-header {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 6px 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .file-explorer-header h3 {
    margin: 0;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: #cccccc;
    letter-spacing: 0.5px;
  }

  .file-controls {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  .file-search {
    background: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 3px;
    padding: 4px 6px;
    color: #cccccc;
    font-size: 12px;
    width: 100px;
    outline: none;
    transition: all 0.15s ease;
  }

  .file-search:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
  }

  .file-search::placeholder {
    color: #858585;
  }

  /* Compact button styling for file controls */
  .btn-sm {
    background: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px 6px;
    color: #cccccc;
    cursor: pointer;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
    min-width: 20px;
    height: 20px;
  }

  .btn-sm:hover {
    background: #3c3c3c;
    border-color: #464647;
  }

  .btn-sm:active {
    background: #094771;
  }

  .btn-sm i {
    font-size: 10px;
  }

  /* Editor Content Area - Monaco container with logo overlay */
  .editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #1e1e1e;
    position: relative;
    overflow: hidden;
    min-height: 0; /* Important for proper flex behavior */
  }

  #monaco-editor {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    position: relative;
  }

  /* Project Logo Watermark in Monaco Editor */
  .monaco-logo-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 1;
    opacity: 0.03;
    transition: opacity 0.3s ease;
  }

  .monaco-logo-overlay img {
    width: 200px;
    height: auto;
    filter: grayscale(100%) brightness(1.5);
  }

  /* Show logo more prominently when editor is empty */
  .editor-content.empty .monaco-logo-overlay {
    opacity: 0.08;
    animation: logoFloat 6s ease-in-out infinite;
  }

  .editor-content.empty .monaco-logo-overlay img {
    width: 300px;
  }

  /* Floating animation for logo */
  @keyframes logoFloat {
    0%, 100% { transform: translate(-50%, -50%) translateY(0px); }
    50% { transform: translate(-50%, -50%) translateY(-10px); }
  }

  /* Responsive logo sizing */
  @media (max-width: 768px) {
    .monaco-logo-overlay img {
      width: 150px;
    }

    .editor-content.empty .monaco-logo-overlay img {
      width: 200px;
    }
  }

  /* Panel Container - Compact VS Code style bottom panel */
  .panel-container {
    height: 400px;  /* Increased from 250px to show more terminal output */
    min-height: 200px;  /* Increased minimum height */
    max-height: 60vh;  /* Increased max height */
    background: #252526;
    border-top: 1px solid #2d2d30;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: height 0.3s ease;
  }

  /* Panel container when collapsed */
  .panel-container.collapsed {
    height: 35px !important;
    min-height: 35px !important;
    max-height: 35px !important;
    resize: none !important;
    overflow: hidden !important;
  }

  .panel-container.collapsed .panel-content {
    display: none !important;
    opacity: 0;
    visibility: hidden;
  }

  /* Smooth transition for panel content */

  /* Panel Tabs - Compact VS Code tab design */
  .panel-tabs {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    align-items: center;
    padding: 0;
    min-height: 30px;
    flex-shrink: 0;
  }

  .panel-controls {
    margin-left: auto;
    padding: 0 8px;
    display: flex;
    align-items: center;
  }

  #toggle-panel-btn {
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.4);
    color: var(--color-border);
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 28px;
  }

  #toggle-panel-btn:hover {
    background: rgba(192, 255, 107, 0.2);
    border-color: var(--color-border);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3);
  }

  #toggle-panel-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(192, 255, 107, 0.2);
  }

  #toggle-panel-btn i {
    transition: transform 0.3s ease;
  }

  .panel-container.collapsed #toggle-panel-btn i {
    transform: rotate(180deg);
  }

  /* Add visual feedback when panel is collapsed */
  .panel-container.collapsed .panel-tabs {
    background: linear-gradient(135deg, rgba(192, 255, 107, 0.1) 0%, rgba(40,40,40,0.95) 100%);
    border-bottom: 1px solid rgba(192, 255, 107, 0.4);
  }

  .panel-tab {
    background: transparent;
    border: none;
    color: #888;
    padding: 6px 12px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    border-bottom: 2px solid transparent;
    position: relative;
    height: 30px;
  }

  .panel-tab:hover {
    color: #cccccc;
    background: #3c3c3c;
  }

  .panel-tab.active {
    color: #ffffff;
    border-bottom-color: #007acc;
    background: #1e1e1e;
  }

  .panel-tab i {
    font-size: 1rem;
  }

  /* Panel Content - Enhanced content areas */
  .panel-content {
    display: none !important;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    opacity: 1;
    visibility: visible;
  }

  .panel-content.active {
    display: flex !important;
  }

  .panel-header {
    background: rgba(40, 40, 40, 0.8);
    border-bottom: 1px solid rgba(192, 255, 107, 0.2);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-border);
    text-shadow: 0 0 5px rgba(192, 255, 107, 0.3);
  }

  /* Output Content - Compact styling */
  #output-content {
    flex: 1;
    padding: 8px 12px;
    background: #1e1e1e;
    color: #cccccc;
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
    font-size: 12px;
    line-height: 1.3;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  /* ===== TERMINAL STYLES ===== */

  /* Terminal Panel */
  #terminal-panel {
    flex-direction: column;
    height: 100%;
    background: #1e1e1e;
    overflow: hidden;
  }

  /* Terminal Controls */
  .terminal-controls {
    display: flex;
    gap: 6px;
    align-items: center;
  }





  /* Terminal Tabs Container */
  #terminal-tabs-container {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 4px 8px;
    display: flex;
    align-items: center;
    gap: 4px;
    overflow-x: auto;
    min-height: 32px;
  }

  /* Terminal Tab */
  .terminal-tab {
    background: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 3px 3px 0 0;
    padding: 4px 12px;
    color: #cccccc;
    cursor: pointer;
    font-size: 12px;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.15s ease;
    white-space: nowrap;
    min-width: 100px;
    max-width: 200px;
    position: relative;
    user-select: none;
  }

  .terminal-tab:hover {
    background: #464647;
    border-color: #6c6c6c;
  }

  .terminal-tab.active {
    background: #1e1e1e;
    border-color: #007acc;
    border-bottom-color: #1e1e1e;
    color: #ffffff;
  }

  .terminal-tab-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .terminal-tab-close {
    color: #888;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 2px;
    transition: all 0.15s ease;
    line-height: 1;
  }

  .terminal-tab-close:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff6b6b;
  }

  /* Terminal Content */
  #terminal-content {
    flex: 1;
    background: #1e1e1e;
    position: relative;
    overflow: hidden;
    min-height: 0; /* Important for proper flex behavior */
  }

  /* Terminal Instance */
  .terminal-instance {
    width: 100%;
    height: 100%;
    background: #1e1e1e;
    display: none;
    position: relative;
    overflow-y: scroll !important;
  }

  .terminal-instance.active {
    display: flex;
    flex-direction: column;
  }

  /* xterm.js Container */
  .xterm-container {
    width: 100%;
    height: 100%;
    min-height: 320px;  /* Minimum height for ~15 lines at 14px font */
    flex: 1;
    padding: 8px;
    box-sizing: border-box;
    overflow: hidden;
    position: relative;
  }

  /* Override xterm.js default styles for VS Code theme */
  .xterm-container .xterm {
    background: #1e1e1e !important;
    color: #cccccc !important;
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
    width: 100% !important;
    height: 100% !important;
    
  }

  .xterm-container .xterm .xterm-helper-textarea {
    position: absolute !important;
    left: -9999px !important;
    top: 0 !important;
    width: 0 !important;
    height: 0 !important;
    z-index: -10 !important;
    opacity: 0 !important;
  }

  .xterm-container .xterm-viewport {
    background: #1e1e1e !important;
    overflow-y: scroll !important; /* Always show scrollbar */
    scrollbar-width: auto !important; /* Firefox */
    scrollbar-color: rgba(192, 255, 107, 0.8) rgba(60, 60, 60, 0.8) !important; /* Firefox */
  }

  .xterm-container .xterm-screen {
    background: #1e1e1e !important;
    position: relative !important;
  }

  .xterm-container .xterm-rows {
    position: relative !important;
    top: 0 !important;
    left: 0 !important;
  }

  .xterm-container .xterm-cursor {
    background: #ffffff !important;
  }

  .xterm-container .xterm-selection {
    background: rgba(255, 255, 255, 0.3) !important;
  }

  /* Terminal scrollbar - Always visible and prominent */
  .xterm-container .xterm-viewport::-webkit-scrollbar {
    width: 16px !important;  /* Much wider scrollbar for better visibility */
    background: rgba(40, 40, 40, 0.9) !important;
  }

  .xterm-container .xterm-viewport::-webkit-scrollbar-track {
    background: rgba(40, 40, 40, 0.9) !important;  /* Dark track background */
    border-radius: 8px !important;
    border: 2px solid rgba(30, 30, 30, 0.8) !important;
    margin: 4px !important;
  }

  .xterm-container .xterm-viewport::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(192, 255, 107, 0.9), rgba(150, 200, 80, 0.9)) !important;
    border-radius: 8px !important;
    border: 2px solid rgba(30, 30, 30, 0.3) !important;
    min-height: 40px !important;  /* Larger minimum thumb height */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  }

  .xterm-container .xterm-viewport::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(192, 255, 107, 1.0), rgba(150, 200, 80, 1.0)) !important;
    border-color: rgba(192, 255, 107, 0.5) !important;
    box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3) !important;
  }

  .xterm-container .xterm-viewport::-webkit-scrollbar-thumb:active {
    background: linear-gradient(180deg, rgba(220, 255, 130, 1.0), rgba(180, 220, 100, 1.0)) !important;
    border-color: rgba(192, 255, 107, 0.8) !important;
    box-shadow: 0 2px 12px rgba(192, 255, 107, 0.5) !important;
  }

  /* Scrollbar corner */
  .xterm-container .xterm-viewport::-webkit-scrollbar-corner {
    background: rgba(40, 40, 40, 0.9) !important;
  }

  /* Force scrollbar to always be visible */
  .xterm-container .xterm-viewport {
    scrollbar-gutter: stable !important;
  }

  /* Allow xterm-scroll-area to expand for full output visibility */
  .xterm-scroll-area {
    height: auto !important;
    min-height: 0 !important;
    max-height: none !important;
  }





  /* Collaboration Panel Enhancements */
  .collab-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--color-border), #a8e063);
    border: none;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(192, 255, 107, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .collab-button:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(192, 255, 107, 0.6);
  }

  .collab-tabs {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid rgba(192, 255, 107, 0.2);
    background: rgba(40, 40, 40, 0.8);
  }

  .tab-btn {
    flex: 1;
    padding: 10px 12px;
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 0 4px;
  }

  .tab-btn:hover {
    color: #cccccc;
    background: rgba(192, 255, 107, 0.1);
  }

  .tab-btn.active {
    color: var(--color-border);
    background: rgba(192, 255, 107, 0.2);
    border: 1px solid rgba(192, 255, 107, 0.3);
  }

  .tab-content {
    display: none;
    flex: 1;
    overflow: hidden;
  }

  .tab-content.active {
    display: flex;
    flex-direction: column;
  }

  /* Hidden class for collaboration panel */
  .hidden {
    display: none !important;
  }

  /* Visible state for collaboration panel */
  .collab-panel:not(.hidden) {
    display: flex !important;
  }

  /* Enhanced Modal Styling */
  .modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
  }

  .modal-content {
    background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(40,40,40,0.98) 100%);
    margin: 10% auto;
    padding: 30px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(15px);
    position: relative;
  }

  .close-modal {
    color: #888;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
    position: absolute;
    top: 15px;
    right: 20px;
  }

  .close-modal:hover {
    color: var(--color-border);
  }

  .modal-content h2 {
    color: var(--color-border);
    margin-bottom: 20px;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  }

  .modal-content input {
    width: 100%;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    color: #ffffff;
    font-size: 1rem;
    margin-bottom: 20px;
    outline: none;
    transition: all 0.3s ease;
  }

  .modal-content input:focus {
    border-color: var(--color-border);
    box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  }

  .modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }





  .diff-line {
    padding: 2px 8px;
    white-space: pre-wrap;
    border-left: 3px solid transparent;
  }

  .diff-line.added {
    background: rgba(0, 255, 0, 0.1);
    border-left-color: #00ff00;
    color: #90ee90;
  }

  .diff-line.removed {
    background: rgba(255, 0, 0, 0.1);
    border-left-color: #ff0000;
    color: #ffb3b3;
  }

  .diff-line.unchanged {
    color: #cccccc;
  }

  .backup-form {
    padding: 16px 0;
  }

  .backup-form label {
    display: block;
    margin-bottom: 8px;
    color: var(--color-border);
    font-weight: 600;
  }

  .backup-form input,
  .backup-form textarea {
    width: 100%;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 6px;
    padding: 10px 12px;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 12px;
    outline: none;
    transition: all 0.3s ease;
  }

  .backup-form input:focus,
  .backup-form textarea:focus {
    border-color: var(--color-border);
    box-shadow: 0 0 8px rgba(192, 255, 107, 0.3);
  }

  .backup-form textarea {
    resize: vertical;
    min-height: 80px;
  }

  .backup-preview {
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 6px;
    padding: 12px;
    margin: 12px 0;
  }

  .backup-preview h4 {
    margin: 0 0 8px 0;
    color: var(--color-border);
    font-size: 0.9rem;
  }

  .backup-preview-info {
    font-size: 0.8rem;
    color: #cccccc;
    margin-bottom: 8px;
  }

  .backup-preview-content {
    background: #1e1e1e;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    padding: 8px;
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
    font-size: 0.8rem;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    color: #d4d4d4;
  }

  /* File Tree Styles - Compact VS Code styling */
  #file-tree {
    flex: 1;
    padding: 4px 0;
    overflow-y: auto;
    background: transparent;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    color: #cccccc;
    outline: none;
  }

  .file-item,
  .folder-item {
    display: flex;
    align-items: center;
    padding: 1px 6px;
    cursor: pointer;
    margin: 0;
    user-select: none;
    position: relative;
    height: 20px;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-item:hover,
  .folder-item:hover {
    background-color: #2a2d2e;
  }

  .file-item.selected,
  .folder-item.selected {
    background-color: #094771 !important;
    color: #ffffff;
  }

  .file-item.focused,
  .folder-item.focused {
    outline: 1px solid #007acc;
    outline-offset: -1px;
  }

  .folder-item {
    font-weight: 400;
  }

  .folder-item.expanded {
    color: #cccccc;
  }

  .folder-item i,
  .file-item i {
    margin-right: 6px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
  }

  .folder-children {
    margin-left: 0;
    padding-left: 16px;
    display: none;
  }

  .folder-children.expanded {
    display: block;
  }

  .toggle-icon {
    transition: transform 0.15s ease;
    margin-right: 2px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: #cccccc;
    font-size: 10px;
  }

  .toggle-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  .folder-item.expanded .toggle-icon {
    transform: rotate(90deg);
  }

  /* File name styling */
  .file-name,
  .folder-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* Indentation for nested items */
  .file-item[data-depth="1"],
  .folder-item[data-depth="1"] {
    padding-left: 24px;
  }

  .file-item[data-depth="2"],
  .folder-item[data-depth="2"] {
    padding-left: 40px;
  }

  .file-item[data-depth="3"],
  .folder-item[data-depth="3"] {
    padding-left: 56px;
  }

  .file-item[data-depth="4"],
  .folder-item[data-depth="4"] {
    padding-left: 72px;
  }

  /* Drag and drop visual feedback */
  .file-item.drag-over,
  .folder-item.drag-over {
    background-color: #094771;
    border: 1px dashed #007acc;
  }

  /* Upload progress styles */
  .upload-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #1e1e1e;
    border: 1px solid #007acc;
    border-radius: 8px;
    padding: 20px;
    min-width: 300px;
    max-width: 400px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .upload-progress h3 {
    margin: 0 0 15px 0;
    color: #007acc;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .upload-progress-bar {
    background: #333;
    border-radius: 4px;
    height: 8px;
    margin-bottom: 15px;
    overflow: hidden;
  }

  .upload-progress-bar-inner {
    background: linear-gradient(90deg, #007acc, #00a8ff);
    height: 100%;
    width: 0%;
    transition: width 0.3s ease;
  }

  .upload-list {
    max-height: 200px;
    overflow-y: auto;
  }

  .upload-item {
    padding: 5px 0;
    font-size: 14px;
    color: #cccccc;
    border-bottom: 1px solid #333;
  }

  .upload-item:last-child {
    border-bottom: none;
  }

  .upload-item.success {
    color: #4caf50;
  }

  .upload-item.error {
    color: #f44336;
  }

  .file-item.dragging,
  .folder-item.dragging {
    opacity: 0.5;
  }

  /* Context Menu Styles - VS Code Like */
  .context-menu {
    position: fixed;
    background-color: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 3px;
    padding: 4px 0;
    min-width: 200px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    color: #cccccc;
  }

  .context-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .context-menu li {
    padding: 6px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    color: #cccccc;
    position: relative;
  }

  .context-menu li:hover {
    background-color: #094771;
    color: #ffffff;
  }

  .context-menu li.disabled {
    color: #656565;
    cursor: default;
  }

  .context-menu li.disabled:hover {
    background-color: transparent;
  }

  /* Extension Panel Styles */
  .extensions-panel {
    background: #252526;
    border-right: 1px solid #3e3e42;
    width: 300px;
    height: 100%;
    overflow-y: auto;
    display: none;
    flex-direction: column;
    overflow: scroll !important;
  }

  .extensions-panel.active {
    display: flex;
  }

  .extensions-header {
    background: #2d2d30;
    padding: 15px;
    border-bottom: 1px solid #3e3e42;
    color: #cccccc;
    font-weight: 600;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .extension-item {
    padding: 12px 15px;
    border-bottom: 1px solid #3e3e42;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .extension-item:hover {
    background: #2a2d2e;
  }

  .extension-info {
    flex: 1;
  }

  .extension-name {
    color: #cccccc;
    font-weight: 500;
    margin-bottom: 4px;
    font-size: 13px;
  }

  .extension-description {
    color: #969696;
    font-size: 11px;
    line-height: 1.4;
  }

  .extension-toggle {
    width: 40px;
    height: 20px;
    background: #3c3c3c;
    border-radius: 10px;
    position: relative;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .extension-toggle.active {
    background: #C0ff6b;
  }

  .extension-toggle::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
  }

  .extension-toggle.active::after {
    transform: translateX(20px);
  }

  .extension-status {
    margin-left: 10px;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    background: #3c3c3c;
    color: #cccccc;
  }

  .extension-status.enabled {
    background: #C0ff6b;
    color: #1e1e1e;
  }

  /* Extension Commands Panel */
  .extension-commands {
    background: #1e1e1e;
    border-top: 1px solid #3e3e42;
    padding: 10px;
    display: none;
  }

  .extension-commands.active {
    display: block;
  }

  .command-button {
    background: #0e639c;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin: 2px;
    font-size: 12px;
    transition: background-color 0.2s;
  }

  .command-button:hover {
    background: #1177bb;
  }

  .command-button.secondary {
    background: #5a5a5a;
  }

  .command-button.secondary:hover {
    background: #6a6a6a;
  }

  /* Extension Icon Styles */
  .extension-icon {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 4px;
    background: #3c3c3c;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #cccccc;
  }

  .extension-icon.blackbox {
    background: linear-gradient(135deg, #000000, #333333);
    color: #ffffff;
  }

  .extension-icon.emmet {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    color: #ffffff;
  }

  .extension-icon.prettier {
    background: linear-gradient(135deg, #1a2b34, #56b3d9);
    color: #ffffff;
  }

  .extension-icon.eslint {
    background: linear-gradient(135deg, #4b32c3, #8b5cf6);
    color: #ffffff;
  }

  .extension-icon.typescript {
    background: linear-gradient(135deg, #007acc, #3178c6);
    color: #ffffff;
  }

  /* Sidebar Tab Styles */
  .sidebar-tabs {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    padding: 0;
  }

  .sidebar-tab {
    background: transparent;
    border: none;
    color: #969696;
    padding: 12px 16px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-tab:hover {
    color: #cccccc;
    background: #2a2d2e;
  }

  .sidebar-tab.active {
    color: #ffffff;
    background: #252526;
    border-bottom-color: #C0ff6b;
  }

  .sidebar-panel {
    display: none;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .sidebar-panel.active {
    display: flex;
  }

  /* Commands Header */
  .commands-header {
    padding: 10px 15px;
    border-bottom: 1px solid #3e3e42;
    background: #2d2d30;
  }

  .commands-header h4 {
    margin: 0;
    color: #cccccc;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .context-menu li i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
  }

  .context-menu li.separator {
    height: 1px;
    background-color: #464647;
    margin: 4px 0;
    padding: 0;
  }

  .context-menu li.separator:hover {
    background-color: #464647;
  }

  /* File Explorer - Enhanced VS Code styling */
  .file-explorer {
    background: #252526;
    border-right: 1px solid #2d2d30;
  }

  .file-explorer-header {
    background: #2d2d30;
    padding: 6px 8px;
    border-bottom: 1px solid #3e3e42;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 28px;
  }

  .file-explorer-header h3 {
    margin: 0;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: #cccccc;
    letter-spacing: 0.5px;
  }

  .file-controls {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .file-search {
    background: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 3px;
    padding: 4px 8px;
    color: #cccccc;
    font-size: 12px;
    width: 120px;
    outline: none;
  }

  .file-search:focus {
    border-color: #007acc;
    box-shadow: 0 0 0 1px #007acc;
  }

  .file-search::placeholder {
    color: #858585;
  }

  .btn-sm {
    background: transparent;
    border: 1px solid transparent;
    border-radius: 3px;
    padding: 4px 6px;
    color: #cccccc;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.15s ease;
  }

  .btn-sm:hover {
    background: #3c3c3c;
    border-color: #464647;
  }

  .btn-sm:active {
    background: #094771;
  }

  .btn-sm i {
    font-size: 10px;
  }



  /* File tabs styling - Compact VS Code style */
  .file-tabs {
    background: #2d2d30;
    border-bottom: 1px solid #3e3e42;
    padding: 0;
    display: flex;
    overflow-x: auto;
    max-height: 30px;
  }

  .file-tab {
    background: #2d2d30;
    border-right: 1px solid #3e3e42;
    padding: 6px 10px;
    color: #cccccc;
    cursor: pointer;
    font-size: 11px;
    white-space: nowrap;
    display: flex;
    align-items: center;
    min-width: 0;
    position: relative;
    height: 30px;
  }

  .file-tab:hover {
    background: #3c3c3c;
  }

  .file-tab.active {
    background: #1e1e1e;
    border-bottom: 2px solid #007acc;
    color: #ffffff;
  }

  .file-tab span {
    margin-left: 8px;
    color: #858585;
    font-size: 14px;
    line-height: 1;
  }

  .file-tab span:hover {
    color: #cccccc;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    padding: 2px 4px;
  }

  /* Empty file tree styling */
  .empty-file-tree {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    padding: 20px;
  }

  .empty-message {
    text-align: center;
    max-width: 300px;
  }

  .empty-message .btn-sm {
    background: #007acc;
    color: white;
    border: 1px solid #007acc;
  }

  .empty-message .btn-sm:hover {
    background: #005a9e;
    border-color: #005a9e;
  }

  /* File Icons */
  .fa-file-code {
    color: #4dabf7;
  }
  .fa-file-image {
    color: #40c057;
  }
  .fa-file-pdf {
    color: #fa5252;
  }
  .fa-file-word {
    color: #228be6;
  }
  .fa-file-excel {
    color: #40c057;
  }
  .fa-file-powerpoint {
    color: #fd7e14;
  }
  .fa-file-archive {
    color: #fab005;
  }
  .fa-file-audio {
    color: #be4bdb;
  }
  .fa-file-video {
    color: #e64980;
  }
  .fa-gem {
    color: #fa5252;
  }

  .fa-python {
    color: #fab005;
  }
  .fa-js {
    color: #fab005;
  }
  .fa-react {
    color: #4dabf7;
  }
  .fa-html5 {
    color: #fa5252;
  }
  .fa-css3-alt {
    color: #228be6;
  }
  .fa-java {
    color: #fa5252;
  }
  .fa-php {
    color: #845ef7;
  }

  /* Button styles */
  .btn-warning {
    background-color: #f0ad4e;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .btn-warning:hover {
    background-color: #ec971f;
  }

  .editor-content {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
  #monaco-editor {
    width: 100%;
    height: 100%;
  }


  /* Language Selector Styling */
  .language-selector {
    background: #2d2d2d;
    color: #e0e0e0;
    border: 1px solid #444;
    border-radius: 4px;
    padding: 6px 12px;
    margin-right: 10px;
    font-size: 14px;
    cursor: pointer;
    outline: none;
    transition: all 0.3s ease;
  }

  .language-selector:hover {
    background: #3d3d3d;
    border-color: #555;
  }

  .language-selector:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  }

  .language-selector option {
    background: #2d2d2d;
    color: #e0e0e0;
    padding: 4px 8px;
  }

  /* Monaco Editor Syntax Highlighting Override Styles - Force with !important */
  #monaco-editor .monaco-editor {
    background: #1e1e1e !important;
    color: #d4d4d4 !important;
  }

  /* Python Comments */
  #monaco-editor .mtk1 { color: #d4d4d4 !important; } /* Default text */
  #monaco-editor .mtk2 { color: #6a9955 !important; } /* Comments */
  #monaco-editor .mtk3 { color: #ce9178 !important; } /* Strings */
  #monaco-editor .mtk4 { color: #569cd6 !important; } /* Keywords */
  #monaco-editor .mtk5 { color: #b5cea8 !important; } /* Numbers */
  #monaco-editor .mtk6 { color: #9cdcfe !important; } /* Variables */
  #monaco-editor .mtk7 { color: #c586c0 !important; } /* Functions */
  #monaco-editor .mtk8 { color: #4ec9b0 !important; } /* Classes */

  /* Force Monaco token colors */
  #monaco-editor .token.comment { color: #6a9955 !important; }
  #monaco-editor .token.string { color: #ce9178 !important; }
  #monaco-editor .token.keyword { color: #569cd6 !important; }
  #monaco-editor .token.number { color: #b5cea8 !important; }
  #monaco-editor .token.operator { color: #d4d4d4 !important; }
  #monaco-editor .token.identifier { color: #9cdcfe !important; }
  #monaco-editor .token.function { color: #dcdcaa !important; }

  /* Monaco specific syntax highlighting classes */
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk1 { color: #d4d4d4 !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk2 { color: #6a9955 !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk3 { color: #ce9178 !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk4 { color: #569cd6 !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk5 { color: #b5cea8 !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk6 { color: #9cdcfe !important; }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk7 { color: #dcdcaa !important; }

  /* Enhanced Chat Message Styles - More specific selectors */
  .chat-messages .chat-message {
    margin: 8px 0 !important;
    padding: 10px !important;
    border-radius: 12px !important;
    max-width: 70% !important;
    word-wrap: break-word !important;
    background: none !important; /* Override the generic div background */
    position: relative;
    clear: both;
  }

  .chat-messages .chat-message.own {
    background: rgba(0, 123, 255, 0.15) !important;
    border: 1px solid rgba(0, 123, 255, 0.4) !important;
    margin-left: 30% !important;
    margin-right: 0 !important;
    float: right;
  }

  .chat-messages .chat-message.other {
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    margin-left: 0 !important;
    margin-right: 30% !important;
    float: left;
  }

  .message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
    font-size: 0.8rem;
  }

  .message-sender {
    font-weight: 600;
    color: #007bff;
  }

  .chat-message.own .message-sender {
    color: #28a745;
  }

  .message-time {
    opacity: 0.7;
    font-size: 0.75rem;
  }

  .message-content {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #cccccc;
  }

  .chat-messages .system-message {
    text-align: center !important;
    color: #888 !important;
    font-style: italic !important;
    margin: 8px 0 !important;
    font-size: 0.8rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
    padding: 8px !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    clear: both;
    float: none !important;
    max-width: 90% !important;
    margin-left: auto !important;
    margin-right: auto !important;
  }

  .system-time {
    font-size: 0.7rem;
    opacity: 0.7;
  }

  /* Chat container improvements */
  .chat-messages {
    padding: 0.5rem;
  }

  /* Clearfix for floating chat messages */
  .chat-messages::after {
    content: "";
    display: table;
    clear: both;
  }

  /* Ensure input container is always visible */
  .chat-input-container {
    display: flex !important;
    gap: 8px !important;
    margin-top: 10px !important;
    background: transparent !important;
    padding: 0 !important;
  }

  #chatInput {
    flex: 1 !important;
    padding: 8px 12px !important;
    border: 1px solid #444 !important;
    border-radius: 6px !important;
    background: #2a2a2a !important;
    color: #fff !important;
    outline: none !important;
  }

  .send-btn {
    padding: 8px 12px !important;
    background: #007bff !important;
    border: none !important;
    border-radius: 6px !important;
    color: white !important;
    cursor: pointer !important;
    transition: background 0.3s ease !important;
  }
  #monaco-editor .monaco-editor .view-lines .view-line span.mtk8 { color: #4ec9b0 !important; }

  /* Python specific syntax highlighting */
  #monaco-editor .monaco-editor .token.comment.python { color: #6a9955 !important; }
  #monaco-editor .monaco-editor .token.string.python { color: #ce9178 !important; }
  #monaco-editor .monaco-editor .token.keyword.python { color: #569cd6 !important; }
  #monaco-editor .monaco-editor .token.number.python { color: #b5cea8 !important; }

  /* Override any global text color that might interfere */
  #monaco-editor * {
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
  }

  /* Ensure Monaco editor container has proper styling */
  .monaco-editor .view-lines {
    background: transparent !important;
  }

  .monaco-editor .margin {
    background: #1e1e1e !important;
  }

  .monaco-editor .monaco-editor-background {
    background: #1e1e1e !important;
  }

  /* Chat and Voice Enhancements */
  .chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 12px;
    overflow: hidden;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 12px;
    padding: 12px;
    background: rgba(40, 40, 40, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(192, 255, 107, 0.1);
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .chat-input-container {
    display: flex;
    gap: 8px;
  }

  #chatInput {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 8px;
    background: rgba(40, 40, 40, 0.8);
    color: #fff;
    outline: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  #chatInput:focus {
    border-color: var(--color-border);
    box-shadow: 0 0 8px rgba(192, 255, 107, 0.3);
  }

  .send-btn {
    padding: 10px 14px;
    background: linear-gradient(135deg, var(--color-border), #a8e063);
    border: none;
    border-radius: 8px;
    color: #000;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
  }

  .send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(192, 255, 107, 0.4);
  }

  .voice-container {
    padding: 16px;
    background: rgba(40, 40, 40, 0.8);
    border-radius: 8px;
    margin: 12px;
    border: 1px solid rgba(192, 255, 107, 0.1);
  }

  .voice-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
  }

  .voice-btn {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 8px;
    background: rgba(192, 255, 107, 0.1);
    color: var(--color-border);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
  }

  .voice-btn:hover {
    background: rgba(192, 255, 107, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3);
  }

  .user-list {
    padding: 12px;
    background: rgba(40, 40, 40, 0.8);
    border-radius: 8px;
    margin: 12px;
    border: 1px solid rgba(192, 255, 107, 0.1);
  }

  .user-list h3 {
    margin: 0 0 12px 0;
    font-size: 0.9rem;
    color: var(--color-border);
    font-weight: 600;
  }

  .user-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .user-list li {
    padding: 8px 12px;
    border-radius: 6px;
    background: rgba(192, 255, 107, 0.1);
    margin-bottom: 6px;
    font-size: 0.85rem;
    color: #cccccc;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  /* Chatbot Styles */
  .chatbot-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 12px;
    overflow: hidden;
  }

  .chatbot-header {
    background: rgba(40, 40, 40, 0.8);
    border-bottom: 1px solid rgba(192, 255, 107, 0.2);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px 8px 0 0;
  }

  .chatbot-header h3 {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--color-border);
    text-shadow: 0 0 5px rgba(192, 255, 107, 0.3);
  }

  .chatbot-controls {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .chatbot-messages {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 12px;
    padding: 12px;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.1);
    border-top: none;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .chatbot-message {
    padding: 12px;
    border-radius: 12px;
    max-width: 85%;
    word-wrap: break-word;
    position: relative;
    clear: both;
  }

  .chatbot-message.user {
    background: rgba(0, 123, 255, 0.15);
    border: 1px solid rgba(0, 123, 255, 0.4);
    margin-left: 15%;
    margin-right: 0;
    float: right;
  }

  .chatbot-message.assistant {
    background: rgba(192, 255, 107, 0.15);
    border: 1px solid rgba(192, 255, 107, 0.4);
    margin-left: 0;
    margin-right: 15%;
    float: left;
  }

  .chatbot-message.system {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin: 0 auto;
    text-align: center;
    max-width: 90%;
    float: none;
  }

  .chatbot-message .message-content {
    font-size: 0.9rem;
    line-height: 1.4;
    color: #cccccc;
  }

  .chatbot-message .message-content ul {
    margin: 8px 0;
    padding-left: 20px;
  }

  .chatbot-message .message-content li {
    margin: 4px 0;
  }

  .chatbot-message .message-content .inline-code {
    background: rgba(0, 0, 0, 0.3);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
    font-size: 0.85em;
    color: #f8f8f2;
  }

  /* Code block container */
  .code-block-container {
    margin: 12px 0;
    border-radius: 8px;
    overflow: hidden;
    background: #282828;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  .code-block-header {
    background: #1e1e1e;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(192, 255, 107, 0.1);
  }

  .code-language {
    font-size: 0.75rem;
    color: var(--color-border);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .copy-code-btn {
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    color: var(--color-border);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .copy-code-btn:hover {
    background: rgba(192, 255, 107, 0.2);
    border-color: var(--color-border);
  }

  .code-block {
    background: #282828;
    padding: 16px;
    margin: 0;
    overflow-x: auto;
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    color: #f8f8f2;
  }

  .code-block code {
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
  }

  .chatbot-input-container {
    display: flex;
    gap: 8px;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.1);
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 12px;
  }

  #chatbotInput {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 8px;
    background: rgba(40, 40, 40, 0.8);
    color: #fff;
    outline: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
  }

  #chatbotInput:focus {
    border-color: var(--color-border);
    box-shadow: 0 0 8px rgba(192, 255, 107, 0.3);
  }

  /* Clearfix for floating chatbot messages */
  .chatbot-messages::after {
    content: "";
    display: table;
    clear: both;
  }

  /* Loading indicator for chatbot */
  .chatbot-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: rgba(192, 255, 107, 0.15);
    border: 1px solid rgba(192, 255, 107, 0.4);
    border-radius: 12px;
    margin-left: 0;
    margin-right: 15%;
    float: left;
    max-width: 85%;
  }

  .chatbot-loading .loading-dots {
    display: flex;
    gap: 4px;
  }

  .chatbot-loading .loading-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-border);
    animation: loadingPulse 1.4s ease-in-out infinite both;
  }

  .chatbot-loading .loading-dot:nth-child(1) { animation-delay: -0.32s; }
  .chatbot-loading .loading-dot:nth-child(2) { animation-delay: -0.16s; }
  .chatbot-loading .loading-dot:nth-child(3) { animation-delay: 0; }

  @keyframes loadingPulse {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* Responsive Design Improvements */
  @media (max-width: 1200px) {
    .file-explorer {
      width: 250px;
    }

    .panel-container {
      height: 350px;  /* Increased for better terminal visibility */
    }
  }

  @media (max-width: 768px) {
    .editor-header {
      padding: 8px 12px;
      flex-direction: column;
      gap: 12px;
      min-height: auto;
    }

    .editor-header h1 {
      font-size: 1.2rem;
    }

    .editor-controls {
      justify-content: center;
      flex-wrap: wrap;
    }

    .editor-controls button {
      padding: 6px 12px;
      font-size: 0.8rem;
    }

    .editor-main {
      height: calc(100vh - 100px);
    }

    .editor-top-section {
      flex-direction: column;
    }

    .file-explorer {
      width: 100%;
      height: 150px;
      border-right: none;
      border-bottom: 1px solid rgba(192, 255, 107, 0.2);
      resize: none;
    }

    .panel-container {
      height: 300px;  /* Increased for mobile terminal visibility */
      min-height: 200px;  /* Increased minimum */
      resize: none;
    }

    .collab-panel {
      width: 90%;
      height: 70%;
      bottom: 5%;
      right: 5%;
      left: 5%;
    }
  }

  @media (max-width: 480px) {
    .file-explorer {
      height: 120px;
    }

    .panel-container {
      height: 250px;  /* Increased for small screen terminal visibility */
      min-height: 180px;  /* Increased minimum */
    }

    .panel-tabs {
      flex-wrap: wrap;
      min-height: auto;
    }

    .panel-tab {
      padding: 8px 12px;
      font-size: 0.8rem;
    }
  }

  /* Hide footer in editor */
  .main-footer {
    display: none !important;
  }

  /* Loading and Animation Enhancements */
  .editor-container {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Scrollbar Styling */
  #file-tree::-webkit-scrollbar,
  #output-content::-webkit-scrollbar,
  .chat-messages::-webkit-scrollbar {
    width: 8px;
  }

  #file-tree::-webkit-scrollbar-track,
  #output-content::-webkit-scrollbar-track,
  .chat-messages::-webkit-scrollbar-track {
    background: rgba(40, 40, 40, 0.5);
    border-radius: 4px;
  }

  #file-tree::-webkit-scrollbar-thumb,
  #output-content::-webkit-scrollbar-thumb,
  .chat-messages::-webkit-scrollbar-thumb {
    background: rgba(192, 255, 107, 0.3);
    border-radius: 4px;
  }

  #file-tree::-webkit-scrollbar-thumb:hover,
  #output-content::-webkit-scrollbar-thumb:hover,
  .chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(192, 255, 107, 0.5);
  }

  /* ===== VERSION CONTROL STYLES ===== */

  /* Version Control Panel */
  .version-control-controls {
    display: flex;
    gap: 6px;
    align-items: center;
  }

  #version-control-content {
    flex: 1;
    padding: 12px;
    overflow-y: auto;
    background: #1e1e1e;
  }

  .vc-section {
    margin-bottom: 16px;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 8px;
    padding: 12px;
  }

  .vc-section h4 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: var(--color-border);
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .vc-info {
    color: #cccccc;
    font-size: 0.85rem;
    margin: 4px 0;
  }

  .vc-branch {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8rem;
    color: var(--color-border);
  }

  /* Commit Modal Styles */
  .commit-form {
    margin: 16px 0;
  }

  .commit-form label {
    display: block;
    margin-bottom: 6px;
    color: var(--color-border);
    font-weight: 600;
    font-size: 0.9rem;
  }

  .commit-form textarea,
  .commit-form select {
    width: 100%;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 6px;
    padding: 10px 12px;
    color: #ffffff;
    font-size: 0.9rem;
    margin-bottom: 12px;
    outline: none;
    transition: all 0.3s ease;
    resize: vertical;
  }

  .commit-form textarea:focus,
  .commit-form select:focus {
    border-color: var(--color-border);
    box-shadow: 0 0 8px rgba(192, 255, 107, 0.3);
  }

  .commit-files-preview {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 6px;
    padding: 12px;
    margin-top: 12px;
  }

  .commit-files-preview h4 {
    margin: 0 0 8px 0;
    font-size: 0.85rem;
    color: var(--color-border);
  }

  #commit-files-list {
    max-height: 150px;
    overflow-y: auto;
  }

  .commit-file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
    font-size: 0.8rem;
    color: #cccccc;
    border-bottom: 1px solid rgba(192, 255, 107, 0.1);
  }

  .commit-file-item:last-child {
    border-bottom: none;
  }

  .commit-file-item i {
    color: var(--color-border);
    width: 16px;
  }

  /* History Modal Styles */
  .history-modal-content {
    max-width: 800px;
    width: 90%;
    position: relative;
    left: 0;
  }

  .history-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 6px;
  }

  .history-controls select {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 4px;
    padding: 6px 10px;
    color: #ffffff;
    font-size: 0.85rem;
    outline: none;
  }

  .history-container {
    max-height: 400px;
    overflow-y: auto;
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 6px;
    padding: 8px;
  }

  .commits-container {
    max-height: 300px;
    overflow-y: auto;
  }

  .commit-item {
    background: rgba(40, 40, 40, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.2);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
  }

  .commit-item:hover {
    background: rgba(50, 50, 50, 0.9);
    border-color: rgba(192, 255, 107, 0.4);
  }

  .commit-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }

  .commit-hash {
    font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
    font-size: 0.8rem;
    color: var(--color-border);
    background: rgba(192, 255, 107, 0.1);
    padding: 2px 6px;
    border-radius: 3px;
  }

  .commit-message {
    font-size: 0.9rem;
    color: #ffffff;
    font-weight: 500;
    margin: 4px 0;
  }

  .commit-meta {
    display: flex;
    gap: 12px;
    align-items: center;
    font-size: 0.75rem;
    color: #888;
  }

  .commit-author {
    color: var(--color-border);
  }

  .commit-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
  }

  .commit-actions button {
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    color: var(--color-border);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .commit-actions button:hover {
    background: rgba(192, 255, 107, 0.2);
    border-color: var(--color-border);
  }

  .commit-actions .restore-btn {
    background: rgba(255, 165, 0, 0.1);
    border-color: rgba(255, 165, 0, 0.3);
    color: #ffa500;
  }

  .commit-actions .restore-btn:hover {
    background: rgba(255, 165, 0, 0.2);
    border-color: #ffa500;
  }

  /* Debug button styling */
  #debug-vc-btn {
    background: rgba(0, 123, 255, 0.1);
    border-color: rgba(0, 123, 255, 0.3);
    color: #007bff;
  }

  #debug-vc-btn:hover {
    background: rgba(0, 123, 255, 0.2);
    border-color: #007bff;
  }
</style>

{% endblock %} {% block extra_js %}
<!-- Load Yjs modules first -->
{% comment %} <script src="{% static 'js/yjs/yjs.js' %}"></script>
<script src="{% static 'js/yjs/y-websocket.js' %}"></script>
<script src="{% static 'js/yjs/y-monaco.js' %}"></script> {% endcomment %}

{% comment %} <script type="module" src="{% static 'js/editor.js' %}"></script> {% endcomment %}

  <!-- Yjs and related dependencies - using local files -->
  <script src="{% static 'js/yjs/yjs.js' %}"></script>
  <script src="{% static 'js/yjs/y-websocket.js' %}"></script>
  <script src="{% static 'js/yjs/y-monaco.js' %}"></script>

  <script>
    // Check if libraries are loaded correctly
    document.addEventListener('DOMContentLoaded', function() {
      console.log("Checking Yjs libraries:");

      const yLoaded = typeof Y !== 'undefined';
      const wsProviderLoaded = typeof WebsocketProvider !== 'undefined';
      const monacoBindingLoaded = typeof MonacoBinding !== 'undefined';

      console.log("Y:", yLoaded ? "Loaded" : "Not loaded");
      console.log("WebsocketProvider:", wsProviderLoaded ? "Loaded" : "Not loaded");
      console.log("MonacoBinding:", monacoBindingLoaded ? "Loaded" : "Not loaded");
    });
  </script>

<!-- Monaco Extensions and Libraries -->
<!-- Emmet for Monaco -->
<script src="https://cdn.jsdelivr.net/npm/emmet-monaco-es@latest/dist/emmet-monaco.min.js"></script>

<!-- Monaco Editor Core -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.33.0/min/vs/loader.min.js"></script>



<script>

  let editor;
  let currentFile = null;
  let fileTree = {};
  let term;
  const projectId = "{{ project.id }}";
  const projectHash = "{{ project.hash }}";
  const apiBaseUrl = "{{ API_BASE_URL }}";
  const fastApiStatus = "{{ fastapi_status }}";

  // Make these variables globally accessible
  window.projectId = projectId;
  window.projectHash = projectHash;
  window.apiBaseUrl = apiBaseUrl;
  window.fastApiStatus = fastApiStatus;

  // Debug information
  console.log("🔧 Editor Debug Info:");
  console.log(`📁 Project ID: ${projectId}`);
  console.log(`🔗 Project Hash: ${projectHash}`);
  console.log(`🌐 API Base URL: ${apiBaseUrl}`);
  console.log(`📊 FastAPI Status: ${fastApiStatus}`);

  // WebSocket configuration - Centralized from Django settings
  const baseWsUrl = "{{ WEBSOCKET_BASE_URL }}";
  const websocketUrls = {
    collaboration: "{{ WEBSOCKET_URLS.collaboration }}",
    collabChat: "{{ WEBSOCKET_URLS.collab_chat }}",
    voice: "{{ WEBSOCKET_URLS.voice }}",
    fileChanges: "{{ WEBSOCKET_URLS.file_changes }}",
    yjsRoom: "{{ WEBSOCKET_URLS.yjs_room }}",
    notifications: "{{ WEBSOCKET_URLS.notifications }}"
  };

  // Make WebSocket URLs globally accessible
  window.baseWsUrl = baseWsUrl;
  window.websocketUrls = websocketUrls;

  // Upload functionality variables
  let uploadProgressContainer = null;
  let uploadProgressBar = null;
  const projectDirectory = "{{ project.directory }}";

  let expandedFolders = new Set();
  let selectedItemPath = null;

  // Global collaboration state - PROPER user tracking system
  let collaborationEnabled = false;
  let currentYjsBinding = null;
  let currentYjsProvider = null;
  let currentYjsDoc = null;
  let isSettingEditorContent = false; // Flag to prevent feedback loops
  let autoSaveTimeout = null;
  let fileChangeTimeout = null;
  let currentCollaborationFile = null; // Currently active collaboration file
  let hasUnsavedChanges = false; // Track if current file has unsaved changes

  // Enhanced state management for file isolation - FIXED VERSION
  let isFileSwitching = false;
  let collaborationSessionId = null;
  let pendingCollaborationCleanup = false;
  let fileCollaborationMap = new Map(); // Track collaboration state per file
  let fileModelMap = new Map(); // Track Monaco models per file for proper cleanup

  // CURSOR PRESERVATION SYSTEM
  let preservedCursorPosition = null;
  let cursorPreservationTimeout = null;
  let isCollaborationInitializing = false;

  // REAL user tracking system with WebSocket presence
  let activeUsersPerFile = new Map(); // Map<filePath, Set<userId>>
  let currentUserId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  let userPresenceSocket = null;
  let userPresenceHeartbeat = null;
  let lastPresenceUpdate = 0;

  // Generate unique window identifier for this browser tab/window
  const windowInstanceId = `window-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  console.log(`🪟 Window Instance ID: ${windowInstanceId}`);
  console.log(`👤 User ID: ${currentUserId}`);



  // Get project title from the page
  const projectTitle = "{{ project.title }}";

  // === REAL User Presence System ===

  // Initialize user presence using YJS awareness
  function initializeUserPresence() {
    console.log(`🌐 PRESENCE: Using YJS awareness for user presence tracking`);

    // Start heartbeat to maintain presence
    if (userPresenceHeartbeat) {
      clearInterval(userPresenceHeartbeat);
    }

    userPresenceHeartbeat = setInterval(() => {
      if (currentFile && collaborationEnabled) {
        updateUserPresenceViaYJS(currentFile);
      }
    }, 5000); // Update presence every 5 seconds

    console.log(`✅ PRESENCE: User presence system initialized with YJS awareness`);
  }

  // Update user presence via YJS awareness
  function updateUserPresenceViaYJS(filePath) {
    // We'll use the YJS provider's awareness to track users
    // This is a simplified approach that works with existing infrastructure
    console.log(`📡 PRESENCE: Updating presence for file: ${filePath} via YJS awareness`);
  }

  // Send user presence update
  function sendUserPresence(filePath) {
    if (userPresenceSocket && userPresenceSocket.readyState === WebSocket.OPEN && filePath) {
      const message = {
        type: 'user_presence',
        user_id: currentUserId,
        window_id: windowInstanceId,
        file_path: filePath,
        timestamp: Date.now()
      };

      userPresenceSocket.send(JSON.stringify(message));
      console.log(`📡 PRESENCE: Sent presence for file: ${filePath}`);
      lastPresenceUpdate = Date.now();
    }
  }

  // Handle presence updates from other users
  function handlePresenceUpdate(data) {
    if (data.type === 'presence_update') {
      console.log(`👥 PRESENCE: Received update:`, data);

      // Update local user tracking
      activeUsersPerFile.clear();

      if (data.users_per_file) {
        Object.entries(data.users_per_file).forEach(([filePath, users]) => {
          activeUsersPerFile.set(filePath, new Set(users));
          console.log(`👥 PRESENCE: File ${filePath} has ${users.length} users: ${users.join(', ')}`);
        });
      }

      // Check if collaboration status changed for current file
      if (currentFile) {
        const userCount = getUserCountForFile(currentFile);
        const needsCollaboration = hasMultipleUsersOnFile(currentFile);

        console.log(`🔄 PRESENCE: Current file ${currentFile} has ${userCount} users, needs collaboration: ${needsCollaboration}`);

        // Re-evaluate collaboration need
        setTimeout(() => {
          checkAndStartCollaboration(currentFile);
        }, 500);
      }
    }
  }

  // Add user to file tracking (now synced with backend)
  function addUserToFile(filePath, userId = currentUserId) {
    if (!activeUsersPerFile.has(filePath)) {
      activeUsersPerFile.set(filePath, new Set());
    }
    activeUsersPerFile.get(filePath).add(userId);
    console.log(`👤 Added user ${userId} to file ${filePath}. Total users: ${activeUsersPerFile.get(filePath).size}`);

    // Send presence update to backend
    sendUserPresence(filePath);
  }

  // Remove user from file tracking (now synced with backend)
  function removeUserFromFile(filePath, userId = currentUserId) {
    if (activeUsersPerFile.has(filePath)) {
      activeUsersPerFile.get(filePath).delete(userId);
      if (activeUsersPerFile.get(filePath).size === 0) {
        activeUsersPerFile.delete(filePath);
      }
      console.log(`👤 Removed user ${userId} from file ${filePath}. Remaining users: ${activeUsersPerFile.has(filePath) ? activeUsersPerFile.get(filePath).size : 0}`);
    }
  }

  // Check if multiple users are on the same file
  function hasMultipleUsersOnFile(filePath) {
    const userCount = activeUsersPerFile.has(filePath) ? activeUsersPerFile.get(filePath).size : 0;
    console.log(`👥 File ${filePath} has ${userCount} users`);
    return userCount > 1;
  }

  // Get user count for a file
  function getUserCountForFile(filePath) {
    return activeUsersPerFile.has(filePath) ? activeUsersPerFile.get(filePath).size : 0;
  }

  // Clean up user tracking when leaving a file
  function cleanupUserTracking(filePath) {
    removeUserFromFile(filePath);
    // Send presence update to remove user from file
    if (userPresenceSocket && userPresenceSocket.readyState === WebSocket.OPEN) {
      const message = {
        type: 'user_leave',
        user_id: currentUserId,
        window_id: windowInstanceId,
        file_path: filePath,
        timestamp: Date.now()
      };
      userPresenceSocket.send(JSON.stringify(message));
    }
    console.log(`🧹 Cleaned up user tracking for ${filePath}`);
  }

  // MOCK: Simulate other users for testing using YJS awareness
  function simulateOtherUserOnFile(filePath) {
    if (!currentYjsProvider || !currentYjsProvider.awareness) {
      console.log(`🎭 MOCK: No YJS provider available. Start collaboration first.`);
      return null;
    }

    const mockUserId = `mock-user-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;

    // Create a mock awareness state
    const mockAwarenessId = Math.floor(Math.random() * 1000000);
    const mockState = {
      user: {
        id: mockUserId,
        name: `MockUser-${mockUserId.slice(-4)}`,
        file: filePath,
        timestamp: Date.now()
      }
    };

    // Manually add to awareness (simulating another user)
    currentYjsProvider.awareness.states.set(mockAwarenessId, mockState);
    currentYjsProvider.awareness.emit('change', [{ added: [mockAwarenessId], updated: [], removed: [] }]);

    console.log(`🎭 MOCK: Simulated user ${mockUserId} joining file ${filePath}`);

    // Auto-remove the mock user after 30 seconds to simulate them leaving
    setTimeout(() => {
      if (currentYjsProvider && currentYjsProvider.awareness) {
        currentYjsProvider.awareness.states.delete(mockAwarenessId);
        currentYjsProvider.awareness.emit('change', [{ added: [], updated: [], removed: [mockAwarenessId] }]);
        console.log(`🎭 MOCK: Simulated user ${mockUserId} leaving file ${filePath}`);
      }
    }, 30000);

    return mockUserId;
  }

  // MOCK: Remove simulated user
  function removeSimulatedUser(filePath, mockUserId) {
    removeUserFromFile(filePath, mockUserId);
    console.log(`🎭 MOCK: Manually removed simulated user ${mockUserId} from file ${filePath}`);

    // Re-check collaboration need
    if (currentFile === filePath) {
      setTimeout(() => checkAndStartCollaboration(filePath), 1000);
    }
  }

  // === Auto-Save and Collaboration Functions (Global Scope) ===

  // Update file tab status to show unsaved changes
  function updateFileTabStatus(filePath, isUnsaved = false) {
    const tab = document.querySelector(`.file-tab[data-path="${filePath}"]`);
    if (tab) {
      const tabText = tab.childNodes[0]; // Get the text node
      const fileName = filePath.split("/").pop();

      if (isUnsaved) {
        tabText.textContent = `● ${fileName}`; // Add dot for unsaved
        tab.style.fontStyle = 'italic';
      } else {
        tabText.textContent = fileName; // Remove dot when saved
        tab.style.fontStyle = 'normal';
      }
    }
  }

  // Auto-save function with collaboration protection
  async function autoSaveCurrentFile() {
    if (!currentFile || !editor) return;

    // CRITICAL: Don't auto-save during collaboration startup to prevent content overwriting
    if (isFileSwitching || pendingCollaborationCleanup || isSettingEditorContent) {
      console.log(`🚫 AUTO-SAVE: Skipping auto-save during collaboration operations for: ${currentFile}`);
      return;
    }

    try {
      const content = editor.getValue();
      console.log(`💾 AUTO-SAVE: Saving file: ${currentFile} (${content.length} chars)`);

      const response = await fetch(`${window.apiBaseUrl}/api/file/${window.projectHash}`, {
        method: "PUT", // Use PUT to update existing file, not POST which creates new files
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ path: currentFile, content: content }),
      });

      if (response.ok) {
        console.log(`✅ AUTO-SAVE: Successfully saved: ${currentFile}`);
        hasUnsavedChanges = false;
        updateFileTabStatus(currentFile, false); // Remove unsaved indicator
        // Don't show output for successful auto-saves to avoid clutter (like VS Code)
      } else {
        console.error(`❌ AUTO-SAVE: Failed for ${currentFile}:`, response.status);
        showOutput(`❌ Auto-save failed for ${currentFile}`);
      }
    } catch (error) {
      console.error(`❌ AUTO-SAVE: Error for ${currentFile}:`, error);
      showOutput(`❌ Auto-save error: ${error.message}`);
    }
  }

  // Schedule auto-save with debouncing (like VS Code)
  function scheduleAutoSave() {
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout);
    }

    // Auto-save after 2 seconds of inactivity (like VS Code)
    autoSaveTimeout = setTimeout(() => {
      autoSaveCurrentFile();
    }, 2000);
  }

  // BULLETPROOF cleanup function - FIXED to prevent cross-file contamination
  async function cleanupCurrentCollaboration() {
    if (pendingCollaborationCleanup) {
      console.log(`🔄 Cleanup already in progress, waiting...`);
      return;
    }

    pendingCollaborationCleanup = true;
    const cleanupFile = currentCollaborationFile;
    console.log(`🧹 CLEANUP: Starting BULLETPROOF cleanup for: ${cleanupFile}`);

    try {
      // STEP 1: CRITICAL - Stop all editor event listeners immediately
      if (editor && currentYjsBinding) {
        console.log(`🛑 CLEANUP: Stopping editor event listeners`);
        isSettingEditorContent = true; // Prevent any further updates
      }

      // STEP 2: CRITICAL - Destroy Monaco binding FIRST to prevent editor updates
      if (currentYjsBinding) {
        try {
          console.log(`🔗 CLEANUP: Destroying Monaco binding for: ${cleanupFile}`);

          // Remove all event listeners from the binding
          if (currentYjsBinding.destroy) {
            currentYjsBinding.destroy();
          }

          currentYjsBinding = null;
          console.log(`✅ CLEANUP: Monaco binding destroyed`);
        } catch (e) {
          console.error(`❌ CLEANUP: Error destroying binding:`, e);
        }
      }

      // STEP 3: CRITICAL - Disconnect and destroy YJS provider with proper cleanup
      if (currentYjsProvider) {
        try {
          console.log(`🌐 CLEANUP: Disconnecting YJS provider for: ${cleanupFile}`);

          // Clear awareness state immediately - FIXED API
          if (currentYjsProvider.awareness) {
            try {
              currentYjsProvider.awareness.setLocalState(null);
            } catch (e) {
              console.warn(`⚠️ CLEANUP: Error clearing awareness state:`, e);
            }

            try {
              if (currentYjsProvider.awareness.destroy) {
                currentYjsProvider.awareness.destroy();
              }
            } catch (e) {
              console.warn(`⚠️ CLEANUP: Error destroying awareness:`, e);
            }
          }

          // Disconnect WebSocket immediately
          if (currentYjsProvider.disconnect) {
            currentYjsProvider.disconnect();
          }

          // Force close WebSocket if still open
          if (currentYjsProvider.ws) {
            if (currentYjsProvider.ws.readyState === WebSocket.OPEN ||
                currentYjsProvider.ws.readyState === WebSocket.CONNECTING) {
              currentYjsProvider.ws.close(1000, 'File switch cleanup');
            }
          }

          // Destroy provider completely
          if (currentYjsProvider.destroy) {
            currentYjsProvider.destroy();
          }

          currentYjsProvider = null;
          console.log(`✅ CLEANUP: YJS provider destroyed`);
        } catch (e) {
          console.error(`❌ CLEANUP: Error destroying provider:`, e);
        }
      }

      // STEP 4: CRITICAL - Destroy YJS document with complete cleanup
      if (currentYjsDoc) {
        try {
          console.log(`📄 CLEANUP: Destroying YJS document for: ${cleanupFile}`);

          // Remove ALL event listeners
          const eventTypes = ['update', 'beforeAllTransactions', 'afterAllTransactions', 'afterTransaction', 'subdocs'];
          eventTypes.forEach(eventType => {
            try {
              currentYjsDoc.off(eventType);
            } catch (e) {
              // Ignore errors for non-existent listeners
            }
          });

          // Clear all shared types
          const sharedTypes = ['content', 'text', 'map'];
          sharedTypes.forEach(typeName => {
            try {
              const sharedType = currentYjsDoc.get(typeName);
              if (sharedType && sharedType.destroy) {
                sharedType.destroy();
              }
            } catch (e) {
              // Ignore errors for non-existent types
            }
          });

          // Destroy the document completely
          currentYjsDoc.destroy();
          currentYjsDoc = null;

          console.log(`✅ CLEANUP: YJS document destroyed`);
        } catch (e) {
          console.error(`❌ CLEANUP: Error destroying document:`, e);
        }
      }

      // STEP 5: CRITICAL - Clear ALL global state
      currentCollaborationFile = null;
      isSettingEditorContent = false;

      // STEP 6: CRITICAL - Clean up user tracking for this file
      if (cleanupFile) {
        cleanupUserTracking(cleanupFile);
      }

      // STEP 7: CRITICAL - Remove from collaboration map with validation
      if (cleanupFile && fileCollaborationMap.has(cleanupFile)) {
        const collabData = fileCollaborationMap.get(cleanupFile);

        // Ensure all resources in the map are also cleaned up
        if (collabData) {
          if (collabData.binding && collabData.binding.destroy) {
            try {
              collabData.binding.destroy();
            } catch (e) {
              console.error(`❌ CLEANUP: Error destroying map binding:`, e);
            }
          }

          if (collabData.provider && collabData.provider.destroy) {
            try {
              collabData.provider.destroy();
            } catch (e) {
              console.error(`❌ CLEANUP: Error destroying map provider:`, e);
            }
          }

          if (collabData.doc && collabData.doc.destroy) {
            try {
              collabData.doc.destroy();
            } catch (e) {
              console.error(`❌ CLEANUP: Error destroying map document:`, e);
            }
          }
        }

        fileCollaborationMap.delete(cleanupFile);
        console.log(`🗑️ CLEANUP: Removed ${cleanupFile} from collaboration map`);
      }

      // STEP 8: CRITICAL - Dispose Monaco model if it exists in our tracking
      if (cleanupFile && fileModelMap.has(cleanupFile)) {
        try {
          const model = fileModelMap.get(cleanupFile);
          if (model && !model.isDisposed()) {
            console.log(`🗑️ CLEANUP: Disposing Monaco model for: ${cleanupFile}`);
            model.dispose();
          }
          fileModelMap.delete(cleanupFile);
        } catch (e) {
          console.error(`❌ CLEANUP: Error disposing model:`, e);
        }
      }

      console.log(`✅ CLEANUP: BULLETPROOF cleanup complete for: ${cleanupFile}`);

    } finally {
      pendingCollaborationCleanup = false;

      // Final validation - ensure all global state is cleared
      if (currentYjsDoc || currentYjsProvider || currentYjsBinding) {
        console.error(`❌ CLEANUP: CRITICAL - Global state not fully cleared!`);
        console.error(`  - YJS Doc: ${!!currentYjsDoc}`);
        console.error(`  - YJS Provider: ${!!currentYjsProvider}`);
        console.error(`  - YJS Binding: ${!!currentYjsBinding}`);

        // Force clear any remaining references
        currentYjsDoc = null;
        currentYjsProvider = null;
        currentYjsBinding = null;
      }
    }
  }

  // BULLETPROOF validation functions for file isolation
  function validateFileIsolation(roomName, filePath) {
    // Ensure room name contains file-specific identifier
    const fileHash = btoa(filePath).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    if (!roomName.includes(fileHash)) {
      console.error(`❌ VALIDATION: Room name ${roomName} does not contain file hash ${fileHash} for ${filePath}`);
      return false;
    }

    // Check if this room is already used by a different file
    for (const [existingFile, collabData] of fileCollaborationMap.entries()) {
      if (existingFile !== filePath && collabData.roomName === roomName) {
        console.error(`❌ VALIDATION: Room ${roomName} already used by different file: ${existingFile}`);
        return false;
      }
    }

    console.log(`✅ VALIDATION: File isolation validated for ${filePath} with room ${roomName}`);
    return true;
  }

  function validateRoomUniqueness(roomName, filePath) {
    // This is now handled by validateFileIsolation
    return validateFileIsolation(roomName, filePath);
  }

  // BULLETPROOF collaboration check with proper file isolation
  async function checkAndStartCollaboration(filePath) {
    if (!collaborationEnabled || !filePath) return;

    try {
      console.log(`🔍 COLLAB CHECK: Starting BULLETPROOF collaboration check for file: ${filePath}`);

      // Ensure the file content is stable before starting collaboration
      if (!currentFile || currentFile !== filePath) {
        console.log(`⚠️ COLLAB CHECK: File changed during check, skipping: ${filePath}`);
        return;
      }

      // CRITICAL: Clean up any existing collaboration for different file
      if (currentCollaborationFile && currentCollaborationFile !== filePath) {
        console.log(`🔄 COLLAB CHECK: Switching from ${currentCollaborationFile} to ${filePath} - cleanup needed`);
        await cleanupCurrentCollaboration();

        // Wait for cleanup to complete
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Always start collaboration with proper file isolation
      console.log(`🤝 COLLAB CHECK: Starting file-isolated collaboration for ${filePath}`);
      await startCollaborationForFile(filePath);

    } catch (error) {
      console.error(`❌ COLLAB CHECK: Error checking collaboration for ${filePath}:`, error);
    }
  }

  // CURSOR PRESERVATION FUNCTIONS
  function preserveCursorPosition() {
    if (editor) {
      preservedCursorPosition = editor.getPosition();
      console.log(`💾 CURSOR: Preserved position:`, preservedCursorPosition);

      // Clear any existing timeout
      if (cursorPreservationTimeout) {
        clearTimeout(cursorPreservationTimeout);
      }

      // Auto-clear preserved position after 5 seconds if not used
      cursorPreservationTimeout = setTimeout(() => {
        preservedCursorPosition = null;
        console.log(`🕐 CURSOR: Auto-cleared preserved position after timeout`);
      }, 5000);
    }
  }

  function restorePreservedCursorPosition() {
    if (preservedCursorPosition && editor) {
      try {
        editor.setPosition(preservedCursorPosition);
        console.log(`✅ CURSOR: Restored preserved position:`, preservedCursorPosition);
        preservedCursorPosition = null;

        if (cursorPreservationTimeout) {
          clearTimeout(cursorPreservationTimeout);
          cursorPreservationTimeout = null;
        }

        return true;
      } catch (e) {
        console.warn(`⚠️ CURSOR: Could not restore preserved position:`, e);
        preservedCursorPosition = null;
        return false;
      }
    }
    return false;
  }

  // BULLETPROOF collaboration starter - FIXED to prevent cross-file contamination
  async function startCollaborationForFile(filePath) {
    if (!collaborationEnabled || !editor || !filePath) return false;

    // Prevent starting collaboration during file switching
    if (isFileSwitching || pendingCollaborationCleanup) {
      console.log(`🚫 COLLAB START: Blocked during file switching for: ${filePath}`);
      return false;
    }

    // CRITICAL: Preserve cursor position before collaboration initialization
    preserveCursorPosition();
    isCollaborationInitializing = true;

    // CRITICAL: Check if we already have collaboration for this exact file
    if (currentCollaborationFile === filePath && currentYjsBinding && currentYjsDoc && currentYjsProvider) {
      console.log(`🤝 COLLAB START: Collaboration already active for ${filePath} - reusing existing session`);

      // Validate that the existing collaboration is still valid
      if (currentYjsProvider.ws && currentYjsProvider.ws.readyState === WebSocket.OPEN) {
        console.log(`✅ COLLAB START: Existing collaboration is valid, no need to restart`);
        return true;
      } else {
        console.log(`⚠️ COLLAB START: Existing collaboration invalid, cleaning up and restarting`);
        await cleanupCurrentCollaboration();
      }
    }

    // ADDITIONAL CHECK: Prevent duplicate sessions for the same file
    if (fileCollaborationMap.has(filePath)) {
      const existingCollab = fileCollaborationMap.get(filePath);
      if (existingCollab.provider && existingCollab.provider.ws &&
          existingCollab.provider.ws.readyState === WebSocket.OPEN) {
        console.log(`🔄 COLLAB START: Found existing valid collaboration for ${filePath}, reusing...`);

        // Reuse existing collaboration
        currentYjsDoc = existingCollab.doc;
        currentYjsProvider = existingCollab.provider;
        currentYjsBinding = existingCollab.binding;
        currentCollaborationFile = filePath;

        console.log(`✅ COLLAB START: Reusing existing collaboration for ${filePath}`);
        return true;
      } else {
        console.log(`🧹 COLLAB START: Cleaning up invalid collaboration entry for ${filePath}`);
        fileCollaborationMap.delete(filePath);
      }
    }

    try {
      console.log(`🤝 COLLAB START: Initializing BULLETPROOF collaboration for: ${filePath}`);

      // 1. Generate UNIQUE session ID per file AND window instance
      const fileSessionId = `${windowInstanceId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

      // 2. Create BULLETPROOF room name with proper file isolation
      // Use file hash to ensure same file = same room, different files = different rooms
      const fileHash = btoa(filePath).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
      const baseRoomName = `project-${projectId}-file-${fileHash}`;

      // CRITICAL: Ensure room name is unique per file but consistent across windows for same file
      let roomName = baseRoomName;

      console.log(`🤝 COLLAB START: Using file-isolated room: ${roomName} for file: ${filePath}`);
      console.log(`🏠 COLLAB START: Room name: ${roomName}`);

      // 3. CRITICAL: Validate no cross-file contamination
      if (!validateFileIsolation(roomName, filePath)) {
        console.error(`❌ COLLAB START: Aborting due to file isolation violation for: ${filePath}`);
        return false;
      }

      // 4. Create fresh YJS document with unique identifier
      const ydoc = new Y.Doc();
      console.log(`📄 COLLAB START: Created new Y.Doc for: ${filePath} with ID: ${ydoc.guid}`);

      // 5. Create WebSocket provider with file-isolated room (connect to port 8001 ASGI server)
      const wsUrl = `ws://${window.location.hostname}:8001/ws/yjs-room/`;
      console.log(`🌐 COLLAB START: Connecting to ASGI server at: ${wsUrl}`);

      const provider = new WebsocketProvider(
        wsUrl,
        roomName,
        ydoc
      );

      // 6. Create a shared text type for this file's content with unique name
      const yText = ydoc.getText(`content-${fileHash}`);

      // 7. Wait for provider connection and sync content with BULLETPROOF isolation
      console.log(`⏳ COLLAB START: Waiting for provider connection for: ${filePath}`);
      console.log(`🌐 COLLAB START: WebSocket URL: ${wsUrl}`);
      console.log(`🏠 COLLAB START: Room: ${roomName}`);

      // 8. NEW APPROACH: Delay Monaco binding creation until after content is stable
      // This prevents the binding from interfering with content during initialization
      console.log(`🤝 COLLAB START: NEW APPROACH - Delaying binding creation for: ${filePath}`);

      // First, ensure YJS has the current content WITHOUT creating the binding yet
      setTimeout(() => {
        if (currentFile !== filePath || isFileSwitching || pendingCollaborationCleanup) {
          console.log(`⚠️ COLLAB START: File context changed, aborting for: ${filePath}`);
          return;
        }

        const currentContent = editor.getValue();
        console.log(`📝 COLLAB START: Current editor content: ${currentContent.length} chars for: ${filePath}`);
        console.log(`📄 COLLAB START: Current YJS content: ${yText.length} chars for: ${filePath}`);

        // CRITICAL: Always populate YJS with current editor content first
        // This ensures YJS has the latest content before any binding is created
        if (currentContent && currentContent.trim() !== '') {
          console.log(`📤 COLLAB START: Populating YJS with current editor content for: ${filePath}`);

          // Clear YJS first to avoid conflicts
          if (yText.length > 0) {
            yText.delete(0, yText.length);
          }

          // Insert current content
          yText.insert(0, currentContent);
          console.log(`✅ COLLAB START: YJS populated with ${currentContent.length} chars for: ${filePath}`);
        }

        // Now create the Monaco binding AFTER YJS has the correct content
        setTimeout(() => {
          if (currentFile !== filePath || isFileSwitching || pendingCollaborationCleanup) {
            console.log(`⚠️ COLLAB START: File context changed during binding creation, aborting for: ${filePath}`);
            return;
          }

          console.log(`🔗 COLLAB START: Now creating Monaco binding for: ${filePath}`);
          createMonacoBinding(filePath, yText, provider, ydoc, fileSessionId, fileHash);
        }, 300);

      }, 200);

      return true;
    } catch (error) {
      console.error(`❌ COLLAB START: Error starting collaboration for ${filePath}:`, error);
      return false;
    }
  }

  // NEW APPROACH: Separate function to create Monaco binding with content protection
  function createMonacoBinding(filePath, yText, provider, ydoc, fileSessionId, fileHash) {
    console.log(`🔗 BINDING: Creating Monaco binding for: ${filePath}`);

    if (currentYjsBinding) {
      console.log(`⚠️ BINDING: Existing binding found, destroying first`);
      currentYjsBinding.destroy();
      currentYjsBinding = null;
    }

    const currentModel = editor.getModel();
    if (!currentModel) {
      console.error(`❌ BINDING: No Monaco model available for: ${filePath}`);
      return false;
    }

    // CRITICAL: Disable Monaco's content change events temporarily
    isSettingEditorContent = true;

    console.log(`📋 BINDING: Model URI: ${currentModel.uri.toString()}`);
    console.log(`📄 BINDING: YJS Doc ID: ${ydoc.guid}`);
    console.log(`🔒 BINDING: File hash: ${fileHash}`);

    // Store model for cleanup
    fileModelMap.set(filePath, currentModel);

    // Get current content before creating binding
    const currentContent = currentModel.getValue();
    console.log(`💾 BINDING: Current content length: ${currentContent.length} chars`);

    // FINAL APPROACH: Go back to Monaco binding but with PROPER initialization
    // The issue was initialization timing, not the binding itself
    console.log(`🔄 BINDING: Using PROPER Monaco binding with correct initialization for: ${filePath}`);

    // CRITICAL: Smart content initialization - prevent duplication
    const yjsContent = yText.toString();
    console.log(`📊 BINDING: Content check - Editor: ${currentContent.length} chars, YJS: ${yjsContent.length} chars`);

    if (currentContent && currentContent.trim() !== '') {
      // SMART: Only populate YJS if it's empty OR content is different
      if (yjsContent.length === 0) {
        console.log(`📤 BINDING: YJS is empty, populating with current content (${currentContent.length} chars)`);
        yText.insert(0, currentContent);
      } else if (yjsContent !== currentContent) {
        console.log(`🔄 BINDING: Content differs, checking which is more recent...`);
        console.log(`📝 Editor content preview: "${currentContent.substring(0, 50)}..."`);
        console.log(`📄 YJS content preview: "${yjsContent.substring(0, 50)}..."`);

        // SMART: If YJS has content and it's different, prefer YJS (collaborative state)
        // This prevents duplication when returning to a file
        if (yjsContent.length > 0) {
          console.log(`🔄 BINDING: Using existing YJS content (collaborative state)`);

          // CRITICAL: Preserve cursor position during content sync
          const currentPosition = editor.getPosition();
          console.log(`💾 BINDING: Preserving cursor position:`, currentPosition);

          isSettingEditorContent = true;
          currentModel.setValue(yjsContent);
          isSettingEditorContent = false;

          // Restore cursor position after content sync
          if (currentPosition) {
            setTimeout(() => {
              try {
                editor.setPosition(currentPosition);
                console.log(`✅ BINDING: Cursor position restored`);
              } catch (e) {
                console.warn(`⚠️ BINDING: Could not restore cursor position:`, e);
                // Fallback: place cursor at end of content
                const lineCount = currentModel.getLineCount();
                const lastLineLength = currentModel.getLineLength(lineCount);
                editor.setPosition({ lineNumber: lineCount, column: lastLineLength + 1 });
              }
            }, 50);
          }
        }
      } else {
        console.log(`✅ BINDING: Content matches, no initialization needed`);
      }
    } else if (yjsContent.length > 0) {
      console.log(`📥 BINDING: Editor is empty but YJS has content, loading from YJS`);

      // CRITICAL: Preserve cursor position during content loading
      const currentPosition = editor.getPosition();
      console.log(`💾 BINDING: Preserving cursor position during YJS load:`, currentPosition);

      isSettingEditorContent = true;
      currentModel.setValue(yjsContent);
      isSettingEditorContent = false;

      // Restore cursor position after loading
      if (currentPosition) {
        setTimeout(() => {
          try {
            editor.setPosition(currentPosition);
            console.log(`✅ BINDING: Cursor position restored after YJS load`);
          } catch (e) {
            console.warn(`⚠️ BINDING: Could not restore cursor position after YJS load:`, e);
          }
        }, 50);
      }
    }

    // Wait a moment for YJS to settle, then create the binding
    setTimeout(() => {
      console.log(`🔗 BINDING: Creating Monaco binding after YJS initialization`);

      const binding = new MonacoBinding(
        yText,
        currentModel,
        new Set([editor]),
        provider.awareness
      );

      console.log(`✅ BINDING: Monaco binding created successfully`);

      // Set up awareness for user tracking
      try {
        provider.awareness.setLocalState({
          user: {
            id: currentUserId,
            name: `User-${currentUserId.slice(-4)}`,
            file: filePath,
            fileHash: fileHash,
            windowId: windowInstanceId,
            timestamp: Date.now()
          }
        });
      } catch (e) {
        console.warn(`⚠️ BINDING: Error setting awareness state:`, e);
      }

      // Listen for awareness changes
      provider.awareness.on('change', () => {
        const states = provider.awareness.getStates();
        const usersOnThisFile = Array.from(states.values())
          .filter(state => state.user && state.user.file === filePath && state.user.fileHash === fileHash)
          .map(state => state.user.id);

        console.log(`👥 AWARENESS: Users on file ${filePath}:`, usersOnThisFile);
        activeUsersPerFile.set(filePath, new Set(usersOnThisFile));

        if (usersOnThisFile.length > 1) {
          showOutput(`👥 ${usersOnThisFile.length} users collaborating on: ${filePath.split('/').pop()}`);
        } else {
          showOutput(`👤 You are the only user on: ${filePath.split('/').pop()}`);
        }
      });

      // Update global references
      currentYjsDoc = ydoc;
      currentYjsProvider = provider;
      currentYjsBinding = binding;
      currentCollaborationFile = filePath;

      // Track in collaboration map
      fileCollaborationMap.set(filePath, {
        doc: ydoc,
        provider: provider,
        binding: binding,
        sessionId: fileSessionId,
        roomName: provider.roomname,
        fileHash: fileHash,
        modelUri: currentModel.uri.toString(),
        timestamp: Date.now()
      });

      console.log(`✅ BINDING: Collaboration successfully started for: ${filePath}`);
      showOutput(`🤝 Real-time collaboration active: ${filePath.split('/').pop()}`);

      // CRITICAL: Restore cursor position after collaboration is fully initialized
      setTimeout(() => {
        if (!restorePreservedCursorPosition()) {
          console.log(`💾 CURSOR: No preserved position to restore, keeping current position`);
        }
        isCollaborationInitializing = false;
      }, 150);

    }, 100); // Small delay for YJS to settle

    // Re-enable content change events
    isSettingEditorContent = false;

    return true;
  }

  // BULLETPROOF file switching with complete isolation - FIXED VERSION
  async function handleFileSwitching(newFilePath) {
    console.log(`🔄 FILE SWITCH: BULLETPROOF switch from "${currentFile}" to "${newFilePath}"`);

    try {
      // STEP 1: Set file switching flag to prevent race conditions
      isFileSwitching = true;

      // STEP 2: Capture current state for validation
      const oldFile = currentFile;
      const oldModel = editor.getModel();
      const oldDocGuid = currentYjsDoc ? currentYjsDoc.guid : 'none';
      const oldRoomName = currentYjsProvider ? currentYjsProvider.roomname : 'none';

      console.log(`📊 FILE SWITCH STATE BEFORE:`);
      console.log(`  - Current file: ${oldFile}`);
      console.log(`  - Target file: ${newFilePath}`);
      console.log(`  - Old model URI: ${oldModel ? oldModel.uri.toString() : 'none'}`);
      console.log(`  - Old YJS Doc GUID: ${oldDocGuid}`);
      console.log(`  - Old room: ${oldRoomName}`);

      // STEP 3: Auto-save current file if needed
      if (oldFile && editor && hasUnsavedChanges) {
        console.log(`💾 FILE SWITCH: Auto-saving current file: ${oldFile}`);
        await autoSaveCurrentFile();
      }

      // STEP 4: CRITICAL - Complete collaboration cleanup with validation
      console.log(`🧹 FILE SWITCH: Starting BULLETPROOF cleanup for: ${oldFile}`);
      await cleanupCurrentCollaboration();

      // STEP 5: Wait for cleanup to settle completely
      console.log(`⏳ FILE SWITCH: Waiting for cleanup to settle...`);
      await new Promise(resolve => setTimeout(resolve, 300));

      // STEP 6: CRITICAL - Validate cleanup was successful
      if (currentYjsDoc || currentYjsProvider || currentYjsBinding) {
        console.error(`❌ FILE SWITCH: CRITICAL - Cleanup failed! Still have active YJS objects!`);
        console.error(`  - YJS Doc: ${!!currentYjsDoc}`);
        console.error(`  - YJS Provider: ${!!currentYjsProvider}`);
        console.error(`  - YJS Binding: ${!!currentYjsBinding}`);

        // Force clear any remaining references
        currentYjsDoc = null;
        currentYjsProvider = null;
        currentYjsBinding = null;
        currentCollaborationFile = null;
      }

      // STEP 7: CRITICAL - Create completely new Monaco model for BULLETPROOF isolation
      if (editor && newFilePath) {
        console.log(`🆕 FILE SWITCH: Creating BULLETPROOF isolated Monaco model for: ${newFilePath}`);

        // CRITICAL: Store cursor position before file switch for potential restoration
        const cursorBeforeSwitch = editor.getPosition();
        console.log(`💾 FILE SWITCH: Storing cursor position before switch:`, cursorBeforeSwitch);

        // Get current model for disposal
        const oldModel = editor.getModel();
        console.log(`🔗 FILE SWITCH: Old model URI: ${oldModel ? oldModel.uri.toString() : 'none'}`);

        // Create new model with MAXIMUM uniqueness to prevent cross-file contamination
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substr(2, 9);
        const fileHash = btoa(newFilePath).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
        const uniqueId = `${timestamp}-${randomId}-${windowInstanceId}-${fileHash}`;
        const newUri = monaco.Uri.file(`${newFilePath}-${uniqueId}`);
        const newModel = monaco.editor.createModel('', getLanguageFromPath(newFilePath), newUri);

        console.log(`🆕 FILE SWITCH: Created new model with URI: ${newUri.toString()}`);

        // CRITICAL: Set new model BEFORE disposing old one
        editor.setModel(newModel);
        console.log(`🔗 FILE SWITCH: Set new Monaco model in editor`);

        // Validate model switch
        const currentModel = editor.getModel();
        if (currentModel.uri.toString() !== newUri.toString()) {
          console.error(`❌ FILE SWITCH: Model switch failed! Expected: ${newUri.toString()}, Got: ${currentModel.uri.toString()}`);
        } else {
          console.log(`✅ FILE SWITCH: Model switch validated successfully`);
        }

        // Dispose old model after a delay to prevent race conditions
        if (oldModel) {
          setTimeout(() => {
            try {
              console.log(`🗑️ FILE SWITCH: Disposing old Monaco model: ${oldModel.uri.toString()}`);
              oldModel.dispose();
              console.log(`✅ FILE SWITCH: Old Monaco model disposed successfully`);
            } catch (e) {
              console.warn(`⚠️ FILE SWITCH: Error disposing old model:`, e);
            }
          }, 300);
        }
      }

      // STEP 7: Update current file reference and validate final state
      // NOTE: currentFile will be updated by the calling function (openFile)
      // currentFile = newFilePath; // Don't update here to prevent race conditions
      hasUnsavedChanges = false;

      // STEP 8: Send presence update for new file
      if (collaborationEnabled && newFilePath) {
        setTimeout(() => {
          sendUserPresence(newFilePath);
        }, 500);
      }

      // STEP 8: Final validation of BULLETPROOF isolation
      const finalModel = editor.getModel();
      console.log(`📊 FILE SWITCH STATE AFTER:`);
      console.log(`  - Current file: ${currentFile}`);
      console.log(`  - New model URI: ${finalModel ? finalModel.uri.toString() : 'none'}`);
      console.log(`  - YJS Doc: ${currentYjsDoc ? 'STILL EXISTS!' : 'null ✅'}`);
      console.log(`  - YJS Provider: ${currentYjsProvider ? 'STILL EXISTS!' : 'null ✅'}`);
      console.log(`  - YJS Binding: ${currentYjsBinding ? 'STILL EXISTS!' : 'null ✅'}`);
      console.log(`  - File switching flag: ${isFileSwitching}`);

      console.log(`✅ FILE SWITCH: BULLETPROOF isolation complete for: ${newFilePath}`);

    } catch (error) {
      console.error(`❌ FILE SWITCH: Error during BULLETPROOF file switching:`, error);
      throw error;
    } finally {
      // Always clear the file switching flag
      isFileSwitching = false;
    }
  }

  // Helper function to get language from file path
  function getLanguageFromPath(filePath) {
    const extension = filePath.split('.').pop().toLowerCase();
    const languageMap = {
      py: 'python',
      js: 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      html: 'html',
      css: 'css',
      java: 'java',
      cs: 'csharp',
      go: 'go',
      rb: 'ruby',
      json: 'json',
      md: 'markdown',
      txt: 'plaintext'
    };
    return languageMap[extension] || 'plaintext';
  }

  // SMART validation function for room names
  function validateRoomUniqueness(roomName, filePath) {
    console.log(`🔍 VALIDATION: Checking room logic for ${filePath}`);
    console.log(`🔍 VALIDATION: Room name: ${roomName}`);

    // Check for CROSS-FILE contamination (different files using same room)
    for (const [existingFile, collaboration] of fileCollaborationMap.entries()) {
      if (existingFile !== filePath && collaboration.roomName === roomName) {
        console.error(`❌ VALIDATION: CROSS-FILE contamination detected!`);
        console.error(`❌ VALIDATION: File ${filePath} trying to use room already used by ${existingFile}`);
        return false;
      }
    }

    // SAME-FILE collaboration is allowed and expected
    const sameFileCollaborations = Array.from(fileCollaborationMap.entries())
      .filter(([file, collab]) => file === filePath);

    if (sameFileCollaborations.length > 0) {
      console.log(`🤝 VALIDATION: Same-file collaboration detected for ${filePath} - this is expected`);
    }

    console.log(`✅ VALIDATION: Room logic is correct for ${filePath}`);
    return true;
  }

  // BULLETPROOF validation function - call this to check isolation
  function validateCollaborationIsolation() {
    console.log(`🔍 === BULLETPROOF ISOLATION VALIDATION ===`);

    const currentModel = editor.getModel();

    console.log(`📊 CURRENT STATE:`);
    console.log(`  - Window Instance ID: ${windowInstanceId}`);
    console.log(`  - Current file: ${currentFile}`);
    console.log(`  - Current collaboration file: ${currentCollaborationFile}`);
    console.log(`  - Monaco model URI: ${currentModel ? currentModel.uri.toString() : 'none'}`);
    console.log(`  - YJS Doc GUID: ${currentYjsDoc ? currentYjsDoc.guid : 'none'}`);
    console.log(`  - YJS Provider room: ${currentYjsProvider ? currentYjsProvider.roomname : 'none'}`);
    console.log(`  - YJS Binding exists: ${!!currentYjsBinding}`);
    console.log(`  - File switching: ${isFileSwitching}`);
    console.log(`  - Pending cleanup: ${pendingCollaborationCleanup}`);

    console.log(`📁 FILE COLLABORATION MAP (${fileCollaborationMap.size} entries):`);
    fileCollaborationMap.forEach((collaboration, filePath) => {
      console.log(`  ${filePath}:`);
      console.log(`    - Room: ${collaboration.roomName}`);
      console.log(`    - File Hash: ${collaboration.fileHash}`);
      console.log(`    - Session ID: ${collaboration.sessionId}`);
      console.log(`    - Model URI: ${collaboration.modelUri}`);
      console.log(`    - Timestamp: ${collaboration.timestamp}`);
    });

    console.log(`🗂️ FILE MODEL MAP (${fileModelMap.size} entries):`);
    fileModelMap.forEach((model, filePath) => {
      console.log(`  ${filePath}: ${model ? model.uri.toString() : 'null'} (disposed: ${model ? model.isDisposed() : 'N/A'})`);
    });

    // Validate current state consistency
    let isValid = true;

    if (currentFile && currentCollaborationFile && currentFile !== currentCollaborationFile) {
      console.error(`❌ VALIDATION: File mismatch! Current: ${currentFile}, Collaboration: ${currentCollaborationFile}`);
      isValid = false;
    }

    if (currentYjsBinding && !currentYjsDoc) {
      console.error(`❌ VALIDATION: Binding exists without YJS Doc!`);
      isValid = false;
    }

    if (currentYjsDoc && !currentYjsProvider) {
      console.error(`❌ VALIDATION: YJS Doc exists without Provider!`);
      isValid = false;
    }

    if (currentModel && currentFile) {
      const expectedFileName = currentFile.split('/').pop();
      if (!currentModel.uri.path.includes(expectedFileName)) {
        console.error(`❌ VALIDATION: Model URI doesn't match current file!`);
        console.error(`  Expected file: ${expectedFileName}`);
        console.error(`  Model URI: ${currentModel.uri.toString()}`);
        isValid = false;
      }
    }

    // CRITICAL: Check for cross-file contamination
    if (fileCollaborationMap.size > 1) {
      const rooms = new Set();
      const fileHashes = new Set();

      fileCollaborationMap.forEach((collaboration, filePath) => {
        if (rooms.has(collaboration.roomName)) {
          console.error(`❌ VALIDATION: CROSS-FILE CONTAMINATION! Room ${collaboration.roomName} used by multiple files!`);
          isValid = false;
        }
        rooms.add(collaboration.roomName);

        if (fileHashes.has(collaboration.fileHash)) {
          console.error(`❌ VALIDATION: DUPLICATE FILE HASH! Hash ${collaboration.fileHash} used by multiple files!`);
          isValid = false;
        }
        fileHashes.add(collaboration.fileHash);
      });

      if (isValid) {
        console.log(`✅ VALIDATION: No cross-file contamination detected across ${fileCollaborationMap.size} files`);
      }
    }

    console.log(`🔍 === VALIDATION RESULT: ${isValid ? '✅ VALID' : '❌ INVALID'} ===`);
    return isValid;
  }

  // BULLETPROOF test function to verify the fix
  function testCrossFileContaminationFix() {
    console.log(`🧪 === TESTING CROSS-FILE CONTAMINATION FIX ===`);

    if (!collaborationEnabled) {
      console.log(`⚠️ TEST: Collaboration not enabled. Enable it first.`);
      return false;
    }

    if (!currentFile) {
      console.log(`⚠️ TEST: No file currently open. Open a file first.`);
      return false;
    }

    console.log(`🧪 TEST: Current file: ${currentFile}`);
    console.log(`🧪 TEST: Current collaboration file: ${currentCollaborationFile}`);
    console.log(`🧪 TEST: YJS Doc GUID: ${currentYjsDoc ? currentYjsDoc.guid : 'none'}`);
    console.log(`🧪 TEST: YJS Provider room: ${currentYjsProvider ? currentYjsProvider.roomname : 'none'}`);

    // Test file hash generation
    const fileHash = btoa(currentFile).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    console.log(`🧪 TEST: File hash for ${currentFile}: ${fileHash}`);

    // Test room name generation
    const baseRoomName = `project-${projectId}-file-${fileHash}`;
    console.log(`🧪 TEST: Expected room name: ${baseRoomName}`);

    if (currentYjsProvider && currentYjsProvider.roomname !== baseRoomName) {
      console.error(`❌ TEST: Room name mismatch! Expected: ${baseRoomName}, Got: ${currentYjsProvider.roomname}`);
      return false;
    }

    console.log(`✅ TEST: Cross-file contamination fix appears to be working correctly`);
    return true;
  }

  // Add test function to global scope for manual testing
  window.testCrossFileContaminationFix = testCrossFileContaminationFix;
  window.validateCollaborationIsolation = validateCollaborationIsolation;

  document.addEventListener("DOMContentLoaded", () => {
    console.log("🚀 DOM Content Loaded - Starting initialization");
    checkApiConnectivity();
    initializeEditor();
  });

  // Additional event listener for window load to ensure everything is ready
  window.addEventListener("load", () => {
    console.log("🌐 Window fully loaded - Ensuring file tree is loaded");
    // Double-check that file tree is loaded after everything is ready
    setTimeout(() => {
      const fileTreeEl = document.getElementById("file-tree");
      if (fileTreeEl && (!fileTreeEl.children.length || fileTreeEl.querySelector('.empty-file-tree'))) {
        console.log("🔄 File tree appears empty after window load, retrying...");
        initializeFileTree();
      }
    }, 1000);
  });

  // Handle page visibility change (when user switches back to tab)
  document.addEventListener("visibilitychange", () => {
    if (!document.hidden) {
      console.log("👁️ Page became visible - Checking file tree status");
      // Refresh file tree when user comes back to the tab
      setTimeout(() => {
        const fileTreeEl = document.getElementById("file-tree");
        if (fileTreeEl && (!fileTreeEl.children.length || fileTreeEl.querySelector('.empty-file-tree'))) {
          console.log("🔄 Refreshing file tree on visibility change");
          initializeFileTree();
        }
      }, 500);
    }
  });

  async function checkApiConnectivity() {
    try {
      console.log(`🔍 Testing API connection to: ${apiBaseUrl}`);
      showOutput(`🔍 Testing API connection to: ${apiBaseUrl}`);

      const response = await fetch(`${apiBaseUrl}`);
      if (response.ok) {
        console.log("✅ Connected to FastAPI service successfully");
        showOutput("✅ Connected to FastAPI service successfully");

        // Test files endpoint
        try {
          const filesResponse = await fetch(`${apiBaseUrl}/api/files/${projectHash}`);
          console.log(`📁 Files endpoint status: ${filesResponse.status}`);
          showOutput(`📁 Files endpoint status: ${filesResponse.status}`);
        } catch (filesError) {
          console.error("❌ Files endpoint error:", filesError);
          showOutput(`❌ Files endpoint error: ${filesError.message}`);
        }
      } else {
        console.error("❌ Failed to connect to FastAPI service:", response.status);
        showOutput(`❌ Could not connect to API server (${response.status})`);
      }
    } catch (error) {
      console.error("❌ API connection error:", error);
      showOutput(`❌ Could not connect to API server: ${error.message}`);
      showOutput("💡 Make sure Docker services are running: docker-compose up -d");
    }
  }






  function initializeEditor() {
    require.config({
      paths: {
        vs: "https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.33.0/min/vs",
      },
    });

    require(["vs/editor/editor.main"], async function () {
      editor = monaco.editor.create(document.getElementById("monaco-editor"), {
        value: "# Welcome to ForgeX - Start coding!\n",
        language: "python",
        theme: "vs-dark",
        automaticLayout: true,
        minimap: { enabled: true },
        fontFamily: "'Fira Code', 'Consolas', 'Monaco', monospace",
        fontSize: 14,
        lineNumbers: "on",
        renderWhitespace: "selection",
        tabSize: 4,
        suggestOnTriggerCharacters: true,
        snippetSuggestions: "inline",
        scrollBeyondLastLine: false,
        wordWrap: "on",
      });

      // Handle logo visibility based on editor content
      function updateLogoVisibility() {
        const editorContent = document.getElementById('editor-content');
        const model = editor.getModel();
        const content = model ? model.getValue().trim() : '';

        if (content === '' || content === '# Welcome to ForgeX - Start coding!') {
          editorContent.classList.add('empty');
        } else {
          editorContent.classList.remove('empty');
        }
      }

      // Initial logo visibility check
      updateLogoVisibility();

      // Update logo visibility when content changes
      editor.onDidChangeModelContent(() => {
        updateLogoVisibility();
      });

      // Emmet support
      emmetMonaco.emmetHTML(monaco);
      emmetMonaco.emmetCSS(monaco);

      // Extend Monaco with additional languages/snippets
      extendMonacoLanguages();

      // Force syntax highlighting and theme
      forceSyntaxHighlighting();

      setupEventListeners();

      // Initialize Extension System after DOM is ready
      setTimeout(() => {
        initializeExtensionSystem();
      }, 500);

      // Initialize file tree with retry mechanism
      initializeFileTree();


      startAutoSave();
      startFileSystemRefreshTimer();







      // === Yjs + Monaco Collaborative Editing Integration ===
      // Get the init button
      const initButton = document.getElementById('init-collab-btn');

      // Function to initialize Yjs collaboration
      function initializeCollaboration() {
        try {
          console.log("Initializing Yjs collaboration system...");
          collaborationEnabled = true;

          // Initialize user presence system
          initializeUserPresence();

          // If there's a current file open, start collaboration for it
          if (currentFile) {
            setTimeout(() => {
              // Send presence for current file
              sendUserPresence(currentFile);
              // Check collaboration after presence is sent
              setTimeout(() => {
                checkAndStartCollaboration(currentFile);
              }, 1000);
            }, 500);
          }

          console.log("✅ Collaborative editing system initialized");
          showOutput("🤝 Collaborative editing system enabled");

          // Hide the init button and show debug/simulate buttons
          if (initButton) {
            initButton.style.display = 'none';
          }

          // Show debug and simulate buttons
          const debugBtn = document.getElementById('debug-collab-btn');
          const simulateBtn = document.getElementById('simulate-user-btn');
          if (debugBtn) debugBtn.style.display = 'inline-block';
          if (simulateBtn) simulateBtn.style.display = 'inline-block';

          return true;
        } catch (error) {
          console.error("❌ Error initializing collaborative editing:", error);
          return false;
        }
      }

      // Add click event listener to the init button
      if (initButton) {
        initButton.addEventListener('click', function() {
          console.log("Manual initialization requested");
          initializeCollaboration();
        });
      }

      // Simulate user button for testing
      const simulateUserButton = document.getElementById('simulate-user-btn');
      if (simulateUserButton) {
        simulateUserButton.addEventListener('click', function() {
          if (!currentFile) {
            showOutput("❌ No file is currently open. Please open a file first.");
            return;
          }

          if (!currentYjsProvider) {
            showOutput("❌ Collaboration not active. The file needs to have collaboration started first.");
            return;
          }

          console.log(`🎭 SIMULATE: Adding mock user to file: ${currentFile}`);
          const mockUserId = simulateOtherUserOnFile(currentFile);

          if (mockUserId) {
            showOutput(`🎭 Simulated user ${mockUserId} joined file: ${currentFile}`);
          } else {
            showOutput("❌ Failed to simulate user. Make sure collaboration is active.");
          }
        });
      }

      // Enhanced debug button with comprehensive state information
      const debugButton = document.getElementById('debug-collab-btn');
      if (debugButton) {
        debugButton.addEventListener('click', function() {
          console.log("🔍 === COLLABORATION DEBUG INFORMATION ===");

          // User tracking information
          console.log("👥 User Tracking:");
          console.log("  - Current User ID:", currentUserId);
          console.log("  - Active Users Per File:", activeUsersPerFile);
          if (currentFile) {
            console.log(`  - Users on current file (${currentFile}):`, getUserCountForFile(currentFile));
            console.log(`  - Multiple users on current file:`, hasMultipleUsersOnFile(currentFile));
          }

          // Check if Yjs libraries are loaded
          console.log("📚 Library Status:");
          console.log("  - Yjs loaded:", typeof Y !== 'undefined');
          console.log("  - WebsocketProvider loaded:", typeof WebsocketProvider !== 'undefined');
          console.log("  - MonacoBinding loaded:", typeof MonacoBinding !== 'undefined');

          // Check collaboration state
          console.log("🤝 Collaboration State:");
          console.log("  - Collaboration enabled:", collaborationEnabled);
          console.log("  - Current file:", currentFile);
          console.log("  - Current collaboration file:", currentCollaborationFile);
          console.log("  - Collaboration session ID:", collaborationSessionId);
          console.log("  - Is file switching:", isFileSwitching);
          console.log("  - Pending cleanup:", pendingCollaborationCleanup);

          // File collaboration map
          console.log("📁 File Collaboration Map:");
          console.log("  - Active collaborations:", fileCollaborationMap.size);
          console.log("  - Files:", Array.from(fileCollaborationMap.keys()));
          fileCollaborationMap.forEach((value, key) => {
            console.log(`    ${key}:`, {
              sessionId: value.sessionId,
              roomName: value.roomName,
              hasDoc: !!value.doc,
              hasProvider: !!value.provider,
              hasBinding: !!value.binding
            });
          });

          // Current YJS state
          console.log("📄 Current YJS State:");
          console.log("  - YJS Doc:", !!currentYjsDoc);
          console.log("  - YJS Provider:", !!currentYjsProvider);
          console.log("  - YJS Binding:", !!currentYjsBinding);

          if (currentYjsProvider) {
            console.log("  - Provider connected:", currentYjsProvider.wsconnected || currentYjsProvider.connected);
            console.log("  - Provider room:", currentYjsProvider.roomname || 'Unknown');
            console.log("  - Provider WebSocket state:", currentYjsProvider.ws ? currentYjsProvider.ws.readyState : 'No WebSocket');
          }

          // Monaco editor state
          console.log("🖥️ Monaco Editor State:");
          console.log("  - Editor initialized:", typeof editor !== 'undefined');
          if (typeof editor !== 'undefined') {
            const model = editor.getModel();
            console.log("  - Current model:", !!model);
            if (model) {
              console.log("  - Model URI:", model.uri.toString());
              console.log("  - Content length:", editor.getValue().length);
            }
          }

          // WebSocket connection test
          console.log("🌐 Testing WebSocket Connection:");
          const wsUrl = `${websocketUrls.yjsRoom}`;
          console.log("  - WebSocket URL:", wsUrl);

          const testWs = new WebSocket(wsUrl);
          testWs.onopen = function() {
            console.log("  ✅ WebSocket connection test successful");
            testWs.send(JSON.stringify({type: 'test', message: 'Debug connection test'}));
            setTimeout(() => testWs.close(), 2000);
          };
          testWs.onerror = function(error) {
            console.error("  ❌ WebSocket connection test failed:", error);
          };
          testWs.onmessage = function(event) {
            console.log("  📨 Received WebSocket message:", event.data);
          };

          console.log("🔍 === END DEBUG INFORMATION ===");

          // Run BULLETPROOF validation
          console.log("");
          validateCollaborationIsolation();
        });
      }

      // Try to initialize automatically
      if (initializeCollaboration()) {
        console.log("Automatic initialization successful");
      } else {
        console.log("Automatic initialization failed, please use the Initialize Collaboration button");
      }

      // Add auto-save triggers to Monaco editor with BULLETPROOF collaboration protection
      editor.onDidChangeModelContent(() => {
        // CRITICAL: Don't trigger auto-save during collaboration operations or file switching
        if (!isSettingEditorContent && !isFileSwitching && !pendingCollaborationCleanup && !isCollaborationInitializing && currentFile) {
          hasUnsavedChanges = true;
          updateFileTabStatus(currentFile, true); // Show unsaved indicator
          // Schedule auto-save when content changes
          scheduleAutoSave();
        } else if (isSettingEditorContent || isFileSwitching || pendingCollaborationCleanup || isCollaborationInitializing) {
          console.log(`🚫 AUTO-SAVE: Skipping auto-save trigger during collaboration operations`);
        }
      });

      // CRITICAL: Preserve cursor position during quick file switches
      editor.onDidChangeCursorPosition((e) => {
        // Only preserve if user is actively moving cursor (not during collaboration init)
        if (!isSettingEditorContent && !isFileSwitching && !isCollaborationInitializing) {
          // Debounce cursor preservation to avoid excessive calls
          if (cursorPreservationTimeout) {
            clearTimeout(cursorPreservationTimeout);
          }
          cursorPreservationTimeout = setTimeout(() => {
            preservedCursorPosition = e.position;
          }, 100);
        }
      });

      // Add cleanup and save on page unload
      window.addEventListener('beforeunload', function(e) {
        // Save current file before leaving
        if (currentFile && editor) {
          autoSaveCurrentFile();
        }

        // Clean up collaboration
        cleanupCurrentCollaboration();
      });

      // Add cleanup and save on tab close
      document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
          // Save when tab becomes hidden
          if (currentFile && editor) {
            autoSaveCurrentFile();
          }
        }
      });
    });
  }

  function extendMonacoLanguages() {
    try {
      configureAdditionalLanguages?.();
      registerCodeSnippets?.();
      updateLanguageSelector?.();
      registerIntelliSenseProviders?.();
      console.log("✅ Successfully extended Monaco editor");
    } catch (error) {
      console.error("❌ Error extending Monaco:", error);
    }
  }

  // ===== COMPREHENSIVE EXTENSION SYSTEM =====

  class ExtensionManager {
    constructor() {
      this.extensions = new Map();
      this.enabledExtensions = new Set();
      this.loadExtensionStates();
      this.initializeBuiltInExtensions();
    }

    loadExtensionStates() {
      const saved = localStorage.getItem('monaco-extensions-state');
      if (saved) {
        try {
          const states = JSON.parse(saved);
          this.enabledExtensions = new Set(states);
        } catch (e) {
          console.warn('Failed to load extension states:', e);
        }
      }
      // Default enabled extensions
      this.enabledExtensions.add('blackbox');
      this.enabledExtensions.add('emmet');
      this.enabledExtensions.add('intellisense');
      this.enabledExtensions.add('syntax-highlighting');
      this.enabledExtensions.add('auto-complete');
    }

    saveExtensionStates() {
      localStorage.setItem('monaco-extensions-state', JSON.stringify([...this.enabledExtensions]));
    }

    initializeBuiltInExtensions() {
      // Blackbox AI Extension
      this.registerExtension('blackbox', {
        name: 'Blackbox AI',
        description: 'AI-powered code completion and generation',
        activate: () => this.activateBlackboxAI(),
        deactivate: () => this.deactivateBlackboxAI(),
        commands: [
          { id: 'blackbox.generateCode', title: 'Generate Code with AI' },
          { id: 'blackbox.explainCode', title: 'Explain Selected Code' },
          { id: 'blackbox.optimizeCode', title: 'Optimize Code' }
        ]
      });

      // Emmet Extension
      this.registerExtension('emmet', {
        name: 'Emmet',
        description: 'HTML/CSS abbreviation expansion',
        activate: () => this.activateEmmet(),
        deactivate: () => this.deactivateEmmet(),
        commands: [
          { id: 'emmet.expandAbbreviation', title: 'Expand Abbreviation' },
          { id: 'emmet.wrapWithAbbreviation', title: 'Wrap with Abbreviation' }
        ]
      });

      // Prettier Extension
      this.registerExtension('prettier', {
        name: 'Prettier',
        description: 'Code formatter for multiple languages',
        activate: () => this.activatePrettier(),
        deactivate: () => this.deactivatePrettier(),
        commands: [
          { id: 'prettier.formatDocument', title: 'Format Document' },
          { id: 'prettier.formatSelection', title: 'Format Selection' }
        ]
      });

      // ESLint Extension
      this.registerExtension('eslint', {
        name: 'ESLint',
        description: 'JavaScript linting and error detection',
        activate: () => this.activateESLint(),
        deactivate: () => this.deactivateESLint(),
        commands: [
          { id: 'eslint.fixAll', title: 'Fix All ESLint Problems' },
          { id: 'eslint.fixSelection', title: 'Fix ESLint Problems in Selection' }
        ]
      });

      // TypeScript Extension
      this.registerExtension('typescript', {
        name: 'TypeScript',
        description: 'TypeScript language support and IntelliSense',
        activate: () => this.activateTypeScript(),
        deactivate: () => this.deactivateTypeScript(),
        commands: [
          { id: 'typescript.compile', title: 'Compile TypeScript' },
          { id: 'typescript.goToDefinition', title: 'Go to Definition' }
        ]
      });

      // IntelliSense Extension
      this.registerExtension('intellisense', {
        name: 'IntelliSense',
        description: 'Advanced code completion and suggestions',
        activate: () => this.activateIntelliSense(),
        deactivate: () => this.deactivateIntelliSense(),
        commands: [
          { id: 'intellisense.triggerSuggest', title: 'Trigger Suggest' },
          { id: 'intellisense.triggerParameterHints', title: 'Trigger Parameter Hints' }
        ]
      });

      // Syntax Highlighting Extension
      this.registerExtension('syntax-highlighting', {
        name: 'Syntax Highlighting',
        description: 'Enhanced syntax highlighting for all languages',
        activate: () => this.activateSyntaxHighlighting(),
        deactivate: () => this.deactivateSyntaxHighlighting(),
        commands: [
          { id: 'syntax.refreshHighlighting', title: 'Refresh Syntax Highlighting' }
        ]
      });

      // Auto Complete Extension
      this.registerExtension('auto-complete', {
        name: 'Auto Complete',
        description: 'Smart auto-completion for faster coding',
        activate: () => this.activateAutoComplete(),
        deactivate: () => this.deactivateAutoComplete(),
        commands: [
          { id: 'autocomplete.toggle', title: 'Toggle Auto Complete' }
        ]
      });
    }

    registerExtension(id, extension) {
      this.extensions.set(id, extension);
      if (this.enabledExtensions.has(id)) {
        this.activateExtension(id);
      }
    }

    activateExtension(id) {
      const extension = this.extensions.get(id);
      if (extension && !this.enabledExtensions.has(id)) {
        try {
          extension.activate();
          this.enabledExtensions.add(id);
          this.updateExtensionUI(id, true);
          this.saveExtensionStates();
          console.log(`✅ Activated extension: ${extension.name}`);
        } catch (error) {
          console.error(`❌ Failed to activate extension ${id}:`, error);
        }
      }
    }

    deactivateExtension(id) {
      const extension = this.extensions.get(id);
      if (extension && this.enabledExtensions.has(id)) {
        try {
          extension.deactivate();
          this.enabledExtensions.delete(id);
          this.updateExtensionUI(id, false);
          this.saveExtensionStates();
          console.log(`🔄 Deactivated extension: ${extension.name}`);
        } catch (error) {
          console.error(`❌ Failed to deactivate extension ${id}:`, error);
        }
      }
    }

    updateExtensionUI(id, enabled) {
      const toggle = document.querySelector(`.extension-toggle[data-extension="${id}"]`);
      const extensionItem = document.querySelector(`.extension-item[data-extension="${id}"]`);
      const status = extensionItem?.querySelector('.extension-status');

      console.log(`Updating UI for ${id}: enabled=${enabled}, toggle found=${!!toggle}, status found=${!!status}`);

      if (toggle) {
        toggle.classList.toggle('active', enabled);
      }
      if (status) {
        status.textContent = enabled ? 'Enabled' : 'Disabled';
        status.classList.toggle('enabled', enabled);
      }
    }

    // Extension Implementation Methods
    activateBlackboxAI() {
      console.log('🤖 Activating Blackbox AI extension...');

      if (typeof monaco !== 'undefined') {
        // Store provider references for cleanup
        this.blackboxProviders = [];

        // JavaScript provider
        const jsProvider = monaco.languages.registerCompletionItemProvider('javascript', {
          triggerCharacters: ['.', ' ', '(', '{', '\n', 'c', 'f', 'a'],
          provideCompletionItems: (model, position) => {
            console.log('🤖 JavaScript completion provider triggered');
            return this.getBlackboxSuggestions(model, position, 'javascript');
          }
        });
        this.blackboxProviders.push(jsProvider);

        // Python provider
        const pyProvider = monaco.languages.registerCompletionItemProvider('python', {
          triggerCharacters: ['.', ' ', '(', ':', '\n'],
          provideCompletionItems: (model, position) => {
            return this.getBlackboxSuggestions(model, position, 'python');
          }
        });
        this.blackboxProviders.push(pyProvider);

        // HTML provider
        const htmlProvider = monaco.languages.registerCompletionItemProvider('html', {
          triggerCharacters: ['<', ' ', '=', '"'],
          provideCompletionItems: (model, position) => {
            return this.getBlackboxSuggestions(model, position, 'html');
          }
        });
        this.blackboxProviders.push(htmlProvider);

        // CSS provider
        const cssProvider = monaco.languages.registerCompletionItemProvider('css', {
          triggerCharacters: ['{', ':', ';', ' '],
          provideCompletionItems: (model, position) => {
            return this.getBlackboxSuggestions(model, position, 'css');
          }
        });
        this.blackboxProviders.push(cssProvider);

        console.log('✅ Blackbox AI providers registered for multiple languages');
      }
    }

    deactivateBlackboxAI() {
      console.log('🔄 Deactivating Blackbox AI extension...');
      if (this.blackboxProviders) {
        this.blackboxProviders.forEach(provider => {
          provider.dispose();
        });
        this.blackboxProviders = [];
      }
    }

    async getBlackboxSuggestions(model, position, language) {
      try {
        const lineContent = model.getLineContent(position.lineNumber);
        const wordInfo = model.getWordUntilPosition(position);
        const currentWord = wordInfo.word;

        const textUntilPosition = model.getValueInRange({
          startLineNumber: Math.max(1, position.lineNumber - 5),
          startColumn: 1,
          endLineNumber: position.lineNumber,
          endColumn: position.column
        });

        console.log(`🤖 Blackbox AI triggered for ${language}, current word: "${currentWord}"`);

        const suggestions = this.generateContextualSuggestions(language, currentWord, lineContent, textUntilPosition);

        return {
          suggestions: suggestions.map(suggestion => ({
            ...suggestion,
            range: {
              startLineNumber: position.lineNumber,
              endLineNumber: position.lineNumber,
              startColumn: wordInfo.startColumn,
              endColumn: wordInfo.endColumn
            }
          }))
        };
      } catch (error) {
        console.error('Blackbox AI error:', error);
        return { suggestions: [] };
      }
    }

    generateContextualSuggestions(language, currentWord, lineContent, context) {
      const suggestions = [];

      // Always add some basic AI suggestions first
      suggestions.push({
        label: '🤖 AI: Generate code comment',
        kind: monaco.languages.CompletionItemKind.Text,
        insertText: language === 'python' ? '# ${1:AI generated comment}' : '// ${1:AI generated comment}',
        insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
        documentation: 'AI: Add descriptive comment',
        detail: 'Blackbox AI Suggestion',
        sortText: '0000' // Ensure it appears first
      });

      switch (language) {
        case 'javascript':
          // Always show console.log suggestion
          suggestions.push({
            label: '🤖 console.log',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'console.log(${1:message});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Log message to console',
            detail: 'Blackbox AI Suggestion',
            sortText: '0001'
          });

          // Always show function suggestion
          suggestions.push({
            label: '🤖 async function',
            kind: monaco.languages.CompletionItemKind.Function,
            insertText: 'async function ${1:functionName}(${2:params}) {\n\ttry {\n\t\t${3:// AI generated code}\n\t\treturn ${4:result};\n\t} catch (error) {\n\t\tconsole.error("Error in ${1:functionName}:", error);\n\t\tthrow error;\n\t}\n}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create async function with error handling',
            detail: 'Blackbox AI Suggestion',
            sortText: '0002'
          });

          // Always show fetch API suggestion
          suggestions.push({
            label: '🤖 fetch API call',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'fetch("${1:url}")\n\t.then(response => {\n\t\tif (!response.ok) {\n\t\t\tthrow new Error(`HTTP error! status: ${response.status}`);\n\t\t}\n\t\treturn response.json();\n\t})\n\t.then(data => {\n\t\t${2:// Handle data}\n\t\tconsole.log(data);\n\t})\n\t.catch(error => {\n\t\tconsole.error("Fetch error:", error);\n\t});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Complete fetch API call with error handling',
            detail: 'Blackbox AI Suggestion',
            sortText: '0003'
          });

          // Context-specific suggestions
          if (currentWord.startsWith('con') || lineContent.includes('console')) {
            suggestions.push({
              label: '🤖 console.error',
              kind: monaco.languages.CompletionItemKind.Method,
              insertText: 'console.error(${1:error});',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'AI: Log error to console',
              detail: 'Blackbox AI Suggestion',
              sortText: '0004'
            });
          }

          if (currentWord.startsWith('fun') || lineContent.includes('function')) {
            suggestions.push({
              label: '🤖 arrow function',
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: 'const ${1:functionName} = (${2:params}) => {\n\t${3:// function body}\n\treturn ${4:result};\n};',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'AI: Create arrow function',
              detail: 'Blackbox AI Suggestion',
              sortText: '0005'
            });
          }
          break;

        case 'python':
          // Always show Python suggestions
          suggestions.push({
            label: '🤖 print statement',
            kind: monaco.languages.CompletionItemKind.Function,
            insertText: 'print(${1:message})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Print message',
            detail: 'Blackbox AI Suggestion',
            sortText: '0001'
          });

          suggestions.push({
            label: '🤖 def function',
            kind: monaco.languages.CompletionItemKind.Function,
            insertText: 'def ${1:function_name}(${2:params}):\n\t"""${3:Function description}\n\t\n\tArgs:\n\t\t${2:params}: ${4:Parameter description}\n\t\t\n\tReturns:\n\t\t${5:Return description}\n\t"""\n\ttry:\n\t\t${6:# Function implementation}\n\t\treturn ${7:result}\n\texcept Exception as e:\n\t\tprint(f"Error in ${1:function_name}: {e}")\n\t\traise',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create Python function with docstring and error handling',
            detail: 'Blackbox AI Suggestion',
            sortText: '0002'
          });

          suggestions.push({
            label: '🤖 class definition',
            kind: monaco.languages.CompletionItemKind.Class,
            insertText: 'class ${1:ClassName}:\n\t"""${2:Class description}"""\n\t\n\tdef __init__(self, ${3:params}):\n\t\t"""Initialize ${1:ClassName}\n\t\t\n\t\tArgs:\n\t\t\t${3:params}: ${4:Parameter description}\n\t\t"""\n\t\t${5:# Initialize attributes}\n\t\tpass\n\t\n\tdef ${6:method_name}(self, ${7:params}):\n\t\t"""${8:Method description}"""\n\t\t${9:# Method implementation}\n\t\tpass',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create Python class with constructor and method',
            detail: 'Blackbox AI Suggestion',
            sortText: '0003'
          });
          break;

        case 'html':
          suggestions.push({
            label: '🤖 responsive div',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: '<div class="${1:container}">\n\t<div class="${2:row}">\n\t\t<div class="${3:col-md-12}">\n\t\t\t${4:<!-- Content here -->}\n\t\t</div>\n\t</div>\n</div>',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create responsive div structure',
            detail: 'Blackbox AI Suggestion',
            sortText: '0001'
          });

          suggestions.push({
            label: '🤖 HTML5 boilerplate',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: '<!DOCTYPE html>\n<html lang="${1:en}">\n<head>\n\t<meta charset="UTF-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1.0">\n\t<title>${2:Document}</title>\n</head>\n<body>\n\t${3:<!-- Content here -->}\n</body>\n</html>',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create HTML5 document structure',
            detail: 'Blackbox AI Suggestion',
            sortText: '0002'
          });
          break;

        case 'css':
          suggestions.push({
            label: '🤖 flexbox center',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'display: flex;\njustify-content: center;\nalign-items: center;\nflex-direction: ${1:row};',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Center content with flexbox',
            detail: 'Blackbox AI Suggestion',
            sortText: '0001'
          });

          suggestions.push({
            label: '🤖 grid layout',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'display: grid;\ngrid-template-columns: ${1:repeat(auto-fit, minmax(250px, 1fr))};\ngap: ${2:1rem};\npadding: ${3:1rem};',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'AI: Create responsive grid layout',
            detail: 'Blackbox AI Suggestion',
            sortText: '0002'
          });
          break;
      }

      console.log(`🤖 Generated ${suggestions.length} AI suggestions for ${language}`);
      return suggestions;
    }

    activateEmmet() {
      // Emmet is already activated in the main initialization
      if (typeof emmetMonaco !== 'undefined') {
        console.log('Emmet extension activated');
      }
    }

    deactivateEmmet() {
      console.log('Emmet extension deactivated');
    }

    activatePrettier() {
      console.log('✨ Activating Prettier extension...');

      // Use built-in formatting instead of external Prettier library
      this.prettierProviders = [];

      const jsProvider = monaco.languages.registerDocumentFormattingEditProvider('javascript', {
        provideDocumentFormattingEdits: (model) => {
          return this.formatWithPrettier(model, 'javascript');
        }
      });
      this.prettierProviders.push(jsProvider);

      const tsProvider = monaco.languages.registerDocumentFormattingEditProvider('typescript', {
        provideDocumentFormattingEdits: (model) => {
          return this.formatWithPrettier(model, 'typescript');
        }
      });
      this.prettierProviders.push(tsProvider);

      const htmlProvider = monaco.languages.registerDocumentFormattingEditProvider('html', {
        provideDocumentFormattingEdits: (model) => {
          return this.formatWithPrettier(model, 'html');
        }
      });
      this.prettierProviders.push(htmlProvider);

      const cssProvider = monaco.languages.registerDocumentFormattingEditProvider('css', {
        provideDocumentFormattingEdits: (model) => {
          return this.formatWithPrettier(model, 'css');
        }
      });
      this.prettierProviders.push(cssProvider);

      console.log('✅ Built-in formatting providers registered');
    }

    deactivatePrettier() {
      console.log('🔄 Deactivating Prettier extension...');
      if (this.prettierProviders) {
        this.prettierProviders.forEach(provider => {
          provider.dispose();
        });
        this.prettierProviders = [];
      }
    }

    formatWithPrettier(model, language) {
      try {
        const text = model.getValue();
        let formatted;

        console.log(`🎨 Formatting ${language} code with Prettier...`);

        // Simplified formatting for demo (since Prettier plugins might not be available)
        switch (language) {
          case 'javascript':
          case 'typescript':
            // Basic JavaScript formatting
            formatted = this.basicJSFormat(text);
            break;
          case 'html':
            // Basic HTML formatting
            formatted = this.basicHTMLFormat(text);
            break;
          case 'css':
            // Basic CSS formatting
            formatted = this.basicCSSFormat(text);
            break;
          default:
            return [];
        }

        console.log('✅ Code formatted successfully');
        return [{
          range: model.getFullModelRange(),
          text: formatted
        }];
      } catch (error) {
        console.error('❌ Prettier formatting error:', error);
        return [];
      }
    }

    basicJSFormat(text) {
      // Enhanced JavaScript formatting
      let formatted = text
        // Add spaces around operators
        .replace(/([=+\-*/%<>!&|])/g, ' $1 ')
        .replace(/\s+/g, ' ')
        // Format braces
        .replace(/\s*{\s*/g, ' {\n  ')
        .replace(/;\s*/g, ';\n  ')
        .replace(/}\s*/g, '\n}\n')
        // Format function parameters
        .replace(/,\s*/g, ', ')
        // Format arrays and objects
        .replace(/\[\s*/g, '[')
        .replace(/\s*\]/g, ']')
        // Clean up multiple newlines
        .replace(/\n\s*\n/g, '\n')
        .trim();

      // Basic indentation
      const lines = formatted.split('\n');
      let indentLevel = 0;
      return lines.map(line => {
        const trimmed = line.trim();
        if (trimmed.includes('}')) indentLevel--;
        const indented = '  '.repeat(Math.max(0, indentLevel)) + trimmed;
        if (trimmed.includes('{')) indentLevel++;
        return indented;
      }).join('\n');
    }

    basicHTMLFormat(text) {
      // Enhanced HTML formatting
      let formatted = text
        .replace(/></g, '>\n<')
        .replace(/^\s+|\s+$/gm, '');

      const lines = formatted.split('\n').filter(line => line.trim());
      let indentLevel = 0;

      return lines.map(line => {
        const trimmed = line.trim();

        // Decrease indent for closing tags
        if (trimmed.startsWith('</') && !trimmed.includes('/>')) {
          indentLevel--;
        }

        const indented = '  '.repeat(Math.max(0, indentLevel)) + trimmed;

        // Increase indent for opening tags (but not self-closing)
        if (trimmed.startsWith('<') && !trimmed.startsWith('</') && !trimmed.includes('/>') && !trimmed.includes('<!')) {
          indentLevel++;
        }

        return indented;
      }).join('\n');
    }

    basicCSSFormat(text) {
      // Enhanced CSS formatting
      let formatted = text
        // Format selectors and braces
        .replace(/\s*{\s*/g, ' {\n  ')
        .replace(/;\s*/g, ';\n  ')
        .replace(/}\s*/g, '\n}\n\n')
        // Format property values
        .replace(/:\s*/g, ': ')
        // Format multiple selectors
        .replace(/,\s*/g, ',\n')
        // Clean up
        .replace(/\n\s*\n\s*\n/g, '\n\n')
        .trim();

      return formatted;
    }

    activateESLint() {
      console.log('🛡️ Activating ESLint extension...');

      // Use built-in linting instead of external ESLint library
      this.eslintProviders = [];

      const jsProvider = monaco.languages.registerCodeActionProvider('javascript', {
        provideCodeActions: (model, range, context) => {
          return this.getESLintActions(model, range, context);
        }
      });
      this.eslintProviders.push(jsProvider);

      console.log('✅ Built-in ESLint providers registered');
    }

    deactivateESLint() {
      console.log('🔄 Deactivating ESLint extension...');
      if (this.eslintProviders) {
        this.eslintProviders.forEach(provider => {
          provider.dispose();
        });
        this.eslintProviders = [];
      }
    }

    getESLintActions(model, range, context) {
      // Built-in ESLint-style fixes
      const text = model.getValueInRange(range);
      const actions = [];

      // Common JavaScript fixes
      if (text.includes('var ')) {
        actions.push({
          title: '🛡️ Replace var with const/let',
          kind: 'quickfix',
          edit: {
            edits: [{
              resource: model.uri,
              edit: {
                range: range,
                text: text.replace(/var /g, 'const ')
              }
            }]
          }
        });
      }

      if (text.includes('==')) {
        actions.push({
          title: '🛡️ Use strict equality (===)',
          kind: 'quickfix',
          edit: {
            edits: [{
              resource: model.uri,
              edit: {
                range: range,
                text: text.replace(/==/g, '===').replace(/!=/g, '!==')
              }
            }]
          }
        });
      }

      return { actions };
    }

    activateTypeScript() {
      console.log('📘 Activating TypeScript extension...');

      // Configure TypeScript compiler options for better IntelliSense
      if (typeof monaco !== 'undefined') {
        monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
          target: monaco.languages.typescript.ScriptTarget.ES2020,
          allowNonTsExtensions: true,
          moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
          module: monaco.languages.typescript.ModuleKind.CommonJS,
          noEmit: true,
          esModuleInterop: true,
          jsx: monaco.languages.typescript.JsxEmit.React,
          reactNamespace: 'React',
          allowJs: true,
          typeRoots: ['node_modules/@types']
        });

        monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
          target: monaco.languages.typescript.ScriptTarget.ES2020,
          allowNonTsExtensions: true,
          moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
          module: monaco.languages.typescript.ModuleKind.CommonJS,
          noEmit: true,
          esModuleInterop: true,
          allowJs: true,
          typeRoots: ['node_modules/@types']
        });

        console.log('✅ TypeScript compiler options configured');
      }
    }

    deactivateTypeScript() {
      console.log('🔄 TypeScript extension deactivated');
    }

    activateIntelliSense() {
      // Enhanced IntelliSense is built into Monaco, just ensure it's configured
      if (typeof monaco !== 'undefined') {
        monaco.languages.registerCompletionItemProvider('javascript', {
          provideCompletionItems: (model, position) => {
            return this.getEnhancedCompletions(model, position, 'javascript');
          }
        });

        monaco.languages.registerCompletionItemProvider('python', {
          provideCompletionItems: (model, position) => {
            return this.getEnhancedCompletions(model, position, 'python');
          }
        });

        monaco.languages.registerCompletionItemProvider('html', {
          provideCompletionItems: (model, position) => {
            return this.getEnhancedCompletions(model, position, 'html');
          }
        });
      }
    }

    deactivateIntelliSense() {
      console.log('IntelliSense extension deactivated');
    }

    getEnhancedCompletions(model, position, language) {
      const suggestions = [];

      switch (language) {
        case 'javascript':
          suggestions.push(
            {
              label: 'console.log',
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: 'console.log(${1:message});',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Log a message to the console'
            },
            {
              label: 'async function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'async function ${1:functionName}(${2:params}) {\n\t${3:// function body}\n\treturn ${4:result};\n}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create an async function'
            }
          );
          break;
        case 'python':
          suggestions.push(
            {
              label: 'print',
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: 'print(${1:message})',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Print a message'
            },
            {
              label: 'def function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'def ${1:function_name}(${2:params}):\n\t"""${3:docstring}"""\n\t${4:pass}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create a Python function'
            }
          );
          break;
        case 'html':
          suggestions.push(
            {
              label: 'html5',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: '<!DOCTYPE html>\n<html lang="${1:en}">\n<head>\n\t<meta charset="UTF-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1.0">\n\t<title>${2:Document}</title>\n</head>\n<body>\n\t${3:content}\n</body>\n</html>',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'HTML5 boilerplate'
            }
          );
          break;
      }

      return { suggestions };
    }

    activateSyntaxHighlighting() {
      // Enhanced syntax highlighting
      if (typeof monaco !== 'undefined') {
        // Register custom themes
        monaco.editor.defineTheme('forgex-dark', {
          base: 'vs-dark',
          inherit: true,
          rules: [
            { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
            { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
            { token: 'string', foreground: 'CE9178' },
            { token: 'number', foreground: 'B5CEA8' },
            { token: 'function', foreground: 'DCDCAA' },
            { token: 'variable', foreground: '9CDCFE' },
            { token: 'type', foreground: '4EC9B0' }
          ],
          colors: {
            'editor.background': '#1e1e1e',
            'editor.foreground': '#d4d4d4',
            'editorLineNumber.foreground': '#858585',
            'editorCursor.foreground': '#C0ff6b'
          }
        });

        monaco.editor.setTheme('forgex-dark');
      }
    }

    deactivateSyntaxHighlighting() {
      if (typeof monaco !== 'undefined') {
        monaco.editor.setTheme('vs-dark');
      }
    }

    activateAutoComplete() {
      // Auto-complete is built into Monaco, just ensure it's enabled
      if (typeof monaco !== 'undefined' && editor) {
        editor.updateOptions({
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnCommitCharacter: true,
          acceptSuggestionOnEnter: 'on',
          snippetSuggestions: 'inline',
          wordBasedSuggestions: true,
          quickSuggestions: {
            other: true,
            comments: false,
            strings: false
          }
        });
      }
    }

    deactivateAutoComplete() {
      if (typeof monaco !== 'undefined' && editor) {
        editor.updateOptions({
          suggestOnTriggerCharacters: false,
          acceptSuggestionOnCommitCharacter: false,
          acceptSuggestionOnEnter: 'off',
          snippetSuggestions: 'none',
          wordBasedSuggestions: false,
          quickSuggestions: false
        });
      }
    }

    executeCommand(commandId) {
      switch (commandId) {
        case 'blackbox.generateCode':
          this.generateCodeWithAI();
          break;
        case 'blackbox.explainCode':
          this.explainSelectedCode();
          break;
        case 'prettier.formatDocument':
          this.formatDocument();
          break;
        case 'eslint.fixAll':
          this.fixAllESLintProblems();
          break;
        case 'emmet.expandAbbreviation':
          this.expandEmmetAbbreviation();
          break;
        default:
          console.log(`Command not implemented: ${commandId}`);
      }
    }

    generateCodeWithAI() {
      const selection = editor.getSelection();
      const selectedText = editor.getModel().getValueInRange(selection);

      // Simulate AI code generation
      const aiCode = `// AI Generated Code\nfunction generatedFunction() {\n\t// This would be AI-generated based on: ${selectedText || 'current context'}\n\treturn 'AI generated result';\n}`;

      editor.executeEdits('ai-generation', [{
        range: selection,
        text: aiCode
      }]);
    }

    explainSelectedCode() {
      const selection = editor.getSelection();
      const selectedText = editor.getModel().getValueInRange(selection);

      if (selectedText) {
        alert(`AI Explanation: This code appears to be ${selectedText.length > 50 ? 'a complex function' : 'a simple statement'} that performs specific operations. In a real implementation, this would provide detailed AI-powered explanations.`);
      } else {
        alert('Please select some code to explain.');
      }
    }

    formatDocument() {
      if (editor) {
        editor.getAction('editor.action.formatDocument').run();
      }
    }

    fixAllESLintProblems() {
      // Simulate ESLint fixes
      const model = editor.getModel();
      const text = model.getValue();
      const fixedText = text.replace(/var /g, 'const ').replace(/==/g, '===');

      editor.setValue(fixedText);
      console.log('Applied ESLint fixes');
    }

    expandEmmetAbbreviation() {
      // Custom Emmet expansion implementation
      if (editor) {
        const selection = editor.getSelection();
        const model = editor.getModel();
        const lineContent = model.getLineContent(selection.startLineNumber);
        const wordInfo = model.getWordUntilPosition(selection.getStartPosition());
        const currentWord = wordInfo.word;

        console.log(`🚀 Expanding Emmet abbreviation: "${currentWord}"`);

        const expanded = this.expandEmmetAbbreviation_Custom(currentWord);
        if (expanded) {
          editor.executeEdits('emmet-expansion', [{
            range: {
              startLineNumber: selection.startLineNumber,
              endLineNumber: selection.endLineNumber,
              startColumn: wordInfo.startColumn,
              endColumn: wordInfo.endColumn
            },
            text: expanded
          }]);
          console.log('✅ Emmet abbreviation expanded');
        } else {
          console.log('❌ No Emmet expansion found for:', currentWord);
        }
      }
    }

    expandEmmetAbbreviation_Custom(abbreviation) {
      const emmetExpansions = {
        // HTML expansions
        'div': '<div>${1}</div>',
        'p': '<p>${1}</p>',
        'span': '<span>${1}</span>',
        'a': '<a href="${1}">${2}</a>',
        'img': '<img src="${1}" alt="${2}">',
        'ul': '<ul>\n\t<li>${1}</li>\n</ul>',
        'ol': '<ol>\n\t<li>${1}</li>\n</ol>',
        'li': '<li>${1}</li>',
        'h1': '<h1>${1}</h1>',
        'h2': '<h2>${1}</h2>',
        'h3': '<h3>${1}</h3>',
        'table': '<table>\n\t<tr>\n\t\t<td>${1}</td>\n\t</tr>\n</table>',
        'form': '<form action="${1}" method="${2:post}">\n\t${3}\n</form>',
        'input': '<input type="${1:text}" name="${2}" value="${3}">',
        'button': '<button type="${1:button}">${2}</button>',

        // CSS expansions
        'm': 'margin: ${1};',
        'mt': 'margin-top: ${1};',
        'mr': 'margin-right: ${1};',
        'mb': 'margin-bottom: ${1};',
        'ml': 'margin-left: ${1};',
        'p': 'padding: ${1};',
        'pt': 'padding-top: ${1};',
        'pr': 'padding-right: ${1};',
        'pb': 'padding-bottom: ${1};',
        'pl': 'padding-left: ${1};',
        'w': 'width: ${1};',
        'h': 'height: ${1};',
        'bg': 'background: ${1};',
        'c': 'color: ${1};',
        'fs': 'font-size: ${1};',
        'fw': 'font-weight: ${1};',
        'ta': 'text-align: ${1};',
        'd': 'display: ${1};',
        'pos': 'position: ${1};',
        'fl': 'float: ${1};',
        'cl': 'clear: ${1};'
      };

      return emmetExpansions[abbreviation.toLowerCase()] || null;
    }
  }

  // Global extension manager instance
  let extensionManager;

  // Initialize Extension System
  function initializeExtensionSystem() {
    try {
      extensionManager = new ExtensionManager();
      setupExtensionUI();
      console.log('✅ Extension system initialized');
    } catch (error) {
      console.error('❌ Failed to initialize extension system:', error);
    }
  }

  // Setup Extension UI Event Handlers
  function setupExtensionUI() {
    console.log('Setting up extension UI...');

    // Sidebar tab switching
    const sidebarTabs = document.querySelectorAll('.sidebar-tab');
    console.log(`Found ${sidebarTabs.length} sidebar tabs`);

    sidebarTabs.forEach(tab => {
      tab.addEventListener('click', (e) => {
        const panelType = e.target.dataset.panel;
        console.log(`Switching to panel: ${panelType}`);
        switchSidebarPanel(panelType);
      });
    });

    // Extension toggle switches
    const extensionToggles = document.querySelectorAll('.extension-toggle');
    console.log(`Found ${extensionToggles.length} extension toggles`);

    extensionToggles.forEach(toggle => {
      console.log(`Setting up toggle for extension: ${toggle.dataset.extension}`);

      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const extensionId = toggle.dataset.extension;
        const isEnabled = toggle.classList.contains('active');

        console.log(`Toggle clicked for extension: ${extensionId}, currently enabled: ${isEnabled}`);

        if (!extensionManager) {
          console.error('Extension manager not initialized!');
          return;
        }

        if (isEnabled) {
          extensionManager.deactivateExtension(extensionId);
        } else {
          extensionManager.activateExtension(extensionId);
        }
      });
    });

    // Extension command buttons
    document.getElementById('format-document')?.addEventListener('click', () => {
      extensionManager.executeCommand('prettier.formatDocument');
    });

    document.getElementById('fix-all-problems')?.addEventListener('click', () => {
      extensionManager.executeCommand('eslint.fixAll');
    });

    document.getElementById('toggle-word-wrap')?.addEventListener('click', () => {
      if (editor) {
        const currentWrap = editor.getOption(monaco.editor.EditorOption.wordWrap);
        editor.updateOptions({
          wordWrap: currentWrap === 'on' ? 'off' : 'on'
        });
        console.log(`Word wrap ${currentWrap === 'on' ? 'disabled' : 'enabled'}`);
      }
    });

    document.getElementById('toggle-minimap')?.addEventListener('click', () => {
      if (editor) {
        const currentMinimap = editor.getOption(monaco.editor.EditorOption.minimap);
        editor.updateOptions({
          minimap: { enabled: !currentMinimap.enabled }
        });
        console.log(`Minimap ${currentMinimap.enabled ? 'disabled' : 'enabled'}`);
      }
    });

    // Refresh extensions button
    document.getElementById('refresh-extensions-btn')?.addEventListener('click', () => {
      location.reload();
    });

    // Initialize extension states in UI
    setTimeout(() => {
      if (extensionManager) {
        extensionManager.enabledExtensions.forEach(extensionId => {
          extensionManager.updateExtensionUI(extensionId, true);
        });
      }
    }, 100);
  }

  function switchSidebarPanel(panelType) {
    // Remove active class from all tabs and panels
    document.querySelectorAll('.sidebar-tab').forEach(tab => {
      tab.classList.remove('active');
    });
    document.querySelectorAll('.sidebar-panel').forEach(panel => {
      panel.classList.remove('active');
    });

    // Add active class to selected tab and panel
    document.querySelector(`[data-panel="${panelType}"]`).classList.add('active');
    document.getElementById(`${panelType}-panel`).classList.add('active');

    // Show/hide extension commands based on panel
    const extensionCommands = document.getElementById('extension-commands');
    if (extensionCommands) {
      extensionCommands.classList.toggle('active', panelType === 'extensions');
    }
  }

  // Add keyboard shortcuts for extensions
  function setupExtensionKeyboardShortcuts() {
    if (typeof monaco !== 'undefined' && editor) {
      // Ctrl+Shift+F for format document
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
        extensionManager.executeCommand('prettier.formatDocument');
      });

      // Ctrl+Shift+P for command palette (simulate)
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyP, () => {
        showExtensionCommandPalette();
      });

      // Ctrl+Space for IntelliSense
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Space, () => {
        editor.trigger('keyboard', 'editor.action.triggerSuggest', {});
      });

      // Ctrl+E for Emmet expansion (safer than Tab)
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyE, () => {
        if (extensionManager && extensionManager.enabledExtensions.has('emmet')) {
          extensionManager.expandEmmetAbbreviation();
        }
      });

      // Alt+/ for AI code generation
      editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.Slash, () => {
        if (extensionManager && extensionManager.enabledExtensions.has('blackbox')) {
          extensionManager.generateCodeWithAI();
        }
      });
    }
  }

  function showExtensionCommandPalette() {
    const commands = [];
    extensionManager.extensions.forEach((extension, id) => {
      if (extensionManager.enabledExtensions.has(id)) {
        extension.commands?.forEach(cmd => {
          commands.push(cmd);
        });
      }
    });

    // Simple command palette simulation
    const commandTitles = commands.map(cmd => cmd.title).join('\n');
    const selected = prompt(`Available Commands:\n${commandTitles}\n\nEnter command title:`);

    const command = commands.find(cmd => cmd.title === selected);
    if (command) {
      extensionManager.executeCommand(command.id);
    }
  }

  // Initialize keyboard shortcuts after Monaco is ready
  setTimeout(() => {
    if (typeof monaco !== 'undefined' && editor) {
      setupExtensionKeyboardShortcuts();
    }
  }, 1000);

    // Function to configure additional languages
    function configureAdditionalLanguages() {
      // Swift support
      monaco.languages.register({ id: "swift" });
      monaco.languages.setMonarchTokensProvider("swift", {
        keywords: [
          "class",
          "struct",
          "enum",
          "protocol",
          "extension",
          "let",
          "var",
          "func",
          "if",
          "else",
          "for",
          "while",
          "do",
          "switch",
          "case",
          "default",
          "break",
          "continue",
          "return",
          "true",
          "false",
          "nil",
          "self",
          "super",
          "init",
          "import",
          "try",
          "catch",
          "throw",
          "guard",
        ],
        typeKeywords: [
          "Int",
          "Double",
          "String",
          "Bool",
          "Character",
          "Array",
          "Dictionary",
          "Set",
          "Any",
          "AnyObject",
        ],
        operators: ["+", "-", "*", "/", "=", "==", "!=", ">", "<", ">=", "<="],
        symbols: /[=><!~?:&|+\-*\/\^%]+/,

        tokenizer: {
          root: [
            [
              /[a-zA-Z_][\w$]*/,
              {
                cases: {
                  "@keywords": "keyword",
                  "@typeKeywords": "type",
                  "@default": "identifier",
                },
              },
            ],
            { include: "@whitespace" },
            [/[{}()\[\]]/, "@brackets"],
            [/@symbols/, { cases: { "@operators": "operator", "@default": "" } }],
            [/\d*\.\d+([eE][\-+]?\d+)?/, "number.float"],
            [/\d+/, "number"],
            [/"/, { token: "string.quote", bracket: "@open", next: "@string" }],
          ],
          string: [
            [/[^"\\]+/, "string"],
            [/\\./, "string.escape"],
            [/"/, { token: "string.quote", bracket: "@close", next: "@pop" }],
          ],
          comment: [
            [/[^/*]+/, "comment"],
            [/\/\*/, "comment", "@push"],
            [/\*\//, "comment", "@pop"],
            [/[/*]/, "comment"],
          ],
          whitespace: [
            [/[ \t\r\n]+/, "white"],
            [/\/\/.*$/, "comment"],
            [/\/\*/, "comment", "@comment"],
          ],
        },
      });

      // Kotlin support
      monaco.languages.register({ id: "kotlin" });
      monaco.languages.setMonarchTokensProvider("kotlin", {
        keywords: [
          "package",
          "import",
          "class",
          "interface",
          "fun",
          "val",
          "var",
          "if",
          "else",
          "when",
          "for",
          "while",
          "do",
          "try",
          "catch",
          "finally",
          "return",
          "break",
          "continue",
          "object",
          "companion",
          "data",
          "sealed",
          "abstract",
          "enum",
          "open",
          "override",
          "private",
          "protected",
          "public",
          "internal",
          "constructor",
          "init",
          "get",
          "set",
          "by",
          "true",
          "false",
          "null",
        ],
        typeKeywords: [
          "Int",
          "Double",
          "Float",
          "Long",
          "Short",
          "Byte",
          "Boolean",
          "String",
          "Char",
          "Any",
        ],
        operators: [
          "+",
          "-",
          "*",
          "/",
          "%",
          "=",
          "==",
          "!=",
          ">",
          "<",
          ">=",
          "<=",
        ],
        symbols: /[=><!~?:&|+\-*\/\^%]+/, // Added missing 'symbols' attribute

        tokenizer: {
          root: [
            [
              /[a-zA-Z_][\w$]*/,
              {
                cases: {
                  "@keywords": "keyword",
                  "@typeKeywords": "type",
                  "@default": "identifier",
                },
              },
            ],
            { include: "@whitespace" },
            [/[{}()\[\]]/, "@brackets"],
            [/[<>](?!@symbols)/, "@brackets"],
            [/@symbols/, "operator"],
            [/\d*\.\d+([eE][\-+]?\d+)?[fFL]?/, "number.float"],
            [/\d+/, "number"],
            [/"/, { token: "string.quote", bracket: "@open", next: "@string" }],
            [/'[^']*'/, "string"],
          ],
          string: [
            [/[^"\\]+/, "string"],
            [/\\./, "string.escape"],
            [/"/, { token: "string.quote", bracket: "@close", next: "@pop" }],
          ],
          whitespace: [
            [/[ \t\r\n]+/, "white"],
            [/\/\/.*$/, "comment"],
            [/\/\*/, "comment", "@comment"],
          ],
          comment: [
            [/[^/*]+/, "comment"],
            [/\/\*/, "comment", "@push"],
            [/\*\//, "comment", "@pop"],
            [/[/*]/, "comment"],
          ],
        },
      });

      // Python language definition - Enhanced syntax highlighting
      monaco.languages.setMonarchTokensProvider("python", {
        defaultToken: "",
        tokenPostfix: ".python",

        keywords: [
          "and",
          "as",
          "assert",
          "break",
          "class",
          "continue",
          "def",
          "del",
          "elif",
          "else",
          "except",
          "exec",
          "finally",
          "for",
          "from",
          "global",
          "if",
          "import",
          "in",
          "is",
          "lambda",
          "None",
          "not",
          "or",
          "pass",
          "print",
          "raise",
          "return",
          "self",
          "try",
          "while",
          "with",
          "yield",

          "int",
          "float",
          "long",
          "complex",
          "hex",

          "abs",
          "all",
          "any",
          "apply",
          "basestring",
          "bin",
          "bool",
          "buffer",
          "bytearray",
          "callable",
          "chr",
          "classmethod",
          "cmp",
          "coerce",
          "compile",
          "complex",
          "delattr",
          "dict",
          "dir",
          "divmod",
          "enumerate",
          "eval",
          "execfile",
          "file",
          "filter",
          "format",
          "frozenset",
          "getattr",
          "globals",
          "hasattr",
          "hash",
          "help",
          "id",
          "input",
          "intern",
          "isinstance",
          "issubclass",
          "iter",
          "len",
          "locals",
          "list",
          "map",
          "max",
          "memoryview",
          "min",
          "next",
          "object",
          "oct",
          "open",
          "ord",
          "pow",
          "print",
          "property",
          "reversed",
          "range",
          "raw_input",
          "reduce",
          "reload",
          "repr",
          "reversed",
          "round",
          "set",
          "setattr",
          "slice",
          "sorted",
          "staticmethod",
          "str",
          "sum",
          "super",
          "tuple",
          "type",
          "unichr",
          "unicode",
          "vars",
          "xrange",
          "zip",

          "True",
          "False",

          "__dict__",
          "__methods__",
          "__members__",
          "__class__",
          "__bases__",
          "__name__",
          "__mro__",
          "__subclasses__",
          "__init__",
          "__import__",
        ],

        brackets: [
          { open: "{", close: "}", token: "delimiter.curly" },
          { open: "[", close: "]", token: "delimiter.bracket" },
          { open: "(", close: ")", token: "delimiter.parenthesis" },
        ],

        tokenizer: {
          root: [
            { include: "@whitespace" },
            { include: "@numbers" },
            { include: "@strings" },

            [/[,:;]/, "delimiter"],
            [/[{}\[\]()]/, "@brackets"],

            [/@[a-zA-Z]\w*/, "tag"],
            [
              /[a-zA-Z]\w*/,
              {
                cases: {
                  "@keywords": "keyword",
                  "@default": "identifier",
                },
              },
            ],
          ],

          // Deal with white space, including single and multi-line comments
          whitespace: [
            [/\s+/, "white"],
            [/(^#.*$)/, "comment"],
            [/('''.*''')|(""".*""")/, "string"],
            [/'''.*$/, "string", "@endDocString"],
            [/""".*$/, "string", "@endDblDocString"],
          ],
          endDocString: [
            [/\\'/, "string"],
            [/.*'''/, "string", "@popall"],
            [/.*$/, "string"],
          ],
          endDblDocString: [
            [/\\"/, "string"],
            [/.*"""/, "string", "@popall"],
            [/.*$/, "string"],
          ],

          // Recognize hex, negatives, decimals, imaginaries, longs, and scientific notation
          numbers: [
            [/-?0x([abcdef]|[ABCDEF]|\d)+[lL]?/, "number.hex"],
            [/-?(\d*\.)?\d+([eE][+\-]?\d+)?[jJ]?[lL]?/, "number"],
          ],

          // Recognize strings, including those broken across lines with \ (but not without)
          strings: [
            [/'$/, "string.escape", "@popall"],
            [/'/, "string.escape", "@stringBody"],
            [/"$/, "string.escape", "@popall"],
            [/"/, "string.escape", "@dblStringBody"],
          ],
          stringBody: [
            [/[^\\']+$/, "string", "@popall"],
            [/[^\\']+/, "string"],
            [/\\./, "string"],
            [/'/, "string.escape", "@popall"],
            [/\\$/, "string"],
          ],
          dblStringBody: [
            [/[^\\"]+$/, "string", "@popall"],
            [/[^\\"]+/, "string"],
            [/\\./, "string"],
            [/"/, "string.escape", "@popall"],
            [/\\$/, "string"],
          ],
        },
      });

      // Java language definition
      monaco.languages.setMonarchTokensProvider("java", {
        defaultToken: "",
        tokenPostfix: ".java",

        keywords: [
          "abstract",
          "continue",
          "for",
          "new",
          "switch",
          "assert",
          "default",
          "goto",
          "package",
          "synchronized",
          "boolean",
          "do",
          "if",
          "private",
          "this",
          "break",
          "double",
          "implements",
          "protected",
          "throw",
          "byte",
          "else",
          "import",
          "public",
          "throws",
          "case",
          "enum",
          "instanceof",
          "return",
          "transient",
          "catch",
          "extends",
          "int",
          "short",
          "try",
          "char",
          "final",
          "interface",
          "static",
          "void",
          "class",
          "finally",
          "long",
          "strictfp",
          "volatile",
          "const",
          "float",
          "native",
          "super",
          "while",
          "true",
          "false",
        ],

        operators: [
          "=",
          ">",
          "<",
          "!",
          "~",
          "?",
          ":",
          "==",
          "<=",
          ">=",
          "!=",
          "&&",
          "||",
          "++",
          "--",
          "+",
          "-",
          "*",
          "/",
          "&",
          "|",
          "^",
          "%",
          "<<",
          ">>",
          ">>>",
          "+=",
          "-=",
          "*=",
          "/=",
          "%=",
          "++",
          "--",
          "&&",
          "||",
          "!",
          "==",
          "!=",
          ">",
          "<",
          ">=",
          "<=",
          "&",
          "|",
          "^",
          "~",
          "<<",
          ">>",
          ">>>",
          "+=",
          "-=",
          "*=",
          "/=",
          "%=",
          "<<=",
          ">>=",
          ">>>=",
        ],

        // we include these common regular expressions
        symbols: /[=><!~?:&|+\-*\/\^%]+/,
        escapes:
          /\\(?:[abfnrtv\\"']|x[0-9A-Fa-f]{1,4}|u[0-9A-Fa-f]{4}|U[0-9A-Fa-f]{8})/,
        digits: /\d+(_+\d+)*/,
        octaldigits: /[0-7]+(_+[0-7]+)*/,
        binarydigits: /[0-1]+(_+[0-1]+)*/,
        hexdigits: /[[0-9a-fA-F]+(_+[0-9a-fA-F]+)*/,

        // The main tokenizer for our languages
        tokenizer: {
          root: [
            // identifiers and keywords
            [
              /[a-zA-Z_$][\w$]*/,
              {
                cases: {
                  "@keywords": { token: "keyword.$0" },
                  "@default": "identifier",
                },
              },
            ],

            // whitespace
            { include: "@whitespace" },

            // delimiters and operators
            [/[{}()\[\]]/, "@brackets"],
            [/[<>](?!@symbols)/, "@brackets"],
            [
              /@symbols/,
              {
                cases: {
                  "@operators": "delimiter",
                  "@default": "",
                },
              },
            ],

            // @ annotations.
            [/@\s*[a-zA-Z_\$][\w\$]*/, "annotation"],

            // numbers
            [/(@digits)[eE]([\-+]?(@digits))?[fFdD]?/, "number.float"],
            [/(@digits)\.(@digits)([eE][\-+]?(@digits))?[fFdD]?/, "number.float"],
            [/0[xX](@hexdigits)[Ll]?/, "number.hex"],
            [/0(@octaldigits)[Ll]?/, "number.octal"],
            [/0[bB](@binarydigits)[Ll]?/, "number.binary"],
            [/(@digits)[fFdD]/, "number.float"],
            [/(@digits)[lL]?/, "number"],

            // delimiter: after number because of .\d floats
            [/[;,.]/, "delimiter"],

            // strings
            [/"([^"\\]|\\.)*$/, "string.invalid"], // non-teminated string
            [/"/, "string", "@string"],

            // characters
            [/'[^\\']'/, "string"],
            [/(')(@escapes)(')/, ["string", "string.escape", "string"]],
            [/'/, "string.invalid"],
          ],

          whitespace: [
            [/[ \t\r\n]+/, ""],
            [/\/\*\*(?!\/)/, "comment.doc", "@javadoc"],
            [/\/\*/, "comment", "@comment"],
            [/\/\/.*$/, "comment"],
          ],

          comment: [
            [/[^\/*]+/, "comment"],
            // [/\/\*/, 'comment', '@push' ],    // nested comment not allowed :-(
            // [/\/\*/,    'comment.invalid' ],    // this breaks block comments in the shape of /* //*/
            [/\*\//, "comment", "@pop"],
            [/[\/*]/, "comment"],
          ],
          //Identical copy of comment above, except for the addition of .doc
          javadoc: [
            [/[^\/*]+/, "comment.doc"],
            // [/\/\*/, 'comment.doc', '@push' ],    // nested comment not allowed :-(
            [/\/\*/, "comment.doc.invalid"],
            [/\*\//, "comment.doc", "@pop"],
            [/[\/*]/, "comment.doc"],
          ],

          string: [
            [/[^\\"]+/, "string"],
            [/@escapes/, "string.escape"],
            [/\\./, "string.escape.invalid"],
            [/"/, "string", "@pop"],
          ],
        },
      });

      // HTML language definition - Enhanced syntax highlighting
      monaco.languages.setMonarchTokensProvider("html", {
        defaultToken: "",
        tokenPostfix: ".html",
        ignoreCase: true,

        // The main tokenizer for our languages
        tokenizer: {
          root: [
            [/<!DOCTYPE/, "metatag", "@doctype"],
            [/<!--/, "comment", "@comment"],
            [
              /(<)((?:[\w\-]+:)?[\w\-]+)(\s*)(\/>)/,
              ["delimiter", "tag", "", "delimiter"],
            ],
            [/(<)(script)/, ["delimiter", { token: "tag", next: "@script" }]],
            [/(<)(style)/, ["delimiter", { token: "tag", next: "@style" }]],
            [
              /(<)((?:[\w\-]+:)?[\w\-]+)/,
              ["delimiter", { token: "tag", next: "@otherTag" }],
            ],
            [
              /(<\/)((?:[\w\-]+:)?[\w\-]+)/,
              ["delimiter", { token: "tag", next: "@otherTag" }],
            ],
            [/</, "delimiter"],
            [/[^<]+/], // text
          ],

          doctype: [
            [/[^>]+/, "metatag.content"],
            [/>/, "metatag", "@pop"],
          ],

          comment: [
            [/-->/, "comment", "@pop"],
            [/[^-]+/, "comment.content"],
            [/./, "comment.content"],
          ],

          otherTag: [
            [/\/?>/, "delimiter", "@pop"],
            [/"([^"]*)"/, "attribute.value"],
            [/'([^']*)'/, "attribute.value"],
            [/[\w\-]+/, "attribute.name"],
            [/=/, "delimiter"],
            [/[ \t\r\n]+/], // whitespace
          ],

          // -- BEGIN <script> tags handling

          // After <script
          script: [
            [/type/, "attribute.name", "@scriptAfterType"],
            [/"([^"]*)"/, "attribute.value"],
            [/'([^']*)'/, "attribute.value"],
            [/[\w\-]+/, "attribute.name"],
            [/=/, "delimiter"],
            [
              />/,
              {
                token: "delimiter",
                next: "@scriptEmbedded",
                nextEmbedded: "text/javascript",
              },
            ],
            [/[ \t\r\n]+/], // whitespace
            [
              /(<\/)(script\s*)(>)/,
              ["delimiter", "tag", { token: "delimiter", next: "@pop" }],
            ],
          ],

          // After <script ... type
          scriptAfterType: [
            [/=/, "delimiter", "@scriptAfterTypeEquals"],
            [
              />/,
              {
                token: "delimiter",
                next: "@scriptEmbedded",
                nextEmbedded: "text/javascript",
              },
            ], // cover invalid e.g. <script type>
            [/[ \t\r\n]+/], // whitespace
            [/<\/script\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          // After <script ... type =
          scriptAfterTypeEquals: [
            [
              /"([^"]*)"/,
              { token: "attribute.value", switchTo: "@scriptWithCustomType.$1" },
            ],
            [
              /'([^']*)'/,
              { token: "attribute.value", switchTo: "@scriptWithCustomType.$1" },
            ],
            [
              />/,
              {
                token: "delimiter",
                next: "@scriptEmbedded.$S2",
                nextEmbedded: "$S2",
              },
            ], // cover invalid e.g. <script type=>
            [/[ \t\r\n]+/], // whitespace
            [/<\/script\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          // After <script ... type = $S2
          scriptWithCustomType: [
            [
              />/,
              {
                token: "delimiter",
                next: "@scriptEmbedded.$S2",
                nextEmbedded: "$S2",
              },
            ],
            [/"([^"]*)"/, "attribute.value"],
            [/'([^']*)'/, "attribute.value"],
            [/[\w\-]+/, "attribute.name"],
            [/=/, "delimiter"],
            [/[ \t\r\n]+/], // whitespace
            [/<\/script\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          scriptEmbedded: [
            [
              /<\/script/,
              { token: "@rematch", next: "@pop", nextEmbedded: "@pop" },
            ],
            [/[^<]+/, ""],
          ],

          // -- END <script> tags handling

          // -- BEGIN <style> tags handling

          // After <style
          style: [
            [/type/, "attribute.name", "@styleAfterType"],
            [/"([^"]*)"/, "attribute.value"],
            [/'([^']*)'/, "attribute.value"],
            [/[\w\-]+/, "attribute.name"],
            [/=/, "delimiter"],
            [
              />/,
              {
                token: "delimiter",
                next: "@styleEmbedded",
                nextEmbedded: "text/css",
              },
            ],
            [/[ \t\r\n]+/], // whitespace
            [
              /(<\/)(style\s*)(>)/,
              ["delimiter", "tag", { token: "delimiter", next: "@pop" }],
            ],
          ],

          // After <style ... type
          styleAfterType: [
            [/=/, "delimiter", "@styleAfterTypeEquals"],
            [
              />/,
              {
                token: "delimiter",
                next: "@styleEmbedded",
                nextEmbedded: "text/css",
              },
            ], // cover invalid e.g. <style type>
            [/[ \t\r\n]+/], // whitespace
            [/<\/style\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          // After <style ... type =
          styleAfterTypeEquals: [
            [
              /"([^"]*)"/,
              { token: "attribute.value", switchTo: "@styleWithCustomType.$1" },
            ],
            [
              /'([^']*)'/,
              { token: "attribute.value", switchTo: "@styleWithCustomType.$1" },
            ],
            [
              />/,
              {
                token: "delimiter",
                next: "@styleEmbedded.$S2",
                nextEmbedded: "$S2",
              },
            ], // cover invalid e.g. <style type=>
            [/[ \t\r\n]+/], // whitespace
            [/<\/style\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          styleWithCustomType: [
            [
              />/,
              {
                token: "delimiter",
                next: "@styleEmbedded.$S2",
                nextEmbedded: "$S2",
              },
            ],
            [/"([^"]*)"/, "attribute.value"],
            [/'([^']*)'/, "attribute.value"],
            [/[\w\-]+/, "attribute.name"],
            [/=/, "delimiter"],
            [/[ \t\r\n]+/], // whitespace
            [/<\/style\s*>/, { token: "@rematch", next: "@pop" }],
          ],

          styleEmbedded: [
            [
              /<\/style/,
              { token: "@rematch", next: "@pop", nextEmbedded: "@pop" },
            ],
            [/[^<]+/, ""],
          ],

          // -- END <style> tags handling
        },
      });

      // R language support
      monaco.languages.register({ id: "r" });
      monaco.languages.setMonarchTokensProvider("r", {
        keywords: [
          "if",
          "else",
          "for",
          "while",
          "repeat",
          "in",
          "next",
          "break",
          "function",
          "TRUE",
          "FALSE",
          "NULL",
          "NA",
          "Inf",
          "NaN",
          "return",
        ],
        operators: [
          "+",
          "-",
          "*",
          "/",
          "%",
          "=",
          "==",
          "!=",
          ">",
          "<",
          ">=",
          "<=",
        ],

        tokenizer: {
          root: [
            [
              /[a-zA-Z_][\w.]*/,
              {
                cases: {
                  "@keywords": "keyword",
                  "@default": "identifier",
                },
              },
            ],
            { include: "@whitespace" },
            [/[{}()\[\]]/, "@brackets"],
            [/@operators/, "operator"],
            [/\d*\.\d+([eE][\-+]?\d+)?/, "number.float"],
            [/\d+/, "number"],
            [
              /"/,
              { token: "string.quote", bracket: "@open", next: "@string_double" },
            ],
            [
              /'/,
              { token: "string.quote", bracket: "@open", next: "@string_single" },
            ],
          ],
          string_double: [
            [/[^"\\]+/, "string"],
            [/\\./, "string.escape"],
            [/"/, { token: "string.quote", bracket: "@close", next: "@pop" }],
          ],
          string_single: [
            [/[^'\\]+/, "string"],
            [/\\./, "string.escape"],
            [/'/, { token: "string.quote", bracket: "@close", next: "@pop" }],
          ],
          whitespace: [
            [/[ \t\r\n]+/, "white"],
            [/#.*$/, "comment"],
          ],
        },
      });
    }

    // Function to update the language selector with all supported languages
    function updateLanguageSelector() {
      const languageSelector = document.getElementById("language-selector");
      const currentSelection = languageSelector.value;

      // Clear existing options but remember the current selection
      languageSelector.innerHTML = "";

      // Define all supported languages with display names and IDs
      const supportedLanguages = [
        { id: "javascript", name: "JavaScript" },
        { id: "typescript", name: "TypeScript" },
        { id: "python", name: "Python" },
        { id: "html", name: "HTML" },
        { id: "css", name: "CSS" },
        { id: "c", name: "C" },
        { id: "cpp", name: "C++" },
        { id: "java", name: "Java" },
        { id: "go", name: "Go" },
        { id: "rust", name: "Rust" },
        { id: "ruby", name: "Ruby" },
        { id: "php", name: "PHP" },
        { id: "json", name: "JSON" },
        { id: "yaml", name: "YAML" },
        { id: "markdown", name: "Markdown" },
        { id: "shell", name: "Bash/Shell" },
        { id: "sql", name: "SQL" },
        { id: "xml", name: "XML" },
        { id: "swift", name: "Swift" },
        { id: "kotlin", name: "Kotlin" },
        { id: "r", name: "R" },
        { id: "csharp", name: "C#" },
        { id: "plaintext", name: "Plain Text" },
      ];

      // Add options to language selector
      supportedLanguages.forEach((lang) => {
        const option = document.createElement("option");
        option.value = lang.id;
        option.textContent = lang.name;
        languageSelector.appendChild(option);
      });

      // Try to restore the previous selection, or default to Python
      try {
        languageSelector.value = currentSelection;
      } catch (e) {
        languageSelector.value = "python";
      }
    }

    // Register code snippets for all supported languages
    function registerCodeSnippets() {
      // JavaScript snippets
      monaco.languages.registerCompletionItemProvider("javascript", {
        provideCompletionItems: function () {
          return {
            suggestions: [
              {
                label: "for",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  "for (let ${1:i} = 0; ${1:i} < ${2:array}.length; ${1:i}++) {\n\t${3}\n}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "For Loop",
              },
              {
                label: "function",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: "function ${1:name}(${2:params}) {\n\t${3}\n}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Function",
              },
              {
                label: "if",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: "if (${1:condition}) {\n\t${2}\n}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "If Statement",
              },
              {
                label: "arrow",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: "(${1:params}) => {\n\t${2}\n}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Arrow Function",
              },
              {
                label: "class",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  "class ${1:Name} {\n\tconstructor(${2:params}) {\n\t\t${3}\n\t}\n\n\t${4}\n}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Class",
              },
            ],
          };
        },
      });

      // Python snippets
      monaco.languages.registerCompletionItemProvider("python", {
        provideCompletionItems: function () {
          return {
            suggestions: [
              {
                label: "for",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: "for ${1:item} in ${2:items}:\n\t${3:pass}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "For Loop",
              },
              {
                label: "def",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  'def ${1:function_name}(${2:parameters}):\n\t"""${3:Docstring}\n\t"""\n\t${4:pass}',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Function definition",
              },
              {
                label: "class",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  'class ${1:ClassName}(${2:object}):\n\t"""${3:Docstring}\n\t"""\n\n\tdef __init__(self, ${4:parameters}):\n\t\t${5:pass}',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Class definition",
              },
              {
                label: "if",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: "if ${1:condition}:\n\t${2:pass}",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "If statement",
              },
            ],
          };
        },
      });

      // HTML snippets
      monaco.languages.registerCompletionItemProvider("html", {
        provideCompletionItems: function () {
          return {
            suggestions: [
              {
                label: "!",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  '<!DOCTYPE html>\n<html lang="en">\n<head>\n\t<meta charset="UTF-8">\n\t<meta name="viewport" content="width=device-width, initial-scale=1.0">\n\t<title>${1:Document}</title>\n</head>\n<body>\n\t${2}\n</body>\n</html>',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "HTML5 Boilerplate",
              },
              {
                label: "div",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText: '<div class="${1:className}">\n\t${2}\n</div>',
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Div with class",
              },
            ],
          };
        },
      });

      // CSS snippets
      monaco.languages.registerCompletionItemProvider("css", {
        provideCompletionItems: function () {
          return {
            suggestions: [
              {
                label: "flexbox",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  "display: flex;\nflex-direction: ${1:row};\njustify-content: ${2:center};\nalign-items: ${3:center};",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "Flexbox Layout",
              },
              {
                label: "grid",
                kind: monaco.languages.CompletionItemKind.Snippet,
                insertText:
                  "display: grid;\ngrid-template-columns: ${1:repeat(3, 1fr)};\ngrid-gap: ${2:10px};",
                insertTextRules:
                  monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                documentation: "CSS Grid",
              },
            ],
          };
        },
      });
    }

    // Function to force syntax highlighting and override any CSS conflicts
    function forceSyntaxHighlighting() {
      // Force Monaco editor theme
      monaco.editor.setTheme('vs-dark');

      // Ensure the model language is properly set
      if (editor && editor.getModel()) {
        const currentLanguage = editor.getModel().getLanguageId();
        console.log(`Current language: ${currentLanguage}`);

        // Re-apply the language to force tokenization
        monaco.editor.setModelLanguage(editor.getModel(), currentLanguage);
      }

      // Add custom CSS to override any conflicting styles
      const style = document.createElement('style');
      style.textContent = `
        /* Force Monaco Editor Syntax Highlighting - High Priority */
        .monaco-editor .view-lines .view-line span[class*="mtk"] {
          font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
        }

        /* Python comment color override */
        .monaco-editor .view-lines .view-line span.mtk2 {
          color: #6a9955 !important;
          font-style: italic !important;
        }

        /* Python string color override */
        .monaco-editor .view-lines .view-line span.mtk3 {
          color: #ce9178 !important;
        }

        /* Python keyword color override */
        .monaco-editor .view-lines .view-line span.mtk4 {
          color: #569cd6 !important;
          font-weight: bold !important;
        }

        /* Python number color override */
        .monaco-editor .view-lines .view-line span.mtk5 {
          color: #b5cea8 !important;
        }

        /* Override any global styles that might interfere */
        #monaco-editor .monaco-editor .view-lines .view-line span {
          color: inherit !important;
        }

        /* Ensure proper background */
        .monaco-editor, .monaco-editor .monaco-editor-background {
          background-color: #1e1e1e !important;
        }
      `;
      document.head.appendChild(style);

      // Force a re-render of the editor
      setTimeout(() => {
        if (editor) {
          editor.layout();
          // Trigger tokenization by making a small change and undoing it
          const model = editor.getModel();
          if (model) {
            const position = editor.getPosition();
            model.pushEditOperations([], [{
              range: new monaco.Range(1, 1, 1, 1),
              text: ' '
            }], () => []);
            model.undo();
            editor.setPosition(position);
          }
        }
      }, 100);

      console.log("✅ Forced syntax highlighting applied");
    }

    // Function to register IntelliSense providers for enhanced code completion
    function registerIntelliSenseProviders() {
      // Python IntelliSense
      monaco.languages.registerCompletionItemProvider("python", {
        provideCompletionItems: function (model, position) {
          const word = model.getWordUntilPosition(position);
          const range = {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn,
          };

          return {
            suggestions: [
              {
                label: "print",
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: "print(${1:value})",
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: range,
                documentation: "Print values to the console",
              },
              {
                label: "len",
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: "len(${1:object})",
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: range,
                documentation: "Return the length of an object",
              },
              {
                label: "range",
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: "range(${1:stop})",
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: range,
                documentation: "Create a range of numbers",
              },
            ],
          };
        },
      });

      // JavaScript IntelliSense
      monaco.languages.registerCompletionItemProvider("javascript", {
        provideCompletionItems: function (model, position) {
          const word = model.getWordUntilPosition(position);
          const range = {
            startLineNumber: position.lineNumber,
            endLineNumber: position.lineNumber,
            startColumn: word.startColumn,
            endColumn: word.endColumn,
          };

          return {
            suggestions: [
              {
                label: "console.log",
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: "console.log(${1:value})",
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: range,
                documentation: "Log values to the console",
              },
              {
                label: "document.getElementById",
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: "document.getElementById('${1:id}')",
                insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
                range: range,
                documentation: "Get element by ID",
              },
            ],
          };
        },
      });

      console.log("✅ IntelliSense providers registered");
    }

    function setupEventListeners() {
      document
        .getElementById("language-selector")
        .addEventListener("change", changeLanguage);
      document.getElementById("run-code-btn").addEventListener("click", runCode);
      document
        .getElementById("preview-btn")
        .addEventListener("click", previewProject);
      document
        .getElementById("save-btn")
        .addEventListener("click", saveCurrentFile);
      document
        .getElementById("download-btn")
        .addEventListener("click", downloadCurrentFile);
      document
        .getElementById("new-file-btn")
        .addEventListener("click", () => showModal("file"));
      document
        .getElementById("new-folder-btn")
        .addEventListener("click", () => showModal("folder"));
      document
        .getElementById("upload-file-btn")
        .addEventListener("click", () => document.getElementById("file-upload-input").click());
      document
        .getElementById("file-upload-input")
        .addEventListener("change", handleFileInputChange);
      document
        .getElementById("clear-output-btn")
        .addEventListener("click", clearOutput);
      document
        .getElementById("output-tab")
        .addEventListener("click", () => showPanel("output"));

      // Terminal tab event listener
      document
        .getElementById("terminal-tab")
        .addEventListener("click", () => {
          showPanel("terminal");
          // Also trigger terminal-specific panel show logic
          if (window.forgeXTerminal) {
            window.forgeXTerminal.showTerminalPanel();
          }
        });




      // Click outside context menu to close it
      document.addEventListener("click", (e) => {
        if (!e.target.closest("#context-menu")) {
          document.getElementById("context-menu").style.display = "none";
        }
      });

      // File search functionality
      document.getElementById("file-search").addEventListener("input", (e) => {
        filterFileTree(e.target.value);
      });

      // Keyboard shortcuts
      document.addEventListener("keydown", (e) => {
        // Ctrl+S to save
        if (e.ctrlKey && e.key === "s") {
          e.preventDefault();
          saveCurrentFile();
        }
        // Ctrl+F to focus file search
        if (e.ctrlKey && e.key === "f") {
          e.preventDefault();
          document.getElementById("file-search").focus();
        }
      });

      window.addEventListener("resize", adjustEditorLayout);
      setupContextMenu();
      setupModal();

    }

    async function initializeFileTree(retryCount = 0) {
      const maxRetries = 3;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff, max 5 seconds

      try {
        console.log(`🚀 Initializing file tree (attempt ${retryCount + 1}/${maxRetries + 1})`);

        // Check if we're already in the middle of loading
        if (window.fileTreeLoading) {
          console.log("⏳ File tree is already loading, skipping duplicate request");
          return;
        }

        window.fileTreeLoading = true;
        await fetchFileTree();
        window.fileTreeLoading = false;

        // Verify that the file tree was actually rendered
        const fileTreeEl = document.getElementById("file-tree");
        if (fileTreeEl && fileTreeEl.children.length > 0 && !fileTreeEl.querySelector('.empty-file-tree')) {
          console.log("✅ File tree successfully loaded and rendered");
        } else {
          throw new Error("File tree was not properly rendered");
        }

      } catch (error) {
        window.fileTreeLoading = false;
        console.error(`❌ File tree initialization failed (attempt ${retryCount + 1}):`, error);

        if (retryCount < maxRetries) {
          console.log(`⏳ Retrying in ${retryDelay}ms...`);
          setTimeout(() => {
            initializeFileTree(retryCount + 1);
          }, retryDelay);
        } else {
          console.error("❌ File tree initialization failed after all retries");
          showEmptyFileTreeMessage();
          showOutput("Failed to load file tree. Please refresh the page to try again.");
        }
      }
    }

    async function fetchFileTree() {
      try {
        console.log("🔄 Fetching file tree for project:", projectId);
        console.log("🌐 Using API URL:", window.apiBaseUrl);
        console.log("📊 FastAPI Status:", window.fastApiStatus);

        // Save current expanded state before refresh
        const currentExpanded = saveExpandedState();

        const fileTreeUrl = `${window.apiBaseUrl}/api/files/${window.projectHash}`;
        console.log("📡 Requesting:", fileTreeUrl);

        const response = await fetch(fileTreeUrl);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("File tree fetch error:", response.status, errorText);

          if (response.status === 404) {
            showOutput(`❌ Project not found or API service not available`);
            showOutput(`💡 Make sure Docker services are running: docker-compose up -d`);
          } else if (response.status >= 500) {
            showOutput(`❌ Server error (${response.status}): ${response.statusText}`);
            showOutput(`💡 Check Docker logs: docker-compose logs api`);
          } else {
            showOutput(`❌ Error fetching file tree: ${response.status} ${response.statusText}`);
          }

          showEmptyFileTreeMessage();
          return;
        }

        fileTree = await response.json();
        console.log("📁 File tree data received:", fileTree);

        // Check if fileTree is empty or null
        if (!fileTree || (Array.isArray(fileTree) && fileTree.length === 0)) {
          console.log("⚠️ File tree is empty, showing empty state message");
          showEmptyFileTreeMessage();
          return;
        }

        renderFileTree(fileTree);
        console.log("✅ File tree rendered successfully");

        // Restore expanded state after render
        restoreExpandedState(currentExpanded);

        // Restore selected item if it exists
        if (selectedItemPath) {
          const item = document.querySelector(
            `[data-path="${selectedItemPath}"]`
          );
          if (item) {
            item.classList.add("selected");
          }
        }
      } catch (error) {
        console.error("❌ Error fetching file tree:", error);

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          showOutput(`❌ Cannot connect to API server at ${window.apiBaseUrl}`);
          showOutput(`💡 Make sure Docker services are running: docker-compose up -d`);
          showOutput(`💡 Check if port 8002 is accessible`);
        } else {
          showOutput(`❌ Error: ${error.message}`);
        }

        showEmptyFileTreeMessage();
      }
    }

    function showEmptyFileTreeMessage() {
      const fileTreeEl = document.getElementById("file-tree");
      fileTreeEl.innerHTML = `
        <div class="empty-file-tree">
          <div class="empty-message">
            <i class="fas fa-folder-open" style="font-size: 48px; color: #666; margin-bottom: 16px;"></i>
            <h3 style="color: #cccccc; margin: 0 0 8px 0; font-size: 16px;">No files yet</h3>
            <p style="color: #858585; margin: 0; font-size: 13px;">Create your first file or folder to get started</p>
            <div style="margin-top: 16px;">
              <button onclick="document.getElementById('new-file-btn').click()" class="btn-sm btn-primary" style="margin-right: 8px;">
                <i class="fas fa-plus"></i> New File
              </button>
              <button onclick="document.getElementById('new-folder-btn').click()" class="btn-sm btn-primary">
                <i class="fas fa-plus"></i> New Folder
              </button>
            </div>
          </div>
        </div>
      `;
    }

    function saveExpandedState() {
      const state = {
        expanded: [],
        selected: selectedItemPath,
      };

      // Collect all expanded folders by their data-path attribute
      document.querySelectorAll(".folder-item.expanded").forEach((folder) => {
        if (folder.dataset.path) {
          state.expanded.push(folder.dataset.path);

          // Also update our global expandedFolders set to ensure persistence across refreshes
          expandedFolders.add(folder.dataset.path);
        }
      });

      // Log the expanded folders for debugging
      console.log(`Saved expanded state: ${state.expanded.length} folders`);

      return state;
    }

    function restoreExpandedState(state) {
      if (!state) return;

      // Combine currently saved state with our persistent expandedFolders set
      const pathsToExpand = new Set([...expandedFolders]);

      // Also add paths from the current state object
      if (state.expanded && state.expanded.length > 0) {
        state.expanded.forEach((path) => pathsToExpand.add(path));
      }

      // Now restore all expanded folders
      pathsToExpand.forEach((path) => {
        const folder = document.querySelector(
          `.folder-item[data-path="${path}"]`
        );
        if (folder) {
          // Add expanded class to folder and its children container
          folder.classList.add("expanded");
          const childrenContainer = folder.querySelector(".folder-children");
          if (childrenContainer) {
            childrenContainer.classList.add("expanded");
          }

          // Ensure toggle icon rotation is applied with transition
          const toggleIcon = folder.querySelector(".toggle-icon");
          if (toggleIcon) {
            toggleIcon.style.transform = "rotate(90deg)";
          }
        }
      });

      if (state.selected) {
        selectedItemPath = state.selected;
      }

      // Log the expanded folders for debugging
      console.log(`Restored expanded state: ${pathsToExpand.size} folders`);
    }

    function renderFileTree(
      data,
      parentEl = document.getElementById("file-tree"),
      depth = 0
    ) {
      if (depth === 0) {
        console.log("🎨 Rendering file tree at root level, data:", data);
        parentEl.innerHTML = "";
        // Make file tree focusable for keyboard navigation
        parentEl.setAttribute('tabindex', '0');

        // Check if data is valid
        if (!data || !Array.isArray(data)) {
          console.error("❌ Invalid file tree data:", data);
          showEmptyFileTreeMessage();
          return;
        }

        if (data.length === 0) {
          console.log("📂 File tree is empty");
          showEmptyFileTreeMessage();
          return;
        }
      }

      data.forEach((item) => {
        const itemEl = document.createElement("div");
        itemEl.className = `${item.type}-item ${
          item.type === "directory" && item.children && item.children.length > 0
            ? "has-children"
            : ""
        }`;
        itemEl.dataset.path = item.path;
        itemEl.dataset.type = item.type;
        itemEl.dataset.depth = depth;
        itemEl.setAttribute('tabindex', '-1');

        if (item.type === "file") {
          const extension = item.name.split(".").pop().toLowerCase();
          itemEl.dataset.ext = extension;
        }

        // Add toggle icon for folders with children
        if (
          item.type === "directory" &&
          item.children &&
          item.children.length > 0
        ) {
          const toggleIcon = document.createElement("i");
          toggleIcon.className = "fas fa-caret-right toggle-icon";
          itemEl.appendChild(toggleIcon);
        } else if (item.type === "directory") {
          // Add empty space for folders without children to maintain alignment
          const spacer = document.createElement("span");
          spacer.className = "toggle-spacer";
          spacer.style.width = "16px";
          spacer.style.height = "16px";
          spacer.style.display = "inline-block";
          spacer.style.marginRight = "2px";
          itemEl.appendChild(spacer);
        }

        const iconEl = document.createElement("i");

        // Set appropriate icon with better VS Code-like icons
        if (item.type === "directory") {
          iconEl.className = itemEl.classList.contains("expanded")
            ? "fas fa-folder-open"
            : "fas fa-folder";
          iconEl.style.color = "#dcb67a";
        } else {
          const extension = item.name.split(".").pop().toLowerCase();
          const iconMap = {
            py: "fab fa-python",
            js: "fab fa-js",
            jsx: "fab fa-react",
            ts: "fab fa-js-square",
            html: "fab fa-html5",
            css: "fab fa-css3-alt",
            json: "fas fa-code",
            md: "fas fa-file-alt",
            txt: "fas fa-file-alt",
            jpg: "fas fa-file-image",
            jpeg: "fas fa-file-image",
            png: "fas fa-file-image",
            gif: "fas fa-file-image",
            svg: "fas fa-file-image",
            pdf: "fas fa-file-pdf",
            docx: "fas fa-file-word",
            xlsx: "fas fa-file-excel",
            pptx: "fas fa-file-powerpoint",
            zip: "fas fa-file-archive",
            rar: "fas fa-file-archive",
            "7z": "fas fa-file-archive",
            mp3: "fas fa-file-audio",
            mp4: "fas fa-file-video",
            java: "fab fa-java",
            php: "fab fa-php",
            c: "fas fa-file-code",
            cpp: "fas fa-file-code",
            h: "fas fa-file-code",
            cs: "fas fa-file-code",
            rb: "fas fa-gem",
            go: "fas fa-file-code",
            sh: "fas fa-file-code",
          };
          iconEl.className = iconMap[extension] || "fas fa-file-code";
        }

        const nameEl = document.createElement("span");
        nameEl.className = item.type === "directory" ? "folder-name" : "file-name";
        nameEl.textContent = item.name;

        itemEl.append(iconEl, nameEl);
        parentEl.appendChild(itemEl);

        if (item.type === "directory" && item.children) {
          const childrenEl = document.createElement("div");
          childrenEl.className = "folder-children";
          itemEl.appendChild(childrenEl);
          renderFileTree(item.children, childrenEl, depth + 1);

          itemEl.addEventListener("click", (e) => {
            // Handle folder expansion/collapse
            if (e.target.classList.contains("toggle-icon") ||
                e.target === itemEl ||
                e.target === iconEl ||
                e.target === nameEl) {

              toggleFolder(itemEl, childrenEl, item.path, iconEl);
              e.stopPropagation();
            }

            // Set selected item
            setSelectedItem(itemEl, item.path);
          });

          // Double-click to toggle folder
          itemEl.addEventListener("dblclick", (e) => {
            toggleFolder(itemEl, childrenEl, item.path, iconEl);
            e.stopPropagation();
          });
        } else if (item.type === "file") {
          itemEl.addEventListener("click", () => {
            openFile(item.path);
            setSelectedItem(itemEl, item.path);
          });

          // Double-click to open file (VS Code behavior)
          itemEl.addEventListener("dblclick", () => {
            openFile(item.path);
          });
        }

        itemEl.addEventListener("contextmenu", (e) => {
          e.preventDefault();
          setSelectedItem(itemEl, item.path);
          showContextMenu(e, item);
        });
      });

      if (depth === 0) {
        enableDragAndDrop();
        setupKeyboardNavigation();
      }
    }

    function toggleFolder(itemEl, childrenEl, path, iconEl) {
      // Toggle expanded state
      itemEl.classList.toggle("expanded");
      childrenEl.classList.toggle("expanded");

      // Update expanded folders set
      if (itemEl.classList.contains("expanded")) {
        expandedFolders.add(path);
        iconEl.className = "fas fa-folder-open";
      } else {
        expandedFolders.delete(path);
        iconEl.className = "fas fa-folder";
      }

      // Rotate toggle icon with animation
      const toggleIcon = itemEl.querySelector(".toggle-icon");
      if (toggleIcon) {
        toggleIcon.style.transform = itemEl.classList.contains("expanded")
          ? "rotate(90deg)"
          : "";
      }
    }

    function setSelectedItem(itemEl, path) {
      // Remove selection from all items
      document.querySelectorAll(".file-item, .folder-item").forEach((el) => {
        el.classList.remove("selected", "focused");
      });

      // Add selection to clicked item
      itemEl.classList.add("selected");
      selectedItemPath = path;
    }

    function setupKeyboardNavigation() {
      const fileTree = document.getElementById("file-tree");

      fileTree.addEventListener("keydown", (e) => {
        const selected = fileTree.querySelector(".selected");
        const allItems = Array.from(fileTree.querySelectorAll(".file-item, .folder-item"));

        if (!selected && allItems.length > 0) {
          // If nothing is selected, select the first item
          setSelectedItem(allItems[0], allItems[0].dataset.path);
          return;
        }

        const currentIndex = allItems.indexOf(selected);

        switch (e.key) {
          case "ArrowDown":
            e.preventDefault();
            if (currentIndex < allItems.length - 1) {
              const nextItem = allItems[currentIndex + 1];
              setSelectedItem(nextItem, nextItem.dataset.path);
              nextItem.scrollIntoView({ block: "nearest" });
            }
            break;

          case "ArrowUp":
            e.preventDefault();
            if (currentIndex > 0) {
              const prevItem = allItems[currentIndex - 1];
              setSelectedItem(prevItem, prevItem.dataset.path);
              prevItem.scrollIntoView({ block: "nearest" });
            }
            break;

          case "ArrowRight":
            e.preventDefault();
            if (selected.classList.contains("folder-item")) {
              if (!selected.classList.contains("expanded")) {
                // Expand folder
                const childrenEl = selected.querySelector(".folder-children");
                const iconEl = selected.querySelector("i:not(.toggle-icon)");
                if (childrenEl) {
                  toggleFolder(selected, childrenEl, selected.dataset.path, iconEl);
                }
              } else {
                // Move to first child
                const firstChild = selected.querySelector(".folder-children > .file-item, .folder-children > .folder-item");
                if (firstChild) {
                  setSelectedItem(firstChild, firstChild.dataset.path);
                  firstChild.scrollIntoView({ block: "nearest" });
                }
              }
            }
            break;

          case "ArrowLeft":
            e.preventDefault();
            if (selected.classList.contains("folder-item") && selected.classList.contains("expanded")) {
              // Collapse folder
              const childrenEl = selected.querySelector(".folder-children");
              const iconEl = selected.querySelector("i:not(.toggle-icon)");
              if (childrenEl) {
                toggleFolder(selected, childrenEl, selected.dataset.path, iconEl);
              }
            } else {
              // Move to parent folder
              const parent = selected.closest(".folder-children")?.parentElement;
              if (parent && parent.classList.contains("folder-item")) {
                setSelectedItem(parent, parent.dataset.path);
                parent.scrollIntoView({ block: "nearest" });
              }
            }
            break;

          case "Enter":
            e.preventDefault();
            if (selected.classList.contains("folder-item")) {
              // Toggle folder expansion
              const childrenEl = selected.querySelector(".folder-children");
              const iconEl = selected.querySelector("i:not(.toggle-icon)");
              if (childrenEl) {
                toggleFolder(selected, childrenEl, selected.dataset.path, iconEl);
              }
            } else if (selected.classList.contains("file-item")) {
              // Open file
              openFile(selected.dataset.path);
            }
            break;

          case "Delete":
            e.preventDefault();
            if (selected) {
              deleteItem(selected.dataset.path, selected.dataset.type);
            }
            break;

          case "F2":
            e.preventDefault();
            if (selected) {
              showModal("rename", selected.dataset.path.split("/").pop(), selected.dataset.path, selected.dataset.type);
            }
            break;
        }
      });

      // Focus file tree when clicked
      fileTree.addEventListener("click", () => {
        fileTree.focus();
      });
    }

    // Patch openFile to save before switching (but not during collaboration startup)
    let isSwitchingFile = false;
    const originalOpenFile = openFile;
    openFile = async function(path) {
      if (currentFile && !isSwitchingFile && !isFileSwitching && !pendingCollaborationCleanup) {
        isSwitchingFile = true;
        console.log(`💾 AUTO-SAVE: Saving current file before switch: ${currentFile}`);
        await saveCurrentFile(true);
        isSwitchingFile = false;
      } else if (isFileSwitching || pendingCollaborationCleanup) {
        console.log(`🚫 AUTO-SAVE: Skipping auto-save during collaboration operations`);
      }
      return originalOpenFile(path);
    };

    async function openFile(path) {
      try {
        showOutput(`Opening file: ${path}...`);

        const response = await fetch(
          `${window.apiBaseUrl}/api/file/${window.projectHash}?path=${encodeURIComponent(path)}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error("Error opening file:", response.status, errorText);
          throw new Error(
            `Could not open file: ${response.status} ${response.statusText}`
          );
        }

        const content = await response.text();

        // BULLETPROOF file switching with complete isolation
        await handleFileSwitching(path);

        // Update current file reference AFTER cleanup
        currentFile = path;

        const extension = path.split(".").pop().toLowerCase();
        const languageMap = {
          py: "python",
          js: "javascript",
          jsx: "javascript",
          ts: "typescript",
          html: "html",
          css: "css",
          java: "java",
          cs: "csharp",
          go: "go",
          rb: "ruby",
          json: "json",
          md: "markdown",
          txt: "plaintext",
        };

        const language = languageMap[extension] || "plaintext";

        // Set editor content in the new model
        isSettingEditorContent = true;
        const currentModel = editor.getModel();
        if (currentModel) {
          currentModel.setValue(content);
          // Set language
          monaco.editor.setModelLanguage(currentModel, language);
        } else {
          // Fallback to editor.setValue if no model
          editor.setValue(content);
          monaco.editor.setModelLanguage(editor.getModel(), language);
        }
        isSettingEditorContent = false;

        document.getElementById("language-selector").value = language;

        console.log(`✅ File content loaded into Monaco model for: ${path}`);

        // Force syntax highlighting after opening file
        setTimeout(() => {
          forceSyntaxHighlighting();
        }, 100);

        // Check if collaboration should be started for this file
        // Wait longer to ensure file content is fully loaded before starting collaboration
        if (collaborationEnabled) {
          console.log(`🤝 COLLAB: Scheduling collaboration check for: ${path}`);
          setTimeout(() => {
            console.log(`🤝 COLLAB: Starting collaboration check for: ${path}`);
            checkAndStartCollaboration(path);
          }, 1500); // Increased delay to ensure file content is stable and prevent refresh conflicts
        }

        addFileTab(path);
        showOutput(`File opened: ${path}`);
      } catch (error) {
        console.error("Failed to open file:", error);
        showOutput(`Error: ${error.message}`);
      }
    }

    function addFileTab(filePath) {
      // Remove existing tab if it exists
      const existingTab = document.querySelector(
        `.file-tab[data-path="${filePath}"]`
      );
      if (existingTab) {
        existingTab.remove();
      }

      const tab = document.createElement("div");
      tab.className = "file-tab";
      tab.dataset.path = filePath;
      tab.textContent = filePath.split("/").pop();
      tab.addEventListener("click", () => openFile(filePath));

      const closeBtn = document.createElement("span");
      closeBtn.textContent = "×";
      closeBtn.style.marginLeft = "10px";
      closeBtn.style.cursor = "pointer";
      closeBtn.addEventListener("click", async (e) => {
        e.stopPropagation();

        // Save the file before closing if it's the current file
        if (currentFile === filePath && editor) {
          await autoSaveCurrentFile();
        }

        // Clean up collaboration if this is the current collaboration file
        if (currentCollaborationFile === filePath) {
          cleanupCurrentCollaboration();
        }

        tab.remove();

        // If this was the current file, clear the current file reference
        if (currentFile === filePath) {
          currentFile = null;
          editor.setValue("# Create or open a file to start working\n");
        }
      });

      tab.appendChild(closeBtn);
      document.getElementById("file-tabs").prepend(tab);
    }

    async function saveCurrentFile(silent = false) {
      if (!currentFile) {
        if (!silent) showOutput("Error: No file is currently open");
        return;
      }

      try {
        const response = await fetch(`${window.apiBaseUrl}/api/file/${window.projectHash}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            path: currentFile,
            content: editor.getValue(),
          }),
        });

        if (!silent) {
          showOutput(
            response.ok ? "File saved successfully" : "Error: Could not save file"
          );
        }
      } catch (error) {
        if (!silent) showOutput(`Error: ${error.message}`);
      }
    }

    async function createFile(name, isFolder = false, parentDirectory = null) {
      try {
        // Get parent directory from current selection or context menu
        let parentPath =
          parentDirectory ||
          (currentFile
            ? currentFile.substring(0, currentFile.lastIndexOf("/"))
            : "");

        console.log(
          `Creating ${
            isFolder ? "folder" : "file"
          } '${name}' in parent path: ${parentPath}`
        );

        // Construct the full path
        const path = parentPath ? `${parentPath}/${name}` : name;

        showOutput(`Creating ${isFolder ? "folder" : "file"}: ${path}...`);

        const response = await fetch(`${window.apiBaseUrl}/api/file/${window.projectHash}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            path: path,
            isDirectory: isFolder,
            content: isFolder ? null : "",
          }),
        });

        // Log the response status for debugging
        console.log(`Response status: ${response.status}`);

        // Handle error responses
        if (!response.ok) {
          // Try to get error details from response
          const errorText = await response.text();
          console.error("API Error Response:", errorText);

          let errorMessage;
          try {
            // Try to parse JSON error response
            const errorData = JSON.parse(errorText);
            errorMessage = errorData.detail || `HTTP error ${response.status}`;
          } catch {
            // If not JSON, use the raw text
            errorMessage = errorText || `HTTP error ${response.status}`;
          }

          throw new Error(errorMessage);
        }

        console.log(
          `Successfully created ${isFolder ? "folder" : "file"}: ${path}`
        );

        // Refresh the file tree
        await refreshFileTreePreservingState();

        // If it's a file, open it in the editor
        if (!isFolder) {
          await openFile(path);
        }

        showOutput(
          `${isFolder ? "Folder" : "File"} created successfully: ${path}`
        );
        return true;
      } catch (error) {
        console.error("Error creating file:", error);
        showOutput(
          `Error creating ${isFolder ? "folder" : "file"}: ${error.message}`
        );
        return false;
      }
    }

    async function deleteItem(path, type) {
      if (!confirm(`Are you sure you want to delete this ${type}?`)) return;

      try {
        console.log(`Deleting ${type}: ${path}`);
        showOutput(`Deleting ${type}: ${path}...`);

        const response = await fetch(
          `${apiBaseUrl}/api/file/${projectHash}?path=${encodeURIComponent(path)}`,
          {
            method: "DELETE",
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Delete error:`, response.status, errorText);

          let errorMessage;
          try {
            const errorData = JSON.parse(errorText);
            errorMessage =
              errorData.detail || `Failed to delete (${response.status})`;
          } catch {
            errorMessage = errorText || `HTTP error ${response.status}`;
          }

          throw new Error(errorMessage);
        }

        console.log(`Successfully deleted ${type}: ${path}`);

        // Refresh the file tree with state preservation
        await refreshFileTreePreservingState();

        // Clean up collaboration for the deleted file
        if (collaborationEnabled) {
          cleanupFileCollaboration(path);
        }

        // If we deleted the current open file, clear the editor
        if (currentFile === path) {
          currentFile = null;
          editor.setValue("");
          // Also remove the tab if it exists
          const tab = document.querySelector(`.file-tab[data-path="${path}"]`);
          if (tab) tab.remove();
        }

        showOutput(`${type} deleted successfully: ${path}`);
      } catch (error) {
        console.error(`Error deleting ${type}:`, error);
        showOutput(`Error: ${error.message}`);
      }
    }

    async function renameItem(path, newName, type) {
      try {
        console.log(`Renaming ${type}: ${path} to ${newName}`);
        showOutput(`Renaming ${type}: ${path}...`);

        const dirPath = path.substring(0, path.lastIndexOf("/"));
        const newPath = dirPath ? `${dirPath}/${newName}` : newName;

        console.log(`Old path: ${path}, New path: ${newPath}`);

        const response = await fetch(
          `${apiBaseUrl}/api/file/${projectHash}/rename`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              oldPath: path,
              newPath: newPath,
            }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Rename error:`, response.status, errorText);

          let errorMessage;
          try {
            const errorData = JSON.parse(errorText);
            errorMessage =
              errorData.detail || `Failed to rename (${response.status})`;
          } catch {
            errorMessage = errorText || `HTTP error ${response.status}`;
          }

          throw new Error(errorMessage);
        }

        console.log(`Successfully renamed ${type} to: ${newName}`);

        // Refresh the file tree with state preservation
        await refreshFileTreePreservingState();

        // Handle collaboration for renamed files
        if (collaborationEnabled && type === "file") {
          // Clean up collaboration for the old path
          cleanupFileCollaboration(path);

          // If this is the current file, initialize collaboration for the new path
          if (currentFile === path) {
            switchFileCollaboration(newPath);
          }
        }

        // If the current file was renamed, update the reference
        if (currentFile === path) {
          currentFile = newPath;

          // Update the tab if it exists
          const tab = document.querySelector(`.file-tab[data-path="${path}"]`);
          if (tab) {
            tab.dataset.path = newPath;
            tab.textContent = newName;

            // Recreate the close button
            const closeBtn = document.createElement("span");
            closeBtn.textContent = "×";
            closeBtn.style.marginLeft = "10px";
            closeBtn.style.cursor = "pointer";
            closeBtn.addEventListener("click", (e) => {
              e.stopPropagation();

              // Clean up collaboration for this file when tab is closed
              if (collaborationEnabled) {
                cleanupFileCollaboration(newPath);
              }

              tab.remove();

              // If this was the current file, clear the current file reference
              if (currentFile === newPath) {
                currentFile = null;
                editor.setValue("# Create or open a file to start working\n");
              }
            });

            tab.appendChild(closeBtn);
          }
        }

        showOutput(`${type} renamed successfully to: ${newName}`);
        return true;
      } catch (error) {
        console.error(`Error renaming ${type}:`, error);
        showOutput(`Error: ${error.message}`);
        return false;
      }
    }

    async function duplicateItem(path, type) {
      try {
        console.log(`Duplicating ${type}: ${path}`);
        showOutput(`Duplicating ${type}: ${path}...`);

        const response = await fetch(
          `${apiBaseUrl}/api/file/${projectHash}/duplicate`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              path: path,
              type: type,
            }),
          }
        );

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Duplication error:`, response.status, errorText);

          let errorMessage;
          try {
            const errorData = JSON.parse(errorText);
            errorMessage =
              errorData.detail || `Failed to duplicate (${response.status})`;
          } catch {
            errorMessage = errorText || `HTTP error ${response.status}`;
          }

          throw new Error(errorMessage);
        }

        // Get the path of the new duplicate
        const result = await response.json();
        const newPath = result.new_path;

        console.log(`Successfully duplicated ${type} to: ${newPath}`);

        // Refresh the file tree with state preservation
        await refreshFileTreePreservingState();

        // If it's a file, open it in the editor
        if (type === "file") {
          await openFile(newPath);
        }

        showOutput(
          `${
            type === "file" ? "File" : "Folder"
          } duplicated successfully to: ${newPath}`
        );
      } catch (error) {
        console.error(`Error duplicating ${type}:`, error);
        showOutput(`Error: ${error.message}`);
      }
    }

    async function downloadFile(path) {
      try {
        const response = await fetch(
          `${apiBaseUrl}/api/file/${projectHash}?path=${encodeURIComponent(path)}`
        );
        if (!response.ok) throw new Error("Could not download file");

        const content = await response.text();
        const blob = new Blob([content], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = path.split("/").pop();
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      } catch (error) {
        showOutput(`Error: ${error.message}`);
      }
    }

    function downloadCurrentFile() {
      if (currentFile) downloadFile(currentFile);
      else showOutput("No file is currently open");
    }

    async function runCode() {
      if (!currentFile) {
        showOutput("Error: No file is currently open");
        return;
      }

      try {
        // First save the file
        await saveCurrentFile(true);

        // Get the current language from the selector
        const language = document.getElementById("language-selector").value;
        const fileExtension = currentFile.split(".").pop().toLowerCase();

        // If this is an HTML file, open it in the preview instead
        if (fileExtension === "html") {
          showOutput("Opening HTML file in preview...");
          openPreviewTab(currentFile);
          return;
        }

        // If this is a Python file, run it in the terminal container
        if (fileExtension === "py") {
          showOutput("Running Python file in terminal...");
          console.log(`Running Python file: ${currentFile}`);

          // Check if terminal is available and connected
          if (window.forgeXTerminal && window.forgeXTerminal.terminals.size > 0) {
            // Switch to terminal panel to show execution
            showPanel('terminal');

            // Execute the Python file in the terminal
            const command = `python "${currentFile}"`;
            const success = window.forgeXTerminal.executeCommand(command);

            if (success) {
              showOutput(`Executing: ${command}`);
            } else {
              showOutput("Failed to execute command in terminal.");
            }
            return;
          }

          // Fallback: If terminal is not available, show message
          showOutput("Terminal not available. Please ensure the terminal is connected.");
          showOutput("Switching to terminal panel...");
          showPanel('terminal');
          return;
        }

        // For other file types, show appropriate message
        if (fileExtension !== "html" && fileExtension !== "py") {
          showOutput(`File type .${fileExtension} is not supported for execution.`);
          showOutput("Supported file types: .py (Python), .html (Preview)");
          return;
        }

        showOutput("Running code...");
        console.log(`Running file: ${currentFile} with language: ${language}`);

        // Send the request to the FastAPI service using project hash
        const response = await fetch(`${window.apiBaseUrl}/api/run/${window.projectHash}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            file_path: currentFile,
            language: language,
          }),
        });

        // Check if the response is ok
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const errorMessage = errorData.error || `HTTP error ${response.status}`;
          throw new Error(errorMessage);
        }

        // Process the response
        const result = await response.json();
        console.log("Code execution result:", result);

        // Show the result
        if (result.output) {
          showOutput(`Output:\n${result.output}`);
        } else if (result.error) {
          showOutput(`Error:\n${result.error}`);
        } else {
          showOutput("Code executed successfully but produced no output.");
        }
      } catch (error) {
        console.error("Code execution error:", error);
        showOutput(`Error: ${error.message}`);
      }
    }

    // Enhanced showOutput function with clickable error links
    function classifyOutputLine(line) {
      if (/error|traceback|exception/i.test(line)) return "error";
      if (/warning|deprecated/i.test(line)) return "warning";
      if (/successfully|done|completed/i.test(line)) return "success";
      return "info";
    }

    function showOutput(output) {
      const outputContent = document.getElementById("output-content");
      const lines = output.split("\n");
      const fileLineRegexes = [
        /File \"(.*?)\", line (\d+)/i,
        /at (\S+):(\d+):?(\d+)?/,
        /(.*?):(\d+):?(\d+)?/,
      ];

      lines.forEach((line) => {
        const div = document.createElement("div");
        const type = classifyOutputLine(line);
        div.className = `output-line ${type}`;

        let matched = false;
        fileLineRegexes.forEach((regex) => {
          const match = line.match(regex);
          if (match) {
            matched = true;
            const file = match[1];
            const lineNumber = match[2];
            const clickablePart = `<a class="clickable-error" data-file="${file}" data-line="${lineNumber}">${file}:${lineNumber}</a>`;
            div.innerHTML = line.replace(regex, clickablePart);
          }
        });

        if (!matched) {
          div.innerText = line;
        }

        outputContent.appendChild(div);
      });
      outputContent.scrollTop = outputContent.scrollHeight;
    }

    // Click handler for clickable error links
    document.addEventListener("click", function (e) {
      if (e.target.classList.contains("clickable-error")) {
        const file = e.target.dataset.file;
        const line = parseInt(e.target.dataset.line, 10);
        openFileAndGoToLine(file, line);
      }
    });

    // Function to open a file and navigate to a specific line
    function openFileAndGoToLine(file, line) {
      openFile(file).then(() => {
        editor.revealLineInCenter(line);
        editor.setPosition({ lineNumber: line, column: 1 });
        editor.focus();
      });
    }



    // Context menu for output panel
    const outputPanel = document.getElementById("output-panel");
    const contextMenu = document.createElement("div");
    contextMenu.id = "output-context-menu";
    contextMenu.className = "context-menu";
    contextMenu.innerHTML = `
        <button onclick="copySelectedOutput()">Copy Line</button>
        <button onclick="copyAllOutput()">Copy All</button>
      `;
    document.body.appendChild(contextMenu);

    outputPanel.addEventListener("contextmenu", function (e) {
      e.preventDefault();
      contextMenu.style.top = e.pageY + "px";
      contextMenu.style.left = e.pageX + "px";
      contextMenu.style.display = "block";
    });

    document.addEventListener("click", () => {
      contextMenu.style.display = "none";
    });

    function copySelectedOutput() {
      const selection = window.getSelection().toString();
      navigator.clipboard.writeText(selection);
    }

    function copyAllOutput() {
      const outputContent = document.getElementById("output-content");
      const outputText = Array.from(outputContent.children)
        .map((div) => div.innerText)
        .join("\n");
      navigator.clipboard.writeText(outputText);
    }

    function clearOutput() {
      document.getElementById("output-content").innerHTML = "";
    }

















    function showPanel(panelId) {
      console.log(`🔄 Switching to panel: ${panelId}`);

      document
        .querySelectorAll(".panel-content")
        .forEach((p) => p.classList.remove("active"));
      document
        .querySelectorAll(".panel-tab")
        .forEach((t) => t.classList.remove("active"));

      const panel = document.getElementById(`${panelId}-panel`);
      const tab = document.getElementById(`${panelId}-tab`);

      if (panel && tab) {
        panel.classList.add("active");
        tab.classList.add("active");
        console.log(`✅ Panel ${panelId} activated successfully`);
      } else {
        console.error(`❌ Panel or tab not found: ${panelId}-panel, ${panelId}-tab`);
      }
    }

    function setupContextMenu() {
      const contextMenu = document.getElementById("context-menu");

      document.getElementById("rename-item").addEventListener("click", () => {
        const { path, type } = contextMenu.dataset;
        if (path) showModal("rename", path.split("/").pop(), path, type);
        contextMenu.style.display = "none";
      });

      document.getElementById("duplicate-item").addEventListener("click", () => {
        const { path, type } = contextMenu.dataset;
        if (path) duplicateItem(path, type);
        contextMenu.style.display = "none";
      });

      document.getElementById("delete-item").addEventListener("click", () => {
        const { path, type } = contextMenu.dataset;
        if (path) deleteItem(path, type);
        contextMenu.style.display = "none";
      });

      document.getElementById("download-item").addEventListener("click", () => {
        const { path, type } = contextMenu.dataset;
        if (path && type === "file") downloadFile(path);
        contextMenu.style.display = "none";
      });

      document.getElementById("new-file-item").addEventListener("click", () => {
        const path = document.getElementById("context-menu").dataset.path;
        const type = document.getElementById("context-menu").dataset.type;

        // Only allow creating files inside directories
        if (type === "directory") {
          showModal("file", "", path, "directory");
        } else {
          // If clicking on a file, use its parent directory
          const parentPath = path.substring(0, path.lastIndexOf("/"));
          showModal("file", "", parentPath, "directory");
        }

        contextMenu.style.display = "none";
      });

      document.getElementById("new-folder-item").addEventListener("click", () => {
        const path = document.getElementById("context-menu").dataset.path;
        const type = document.getElementById("context-menu").dataset.type;

        // Only allow creating folders inside directories
        if (type === "directory") {
          showModal("folder", "", path, "directory");
        } else {
          // If clicking on a file, use its parent directory
          const parentPath = path.substring(0, path.lastIndexOf("/"));
          showModal("folder", "", parentPath, "directory");
        }

        contextMenu.style.display = "none";
      });

      document.getElementById("upload-files-item").addEventListener("click", () => {
        const path = document.getElementById("context-menu").dataset.path;
        const type = document.getElementById("context-menu").dataset.type;

        // Create a temporary file input for this specific folder
        const tempInput = document.createElement("input");
        tempInput.type = "file";
        tempInput.multiple = true;
        tempInput.style.display = "none";

        tempInput.addEventListener("change", (event) => {
          const files = event.target.files;
          if (files && files.length > 0) {
            // Upload to the selected folder (or root if it's a file's parent)
            const targetPath = type === "directory" ? path : path.substring(0, path.lastIndexOf("/"));
            handleExternalFileDrop(files, targetPath);
          }
          // Remove the temporary input
          document.body.removeChild(tempInput);
        });

        document.body.appendChild(tempInput);
        tempInput.click();
        contextMenu.style.display = "none";
      });

      document.getElementById("copy-path-item").addEventListener("click", () => {
        const path = document.getElementById("context-menu").dataset.path;
        navigator.clipboard.writeText(path);
        showOutput(`Copied path: ${path}`);
      });

      document
        .getElementById("paste-item")
        .addEventListener("click", async () => {
          const targetPath = document.getElementById("context-menu").dataset.path;
          const sourcePath = await navigator.clipboard.readText();

          try {
            const response = await fetch(
              `${apiBaseUrl}/api/file/${projectHash}/move`,
              {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ source: sourcePath, target: targetPath }),
              }
            );

            if (response.ok) {
              fetchFileTree();
              showOutput(`Pasted ${sourcePath} to ${targetPath}`);
            } else {
              showOutput("Error: Could not paste file or folder");
            }
          } catch (error) {
            showOutput(`Error: ${error.message}`);
          }
        });
    }

    function showContextMenu(event, item) {
      const contextMenu = document.getElementById("context-menu");

      // Position the context menu near the cursor with bounds checking
      let posX = event.pageX;
      let posY = event.pageY;

      // Calculate menu dimensions
      const menuWidth = 180; // Minimum width from CSS
      const menuHeight = 220; // Approximate height

      // Check if menu would go off-screen and adjust if needed
      if (posX + menuWidth > window.innerWidth) {
        posX = window.innerWidth - menuWidth - 10;
      }

      if (posY + menuHeight > window.innerHeight) {
        posY = window.innerHeight - menuHeight - 10;
      }

      // Set position and show menu
      contextMenu.style.display = "block";
      contextMenu.style.left = `${posX}px`;
      contextMenu.style.top = `${posY}px`;

      // Store item info in dataset for handlers
      contextMenu.dataset.path = item.path;
      contextMenu.dataset.type = item.type;
      contextMenu.dataset.name = item.name;

      // Show/hide options based on item type
      document.getElementById("download-item").style.display =
        item.type === "file" ? "block" : "none";

      // Adjust other options based on context
      if (item.type === "directory") {
        // When right-clicking on a folder, make "New File" and "New Folder" more prominent
        document.getElementById("new-file-item").style.order = "1";
        document.getElementById("new-folder-item").style.order = "2";
      } else {
        // Reset order when right-clicking on a file
        document.getElementById("new-file-item").style.order = "";
        document.getElementById("new-folder-item").style.order = "";
      }

      // Add visual feedback for the selected item
      const allItems = document.querySelectorAll(".file-item, .folder-item");
      allItems.forEach((el) => {
        if (el.dataset.path === item.path) {
          el.classList.add("selected");
        }
      });
    }

    function setupModal() {
      const modal = document.getElementById("file-modal");
      const closeBtn = document.querySelector(".close-modal");
      const cancelBtn = document.getElementById("cancel-modal-btn");

      closeBtn.addEventListener("click", () => (modal.style.display = "none"));
      cancelBtn.addEventListener("click", () => (modal.style.display = "none"));

      document
        .getElementById("confirm-modal-btn")
        .addEventListener("click", async function () {
          // Get values from the modal
          const modal = document.getElementById("file-modal");
          const action = modal.dataset.action;
          const name = document.getElementById("file-name-input").value.trim();
          const parentPath = modal.dataset.path || "";

          if (!name) return;

          console.log(
            `Modal confirm clicked: action=${action}, name=${name}, parentPath=${parentPath}`
          );

          // Close the modal first
          modal.style.display = "none";
          document.getElementById("file-name-input").value = "";

          // Handle the action based on the type
          if (action === "file" || action === "folder") {
            const isFolder = action === "folder";
            await createFile(name, isFolder, parentPath);
          } else if (action === "rename") {
            const oldPath = modal.dataset.path;
            const type = modal.dataset.type;

            if (!oldPath) {
              showOutput("Error: Missing path information for rename operation");
              return;
            }

            await renameItem(oldPath, name, type);
          }
        });
    }

    function showModal(action, defaultValue = "", path = "", type = "") {
      const modal = document.getElementById("file-modal");
      const input = document.getElementById("file-name-input");
      const title = document.getElementById("modal-title");
      const confirmBtn = document.getElementById("confirm-modal-btn");

      modal.dataset.action = action;
      modal.dataset.path = path;
      modal.dataset.type = type;

      // Log the modal parameters for debugging
      console.log(`Showing modal: action=${action}, path=${path}, type=${type}`);

      if (action === "file") {
        title.textContent = "Create New File";
        confirmBtn.textContent = "Create";
        input.placeholder = "Enter file name";
        // Show the parent path in title if available
        if (path) {
          title.textContent += ` in ${path}`;
        }
      } else if (action === "folder") {
        title.textContent = "Create New Folder";
        confirmBtn.textContent = "Create";
        input.placeholder = "Enter folder name";
        // Show the parent path in title if available
        if (path) {
          title.textContent += ` in ${path}`;
        }
      } else if (action === "rename") {
        title.textContent = `Rename ${type === "directory" ? "Folder" : "File"}`;
        confirmBtn.textContent = "Rename";
        input.placeholder = `Enter new ${
          type === "directory" ? "folder" : "file"
        } name`;
      }

      // Clear and focus the input
      input.value = defaultValue;
      modal.style.display = "block";

      // Add a slight delay before focusing to ensure modal is displayed
      setTimeout(() => input.focus(), 100);

      // Add event listener for Enter key
      const handleEnterKey = function (e) {
        if (e.key === "Enter") {
          document.getElementById("confirm-modal-btn").click();
          input.removeEventListener("keydown", handleEnterKey);
        }
      };

      input.addEventListener("keydown", handleEnterKey);
    }

    // Add functionality for file search
    document
      .getElementById("file-search")
      .addEventListener("input", function (e) {
        const query = e.target.value.toLowerCase();
        document.querySelectorAll(".file-item, .folder-item").forEach((item) => {
          const name = item.querySelector("span").textContent.toLowerCase();
          item.style.display = name.includes(query) ? "flex" : "none";
        });
      });

    // Setup global event listeners for file drag-and-drop
    document.addEventListener("DOMContentLoaded", function () {
      // Setup file upload progress UI container
      uploadProgressContainer = document.createElement("div");
      uploadProgressContainer.className = "upload-progress";
      uploadProgressContainer.style.display = "none";
      uploadProgressContainer.innerHTML = `
          <h3><i class="fas fa-upload"></i> Uploading Files</h3>
          <div class="upload-progress-bar">
            <div class="upload-progress-bar-inner"></div>
          </div>
          <div class="upload-list"></div>
        `;
      document.body.appendChild(uploadProgressContainer);

      // Get reference to the progress bar inner element
      uploadProgressBar = uploadProgressContainer.querySelector(".upload-progress-bar-inner");

      // Prevent default drag behaviors
      ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
        document.addEventListener(eventName, preventDefaults, false);
      });

      function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }

      // Setup file explorer for receiving file drops
      setupFileDropZone();
    });

    function setupFileDropZone() {
      // Get the file explorer container
      const fileExplorer = document.querySelector(".file-explorer");

      // Highlight drop area when file is dragged over the document
      document.addEventListener("dragenter", function (e) {
        // Only react to actual files being dragged
        if (e.dataTransfer.types.includes("Files")) {
          showDropFeedback(true);
        }
      });

      document.addEventListener("dragleave", function (e) {
        // Only hide if we're leaving the document
        if (e.relatedTarget === null || e.relatedTarget.nodeName === "HTML") {
          showDropFeedback(false);
        }
      });

      document.addEventListener("drop", function (e) {
        showDropFeedback(false);
      });

      function showDropFeedback(show) {
        // Highlight all folder items to indicate drop targets
        document.querySelectorAll(".folder-item").forEach((folder) => {
          if (show) {
            folder.classList.add("drop-target");
          } else {
            folder.classList.remove("drop-target");
          }
        });
      }
    }

    // Drag-and-drop functionality for file tree
    function enableDragAndDrop() {
      // Enable internal drag-and-drop for moving files within the project
      document.querySelectorAll(".file-item, .folder-item").forEach((item) => {
        item.setAttribute("draggable", true);

        item.addEventListener("dragstart", (e) => {
          e.dataTransfer.setData("text/plain", item.dataset.path);
          e.dataTransfer.setData("application/vnd.editor.source", "internal"); // Mark as internal drag
        });

        item.addEventListener("dragover", (e) => {
          e.preventDefault();

          // Only show drop feedback for valid drop targets (folders)
          if (item.classList.contains("folder-item")) {
            item.classList.add("drag-over");
          }
        });

        item.addEventListener("dragleave", () => {
          item.classList.remove("drag-over");
        });

        item.addEventListener("drop", async (e) => {
          e.preventDefault();
          item.classList.remove("drag-over");

          // Check if this is an internal file move operation
          const isInternalMove = e.dataTransfer.types.includes(
            "application/vnd.editor.source"
          );

          // Only allow dropping files in folders
          if (!item.classList.contains("folder-item")) {
            return;
          }

          // If it's an internal move operation
          if (isInternalMove) {
            const sourcePath = e.dataTransfer.getData("text/plain");
            const targetPath = item.dataset.path;

            if (sourcePath && targetPath && sourcePath !== targetPath) {
              // Handle moving files within the project
              handleInternalFileDrop(sourcePath, targetPath);
            }
          } else if (e.dataTransfer.files.length > 0) {
            // Handle external files dropped from the user's system
            const targetFolderPath = item.dataset.path;
            handleExternalFileDrop(e.dataTransfer.files, targetFolderPath);
          }
        });
      });

      // Also setup the root file tree as a drop zone
      const fileTree = document.getElementById("file-tree");
      fileTree.addEventListener("dragover", (e) => {
        e.preventDefault();
        // When dragging over the root, only react to external files
        if (
          !e.dataTransfer.types.includes("application/vnd.editor.source") &&
          e.dataTransfer.types.includes("Files")
        ) {
          fileTree.classList.add("drag-over");
        }
      });

      fileTree.addEventListener("dragleave", () => {
        fileTree.classList.remove("drag-over");
      });

      fileTree.addEventListener("drop", async (e) => {
        e.preventDefault();
        fileTree.classList.remove("drag-over");

        // Check if this is an external file drop
        if (
          !e.dataTransfer.types.includes("application/vnd.editor.source") &&
          e.dataTransfer.files.length > 0
        ) {
          // Drop in root directory
          handleExternalFileDrop(e.dataTransfer.files, "");
        }
      });
    }

    async function handleInternalFileDrop(sourcePath, targetPath) {
      try {
        showOutput(`Moving ${sourcePath} to ${targetPath}...`);

        const response = await fetch(`${apiBaseUrl}/api/file/${projectHash}/move`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            source: sourcePath,
            target: targetPath,
          }),
        });

        if (response.ok) {
          // Use our improved function that preserves state
          await refreshFileTreePreservingState();
          showOutput(`Moved ${sourcePath} to ${targetPath}`);
        } else {
          showOutput("Error: Could not move file or folder");
        }
      } catch (error) {
        showOutput(`Error: ${error.message}`);
      }
    }

    async function handleExternalFileDrop(files, targetFolderPath) {
      if (!files || files.length === 0) return;
      const uploadList = document.querySelector(".upload-list");

      uploadProgressContainer.style.display = "block";
      uploadList.innerHTML = "";

      let totalFiles = files.length;
      let processedFiles = 0;
      let successfulUploads = 0;

      showOutput(
        `Processing ${totalFiles} files for upload to ${
          targetFolderPath || "root"
        }...`
      );

      // Process each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileName = file.name;

        // Create path
        const destPath = targetFolderPath
          ? `${targetFolderPath}/${fileName}`
          : fileName;

        // Add to list of files being processed
        const fileItem = document.createElement("div");
        fileItem.className = "upload-item";
        fileItem.textContent = destPath;
        fileItem.dataset.path = destPath;
        uploadList.appendChild(fileItem);

        try {
          // Read file with FileReader
          const fileContent = await readFileAsText(file);

          // Upload the file
          const response = await fetch(`${apiBaseUrl}/api/file/${projectHash}`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              path: destPath,
              content: fileContent,
              isDirectory: false,
            }),
          });

          // Update progress
          processedFiles++;
          const progressPercent = (processedFiles / totalFiles) * 100;
          uploadProgressBar.style.width = progressPercent + "%";

          if (response.ok) {
            successfulUploads++;
            fileItem.classList.add("success");
            fileItem.textContent = `✓ ${destPath}`;
          } else {
            const errorText = await response.text();
            fileItem.classList.add("error");
            fileItem.textContent = `✗ ${destPath} - ${response.status}: ${errorText}`;
            console.error(`Failed to upload ${destPath}:`, errorText);
          }
        } catch (error) {
          processedFiles++;
          const progressPercent = (processedFiles / totalFiles) * 100;
          uploadProgressBar.style.width = progressPercent + "%";

          fileItem.classList.add("error");
          fileItem.textContent = `✗ ${destPath} - ${error.message}`;
          console.error(`Error processing ${destPath}:`, error);
        }
      }

      // Update UI when all files are processed
      showOutput(
        `Upload complete: ${successfulUploads} of ${totalFiles} files uploaded successfully.`
      );

      // Refresh file tree to show new files while preserving state
      await refreshFileTreePreservingState();

      // Hide the progress after a delay
      setTimeout(() => {
        uploadProgressContainer.style.display = "none";
        uploadProgressBar.style.width = "0%";
      }, 3000);
    }

    function readFileAsText(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          resolve(event.target.result);
        };

        reader.onerror = (error) => {
          reject(error);
        };

        // For binary files like images, use readAsDataURL
        const isBinaryFile =
          /\.(jpg|jpeg|png|gif|bmp|ico|pdf|zip|tar|gz|7z|rar|exe|dll|so|bin)$/i.test(
            file.name
          );

        if (isBinaryFile) {
          reader.readAsDataURL(file);
        } else {
          reader.readAsText(file);
        }
      });
    }

    // Handle file input change (for upload button)
    function handleFileInputChange(event) {
      const files = event.target.files;
      if (files && files.length > 0) {
        // Upload to root directory by default
        handleExternalFileDrop(files, "");
        // Clear the input so the same file can be selected again
        event.target.value = "";
      }
    }



    function startFileSystemRefreshTimer() {
      // Auto-refresh is disabled to prevent folder collapse issues

      // The code below is commented out to stop the automatic file tree refresh
      // that was causing folders to collapse every 5 seconds

      /*
        // Global set to track expanded folders
        if (!window.expandedFolders) {
          window.expandedFolders = new Set();
        }

        // Refresh every 5 seconds but maintain expanded state
        setInterval(() => {
          // Before refresh: Save expanded folders state
          const currentExpandedState = saveExpandedState();

          // Perform file tree refresh
          fetchFileTree().then(() => {
            // After refresh: Make sure expanded folders stay expanded
            if (currentExpandedState) {
              restoreExpandedState(currentExpandedState);
            }
          });
        }, 5000);
        */

      console.log("Automatic file tree refresh is disabled");
    }

    function previewProject() {
      // First save the current file if there is one
      if (currentFile) {
        saveCurrentFile(true).then(() => {
          // After saving, open the preview
          openPreviewTab();
        });
      } else {
        // If no file is open, just open the preview
        openPreviewTab();
      }
    }

    function openPreviewTab(specificFile = null) {
      // Construct the preview URL using project hash for security
      let previewUrl;
      if (specificFile) {
        // If a specific file is provided, preview that file directly
        previewUrl = `${apiBaseUrl}/preview/${projectHash}/${specificFile}`;
      } else {
        // Default to index.html or project root
        previewUrl = `${apiBaseUrl}/preview/${projectHash}/`;
      }

      // Log the action
      console.log(`Opening project preview at ${previewUrl}`);
      if (specificFile) {
        showOutput(`Opening ${specificFile} in preview...`);
      } else {
        showOutput(`Opening full-screen preview in a new tab...`);
      }

      // Open in a new tab
      window.open(previewUrl, "_blank");
    }

    /**
     * Refreshes the file tree while preserving the expanded folder state
     * @returns {Promise<void>} A promise that resolves when the file tree has been refreshed
     */
    async function refreshFileTreePreservingState() {
      try {
        // Save the current expanded state before refreshing
        const currentExpanded = saveExpandedState();

        // Show a subtle indicator that we're refreshing (optional)
        const fileExplorer = document.querySelector(".file-explorer");
        fileExplorer.classList.add("refreshing");

        // Fetch the updated file tree
        const response = await fetch(`${apiBaseUrl}/api/files/${projectHash}`);

        if (!response.ok) {
          const errorText = await response.text();
          console.error("File tree fetch error:", response.status, errorText);
          showOutput(
            `Error refreshing file tree: ${response.status} ${response.statusText}`
          );
          return;
        }

        // Update the file tree data and render it
        fileTree = await response.json();
        renderFileTree(fileTree);

        // Restore the expanded state after rendering
        restoreExpandedState(currentExpanded);

        // Restore selected item if it exists
        if (selectedItemPath) {
          const item = document.querySelector(
            `[data-path="${selectedItemPath}"]`
          );
          if (item) {
            item.classList.add("selected");
          }
        }

        // Remove the refreshing indicator
        fileExplorer.classList.remove("refreshing");

        console.log("File tree refreshed successfully while preserving state");
      } catch (error) {
        console.error("Failed to refresh file tree:", error);
        showOutput(`Error refreshing file tree: ${error.message}`);
      }
    }

    // Function to change editor language
    function changeLanguage() {
      if (!editor) return;

      const language = document.getElementById("language-selector").value;
      monaco.editor.setModelLanguage(editor.getModel(), language);

      // Force syntax highlighting after language change
      setTimeout(() => {
        forceSyntaxHighlighting();
      }, 50);

      // Log the language change
      console.log(`Changed editor language to: ${language}`);

      // If we have a current file, remember this language preference
      if (currentFile) {
        const extension = currentFile.split(".").pop().toLowerCase();
        // For future files with this extension, use this language
        localStorage.setItem(`lang_pref_${extension}`, language);
      }
    }

    // Auto-save function
    function startAutoSave() {
      // Auto-save every 30 seconds
      setInterval(() => {
        if (currentFile) {
          saveCurrentFile(true);
        }
      }, 30000);
    }

    // Adjust layout when window is resized
    function adjustEditorLayout() {
      if (editor) {
        editor.layout();
      }
    }

    function registerIntelliSenseProviders() {
      // Helper: Register dummy completion, hover, and signature help
      function registerDummyProviders(lang, keywords, functions) {
        // Completion
        monaco.languages.registerCompletionItemProvider(lang, {
          provideCompletionItems: () => ({
            suggestions: [
              ...keywords.map((k) => ({
                label: k,
                kind: monaco.languages.CompletionItemKind.Keyword,
                insertText: k,
                documentation: `Keyword: ${k}`,
              })),
              ...functions.map((f) => ({
                label: f.name,
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: f.snippet || `${f.name}()`,
                documentation: f.doc || `Function: ${f.name}`,
              })),
            ],
          }),
        });
        // Hover
        monaco.languages.registerHoverProvider(lang, {
          provideHover: (model, position) => {
            const word = model.getWordAtPosition(position);
            if (!word) return;
            if (keywords.includes(word.word)) {
              return { contents: [{ value: `**Keyword**: \`${word.word}\`` }] };
            }
            const func = functions.find((f) => f.name === word.word);
            if (func) {
              return {
                contents: [
                  {
                    value: `**Function**: \`${func.name}\`\n\n${func.doc || ""}`,
                  },
                ],
              };
            }
          },
        });
        // Signature Help (for functions)
        monaco.languages.registerSignatureHelpProvider(lang, {
          signatureHelpTriggerCharacters: ["(", ","],
          provideSignatureHelp: (model, position) => {
            const text = model
              .getLineContent(position.lineNumber)
              .slice(0, position.column - 1);
            const match = text.match(/(\w+)\($/);
            if (match) {
              const func = functions.find((f) => f.name === match[1]);
              if (func) {
                return {
                  value: {
                    signatures: [
                      {
                        label: `${func.name}(${func.signature || ""})`,
                        documentation: func.doc || "",
                        parameters: (func.signature || "")
                          .split(",")
                          .map((p) => ({ label: p.trim() })),
                      },
                    ],
                    activeSignature: 0,
                    activeParameter: 0,
                  },
                  dispose: () => {},
                };
              }
            }
            return null;
          },
        });
      }

      // Example: Register for Python (Monaco has built-in, but we can enhance)
      (function () {
        const snippetSuggestions = [
          {
            label: "for",
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: "for ${1:item} in ${2:items}:\n\t${3:pass}",
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: "For Loop",
          },
          {
            label: "def",
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText:
              'def ${1:function_name}(${2:parameters}):\n\t"""${3:Docstring}\n\t"""\n\t${4:pass}',
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: "Function definition",
          },
          {
            label: "class",
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText:
              'class ${1:ClassName}(${2:object}):\n\t"""${3:Docstring}\n\t"""\n\n\tdef __init__(self, ${4:parameters}):\n\t\t${5:pass}',
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: "Class definition",
          },
          {
            label: "if",
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: "if ${1:condition}:\n\t${2:pass}",
            insertTextRules:
              monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: "If statement",
          },
        ];
        const keywords = [
          "def",
          "class",
          "import",
          "for",
          "while",
          "if",
          "elif",
          "else",
          "try",
          "except",
          "return",
        ];
        const functions = [
          {
            name: "print",
            signature: "value, ...",
            doc: "Prints values to the console.",
          },
          {
            name: "len",
            signature: "obj",
            doc: "Returns the length of an object.",
          },
        ];
        monaco.languages.registerCompletionItemProvider("python", {
          provideCompletionItems: () => ({
            suggestions: [
              ...snippetSuggestions,
              ...keywords.map((k) => ({
                label: k,
                kind: monaco.languages.CompletionItemKind.Keyword,
                insertText: k,
                documentation: `Keyword: ${k}`,
              })),
              ...functions.map((f) => ({
                label: f.name,
                kind: monaco.languages.CompletionItemKind.Function,
                insertText: f.snippet || `${f.name}()`,
                documentation: f.doc || `Function: ${f.name}`,
              })),
            ],
          }),
        });
        // Hover
        monaco.languages.registerHoverProvider("python", {
          provideHover: (model, position) => {
            const word = model.getWordAtPosition(position);
            if (!word) return;
            if (keywords.includes(word.word)) {
              return { contents: [{ value: `**Keyword**: \`${word.word}\`` }] };
            }
            const func = functions.find((f) => f.name === word.word);
            if (func) {
              return {
                contents: [
                  {
                    value: `**Function**: \`${func.name}\`\n\n${func.doc || ""}`,
                  },
                ],
              };
            }
          },
        });
        // Signature Help (for functions)
        monaco.languages.registerSignatureHelpProvider("python", {
          signatureHelpTriggerCharacters: ["(", ","],
          provideSignatureHelp: (model, position) => {
            const text = model
              .getLineContent(position.lineNumber)
              .slice(0, position.column - 1);
            const match = text.match(/(\w+)\($/);
            if (match) {
              const func = functions.find((f) => f.name === match[1]);
              if (func) {
                return {
                  value: {
                    signatures: [
                      {
                        label: `${func.name}(${func.signature || ""})`,
                        documentation: func.doc || "",
                        parameters: (func.signature || "")
                          .split(",")
                          .map((p) => ({ label: p.trim() })),
                      },
                    ],
                    activeSignature: 0,
                    activeParameter: 0,
                  },
                  dispose: () => {},
                };
              }
            }
            return null;
          },
        });
      })();

      // JavaScript/TypeScript (Monaco has built-in, but you can add more)
      registerDummyProviders(
        "javascript",
        [
          "function",
          "const",
          "let",
          "var",
          "if",
          "else",
          "for",
          "while",
          "return",
        ],
        [
          {
            name: "console.log",
            signature: "message, ...",
            doc: "Logs output to the console.",
          },
          {
            name: "setTimeout",
            signature: "fn, delay",
            doc: "Calls a function after a delay.",
          },
        ]
      );
      registerDummyProviders(
        "typescript",
        ["interface", "type", "enum", "implements"],
        []
      );

      // C
      registerDummyProviders(
        "c",
        ["int", "float", "char", "if", "else", "for", "while", "return"],
        [
          {
            name: "printf",
            signature: "const char *format, ...",
            doc: "Prints formatted output.",
          },
        ]
      );

      // C++
      registerDummyProviders(
        "cpp",
        [
          "int",
          "float",
          "double",
          "class",
          "public",
          "private",
          "if",
          "else",
          "for",
          "while",
          "return",
        ],
        [
          {
            name: "std::cout",
            signature: "<< value",
            doc: "Standard output stream.",
          },
        ]
      );

      // Java
      registerDummyProviders(
        "java",
        [
          "public",
          "private",
          "class",
          "interface",
          "if",
          "else",
          "for",
          "while",
          "return",
        ],
        [
          {
            name: "System.out.println",
            signature: "String x",
            doc: "Prints a line to the console.",
          },
        ]
      );

      // Go
      registerDummyProviders(
        "go",
        ["func", "var", "const", "if", "else", "for", "return"],
        [
          {
            name: "fmt.Println",
            signature: "a ...interface{}",
            doc: "Prints to standard output.",
          },
        ]
      );

      // Rust
      registerDummyProviders(
        "rust",
        ["fn", "let", "mut", "if", "else", "for", "while", "return"],
        [
          {
            name: "println!",
            signature: "format, args",
            doc: "Prints to the console.",
          },
        ]
      );

      // PHP
      registerDummyProviders(
        "php",
        ["function", "echo", "if", "else", "for", "while", "return"],
        [
          {
            name: "echo",
            signature: "arg1, ...",
            doc: "Outputs one or more strings.",
          },
        ]
      );

      // Ruby
      registerDummyProviders(
        "ruby",
        ["def", "class", "if", "else", "elsif", "end", "do", "while", "return"],
        [{ name: "puts", signature: "obj", doc: "Writes to standard output." }]
      );

      // HTML
      registerDummyProviders(
        "html",
        ["html", "head", "body", "div", "span", "a", "img", "script", "style"],
        [{ name: "div", signature: "class", doc: "A generic container element." }]
      );

      // CSS
      registerDummyProviders(
        "css",
        ["color", "background", "margin", "padding", "display", "flex", "grid"],
        [
          {
            name: "display",
            signature: "value",
            doc: "Specifies the display behavior.",
          },
        ]
      );

      // SQL
      registerDummyProviders(
        "sql",
        ["SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "JOIN"],
        [{ name: "COUNT", signature: "(*)", doc: "Returns the number of rows." }]
      );

      // Shell
      registerDummyProviders(
        "shell",
        ["ls", "cd", "echo", "cat", "grep", "find", "pwd"],
        [{ name: "echo", signature: "args", doc: "Display a line of text." }]
      );

      // JSON, YAML, XML (minimal, as these are data formats)
      registerDummyProviders("json", ["true", "false", "null"], []);
      registerDummyProviders("yaml", ["true", "false", "null"], []);
      registerDummyProviders("xml", ["<tag>", "</tag>"], []);

      // Swift
      registerDummyProviders(
        "swift",
        ["let", "var", "func", "if", "else", "for", "while", "return"],
        [
          {
            name: "print",
            signature: "items",
            doc: "Prints items to the console.",
          },
        ]
      );

      // Kotlin
      registerDummyProviders(
        "kotlin",
        ["val", "var", "fun", "if", "else", "for", "while", "return"],
        [
          {
            name: "println",
            signature: "message",
            doc: "Prints a line to the console.",
          },
        ]
      );

      // R
      registerDummyProviders(
        "r",
        ["if", "else", "for", "while", "function", "return"],
        [{ name: "print", signature: "x", doc: "Prints its argument." }]
      );
    }

    // Update runCode to support real-time streaming
    async function runCodeStream(projectId) {
      showOutput("▶ Running...", "plaintext");
      const outputPanel = document.getElementById("output-panel");

      // Disable the Run button during execution
      const runButton = document.getElementById("run-code-btn");
      runButton.disabled = true;

      const eventSource = new EventSource(`/api/stream/${projectHash}`);
      const outputBlock = document.createElement("div");
      outputBlock.classList.add("output-block");
      const codeEl = document.createElement("pre");
      codeEl.innerHTML = `<code class="language-plaintext"></code>`;
      outputBlock.appendChild(codeEl);
      outputPanel.appendChild(outputBlock);

      eventSource.onmessage = function (event) {
        if (event.data === ">>>END<<<") {
          eventSource.close();
          appendToOutput("✅ Execution complete.\n");
          runButton.disabled = false; // Re-enable the Run button
          return;
        }

        codeEl.querySelector("code").innerText += event.data + "\n";
        outputPanel.scrollTop = outputPanel.scrollHeight;
        hljs.highlightElement(codeEl.querySelector("code"));
      };

      eventSource.onerror = function () {
        eventSource.close();
        appendToOutput("❌ Execution failed or cancelled.");
        runButton.disabled = false; // Re-enable the Run button
      };
    }









  // === GLOBALS ===
    let collabSocket, voiceSocket;
    let clientId, isApplyingRemoteChange = false;
    let editorReadyInterval;
    const roomId = {{ project.id }};
    // baseWsUrl is now declared globally at the top of the file
    {% comment %} const baseWsUrl = `${wsProtocol}192.168.1.133:8001`; {% endcomment %}

    // === UI TOGGLE ===
    document.addEventListener("DOMContentLoaded", () => {
      const toggleBtn = document.getElementById("collabToggle");
      const panel = document.getElementById("collabPanel");

      console.log("🔧 Setting up collaboration panel toggle");
      console.log("Toggle button:", toggleBtn);
      console.log("Panel:", panel);

      if (toggleBtn && panel) {
        // Ensure panel starts hidden
        panel.classList.add("hidden");

        toggleBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();

          console.log("🤝 Collaboration button clicked");
          console.log("Panel classes before toggle:", panel.className);

          panel.classList.toggle("hidden");

          console.log("Panel classes after toggle:", panel.className);
          console.log("Panel is hidden:", panel.classList.contains("hidden"));
        });

        // Also add keyboard support
        toggleBtn.addEventListener("keydown", (e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            panel.classList.toggle("hidden");
          }
        });

        // Add click outside to close functionality
        document.addEventListener("click", (e) => {
          // Check if click is outside both the panel and the toggle button
          if (!panel.contains(e.target) && !toggleBtn.contains(e.target)) {
            if (!panel.classList.contains("hidden")) {
              console.log("🔄 Closing collaboration panel (clicked outside)");
              panel.classList.add("hidden");
            }
          }
        });

        // Add escape key to close
        document.addEventListener("keydown", (e) => {
          if (e.key === "Escape" && !panel.classList.contains("hidden")) {
            console.log("🔄 Closing collaboration panel (escape key)");
            panel.classList.add("hidden");
          }
        });

        console.log("✅ Collaboration panel toggle setup complete");
      } else {
        console.error("❌ Could not find collaboration toggle elements");
        console.error("Toggle button found:", !!toggleBtn);
        console.error("Panel found:", !!panel);
      }

      // === PANEL TOGGLE FUNCTIONALITY ===
      const panelToggleBtn = document.getElementById("toggle-panel-btn");
      const panelContainer = document.querySelector(".panel-container");

      console.log("🔧 Setting up panel toggle functionality");
      console.log("Panel toggle button:", panelToggleBtn);
      console.log("Panel container:", panelContainer);

      if (panelToggleBtn && panelContainer) {
        // Add click event listener
        panelToggleBtn.addEventListener("click", function(e) {
          e.preventDefault();
          e.stopPropagation();

          console.log("📋 Panel toggle button clicked!");
          console.log("Panel classes before toggle:", panelContainer.className);

          // Toggle the collapsed class
          panelContainer.classList.toggle("collapsed");

          const isCollapsed = panelContainer.classList.contains("collapsed");
          const icon = panelToggleBtn.querySelector("i");

          console.log("Panel is now collapsed:", isCollapsed);
          console.log("Panel classes after toggle:", panelContainer.className);

          if (isCollapsed) {
            if (icon) {
              icon.className = "fas fa-chevron-up";
            }
            panelToggleBtn.title = "Expand Panel";
            console.log("📋 Panel collapsed - height should be 35px");
          } else {
            if (icon) {
              icon.className = "fas fa-chevron-down";
            }
            panelToggleBtn.title = "Collapse Panel";
            console.log("📋 Panel expanded - height should be normal");
          }

          // Force a style recalculation
          panelContainer.offsetHeight;
        });

        // Also add keyboard support
        panelToggleBtn.addEventListener("keydown", function(e) {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            panelToggleBtn.click();
          }
        });

        console.log("✅ Panel toggle functionality setup complete");
      } else {
        console.error("❌ Could not find panel toggle elements");
        console.error("Panel toggle button found:", !!panelToggleBtn);
        console.error("Panel container found:", !!panelContainer);
      }
    });

    // === CHAT SOCKET ===
    function connectChatSocket() {
      collabSocket = new WebSocket(`${websocketUrls.collabChat}${roomId}/`);

      collabSocket.onopen = () => {
        console.log("✅ Chat WebSocket connected");

        // Fallback: Load chat history via AJAX if WebSocket doesn't send it within 3 seconds
        setTimeout(() => {
          loadChatHistoryFallback();
        }, 3000);
      };

      collabSocket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleChatMessage(data);
      };

      collabSocket.onclose = () => {
        console.warn("⚠️ Chat WebSocket closed. Reconnecting...");
        setTimeout(connectChatSocket, 3000);
      };

      collabSocket.onerror = (err) => {
        console.error("❌ Chat WebSocket error:", err);
        collabSocket.close();
      };
    }

    connectChatSocket();

    // Test function to verify chat is working
    window.testChat = function() {
      console.log('Testing chat functionality...');
      console.log('Chat input element:', document.getElementById('chatInput'));
      console.log('Send button element:', document.getElementById('sendMessage'));
      console.log('Chat messages container:', document.getElementById('chatMessages'));
      console.log('WebSocket state:', collabSocket ? collabSocket.readyState : 'Not connected');

      // Add a test message
      addChatMessage('Test message from current user', '{{ user.username }}', true);
      addChatMessage('Test message from another user', 'TestUser', false);
      addSystemMessage('Test system message');
    };

    // Chat message handling functions
    function handleChatMessage(data) {
      console.log('Received chat message:', data); // Debug log

      switch(data.type) {
        case 'chat':
          const isOwn = data.user_id === {{ user.id }};
          console.log('Chat message - user_id:', data.user_id, 'current user:', {{ user.id }}, 'isOwn:', isOwn);

          // Fix timestamp handling
          let timestamp = null;
          if (data.timestamp) {
            // If timestamp is already in milliseconds, use it directly
            // If it's in seconds, multiply by 1000
            const ts = data.timestamp > 1000000000000 ? data.timestamp : data.timestamp * 1000;
            timestamp = new Date(ts);
          }

          addChatMessage(data.message, data.username, isOwn, timestamp);
          break;

        case 'chat_history':
          // Handle chat history messages from WebSocket
          const isOwnHistory = data.user_id === {{ user.id }};
          console.log('History message - user_id:', data.user_id, 'current user:', {{ user.id }}, 'isOwnHistory:', isOwnHistory);

          // Fix timestamp handling for history
          let historyTimestamp = null;
          if (data.timestamp) {
            const ts = data.timestamp > 1000000000000 ? data.timestamp : data.timestamp * 1000;
            historyTimestamp = new Date(ts);
          }

          addChatMessage(data.message, data.username, isOwnHistory, historyTimestamp);
          break;

        case 'system_message':
          addSystemMessage(data.message);
          break;

        case 'user_list_update':
          // Handle user list updates (existing functionality)
          if (typeof updateUserLists === 'function') {
            updateUserLists(data);
          }
          break;
      }
    }

    function addChatMessage(message, username, isOwn, messageTimestamp = null) {
      console.log('Adding chat message:', { message, username, isOwn, messageTimestamp }); // Debug log

      const chatMessages = document.getElementById('chatMessages');
      if (!chatMessages) {
        console.error('Chat messages container not found');
        return;
      }

      const messageDiv = document.createElement('div');
      messageDiv.className = `chat-message ${isOwn ? 'own' : 'other'}`;

      // Better timestamp handling
      let timestamp = 'Now';
      if (messageTimestamp && messageTimestamp instanceof Date && !isNaN(messageTimestamp)) {
        timestamp = messageTimestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
      } else if (!messageTimestamp) {
        timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
      }

      // Escape HTML to prevent XSS
      const escapedMessage = message.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      const escapedUsername = username.replace(/</g, '&lt;').replace(/>/g, '&gt;');

      messageDiv.innerHTML = `
        <div class="message-header">
          <span class="message-sender">${escapedUsername}</span>
          <span class="message-time">${timestamp}</span>
        </div>
        <div class="message-content">${escapedMessage}</div>
      `;

      chatMessages.appendChild(messageDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;

      console.log('Message added successfully'); // Debug log
    }

    function addSystemMessage(message) {
      console.log('Adding system message:', message); // Debug log

      const chatMessages = document.getElementById('chatMessages');
      if (!chatMessages) {
        console.error('Chat messages container not found for system message');
        return;
      }

      const messageDiv = document.createElement('div');
      messageDiv.className = 'system-message';

      const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

      // Escape HTML to prevent XSS
      const escapedMessage = message.replace(/</g, '&lt;').replace(/>/g, '&gt;');

      messageDiv.innerHTML = `
        <i class="fas fa-info-circle"></i>
        <span>${escapedMessage}</span>
        <span class="system-time">${timestamp}</span>
      `;

      chatMessages.appendChild(messageDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;

      console.log('System message added successfully'); // Debug log
    }

    // Send message functionality
    function sendChatMessage() {
      const input = document.getElementById("chatInput");
      if (input.value.trim() && collabSocket.readyState === WebSocket.OPEN) {
        collabSocket.send(JSON.stringify({ type: "chat", message: input.value }));
        // Don't add message immediately - wait for WebSocket broadcast
        input.value = "";
      }
    }

    document.getElementById("chatInput").addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        sendChatMessage();
      }
    });

    // Fallback function to load chat history via AJAX
    async function loadChatHistoryFallback() {
      try {
        console.log('Loading chat history via AJAX fallback for project:', projectHash);
        const response = await fetch(`/collaborate/project/${projectHash}/chat-history/`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Chat history loaded via AJAX:', data);

        if (data.success && data.messages && data.messages.length > 0) {
          // Clear existing messages first
          const chatMessages = document.getElementById('chatMessages');
          if (chatMessages) {
            chatMessages.innerHTML = '';
          }

          // Add each message to the chat
          data.messages.forEach(message => {
            const isOwn = message.user_id === {{ user.id }};
            const messageTimestamp = new Date(message.timestamp * 1000);
            addChatMessage(message.content, message.username, isOwn, messageTimestamp);
          });

          console.log(`Loaded ${data.messages.length} chat messages via AJAX`);
        } else {
          console.log('No chat history found via AJAX');
        }
      } catch (error) {
        console.error('Error loading chat history via AJAX:', error);
      }
    }

    // === VOICE CHAT ===
    document.getElementById("startCall").addEventListener("click", async () => {
      try {
          const stream = await navigator.mediaDevices.getUserMedia({
              audio: {
                  echoCancellation: true,
                  noiseSuppression: true,
                  autoGainControl: true
              }
          });
        const context = new AudioContext();

          // Load and register the audio worklet
          await context.audioWorklet.addModule('/static/js/audio-worklet-processor.js');

        const source = context.createMediaStreamSource(stream);
          const workletNode = new AudioWorkletNode(context, 'audio-worklet-processor');

        voiceSocket = new WebSocket(`${websocketUrls.voice}${window.projectHash}/`);

          // Handle audio data from the worklet
          workletNode.port.onmessage = (event) => {
              if (voiceSocket && voiceSocket.readyState === WebSocket.OPEN) {
                  voiceSocket.send(event.data);
              }
        };

        voiceSocket.onmessage = async (event) => {
              try {
          if (event.data instanceof Blob) {
              const arrayBuffer = await event.data.arrayBuffer();
                      const audioBuffer = context.createBuffer(1, arrayBuffer.byteLength / 2, 44100);
                      const channelData = audioBuffer.getChannelData(0);

                      const view = new Int16Array(arrayBuffer);
                      for (let i = 0; i < view.length; i++) {
                          channelData[i] = view[i] / 32768.0;
                      }

                      const source = context.createBufferSource();
                      source.buffer = audioBuffer;
                      source.connect(context.destination);
                  source.start();
                  }
              } catch (error) {
                  console.error("Error processing received audio:", error);
          }
        };

        voiceSocket.onopen = () => {
          console.log("Voice WebSocket connected");
              source.connect(workletNode);

              // Update UI
              document.getElementById("startCall").style.display = "none";
              document.getElementById("endCall").style.display = "flex";
              document.querySelector(".mic-status .status-dot").classList.add("active");
              document.querySelector(".mic-status .status-text").textContent = "Microphone: On";
              document.querySelector(".connection-status .status-dot").classList.add("active");
              document.querySelector(".connection-status .status-text").textContent = "Connection: Connected";

              // Notify others
              collabSocket.send(JSON.stringify({
                  type: "voice",
                  action: "join"
              }));
        };

        voiceSocket.onclose = () => {
          console.log("Voice WebSocket disconnected");
              workletNode.disconnect();
          source.disconnect();

              // Update UI
              document.getElementById("startCall").style.display = "flex";
              document.getElementById("endCall").style.display = "none";
              document.querySelector(".mic-status .status-dot").classList.remove("active");
              document.querySelector(".mic-status .status-text").textContent = "Microphone: Off";
              document.querySelector(".connection-status .status-dot").classList.remove("active");
              document.querySelector(".connection-status .status-text").textContent = "Connection: Disconnected";

              // Notify others
              collabSocket.send(JSON.stringify({
                  type: "voice",
                  action: "leave"
              }));
          };

          voiceSocket.onerror = (error) => {
              console.error("Voice WebSocket error:", error);
              document.querySelector(".connection-status .status-dot").classList.remove("active");
              document.querySelector(".connection-status .status-text").textContent = "Connection: Error";
          };

      } catch (error) {
        console.error("Error starting voice call:", error);
          alert("Could not access microphone. Please check your permissions.");
      }
  });

    document.getElementById("endCall").addEventListener("click", () => {
      if (voiceSocket) {
          voiceSocket.close();
          voiceSocket = null;
      }
    });

    function encodeWAV(samples, sampleRate) {
      const buffer = new ArrayBuffer(44 + samples.length * 2);
      const view = new DataView(buffer);

      function writeString(view, offset, string) {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      }

      let offset = 0;
      writeString(view, offset, 'RIFF'); offset += 4;
      view.setUint32(offset, 36 + samples.length * 2, true); offset += 4;
      writeString(view, offset, 'WAVE'); offset += 4;
      writeString(view, offset, 'fmt '); offset += 4;
      view.setUint32(offset, 16, true); offset += 4;
      view.setUint16(offset, 1, true); offset += 2;
      view.setUint16(offset, 1, true); offset += 2;
      view.setUint32(offset, sampleRate, true); offset += 4;
      view.setUint32(offset, sampleRate * 2, true); offset += 4;
      view.setUint16(offset, 2, true); offset += 2;
      view.setUint16(offset, 16, true); offset += 2;
      writeString(view, offset, 'data'); offset += 4;
      view.setUint32(offset, samples.length * 2, true); offset += 4;

      for (let i = 0; i < samples.length; i++, offset += 2) {
        view.setInt16(offset, samples[i] * 0x7fff, true);
      }

      return new Blob([view], { type: 'audio/wav' });
    }
























  </script>

<script>
  const chatUserList = document.getElementById("chat-user-list");
  const voiceUserList = document.getElementById("voice-user-list");

  function updateUserLists(userList) {
    // Add error checking to prevent forEach errors
    if (!userList || typeof userList !== 'object') {
      console.warn('Invalid userList data:', userList);
      return;
    }

    // Update chat users
    if (chatUserList) {
      chatUserList.innerHTML = "";
      if (userList.chat && Array.isArray(userList.chat)) {
        userList.chat.forEach((user) => {
          const li = document.createElement("li");
          li.textContent = user;
          chatUserList.appendChild(li);
        });
      }
    }

    // Update voice users
    if (voiceUserList) {
      voiceUserList.innerHTML = "";
      if (userList.voice && Array.isArray(userList.voice)) {
        userList.voice.forEach((user) => {
          const li = document.createElement("li");
          li.textContent = user;
          voiceUserList.appendChild(li);
        });
      }
    }
  }

  // WebSocket connection for user presence (moved to appropriate script block)
</script>

<script>
  const chatUsersList = document.getElementById("chat-users-list");
  const voiceUsersList = document.getElementById("voice-users-list");

  function updateUserListUI(chatUsers, voiceUsers) {
    // Update chat users list
    chatUsersList.innerHTML = "";
    chatUsers.forEach((user) => {
      const li = document.createElement("li");
      li.textContent = user;
      chatUsersList.appendChild(li);
    });

    // Update voice users list
    voiceUsersList.innerHTML = "";
    voiceUsers.forEach((user) => {
      const li = document.createElement("li");
      li.textContent = user;
      voiceUsersList.appendChild(li);
    });
  }

  // WebSocket connection for user presence (properly scoped)
  // Note: This requires baseWsUrl to be available globally
  if (typeof baseWsUrl !== 'undefined' && typeof projectHash !== 'undefined') {
    try {
      const presenceSocket = new WebSocket(
        `${websocketUrls.collaboration}${window.projectHash}/`
      );

      presenceSocket.onmessage = function (event) {
        const data = JSON.parse(event.data);
        if (data.type === "user_list_update") {
          updateUserListUI(data.chat_users, data.voice_users);
        }
      };
    } catch (error) {
      console.warn('Could not create presence WebSocket:', error);
    }
  }

  // WebSocket connection for user presence (duplicate removed - already declared above)

  // Voice chat logic (moved to appropriate scope)
  // Audio context and WebSocket handling moved to collaboration section
</script>

<script>
  // ... existing code ...

  // Tab switching functionality
  document.querySelectorAll(".tab-btn").forEach((button) => {
    button.addEventListener("click", () => {
      // Remove active class from all tabs
      document
        .querySelectorAll(".tab-btn")
        .forEach((btn) => btn.classList.remove("active"));
      document
        .querySelectorAll(".tab-content")
        .forEach((content) => content.classList.remove("active"));

      // Add active class to clicked tab
      button.classList.add("active");
      document
        .getElementById(`${button.dataset.tab}-tab`)
        .classList.add("active");
    });
  });

  // Send message button functionality
  document.getElementById("sendMessage").addEventListener("click", () => {
    sendChatMessage();
  });

  // ... rest of the existing code ...
</script>









<script>








  // ==== Save File (patch) ====
  async function saveCurrentFile(silent = false) {
    if (!currentFile) {
      if (!silent) showOutput("Error: No file is currently open");
      return;
    }

    try {
      const response = await fetch(`${window.apiBaseUrl}/api/file/${window.projectHash}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          path: currentFile,
          content: editor.getValue(),
        }),
      });

      if (!silent) {
        showOutput(response.ok ? "File saved successfully" : "Error: Could not save file");
      }


    } catch (error) {
      if (!silent) showOutput(`Error: ${error.message}`);
    }
  }






  // ==== File Search Functionality ====
  function setupFileSearch() {
    const fileSearch = document.getElementById("file-search");

    fileSearch.addEventListener("keydown", (e) => {
      if (e.key === "Escape") {
        fileSearch.value = "";
        filterFileTree("");
        fileSearch.blur();
      }
    });
  }

  function filterFileTree(searchTerm) {
    const allItems = document.querySelectorAll(".file-item, .folder-item");
    const searchLower = searchTerm.toLowerCase();

    if (!searchTerm) {
      // Show all items when search is empty
      allItems.forEach(item => {
        item.style.display = "";
      });
      return;
    }

    allItems.forEach(item => {
      const fileName = item.querySelector(".file-name, .folder-name").textContent.toLowerCase();
      const matches = fileName.includes(searchLower);

      if (matches) {
        item.style.display = "";
        // Expand parent folders to show matching items
        expandParentFolders(item);
      } else {
        item.style.display = "none";
      }
    });
  }

  function expandParentFolders(item) {
    let parent = item.closest(".folder-children");
    while (parent) {
      const parentFolder = parent.parentElement;
      if (parentFolder && parentFolder.classList.contains("folder-item")) {
        parentFolder.classList.add("expanded");
        parent.classList.add("expanded");

        // Update icon and toggle
        const iconEl = parentFolder.querySelector("i:not(.toggle-icon)");
        if (iconEl) {
          iconEl.className = "fas fa-folder-open";
        }

        const toggleIcon = parentFolder.querySelector(".toggle-icon");
        if (toggleIcon) {
          toggleIcon.style.transform = "rotate(90deg)";
        }

        expandedFolders.add(parentFolder.dataset.path);
      }
      parent = parent.parentElement?.closest(".folder-children");
    }
  }

  // === CHATBOT FUNCTIONALITY ===

  let chatbotMessages = [];
  let chatbotLoading = false;

  // Initialize chatbot functionality
  function initializeChatbot() {
    const chatbotInput = document.getElementById('chatbotInput');
    const sendChatbotBtn = document.getElementById('sendChatbotMessage');
    const clearChatbotBtn = document.getElementById('clearChatbot');
    const analyzeCodeBtn = document.getElementById('analyzeCode');

    if (chatbotInput && sendChatbotBtn) {
      // Send message on button click
      sendChatbotBtn.addEventListener('click', sendChatbotMessage);

      // Send message on Enter key
      chatbotInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendChatbotMessage();
        }
      });
    }

    if (clearChatbotBtn) {
      clearChatbotBtn.addEventListener('click', clearChatbotHistory);
    }

    if (analyzeCodeBtn) {
      analyzeCodeBtn.addEventListener('click', analyzeCurrentCode);
    }

    console.log('🤖 Chatbot functionality initialized');
  }

  // Send message to chatbot
  async function sendChatbotMessage() {
    const input = document.getElementById('chatbotInput');
    const message = input.value.trim();

    if (!message || chatbotLoading) return;

    // Clear input
    input.value = '';

    // Add user message to chat
    addChatbotMessage(message, 'user');

    // Show loading indicator
    showChatbotLoading();

    try {
      // Get current code context if available
      const codeContext = editor ? editor.getValue() : '';
      const fileName = currentFile ? currentFile.split('/').pop() : '';

      const response = await fetch(`/collaborate/chatbot/${window.projectHash}/message/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCsrfToken(),
        },
        body: JSON.stringify({
          message: message,
          code_context: codeContext,
          file_name: fileName
        })
      });

      const data = await response.json();

      if (data.success) {
        // Add assistant response
        addChatbotMessage(data.response, 'assistant');

        // Show analysis if available
        if (data.analysis) {
          showCodeAnalysis(data.analysis);
        }
      } else {
        addChatbotMessage(`Error: ${data.error || 'Failed to get response'}`, 'system');
      }
    } catch (error) {
      console.error('Chatbot error:', error);
      addChatbotMessage('Sorry, I encountered an error. Please try again.', 'system');
    } finally {
      hideChatbotLoading();
    }
  }

  // Analyze current code
  async function analyzeCurrentCode() {
    if (!editor || !currentFile || chatbotLoading) {
      addChatbotMessage('Please open a file first to analyze code.', 'system');
      return;
    }

    const code = editor.getValue();
    if (!code.trim()) {
      addChatbotMessage('The current file is empty. Please add some code to analyze.', 'system');
      return;
    }

    showChatbotLoading();

    try {
      const fileName = currentFile.split('/').pop();

      const response = await fetch(`/collaborate/chatbot/${window.projectHash}/analyze/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCsrfToken(),
        },
        body: JSON.stringify({
          code: code,
          file_name: fileName
        })
      });

      const data = await response.json();

      if (data.success && data.analysis) {
        showCodeAnalysis(data.analysis);

        // Also send analysis to chatbot for AI insights
        addChatbotMessage(`📊 **Code Analysis for ${fileName}:**`, 'assistant');

        if (data.analysis.errors && data.analysis.errors.length > 0) {
          addChatbotMessage(`🐛 **Found ${data.analysis.errors.length} potential issues:**\n${data.analysis.errors.map(e => `• ${e}`).join('\n')}`, 'assistant');
        } else {
          addChatbotMessage('✅ No obvious syntax errors detected!', 'assistant');
        }

        if (data.analysis.suggestions && data.analysis.suggestions.length > 0) {
          addChatbotMessage(`💡 **Suggestions:**\n${data.analysis.suggestions.map(s => `• ${s}`).join('\n')}`, 'assistant');
        }

        addChatbotMessage(`📈 **Complexity:** ${data.analysis.complexity} (${data.analysis.language})`, 'assistant');
      } else {
        addChatbotMessage(`Error analyzing code: ${data.error || 'Analysis failed'}`, 'system');
      }
    } catch (error) {
      console.error('Code analysis error:', error);
      addChatbotMessage('Sorry, I encountered an error while analyzing the code.', 'system');
    } finally {
      hideChatbotLoading();
    }
  }

  // Clear chatbot history
  async function clearChatbotHistory() {
    try {
      const response = await fetch(`/collaborate/chatbot/${window.projectHash}/clear/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': getCsrfToken(),
        }
      });

      const data = await response.json();

      if (data.success) {
        // Clear local messages
        chatbotMessages = [];
        const messagesContainer = document.getElementById('chatbotMessages');
        if (messagesContainer) {
          // Keep only the system welcome message
          const welcomeMessage = messagesContainer.querySelector('.chatbot-message.system');
          messagesContainer.innerHTML = '';
          if (welcomeMessage) {
            messagesContainer.appendChild(welcomeMessage);
          }
        }
        console.log('🧹 Chatbot history cleared');
      }
    } catch (error) {
      console.error('Error clearing chatbot history:', error);
    }
  }

  // Add message to chatbot interface
  function addChatbotMessage(content, type = 'assistant') {
    const messagesContainer = document.getElementById('chatbotMessages');
    if (!messagesContainer) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `chatbot-message ${type}`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';

    // Process markdown-like formatting
    const formattedContent = formatChatbotMessage(content);
    contentDiv.innerHTML = formattedContent;

    messageDiv.appendChild(contentDiv);
    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    // Store message
    chatbotMessages.push({ content, type, timestamp: Date.now() });

    // Clear float
    const clearDiv = document.createElement('div');
    clearDiv.style.clear = 'both';
    messagesContainer.appendChild(clearDiv);
  }

  // Format chatbot message with basic markdown support
  function formatChatbotMessage(content) {
    // First handle code blocks (before line breaks)
    content = content.replace(/```(\w+)?\n?([\s\S]*?)```/g, function(match, language, code) {
      const lang = language || 'text';
      const cleanCode = code.trim();

      // Escape HTML in the code for display but preserve original for copying
      const escapedCode = cleanCode
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');

      // Encode the original code for the data attribute
      const encodedOriginalCode = encodeURIComponent(cleanCode);

      return `<div class="code-block-container">
        <div class="code-block-header">
          <span class="code-language">${lang}</span>
          <button class="copy-code-btn" onclick="copyCodeToClipboard(this)">
            <i class="fas fa-copy"></i> Copy
          </button>
        </div>
        <pre class="code-block"><code class="language-${lang}" data-original-code="${encodedOriginalCode}">${escapedCode}</code></pre>
      </div>`;
    });

    // Then handle other formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>') // Inline code
      .replace(/\n/g, '<br>') // Line breaks
      .replace(/^(#{1,6})\s+(.+)$/gm, function(match, hashes, text) {
        const level = hashes.length;
        return `<h${level}>${text}</h${level}>`;
      }); // Headers
  }

  // Show loading indicator
  function showChatbotLoading() {
    chatbotLoading = true;
    const messagesContainer = document.getElementById('chatbotMessages');
    if (!messagesContainer) return;

    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'chatbot-loading';
    loadingDiv.id = 'chatbot-loading-indicator';

    loadingDiv.innerHTML = `
      <span>🤖 Thinking...</span>
      <div class="loading-dots">
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
        <div class="loading-dot"></div>
      </div>
    `;

    messagesContainer.appendChild(loadingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  }

  // Hide loading indicator
  function hideChatbotLoading() {
    chatbotLoading = false;
    const loadingIndicator = document.getElementById('chatbot-loading-indicator');
    if (loadingIndicator) {
      loadingIndicator.remove();
    }
  }

  // Show code analysis results
  function showCodeAnalysis(analysis) {
    console.log('📊 Code Analysis Results:', analysis);

    // You could add a visual indicator in the editor or file tree
    // For now, we'll just log it and let the chatbot handle the display
  }

  // Copy code to clipboard
  function copyCodeToClipboard(button) {
    const codeBlock = button.closest('.code-block-container').querySelector('code');

    // Get the original code with proper formatting
    let code = codeBlock.getAttribute('data-original-code');

    if (code) {
      // Decode the URL-encoded original code
      code = decodeURIComponent(code);
    } else {
      // Fallback: get the text content and try to preserve formatting
      code = codeBlock.textContent || codeBlock.innerText;
    }

    navigator.clipboard.writeText(code).then(() => {
      // Show success feedback
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-check"></i> Copied!';
      button.style.background = '#28a745';

      // Add option to insert into editor
      const insertBtn = document.createElement('button');
      insertBtn.innerHTML = '<i class="fas fa-plus"></i> Insert';
      insertBtn.className = 'copy-code-btn';
      insertBtn.style.marginLeft = '8px';
      insertBtn.onclick = () => insertCodeIntoEditor(code);

      const header = button.parentElement;
      header.appendChild(insertBtn);

      setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '';
        if (insertBtn.parentElement) {
          insertBtn.remove();
        }
      }, 5000); // Show insert button for 5 seconds
    }).catch(err => {
      console.error('Failed to copy code:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = code;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);

      // Show success feedback
      const originalText = button.innerHTML;
      button.innerHTML = '<i class="fas fa-check"></i> Copied!';
      button.style.background = '#28a745';

      setTimeout(() => {
        button.innerHTML = originalText;
        button.style.background = '';
      }, 2000);
    });
  }

  // Insert code into Monaco editor
  function insertCodeIntoEditor(code) {
    if (!editor) {
      alert('Please open a file in the editor first.');
      return;
    }

    // Get current cursor position
    const position = editor.getPosition();

    // Insert the code at cursor position
    editor.executeEdits('chatbot-insert', [{
      range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
      text: code
    }]);

    // Focus back to editor
    editor.focus();

    // Show notification
    showOutput('Code inserted into editor successfully!');
  }

  // Get CSRF token for requests
  function getCsrfToken() {
    // Try multiple ways to get CSRF token
    let token = document.querySelector('[name=csrfmiddlewaretoken]');
    if (token && token.value) {
      return token.value;
    }

    // Try getting from meta tag
    token = document.querySelector('meta[name="csrf-token"]');
    if (token && token.content) {
      return token.content;
    }

    // Try getting from cookie
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrftoken') {
        return value;
      }
    }

    console.warn('CSRF token not found');
    return '';
  }

  // Initialize chatbot when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for other initializations
    setTimeout(initializeChatbot, 1000);
  });
</script>

<!-- xterm.js for VS Code-like terminal - Load after Monaco to avoid AMD conflicts -->
<script>
  // Temporarily disable AMD to avoid conflicts with Monaco Editor
  window.xtermDefine = window.define;
  window.define = undefined;
</script>
<script src="https://unpkg.com/xterm@5.3.0/lib/xterm.js"></script>
<script src="https://unpkg.com/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.js"></script>
<script src="https://unpkg.com/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.js"></script>
<script>
  // Restore AMD define for Monaco
  window.define = window.xtermDefine;
  delete window.xtermDefine;
</script>

<script>
  // ===== PERFECT TERMINAL IMPLEMENTATION =====

  // Debug: Check if xterm.js is loading
  console.log('🔍 Initial xterm.js check:');
  console.log('- Terminal:', typeof Terminal);
  console.log('- FitAddon:', typeof FitAddon);
  console.log('- WebLinksAddon:', typeof WebLinksAddon);

  class ForgeXTerminal {
    constructor() {
      this.terminals = new Map();
      this.activeTerminalId = null;
      this.terminalCounter = 1;
      this.terminalServiceUrl = 'localhost:8003';
      this.projectId = window.projectHash;

      this.initializeTerminalSystem();
    }

    initializeTerminalSystem() {
      console.log('🚀 Initializing ForgeX Terminal System');

      // Setup event listeners
      this.setupEventListeners();

      // Create first terminal
      this.createTerminal();

      console.log('✅ Terminal System initialized');
    }

    setupEventListeners() {
      // New terminal button
      document.getElementById('new-terminal-btn').addEventListener('click', () => {
        this.createTerminal();
      });

      // Clear terminal button
      document.getElementById('clear-terminal-btn').addEventListener('click', () => {
        this.clearActiveTerminal();
      });

      // Window resize handler
      window.addEventListener('resize', () => {
        this.resizeActiveTerminal();
      });
    }

    showTerminalPanel() {
      console.log('📺 Showing terminal panel');
      // Fit active terminal when panel is shown
      setTimeout(() => {
        this.resizeActiveTerminal();
        // Also ensure terminal is focused
        if (this.activeTerminalId) {
          const terminalData = this.terminals.get(this.activeTerminalId);
          if (terminalData && terminalData.terminal) {
            terminalData.terminal.focus();
          }
        }
      }, 200);
    }

    async createTerminal() {
      const terminalId = `terminal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const terminalName = `Terminal ${this.terminalCounter++}`;

      console.log(`🚀 Creating terminal: ${terminalId}`);

      try {
        // Create terminal instance container
        const terminalContainer = this.createTerminalContainer(terminalId);

        // Create xterm.js terminal
        const terminal = new Terminal({
          theme: {
            background: '#1e1e1e',
            foreground: '#cccccc',
            cursor: '#ffffff',
            selection: 'rgba(255, 255, 255, 0.3)',
            black: '#000000',
            red: '#cd3131',
            green: '#0dbc79',
            yellow: '#e5e510',
            blue: '#2472c8',
            magenta: '#bc3fbc',
            cyan: '#11a8cd',
            white: '#e5e5e5',
            brightBlack: '#666666',
            brightRed: '#f14c4c',
            brightGreen: '#23d18b',
            brightYellow: '#f5f543',
            brightBlue: '#3b8eea',
            brightMagenta: '#d670d6',
            brightCyan: '#29b8db',
            brightWhite: '#e5e5e5'
          },
          fontSize: 14,
          fontFamily: '"Fira Code", "Consolas", "Monaco", monospace',
          cursorBlink: true,
          scrollback: 10000,  // Increased for long outputs like package installations
          convertEol: true,
          allowTransparency: false,
          scrollOnUserInput: true,  // Auto-scroll on user input to follow commands
          fastScrollModifier: 'alt', // Alt+scroll for fast scrolling
          fastScrollSensitivity: 5,
          scrollSensitivity: 1,
          rows: 15,  // Set minimum visible rows (15 lines visible by default)
          cols: 80   // Set default columns
        });

        // Create addons
        const fitAddon = new FitAddon.FitAddon();
        const webLinksAddon = new WebLinksAddon.WebLinksAddon();

        // Load addons
        terminal.loadAddon(fitAddon);
        terminal.loadAddon(webLinksAddon);

        // Open terminal in container
        const xtermContainer = terminalContainer.querySelector('.xterm-container');
        terminal.open(xtermContainer);

        // Setup auto-scroll functionality
        this.setupAutoScroll(terminal);

        // Ensure proper sizing
        setTimeout(() => {
          fitAddon.fit();
          terminal.focus();
        }, 100);

        // Connect to terminal service
        let websocket;
        try {
          websocket = await this.connectToTerminalService(terminalId, terminal);
        } catch (error) {
          console.warn(`⚠️ Failed to connect to terminal service, creating local terminal`);
          websocket = null;
          // Show local terminal message
          terminal.write('\r\n\x1b[33m⚠️ Terminal service not available - Local terminal mode\x1b[0m\r\n');
          terminal.write('\x1b[36mTo enable full terminal functionality, start the terminal service:\x1b[0m\r\n');
          terminal.write('\x1b[36m  docker-compose up terminal\x1b[0m\r\n\r\n');
          terminal.write('\x1b[32mLocal terminal ready (limited functionality)\x1b[0m\r\n');
          terminal.write('$ ');

          // Setup local terminal input handler
          this.setupLocalTerminal(terminal);
        }

        // Store terminal data
        const terminalData = {
          id: terminalId,
          name: terminalName,
          terminal: terminal,
          fitAddon: fitAddon,
          container: terminalContainer,
          websocket: websocket,
          active: false
        };

        this.terminals.set(terminalId, terminalData);

        // Create terminal tab
        this.createTerminalTab(terminalId, terminalName);

        // Switch to new terminal
        this.switchToTerminal(terminalId);

        console.log(`✅ Terminal ${terminalId} created successfully`);

      } catch (error) {
        console.error(`❌ Failed to create terminal: ${error}`);
        this.showTerminalError(`Failed to create terminal: ${error.message}`);
      }
    }

    createTerminalContainer(terminalId) {
      console.log(`📦 Creating terminal container for: ${terminalId}`);

      const container = document.createElement('div');
      container.className = 'terminal-instance';
      container.id = `terminal-${terminalId}`;

      const xtermContainer = document.createElement('div');
      xtermContainer.className = 'xterm-container';

      container.appendChild(xtermContainer);

      const terminalContent = document.getElementById('terminal-content');
      if (terminalContent) {
        terminalContent.appendChild(container);
        console.log(`✅ Terminal container added to terminal-content`);
      } else {
        console.error(`❌ terminal-content element not found!`);
      }

      return container;
    }

    async connectToTerminalService(terminalId, terminal) {
      return new Promise((resolve, reject) => {
        const wsUrl = `ws://${this.terminalServiceUrl}/ws/terminal/${this.projectId}/${terminalId}`;
        console.log(`🔌 Connecting to terminal service: ${wsUrl}`);

        const websocket = new WebSocket(wsUrl);

        websocket.onopen = () => {
          console.log(`✅ Terminal WebSocket connected: ${terminalId}`);

          // Setup terminal input handler
          terminal.onData(data => {
            if (websocket.readyState === WebSocket.OPEN) {
              websocket.send(JSON.stringify({
                type: 'input',
                data: data
              }));
            }
          });

          // Setup terminal resize handler
          terminal.onResize(({ cols, rows }) => {
            if (websocket.readyState === WebSocket.OPEN) {
              websocket.send(JSON.stringify({
                type: 'resize',
                data: { cols, rows }
              }));
            }
          });

          resolve(websocket);
        };

        websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.type === 'output') {
              terminal.write(message.data);
              // Keep prompt visible for continuous output
              this.ensurePromptVisible(terminal);
            } else if (message.type === 'welcome') {
              terminal.write(message.data.message);
              this.ensurePromptVisible(terminal);
            } else if (message.type === 'error') {
              terminal.write(`\r\n\x1b[31m❌ ${message.data}\x1b[0m\r\n`);
              this.ensurePromptVisible(terminal);
            }
          } catch (e) {
            // Handle raw data
            terminal.write(event.data);
            // Keep prompt visible for any terminal output
            this.ensurePromptVisible(terminal);
          }
        };

        websocket.onerror = (error) => {
          console.error(`❌ Terminal WebSocket error:`, error);
          terminal.write('\r\n\x1b[31m❌ Connection error - Terminal service unavailable\x1b[0m\r\n');
          terminal.write('\x1b[33m💡 Make sure the terminal service is running:\x1b[0m\r\n');
          terminal.write('\x1b[36m   docker-compose up terminal\x1b[0m\r\n');
          terminal.write('\x1b[36m   curl http://localhost:8003/health\x1b[0m\r\n');
          reject(error);
        };

        websocket.onclose = () => {
          console.log(`🔌 Terminal WebSocket closed: ${terminalId}`);
          terminal.write('\r\n\x1b[33m⚠️ Connection closed\x1b[0m\r\n');

          // Attempt reconnection after delay
          setTimeout(() => {
            if (this.terminals.has(terminalId)) {
              console.log(`🔄 Attempting to reconnect terminal: ${terminalId}`);
              this.reconnectTerminal(terminalId);
            }
          }, 3000);
        };
      });
    }

    createTerminalTab(terminalId, terminalName) {
      const tab = document.createElement('div');
      tab.className = 'terminal-tab';
      tab.dataset.terminalId = terminalId;

      tab.innerHTML = `
        <span class="terminal-tab-label">${terminalName}</span>
        <span class="terminal-tab-close">×</span>
      `;

      // Tab click handler
      tab.addEventListener('click', (e) => {
        if (!e.target.classList.contains('terminal-tab-close')) {
          this.switchToTerminal(terminalId);
        }
      });

      // Close button handler
      tab.querySelector('.terminal-tab-close').addEventListener('click', (e) => {
        e.stopPropagation();
        this.closeTerminal(terminalId);
      });

      document.getElementById('terminal-tabs-container').appendChild(tab);
    }

    switchToTerminal(terminalId) {
      // Deactivate all terminals
      this.terminals.forEach((terminalData, id) => {
        terminalData.active = false;
        terminalData.container.classList.remove('active');

        // Remove active class from tab
        const tab = document.querySelector(`[data-terminal-id="${id}"]`);
        if (tab) tab.classList.remove('active');
      });

      // Activate selected terminal
      const terminalData = this.terminals.get(terminalId);
      if (terminalData) {
        terminalData.active = true;
        terminalData.container.classList.add('active');
        this.activeTerminalId = terminalId;

        // Add active class to tab
        const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (tab) tab.classList.add('active');

        // Fit and focus terminal
        setTimeout(() => {
          try {
            terminalData.fitAddon.fit();
            terminalData.terminal.focus();
            console.log(`🎯 Terminal ${terminalId} fitted and focused`);
          } catch (error) {
            console.error(`❌ Error fitting terminal ${terminalId}:`, error);
          }
        }, 150);

        console.log(`Switched to terminal: ${terminalId}`);
      }
    }

    closeTerminal(terminalId) {
      const terminalData = this.terminals.get(terminalId);
      if (!terminalData) return;

      // Don't close the last terminal
      if (this.terminals.size <= 1) {
        alert('Cannot close the last terminal');
        return;
      }

      console.log(`Closing terminal: ${terminalId}`);

      // Close WebSocket connection
      if (terminalData.websocket) {
        terminalData.websocket.close();
      }

      // Dispose terminal
      terminalData.terminal.dispose();

      // Remove container
      terminalData.container.remove();

      // Remove tab
      const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
      if (tab) tab.remove();

      // Remove from terminals map
      this.terminals.delete(terminalId);

      // Switch to another terminal if this was active
      if (this.activeTerminalId === terminalId) {
        const firstTerminalId = this.terminals.keys().next().value;
        if (firstTerminalId) {
          this.switchToTerminal(firstTerminalId);
        }
      }
    }

    clearActiveTerminal() {
      if (!this.activeTerminalId) return;

      const terminalData = this.terminals.get(this.activeTerminalId);
      if (terminalData) {
        terminalData.terminal.clear();
        terminalData.terminal.write('Terminal cleared\r\n');
        // Ensure we're at the top after clearing
        setTimeout(() => {
          this.scrollToBottom(terminalData.terminal);
        }, 10);
      }
    }

    resizeActiveTerminal() {
      if (!this.activeTerminalId) return;

      const terminalData = this.terminals.get(this.activeTerminalId);
      if (terminalData && terminalData.active) {
        setTimeout(() => {
          try {
            terminalData.fitAddon.fit();
            console.log(`📏 Terminal ${this.activeTerminalId} resized`);
          } catch (error) {
            console.error(`❌ Error resizing terminal:`, error);
          }
        }, 100);
      }
    }

    async reconnectTerminal(terminalId) {
      const terminalData = this.terminals.get(terminalId);
      if (!terminalData) return;

      try {
        terminalData.websocket = await this.connectToTerminalService(terminalId, terminalData.terminal);
        terminalData.terminal.write('\r\n\x1b[32m✅ Reconnected successfully\x1b[0m\r\n');
      } catch (error) {
        console.error(`Failed to reconnect terminal ${terminalId}:`, error);
        terminalData.terminal.write('\r\n\x1b[31m❌ Reconnection failed\x1b[0m\r\n');
      }
    }

    setupAutoScroll(terminal) {
      // Simple approach: Always keep the prompt visible at the bottom
      let userScrolledUp = false;
      let scrollTimeout = null;

      const viewport = terminal.element?.querySelector('.xterm-viewport');
      if (!viewport) return;

      // Monitor user scrolling
      viewport.addEventListener('scroll', () => {
        const isAtBottom = viewport.scrollTop >= (viewport.scrollHeight - viewport.clientHeight - 10);
        userScrolledUp = !isAtBottom;

        // If user scrolled up, give them 2 seconds to read, then auto-resume
        if (userScrolledUp) {
          if (scrollTimeout) clearTimeout(scrollTimeout);
          scrollTimeout = setTimeout(() => {
            userScrolledUp = false;
            this.ensurePromptVisible(terminal);
          }, 2000);
        }
      });

      // Override terminal write to always keep prompt visible
      const originalWrite = terminal.write.bind(terminal);
      terminal.write = (data) => {
        const result = originalWrite(data);

        // Always ensure prompt stays visible unless user is actively scrolling
        if (!userScrolledUp) {
          // Use immediate scroll to keep prompt visible
          this.ensurePromptVisible(terminal);
        }

        return result;
      };

      // On user input, always scroll to show prompt
      terminal.onData(() => {
        userScrolledUp = false;
        if (scrollTimeout) {
          clearTimeout(scrollTimeout);
          scrollTimeout = null;
        }

        // Immediate scroll on user input
        this.ensurePromptVisible(terminal);
      });
    }

    // New method to ensure prompt is always visible
    ensurePromptVisible(terminal) {
      try {
        const viewport = terminal.element?.querySelector('.xterm-viewport');
        if (!viewport) return;

        // Always scroll to bottom to keep prompt visible
        const maxScroll = viewport.scrollHeight - viewport.clientHeight;

        // Use xterm's built-in method first
        if (terminal && typeof terminal.scrollToBottom === 'function') {
          terminal.scrollToBottom();
        }

        // Also set scroll position directly
        viewport.scrollTop = maxScroll;

      } catch (error) {
        console.debug('Scroll failed:', error);
      }
    }

    scrollToBottomIfNeeded(terminal) {
      try {
        const viewport = terminal.element?.querySelector('.xterm-viewport');
        if (!viewport) return;

        // Check if we're already near the bottom (within 50px)
        const isNearBottom = viewport.scrollTop >= (viewport.scrollHeight - viewport.clientHeight - 50);

        // Only auto-scroll if we're near the bottom or if this is new content
        if (isNearBottom) {
          this.scrollToBottom(terminal);
        }
      } catch (error) {
        // Fallback: always scroll to bottom
        this.scrollToBottom(terminal);
      }
    }

    scrollToBottom(terminal) {
      try {
        // Get viewport
        const viewport = terminal.element?.querySelector('.xterm-viewport');
        if (!viewport) {
          console.debug('No viewport found for scrolling');
          return;
        }

        // Calculate scroll position
        const maxScroll = viewport.scrollHeight - viewport.clientHeight;

        // Use multiple methods to ensure reliable scrolling

        // Method 1: xterm's built-in scrollToBottom (most reliable for xterm)
        if (terminal && typeof terminal.scrollToBottom === 'function') {
          terminal.scrollToBottom();
        }

        // Method 2: Direct viewport scrolling (ensures we reach the very bottom)
        viewport.scrollTop = maxScroll;

        // Method 3: Use scrollTo for better browser compatibility
        viewport.scrollTo({
          top: maxScroll,
          behavior: 'auto' // Instant scroll for responsiveness
        });

        // Method 4: Fallback using scrollIntoView on the last element
        const lastElement = viewport.lastElementChild;
        if (lastElement) {
          lastElement.scrollIntoView({ block: 'end', behavior: 'auto' });
        }

        // Verify we actually scrolled to the bottom
        setTimeout(() => {
          const currentScroll = viewport.scrollTop;
          const newMaxScroll = viewport.scrollHeight - viewport.clientHeight;
          if (Math.abs(currentScroll - newMaxScroll) > 5) {
            // If we're not at the bottom, try one more time
            viewport.scrollTop = newMaxScroll;
          }
        }, 10);

      } catch (error) {
        console.debug('Auto-scroll failed:', error);
      }
    }

    setupLocalTerminal(terminal) {
      let currentLine = '';

      terminal.onData(data => {
        // Handle input in local mode
        if (data === '\r') {
          // Enter pressed
          terminal.write('\r\n');
          this.handleLocalCommand(terminal, currentLine.trim());
          currentLine = '';
          terminal.write('$ ');
        } else if (data === '\u007F') {
          // Backspace
          if (currentLine.length > 0) {
            currentLine = currentLine.slice(0, -1);
            terminal.write('\b \b');
          }
        } else if (data >= ' ') {
          // Printable character
          currentLine += data;
          terminal.write(data);
        }
      });
    }

    handleLocalCommand(terminal, command) {
      if (!command) return;

      // Simple local commands
      switch (command.toLowerCase()) {
        case 'help':
          terminal.write('Available commands:\r\n');
          terminal.write('  help     - Show this help\r\n');
          terminal.write('  clear    - Clear terminal\r\n');
          terminal.write('  echo     - Echo text\r\n');
          terminal.write('  date     - Show current date\r\n');
          terminal.write('  status   - Show terminal status\r\n');
          break;

        case 'clear':
          terminal.clear();
          return; // No need to scroll after clear

        case 'date':
          terminal.write(new Date().toString() + '\r\n');
          break;

        case 'status':
          terminal.write('Local terminal mode - Limited functionality\r\n');
          terminal.write('Start terminal service for full features\r\n');
          break;

        default:
          if (command.startsWith('echo ')) {
            terminal.write(command.substring(5) + '\r\n');
          } else {
            terminal.write(`Command not found: ${command}\r\n`);
            terminal.write('Type "help" for available commands\r\n');
          }
      }

      // Auto-scroll after command output
      setTimeout(() => this.scrollToBottom(terminal), 20);
    }

    getActiveTerminal() {
      if (!this.activeTerminalId) {
        // If no active terminal, try to get the first available terminal
        const firstTerminalId = this.terminals.keys().next().value;
        if (firstTerminalId) {
          this.switchToTerminal(firstTerminalId);
          return this.terminals.get(firstTerminalId);
        }
        return null;
      }

      return this.terminals.get(this.activeTerminalId);
    }

    executeCommand(command) {
      const activeTerminal = this.getActiveTerminal();
      if (activeTerminal && activeTerminal.websocket && activeTerminal.websocket.readyState === WebSocket.OPEN) {
        // Send command to the terminal via WebSocket
        activeTerminal.websocket.send(JSON.stringify({
          type: 'input',
          data: command + '\r'
        }));
        return true;
      } else if (activeTerminal && activeTerminal.terminal) {
        // Fallback: write to local terminal
        activeTerminal.terminal.write(command + '\r\n');
        this.handleLocalCommand(activeTerminal.terminal, command);
        return true;
      }
      return false;
    }

    showTerminalError(message) {
      console.error('Terminal Error:', message);
      // You could show a notification or error message in the UI here
    }
  }

  // Initialize terminal system when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    // Wait for other systems to initialize
    setTimeout(() => {
      console.log('🔍 Checking xterm.js availability...');
      console.log('Terminal:', typeof Terminal);
      console.log('FitAddon:', typeof FitAddon);
      console.log('WebLinksAddon:', typeof WebLinksAddon);

      if (typeof Terminal !== 'undefined' && typeof FitAddon !== 'undefined') {
        window.forgeXTerminal = new ForgeXTerminal();
        console.log('✅ ForgeX Terminal available globally');
      } else {
        console.error('❌ xterm.js not loaded properly');
        console.log('🔄 Attempting to load xterm.js fallback...');
        loadXtermFallback();
      }
    }, 2000); // Increased wait time
  });

  // Fallback xterm.js loader
  function loadXtermFallback() {
    console.log('📦 Loading xterm.js fallback from alternative CDN...');

    // Temporarily disable AMD to avoid conflicts
    const originalDefine = window.define;
    window.define = undefined;

    const scripts = [
      'https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.js',
      'https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.js',
      'https://cdn.jsdelivr.net/npm/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.js'
    ];

    let loadedCount = 0;

    scripts.forEach((src, index) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = () => {
        loadedCount++;
        console.log(`✅ Loaded: ${src}`);

        if (loadedCount === scripts.length) {
          // Restore AMD define
          window.define = originalDefine;

          setTimeout(() => {
            if (typeof Terminal !== 'undefined' && typeof FitAddon !== 'undefined') {
              window.forgeXTerminal = new ForgeXTerminal();
              console.log('✅ ForgeX Terminal loaded via fallback');
            } else {
              console.error('❌ Fallback loading failed');
              showTerminalLoadError();
            }
          }, 500);
        }
      };
      script.onerror = () => {
        console.error(`❌ Failed to load: ${src}`);
        // Restore AMD define on error too
        if (loadedCount === 0) {
          window.define = originalDefine;
        }
      };
      document.head.appendChild(script);
    });
  }

  // Show terminal load error
  function showTerminalLoadError() {
    const terminalContent = document.getElementById('terminal-content');
    if (terminalContent) {
      terminalContent.innerHTML = `
        <div style="padding: 20px; color: #ff6b6b; font-family: monospace;">
          <h3>❌ Terminal Loading Error</h3>
          <p>Failed to load xterm.js library. Please check your internet connection.</p>
          <p>You can try:</p>
          <ul>
            <li>Refreshing the page</li>
            <li>Checking your internet connection</li>
            <li>Disabling ad blockers temporarily</li>
          </ul>
          <button onclick="location.reload()" style="background: #007acc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
            🔄 Retry
          </button>
        </div>
      `;
    }
  }

  // ===== VERSION CONTROL FUNCTIONALITY =====

  class VersionControl {
    constructor() {
      this.currentBranch = 'main';
      this.initializeEventListeners();
      this.loadBranches();
    }

    initializeEventListeners() {
      // Panel tab switching
      document.getElementById('version-control-tab').addEventListener('click', () => {
        this.switchToVersionControlPanel();
      });

      // Version control buttons
      document.getElementById('commit-btn').addEventListener('click', () => {
        this.showCommitModal();
      });

      document.getElementById('history-btn').addEventListener('click', () => {
        this.showHistoryModal();
      });

      document.getElementById('branches-btn').addEventListener('click', () => {
        this.showBranchesModal();
      });

      document.getElementById('debug-vc-btn').addEventListener('click', () => {
        this.debugVersionControl();
      });

      // Commit modal
      document.getElementById('confirm-commit-btn').addEventListener('click', () => {
        this.createCommit();
      });

      document.getElementById('cancel-commit-btn').addEventListener('click', () => {
        this.hideCommitModal();
      });

      document.getElementById('close-commit-modal').addEventListener('click', () => {
        this.hideCommitModal();
      });

      // History modal
      document.getElementById('close-history-modal').addEventListener('click', () => {
        this.hideHistoryModal();
      });

      document.getElementById('refresh-history-btn').addEventListener('click', () => {
        this.loadCommitHistory();
      });

      // Close modals when clicking outside
      document.getElementById('commit-modal').addEventListener('click', (e) => {
        if (e.target.id === 'commit-modal') {
          this.hideCommitModal();
        }
      });

      document.getElementById('history-modal').addEventListener('click', (e) => {
        if (e.target.id === 'history-modal') {
          this.hideHistoryModal();
        }
      });
    }

    switchToVersionControlPanel() {
      // Hide other panels
      document.querySelectorAll('.panel-content').forEach(panel => {
        panel.classList.remove('active');
      });

      // Show version control panel
      document.getElementById('version-control-panel').classList.add('active');

      // Update tab states
      document.querySelectorAll('.panel-tab').forEach(tab => {
        tab.classList.remove('active');
      });
      document.getElementById('version-control-tab').classList.add('active');

      // Load current status
      this.updateStatus();
    }

    async updateStatus() {
      try {
        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/branches`);
        if (response.ok) {
          const data = await response.json();
          const defaultBranch = data.branches.find(b => b.is_default);
          if (defaultBranch) {
            this.currentBranch = defaultBranch.name;
            document.getElementById('current-branch').textContent = this.currentBranch;
          }
        }
      } catch (error) {
        console.error('Failed to update version control status:', error);
      }
    }

    async showCommitModal() {
      // Collect all open files and their content
      const filesToCommit = await this.collectFilesToCommit();

      if (filesToCommit.length === 0) {
        alert('No files to commit. Please open and modify some files first.');
        return;
      }

      // Populate commit files list
      const filesList = document.getElementById('commit-files-list');
      filesList.innerHTML = '';

      filesToCommit.forEach(file => {
        const fileItem = document.createElement('div');
        fileItem.className = 'commit-file-item';
        fileItem.innerHTML = `
          <i class="fas fa-file-code"></i>
          <span>${file.path}</span>
          <small>(${file.size} bytes)</small>
        `;
        filesList.appendChild(fileItem);
      });

      // Store files for commit
      this.pendingCommitFiles = filesToCommit;

      // Show modal
      document.getElementById('commit-modal').style.display = 'block';
      document.getElementById('commit-message').focus();
    }

    hideCommitModal() {
      document.getElementById('commit-modal').style.display = 'none';
      document.getElementById('commit-message').value = '';
      this.pendingCommitFiles = null;
    }

    async collectFilesToCommit() {
      const files = [];

      // Files and directories to ignore (like .gitignore)
      const ignoredPaths = [
        '.forgex_vc',
        '.git',
        '.gitignore',
        'node_modules',
        '__pycache__',
        '.DS_Store',
        'Thumbs.db',
        '.vscode',
        '.idea'
      ];

      // Check if a path should be ignored
      const shouldIgnore = (path) => {
        return ignoredPaths.some(ignored => {
          return path === ignored ||
                 path.startsWith(ignored + '/') ||
                 path.includes('/' + ignored + '/') ||
                 path.endsWith('/' + ignored);
        });
      };

      // Get all files from the file tree
      try {
        const response = await fetch(`${window.apiBaseUrl}/api/files/${window.projectHash}`);
        if (response.ok) {
          const fileTree = await response.json();

          // Recursively collect all files (excluding ignored ones)
          const collectFiles = (items) => {
            items.forEach(item => {
              // Skip ignored files and directories
              if (shouldIgnore(item.path)) {
                console.log(`🚫 Ignoring: ${item.path}`);
                return;
              }

              if (!item.isDirectory) {
                files.push({
                  path: item.path,
                  content: '', // Will be loaded when needed
                  size: 0
                });
              }
              if (item.children) {
                collectFiles(item.children);
              }
            });
          };

          collectFiles(fileTree);

          console.log(`📁 Collected ${files.length} files for commit (ignored version control and system files)`);

          // Load content for each file
          for (const file of files) {
            try {
              const fileResponse = await fetch(`${window.apiBaseUrl}/api/file/${window.projectHash}?path=${encodeURIComponent(file.path)}`);
              if (fileResponse.ok) {
                file.content = await fileResponse.text();
                file.size = new Blob([file.content]).size;
              }
            } catch (error) {
              console.warn(`Failed to load content for ${file.path}:`, error);
              file.content = '';
            }
          }
        }
      } catch (error) {
        console.error('Failed to collect files:', error);
      }

      return files;
    }

    async createCommit() {
      const message = document.getElementById('commit-message').value.trim();
      const branch = document.getElementById('commit-branch').value;

      if (!message) {
        alert('Please enter a commit message.');
        return;
      }

      if (!this.pendingCommitFiles || this.pendingCommitFiles.length === 0) {
        alert('No files to commit.');
        return;
      }

      try {
        const commitData = {
          message: message,
          branch_name: branch,
          author_username: '{{ user.username }}', // Django template variable
          files: this.pendingCommitFiles
        };

        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/commit`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(commitData)
        });

        if (response.ok) {
          const result = await response.json();
          alert(`Commit created successfully!\nHash: ${result.commit_hash.substring(0, 8)}\nFiles: ${result.files_changed}`);
          this.hideCommitModal();
          this.updateStatus();
        } else {
          const error = await response.json();
          alert(`Failed to create commit: ${error.detail}`);
        }
      } catch (error) {
        console.error('Error creating commit:', error);
        alert('Failed to create commit. Please try again.');
      }
    }

    async showHistoryModal() {
      document.getElementById('history-modal').style.display = 'block';
      await this.loadCommitHistory();
    }

    hideHistoryModal() {
      document.getElementById('history-modal').style.display = 'none';
    }

    async loadCommitHistory() {
      try {
        const branch = document.getElementById('history-branch-filter').value;
        const url = `${window.apiBaseUrl}/api/version-control/${window.projectHash}/history?branch=${branch}&limit=20`;

        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          this.displayCommitHistory(data.commits);
        } else {
          console.error('Failed to load commit history');
          document.getElementById('history-list').innerHTML = '<p>Failed to load commit history.</p>';
        }
      } catch (error) {
        console.error('Error loading commit history:', error);
        document.getElementById('history-list').innerHTML = '<p>Error loading commit history.</p>';
      }
    }

    displayCommitHistory(commits) {
      const historyList = document.getElementById('history-list');

      if (commits.length === 0) {
        historyList.innerHTML = '<p>No commits found.</p>';
        return;
      }

      historyList.innerHTML = commits.map(commit => `
        <div class="commit-item">
          <div class="commit-header">
            <span class="commit-hash">${commit.commit_hash.substring(0, 8)}</span>
            <div class="commit-meta">
              <span class="commit-author">${commit.author_username}</span>
              <span>${new Date(commit.created_at).toLocaleString()}</span>
            </div>
          </div>
          <div class="commit-message">${commit.message}</div>
          <div class="commit-meta">
            <span><i class="fas fa-code-branch"></i> ${commit.branch_name}</span>
            <span><i class="fas fa-file"></i> ${commit.files_changed} files</span>
          </div>
          <div class="commit-actions">
            <button onclick="versionControl.restoreCommit('${commit.commit_hash}')" class="restore-btn">
              <i class="fas fa-undo"></i> Restore
            </button>
            <button onclick="versionControl.viewCommitDiff('${commit.commit_hash}')">
              <i class="fas fa-eye"></i> View
            </button>
            <button onclick="versionControl.compareCommit('${commit.commit_hash}')">
              <i class="fas fa-code-compare"></i> Compare
            </button>
          </div>
        </div>
      `).join('');
    }

    async restoreCommit(commitHash) {
      if (!confirm(`Are you sure you want to restore to commit ${commitHash.substring(0, 8)}? This will overwrite current files.`)) {
        return;
      }

      try {
        console.log(`🔄 Restoring commit ${commitHash.substring(0, 8)}...`);

        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/restore/${commitHash}`, {
          method: 'POST'
        });

        if (response.ok) {
          const result = await response.json();
          console.log('✅ Restore response:', result);

          const restoreMessage = `Successfully restored to commit ${commitHash.substring(0, 8)}
Files restored: ${result.files_restored}
Files deleted: ${result.files_deleted || 0}
Total changes: ${(result.files_restored || 0) + (result.files_deleted || 0)}`;

          alert(restoreMessage);

          // Refresh file tree and reload current file using correct function names
          try {
            console.log('🔄 Refreshing file tree...');
            await fetchFileTree(); // Use the correct function name

            if (currentFile) {
              console.log(`🔄 Reloading current file: ${currentFile}`);
              await openFile(currentFile); // Use the correct function name
            }

            console.log('✅ File tree and current file refreshed successfully');
          } catch (refreshError) {
            console.warn('⚠️ Error refreshing UI after restore:', refreshError);
            // Don't show error to user since restore was successful
          }

          this.hideHistoryModal();
        } else {
          const error = await response.json();
          console.error('❌ Restore failed:', error);
          alert(`Failed to restore commit: ${error.detail}`);
        }
      } catch (error) {
        console.error('❌ Error restoring commit:', error);
        alert('Failed to restore commit. Please try again.');
      }
    }

    async viewCommitDiff(commitHash) {
      // This would open a diff viewer - for now just show an alert
      alert(`Diff viewer for commit ${commitHash.substring(0, 8)} - Feature coming soon!`);
    }

    async compareCommit(commitHash) {
      try {
        console.log(`🔍 Comparing commit ${commitHash.substring(0, 8)} with current state...`);

        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/compare/${commitHash}`);
        if (response.ok) {
          const comparison = await response.json();

          let compareMessage = `🔍 COMMIT COMPARISON

Commit: ${comparison.commit_hash.substring(0, 8)}
Message: ${comparison.commit_message}

📊 DIFFERENCES (${comparison.differences.length}):`;

          if (comparison.differences.length === 0) {
            compareMessage += '\n✅ No differences - project matches commit exactly!';
          } else {
            comparison.differences.forEach(diff => {
              compareMessage += `\n\n📄 ${diff.file}`;

              if (diff.type === 'modified') {
                compareMessage += ` (MODIFIED)`;
                compareMessage += `\n  Commit: ${diff.commit_size} chars - ${diff.commit_preview}`;
                compareMessage += `\n  Current: ${diff.current_size} chars - ${diff.current_preview}`;
              } else if (diff.type === 'deleted_from_current') {
                compareMessage += ` (DELETED FROM CURRENT)`;
                compareMessage += `\n  Was in commit: ${diff.commit_size} chars - ${diff.commit_preview}`;
              } else if (diff.type === 'added_to_current') {
                compareMessage += ` (ADDED TO CURRENT)`;
                compareMessage += `\n  Current: ${diff.current_size} chars - ${diff.current_preview}`;
              }
            });
          }

          console.log('🔍 Comparison result:', comparison);
          alert(compareMessage);
        } else {
          const error = await response.json();
          alert(`Comparison failed: ${error.detail}`);
        }
      } catch (error) {
        console.error('🔍 Comparison error:', error);
        alert('Comparison failed. Check console for details.');
      }
    }

    async loadBranches() {
      try {
        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/branches`);
        if (response.ok) {
          const data = await response.json();

          // Update branch selectors
          const commitBranchSelect = document.getElementById('commit-branch');
          const historyBranchSelect = document.getElementById('history-branch-filter');

          // Clear existing options (except "All Branches" for history)
          commitBranchSelect.innerHTML = '';
          historyBranchSelect.innerHTML = '<option value="">All Branches</option>';

          data.branches.forEach(branch => {
            const option1 = document.createElement('option');
            option1.value = branch.name;
            option1.textContent = branch.name;
            if (branch.is_default) option1.selected = true;
            commitBranchSelect.appendChild(option1);

            const option2 = document.createElement('option');
            option2.value = branch.name;
            option2.textContent = branch.name;
            historyBranchSelect.appendChild(option2);
          });
        }
      } catch (error) {
        console.error('Failed to load branches:', error);
      }
    }

    showBranchesModal() {
      // For now, just show current branch info
      alert(`Current branch: ${this.currentBranch}\n\nBranch management features coming soon!`);
    }

    async debugVersionControl() {
      try {
        console.log('🐛 Running version control debug...');

        const response = await fetch(`${window.apiBaseUrl}/api/version-control/${window.projectHash}/debug`);
        if (response.ok) {
          const debugInfo = await response.json();

          let debugMessage = `🐛 VERSION CONTROL DEBUG INFO

📁 Project ID: ${debugInfo.project_id}
📂 Project Path: ${debugInfo.project_path}
🗂️ VC Directory Exists: ${debugInfo.vc_dir_exists}

📝 Commits (${debugInfo.commits.length}):`;

          if (debugInfo.commits.length > 0) {
            debugInfo.commits.forEach(commit => {
              debugMessage += `\n  • ${commit.hash} - ${commit.message}`;
              debugMessage += `\n    Files: ${commit.files_count}`;
              if (commit.files && commit.files.length > 0) {
                commit.files.forEach(file => {
                  debugMessage += `\n      - ${file.path} (${file.size} chars)`;
                  if (file.content_preview) {
                    debugMessage += `\n        Preview: ${file.content_preview.substring(0, 50)}...`;
                  }
                });
              }
            });
          } else {
            debugMessage += '\n  (none)';
          }

          debugMessage += `\n\n🌿 Branches (${debugInfo.branches.length}):
${debugInfo.branches.map(b => `  • ${b}`).join('\n') || '  (none)'}

📄 Current Project Files (${debugInfo.project_files.length}):
${debugInfo.project_files.slice(0, 5).map(f => `  • ${f}`).join('\n')}
${debugInfo.project_files.length > 5 ? `  ... and ${debugInfo.project_files.length - 5} more` : ''}`;

          if (debugInfo.current_file_contents && Object.keys(debugInfo.current_file_contents).length > 0) {
            debugMessage += '\n\n📖 Current File Contents:';
            Object.entries(debugInfo.current_file_contents).forEach(([path, info]) => {
              debugMessage += `\n  • ${path} (${info.size || 0} chars)`;
              if (info.preview) {
                debugMessage += `\n    Preview: ${info.preview.substring(0, 100)}...`;
              }
            });
          }

          console.log('🐛 Debug info:', debugInfo);
          alert(debugMessage);
        } else {
          const error = await response.json();
          alert(`Debug failed: ${error.detail}`);
        }
      } catch (error) {
        console.error('🐛 Debug error:', error);
        alert('Debug failed. Check console for details.');
      }
    }
  }

  // Initialize version control when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    // Wait for other systems to initialize
    setTimeout(() => {
      window.versionControl = new VersionControl();
      console.log('✅ Version Control system initialized');
    }, 1000);
  });

</script>

{% endblock %}
