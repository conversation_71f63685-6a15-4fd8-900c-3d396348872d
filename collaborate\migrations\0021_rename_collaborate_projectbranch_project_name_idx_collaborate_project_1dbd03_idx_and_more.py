# Generated by Django 5.2.1 on 2025-06-04 06:36

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0020_add_version_control_models"),
    ]

    operations = [
        migrations.RenameIndex(
            model_name="projectbranch",
            new_name="collaborate_project_1dbd03_idx",
            old_name="collaborate_projectbranch_project_name_idx",
        ),
        migrations.RenameIndex(
            model_name="projectbranch",
            new_name="collaborate_project_7e471e_idx",
            old_name="collaborate_projectbranch_project_default_idx",
        ),
        migrations.RenameIndex(
            model_name="projectcommit",
            new_name="collaborate_project_3ce5d5_idx",
            old_name="collaborate_projectcommit_project_created_idx",
        ),
        migrations.RenameIndex(
            model_name="projectcommit",
            new_name="collaborate_project_78c0be_idx",
            old_name="collaborate_projectcommit_project_branch_created_idx",
        ),
        migrations.RenameIndex(
            model_name="projectcommit",
            new_name="collaborate_commit__0ba75b_idx",
            old_name="collaborate_projectcommit_commit_hash_idx",
        ),
        migrations.RenameIndex(
            model_name="projectfileversion",
            new_name="collaborate_commit__f14ea8_idx",
            old_name="collaborate_projectfileversion_commit_path_idx",
        ),
        migrations.RenameIndex(
            model_name="projectfileversion",
            new_name="collaborate_content_aa2c19_idx",
            old_name="collaborate_projectfileversion_content_hash_idx",
        ),
        migrations.RenameIndex(
            model_name="projectfileversion",
            new_name="collaborate_file_pa_448e08_idx",
            old_name="collaborate_projectfileversion_path_created_idx",
        ),
    ]
