{% extends "base.html" %}
{% block title %}Create Project{% endblock %}
{% block content %}

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
  <h1>Create New Project</h1>

  {% if messages %}
  <div class="messages">
    {% for message in messages %}
    <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
      {{ message }}
    </div>
    {% endfor %}
  </div>
  {% endif %}

  <form method="post" class="mt-4">
    {% csrf_token %}
    {{ form.non_field_errors }}

    {% for field in form %}
      {% if field.name != 'required_languages' and field.name != 'critical_skills_input' and field.name != 'required_skills_input' %}
      <div class="form-group mb-4">
          {% if field.name == 'type' %}
              {% if user.is_superuser %}
                  {{ field }}
              {% else %}
                  <input type="hidden" name="type" value="userproject">
              {% endif %}
          {% else %}
              <label for="{{ field.id_for_label }}" class="form-label fw-bold">{{ field.label }}:</label>
              {{ field }}
          {% endif %}
          {% if field.help_text %}
          <small class="form-text text-muted">{{ field.help_text }}</small>
          {% endif %}

          {% for error in field.errors %}
          <div class="text-danger">{{ error }}</div>
          {% endfor %}
      </div>
      {% endif %}
    {% endfor %}

    <!-- Required Skills Input -->
    <div class="form-group mb-4">
      <label for="{{ form.required_skills_input.id_for_label }}" class="form-label fw-bold">Required Skills:</label>
      {{ form.required_skills_input }}
      <small class="form-text text-muted">
        Enter all skills required for your project. Our AI will match you with teammates who have these skills.
      </small>
    </div>

    <!-- Critical Skills Input -->
    <div class="form-group mb-4">
      <label for="{{ form.critical_skills_input.id_for_label }}" class="form-label fw-bold">Critical Skills:</label>
      {{ form.critical_skills_input }}
      <small class="form-text text-muted">
        Mark which of the required skills are critical for your project. These will have higher priority during team matching.
      </small>
      <div class="alert alert-info mt-2">
        <i class="fas fa-info-circle"></i> Critical skills are given double weight in the matching algorithm. Users matching critical skills will be preferred over those with only regular skills.
      </div>
    </div>

    <div class="d-grid">
        <button type="submit" class="btn btn-primary btn-lg">Create Project</button>
    </div>
  </form>
  </div>
</section>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // When required skills change, update the available options for critical skills
    const requiredSkillsInput = document.getElementById('id_required_skills_input');
    const criticalSkillsInput = document.getElementById('id_critical_skills_input');

    // Initialize tagify for required skills
    var requiredSkillsTagify = new Tagify(requiredSkillsInput);

    // Initialize tagify for critical skills with whitelist
    var criticalSkillsTagify = new Tagify(criticalSkillsInput, {
      whitelist: [],
      enforceWhitelist: true
    });

    // Update critical skills whitelist when required skills change
    requiredSkillsTagify.on('change', function(e) {
      const requiredSkills = e.detail.value;
      var skills = [];

      // Parse the JSON string to get the skill names
      try {
        const parsedSkills = JSON.parse(requiredSkills);
        skills = parsedSkills.map(item => item.value);
      } catch (error) {
        // If parsing fails, try splitting by comma (old format)
        skills = requiredSkills.split(',').map(s => s.trim()).filter(s => s);
      }

      // Update critical skills whitelist
      criticalSkillsTagify.settings.whitelist = skills;

      // Remove any critical skills that are no longer in the required skills
      var currentCriticalSkills = criticalSkillsTagify.value;
      if (currentCriticalSkills && currentCriticalSkills.length) {
        criticalSkillsTagify.value = currentCriticalSkills.filter(item =>
          skills.includes(item.value)
        );
      }
    });
  });
</script>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);
});
</script>

<style>
/* Custom styles for tagify inputs */
#id_required_skills_input,
#id_critical_skills_input {
  width: 100%;
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: #fff;
  margin-bottom: 10px;
  min-height: 50px;
  font-size: 1rem;
  resize: vertical;
}

#id_required_skills_input:focus,
#id_critical_skills_input:focus {
  border-color: var(--color-border);
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  outline: none;
}

/* Bootstrap compatibility classes */
.d-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 1.25rem;
}
</style>
{% endblock %}