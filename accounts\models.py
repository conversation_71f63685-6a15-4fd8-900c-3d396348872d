from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
import pytz
import json
from collaborate.models import Skill as CollaborateSkill

# Define timezone choices
timezone_choices = [(tz, tz) for tz in pytz.common_timezones]

class Skill(models.Model):
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    bio = models.TextField(max_length=500, blank=True)
    description = models.TextField(blank=True, null=True)
    skills = models.ManyToManyField(Skill, blank=True)
    cv = models.FileField(upload_to='user_cvs/', null=True, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pics/', null=True, blank=True)
    social_profile_picture_url = models.URLField(max_length=500, blank=True, null=True, help_text="Profile picture URL from social login")
    timezone = models.Char<PERSON>ield(max_length=100, choices=timezone_choices, default='UTC')
    availability_start = models.TimeField(blank=True, null=True)
    availability_end = models.TimeField(blank=True, null=True)

    AVAILABILITY_CHOICES = (
        ('Full Time', 'Full Time'),
        ('Part Time', 'Part Time'),
        ('Weekends Only', 'Weekends Only'),
        ('Flexible', 'Flexible'),
    )
    availability_type = models.CharField(max_length=50, choices=AVAILABILITY_CHOICES, default='Flexible')
    country = models.CharField(max_length=100, blank=True)

    # Additional profile fields
    phone_number = models.CharField(max_length=20, blank=True, help_text="Your contact phone number")
    linkedin_url = models.URLField(max_length=500, blank=True, help_text="Your LinkedIn profile URL")
    github_url = models.URLField(max_length=500, blank=True, help_text="Your GitHub profile URL")
    professional_title = models.CharField(max_length=100, blank=True, help_text="Your current job title or role")
    website_url = models.URLField(max_length=500, blank=True, help_text="Your personal website or portfolio")

    # Profile completion tracking
    profile_completed = models.BooleanField(default=False, help_text="Whether the user has completed their profile setup")
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s profile"

    def get_skills_list(self):
        return [skill.name for skill in self.skills.all()]

    def get_skills_with_levels(self):
        """Get user skills with their proficiency levels from UserSkill model"""
        from collaborate.models import UserSkill
        user_skills = UserSkill.objects.filter(user=self.user).select_related('skill')
        return [(us.skill, us.get_proficiency_display(), us.proficiency) for us in user_skills]

    def add_skill_with_level(self, skill_name, level):
        """Add a skill with proficiency level to the user's profile"""
        from collaborate.models import UserSkill, Skill as CollaborateSkill
        from accounts.models import Skill as AccountsSkill

        # First, ensure the skill exists in the collaborate app's Skill model
        collab_skill, created = CollaborateSkill.objects.get_or_create(name=skill_name)

        # Then link it to the user with the specified level
        UserSkill.objects.get_or_create(
            user=self.user,
            skill=collab_skill,
            defaults={'proficiency': level}
        )

        # Also add to the legacy skills field for backward compatibility
        accounts_skill, created = AccountsSkill.objects.get_or_create(name=skill_name)
        self.skills.add(accounts_skill)

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    instance.profile.save()


class Event(models.Model):
    """Model for system events that can be created by superusers"""
    title = models.CharField(max_length=200)
    description = models.TextField()
    event_date = models.DateTimeField()
    location = models.CharField(max_length=300, blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_events')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-event_date']

    def __str__(self):
        return f"{self.title} - {self.event_date.strftime('%Y-%m-%d %H:%M')}"

    def is_upcoming(self):
        """Check if the event is in the future"""
        return self.event_date > timezone.now()

    def is_past(self):
        """Check if the event is in the past"""
        return self.event_date < timezone.now()


class SupportCategory(models.Model):
    """Categories for support tickets"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['name']
        verbose_name_plural = "Support Categories"

    def __str__(self):
        return self.name


class SupportTicket(models.Model):
    """Support tickets for help center"""
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('waiting_user', 'Waiting for User'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    ticket_id = models.CharField(max_length=20, unique=True, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_tickets')
    category = models.ForeignKey(SupportCategory, on_delete=models.SET_NULL, null=True)
    subject = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='open')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tickets')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"#{self.ticket_id} - {self.subject}"

    def save(self, *args, **kwargs):
        if not self.ticket_id:
            # Generate unique ticket ID
            import random
            import string
            while True:
                ticket_id = 'TK' + ''.join(random.choices(string.digits, k=8))
                if not SupportTicket.objects.filter(ticket_id=ticket_id).exists():
                    self.ticket_id = ticket_id
                    break
        super().save(*args, **kwargs)


class SupportMessage(models.Model):
    """Messages in support ticket conversations"""
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    is_internal = models.BooleanField(default=False)  # Internal notes for staff
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message in {self.ticket.ticket_id} by {self.sender.username}"


class SupportAttachment(models.Model):
    """File attachments for support tickets"""
    ticket = models.ForeignKey(SupportTicket, on_delete=models.CASCADE, related_name='attachments')
    message = models.ForeignKey(SupportMessage, on_delete=models.CASCADE, related_name='attachments', null=True, blank=True)
    file = models.FileField(upload_to='support_attachments/')
    filename = models.CharField(max_length=255)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    uploaded_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Attachment: {self.filename} for {self.ticket.ticket_id}"


# Security Models

class SecurityLog(models.Model):
    """Log security events and threats"""
    EVENT_TYPES = [
        ('devtools_open', 'Developer Tools Opened'),
        ('console_access', 'Console Access Attempt'),
        ('script_injection', 'Script Injection Detected'),
        ('api_abuse', 'API Abuse Detected'),
        ('rate_limit_exceeded', 'Rate Limit Exceeded'),
        ('invalid_token', 'Invalid Token Attempt'),
        ('security_threat', 'General Security Threat'),
        ('login_attempt', 'Login Attempt'),
        ('permission_denied', 'Permission Denied'),
        ('data_access', 'Sensitive Data Access'),
        ('settings_change', 'Security Settings Changed'),
        ('other', 'Other Security Event')
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='security_logs')
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    event_data = models.JSONField(default=dict, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    timestamp = models.DateTimeField(default=timezone.now)
    severity = models.CharField(max_length=20, choices=[
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical')
    ], default='medium')
    resolved = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_event_type_display()} at {self.timestamp}"


class SecuritySettings(models.Model):
    """Global security settings for the application"""
    obfuscation_enabled = models.BooleanField(default=True)
    encryption_enabled = models.BooleanField(default=True)
    devtools_protection = models.BooleanField(default=True)
    console_protection = models.BooleanField(default=True)
    rate_limiting_enabled = models.BooleanField(default=True)
    max_requests_per_minute = models.IntegerField(default=100)
    security_logging_level = models.CharField(max_length=20, choices=[
        ('minimal', 'Minimal'),
        ('standard', 'Standard'),
        ('verbose', 'Verbose'),
        ('debug', 'Debug')
    ], default='standard')

    # API Security
    api_encryption_required = models.BooleanField(default=True)
    api_token_expiry_minutes = models.IntegerField(default=60)

    # Session Security
    session_timeout_minutes = models.IntegerField(default=60)
    force_https = models.BooleanField(default=True)

    # Content Security
    disable_right_click = models.BooleanField(default=True)
    disable_text_selection = models.BooleanField(default=False)
    disable_keyboard_shortcuts = models.BooleanField(default=True)

    # Monitoring
    monitor_api_calls = models.BooleanField(default=True)
    monitor_file_access = models.BooleanField(default=True)
    monitor_user_behavior = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        verbose_name = "Security Settings"
        verbose_name_plural = "Security Settings"

    def __str__(self):
        return f"Security Settings (Updated: {self.updated_at})"

    @classmethod
    def get_current_settings(cls):
        """Get the current security settings, creating defaults if none exist"""
        settings, created = cls.objects.get_or_create(
            id=1,  # Singleton pattern
            defaults={
                'obfuscation_enabled': True,
                'encryption_enabled': True,
                'devtools_protection': True,
                'console_protection': True,
                'rate_limiting_enabled': True,
            }
        )
        return settings


class UserSecurityProfile(models.Model):
    """Extended security profile for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='security_profile')

    # Security Clearance
    security_clearance = models.CharField(max_length=20, choices=[
        ('user', 'Regular User'),
        ('developer', 'Developer'),
        ('tester', 'Tester'),
        ('admin', 'Administrator'),
        ('security_admin', 'Security Administrator')
    ], default='user')

    # Access Control
    can_access_debug_mode = models.BooleanField(default=False)
    can_view_source_code = models.BooleanField(default=False)
    can_access_api_docs = models.BooleanField(default=False)
    can_modify_security_settings = models.BooleanField(default=False)

    # Security Monitoring
    failed_login_attempts = models.IntegerField(default=0)
    last_failed_login = models.DateTimeField(null=True, blank=True)
    account_locked_until = models.DateTimeField(null=True, blank=True)

    # Session Management
    max_concurrent_sessions = models.IntegerField(default=3)
    force_password_change = models.BooleanField(default=False)
    password_last_changed = models.DateTimeField(null=True, blank=True)

    # Audit Trail
    last_security_review = models.DateTimeField(null=True, blank=True)
    security_notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - Security Profile ({self.get_security_clearance_display()})"

    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def can_debug(self):
        """Check if user can access debug features"""
        return (self.can_access_debug_mode or
                self.user.is_superuser or
                self.user.is_staff or
                self.security_clearance in ['developer', 'tester', 'admin', 'security_admin'])

    def reset_failed_attempts(self):
        """Reset failed login attempts"""
        self.failed_login_attempts = 0
        self.last_failed_login = None
        self.account_locked_until = None
        self.save()

    def record_failed_login(self):
        """Record a failed login attempt"""
        self.failed_login_attempts += 1
        self.last_failed_login = timezone.now()

        # Lock account after 5 failed attempts for 15 minutes
        if self.failed_login_attempts >= 5:
            self.account_locked_until = timezone.now() + timezone.timedelta(minutes=15)

        self.save()


@receiver(post_save, sender=User)
def create_user_security_profile(sender, instance, created, **kwargs):
    """Create security profile when user is created"""
    if created:
        UserSecurityProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_user_security_profile(sender, instance, **kwargs):
    """Save security profile when user is saved"""
    if hasattr(instance, 'security_profile'):
        instance.security_profile.save()


# Threat Management Models

class SecurityWarning(models.Model):
    """Security warnings sent to users by administrators"""
    SEVERITY_CHOICES = [
        ('warning', 'Warning'),
        ('final_warning', 'Final Warning'),
        ('critical', 'Critical'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('delivered', 'Delivered'),
        ('acknowledged', 'Acknowledged'),
        ('dismissed', 'Dismissed'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='security_warnings')
    admin = models.ForeignKey(User, on_delete=models.CASCADE, related_name='warnings_sent')
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='warning')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    title = models.CharField(max_length=200)
    message = models.TextField()
    template_used = models.CharField(max_length=100, blank=True)

    # Related security event
    security_log = models.ForeignKey(SecurityLog, on_delete=models.SET_NULL, null=True, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    delivered_at = models.DateTimeField(null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Additional data
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    additional_data = models.JSONField(default=dict, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['admin', 'created_at']),
            models.Index(fields=['severity', 'status']),
        ]

    def __str__(self):
        return f"{self.get_severity_display()} for {self.user.username} - {self.title}"

    def mark_delivered(self):
        """Mark warning as delivered"""
        self.status = 'delivered'
        self.delivered_at = timezone.now()
        self.save()

    def mark_acknowledged(self):
        """Mark warning as acknowledged by user"""
        self.status = 'acknowledged'
        self.acknowledged_at = timezone.now()
        self.save()

    def is_expired(self):
        """Check if warning has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class IPBlock(models.Model):
    """IP address blocking system"""
    BLOCK_TYPE_CHOICES = [
        ('temporary', 'Temporary'),
        ('permanent', 'Permanent'),
    ]

    REASON_CHOICES = [
        ('security_violation', 'Security Violation'),
        ('repeated_warnings', 'Repeated Warnings Ignored'),
        ('malicious_activity', 'Malicious Activity'),
        ('spam', 'Spam/Abuse'),
        ('manual_block', 'Manual Block'),
        ('other', 'Other'),
    ]

    ip_address = models.GenericIPAddressField(unique=True)
    block_type = models.CharField(max_length=20, choices=BLOCK_TYPE_CHOICES, default='temporary')
    reason = models.CharField(max_length=50, choices=REASON_CHOICES, default='security_violation')
    custom_reason = models.TextField(blank=True)

    # Admin who created the block
    admin = models.ForeignKey(User, on_delete=models.CASCADE, related_name='ip_blocks_created')

    # Related user (if known)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='ip_blocks')

    # Related security events
    security_logs = models.ManyToManyField(SecurityLog, blank=True, related_name='ip_blocks')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    last_attempt_at = models.DateTimeField(null=True, blank=True)

    # Statistics
    attempt_count = models.IntegerField(default=0)

    # Status
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['ip_address', 'is_active']),
            models.Index(fields=['admin', 'created_at']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        return f"IP Block: {self.ip_address} ({self.get_block_type_display()})"

    def is_expired(self):
        """Check if block has expired"""
        if self.block_type == 'permanent':
            return False
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    def is_blocked(self):
        """Check if IP is currently blocked"""
        return self.is_active and not self.is_expired()

    def record_attempt(self):
        """Record a blocked access attempt"""
        self.attempt_count += 1
        self.last_attempt_at = timezone.now()
        self.save()


class UserThreatLevel(models.Model):
    """Track user threat levels based on security violations"""
    THREAT_LEVELS = [
        ('green', 'Green - Low Risk'),
        ('yellow', 'Yellow - Medium Risk'),
        ('red', 'Red - High Risk'),
        ('critical', 'Critical - Immediate Threat'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='threat_level')
    level = models.CharField(max_length=20, choices=THREAT_LEVELS, default='green')

    # Violation counts
    total_violations = models.IntegerField(default=0)
    warnings_received = models.IntegerField(default=0)
    critical_events = models.IntegerField(default=0)

    # Timestamps
    last_violation = models.DateTimeField(null=True, blank=True)
    last_escalation = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Additional data
    notes = models.TextField(blank=True)
    auto_calculated = models.BooleanField(default=True)

    class Meta:
        ordering = ['-last_violation']

    def __str__(self):
        return f"{self.user.username} - {self.get_level_display()}"

    def calculate_threat_level(self):
        """Auto-calculate threat level based on violations"""
        if self.critical_events >= 3:
            self.level = 'critical'
        elif self.total_violations >= 10 or self.warnings_received >= 3:
            self.level = 'red'
        elif self.total_violations >= 5 or self.warnings_received >= 1:
            self.level = 'yellow'
        else:
            self.level = 'green'

        self.updated_at = timezone.now()
        if self.auto_calculated:
            self.save()

    def escalate_threat_level(self):
        """Manually escalate threat level"""
        level_order = ['green', 'yellow', 'red', 'critical']
        current_index = level_order.index(self.level)
        if current_index < len(level_order) - 1:
            self.level = level_order[current_index + 1]
            self.last_escalation = timezone.now()
            self.save()


class SecurityAction(models.Model):
    """Log all security actions taken by administrators"""
    ACTION_TYPES = [
        ('warning_sent', 'Warning Sent'),
        ('ip_blocked', 'IP Address Blocked'),
        ('ip_unblocked', 'IP Address Unblocked'),
        ('threat_level_changed', 'Threat Level Changed'),
        ('user_suspended', 'User Suspended'),
        ('user_unsuspended', 'User Unsuspended'),
        ('security_review', 'Security Review'),
        ('other', 'Other Action'),
    ]

    admin = models.ForeignKey(User, on_delete=models.CASCADE, related_name='security_actions')
    target_user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='security_actions_received')
    action_type = models.CharField(max_length=30, choices=ACTION_TYPES)

    # Related objects
    security_warning = models.ForeignKey(SecurityWarning, on_delete=models.SET_NULL, null=True, blank=True)
    ip_block = models.ForeignKey(IPBlock, on_delete=models.SET_NULL, null=True, blank=True)
    security_log = models.ForeignKey(SecurityLog, on_delete=models.SET_NULL, null=True, blank=True)

    # Action details
    description = models.TextField()
    reason = models.TextField(blank=True)
    additional_data = models.JSONField(default=dict, blank=True)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)

    # Context
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['admin', 'created_at']),
            models.Index(fields=['target_user', 'action_type']),
            models.Index(fields=['action_type', 'created_at']),
        ]

    def __str__(self):
        target = self.target_user.username if self.target_user else "System"
        return f"{self.admin.username} -> {target}: {self.get_action_type_display()}"


# Signal handlers for threat management
@receiver(post_save, sender=SecurityLog)
def update_threat_level_on_violation(sender, instance, created, **kwargs):
    """Update user threat level when security violation occurs"""
    if created and instance.user:
        threat_level, created = UserThreatLevel.objects.get_or_create(
            user=instance.user,
            defaults={'level': 'green'}
        )

        threat_level.total_violations += 1
        threat_level.last_violation = timezone.now()

        if instance.severity in ['high', 'critical']:
            threat_level.critical_events += 1

        threat_level.calculate_threat_level()


@receiver(post_save, sender=User)
def create_user_threat_level(sender, instance, created, **kwargs):
    """Create threat level when user is created"""
    if created:
        UserThreatLevel.objects.create(user=instance)
