/**
 * ForgeX Secure API Client
 * Handles all API communications with built-in security features
 */

import CryptoJS from 'crypto-js';

class SecureAPIClient {
  constructor() {
    this.baseURL = '';
    this.endpoints = new Map();
    this.requestQueue = [];
    this.rateLimiter = new Map();
    this.encryptionEnabled = true;
    this.obfuscationEnabled = true;
    
    this.init();
  }
  
  async init() {
    // Wait for security system to initialize
    if (window.ForgeXSecurity) {
      this.encryptionEnabled = window.ForgeXSecurity.isSecureMode();
      this.obfuscationEnabled = window.ForgeXSecurity.isSecureMode();
    }
    
    // Initialize endpoint mappings
    this.initializeEndpoints();
    
    // Set up request interceptors
    this.setupInterceptors();
    
    console.log('🔗 Secure API Client initialized');
  }
  
  initializeEndpoints() {
    // Define real endpoints (will be obfuscated in production)
    const realEndpoints = {
      // User & Authentication
      'auth.login': '/accounts/login/',
      'auth.logout': '/accounts/logout/',
      'auth.profile': '/accounts/api/profile/',
      'auth.role': '/accounts/api/user-role/',
      
      // Learn Module
      'learn.courses': '/learn/api/courses/',
      'learn.lessons': '/learn/api/lessons/',
      'learn.progress': '/learn/api/progress/',
      'learn.ai-chat': '/learn/ai/chat/',
      'learn.analyze-code': '/learn/ai/analyze-code/',
      
      // Collaborate Module
      'collab.projects': '/collaborate/api/projects/',
      'collab.files': '/collaborate/api/files/',
      'collab.chat': '/collaborate/api/chat/',
      'collab.notifications': '/collaborate/api/notifications/',
      
      // Mentorship Module
      'mentor.list': '/mentorship/api/mentors/',
      'mentor.sessions': '/mentorship/api/sessions/',
      'mentor.book': '/mentorship/api/book/',
      'mentor.chat': '/mentorship/api/chat/',
      
      // FastAPI Endpoints
      'fastapi.files': '/api/files/',
      'fastapi.execute': '/api/execute/',
      'fastapi.terminal': '/api/terminal/',
      'fastapi.yjs': '/api/yjs/'
    };
    
    // Generate obfuscated endpoints for production
    Object.entries(realEndpoints).forEach(([key, endpoint]) => {
      if (this.obfuscationEnabled) {
        this.endpoints.set(key, this.obfuscateEndpoint(endpoint));
      } else {
        this.endpoints.set(key, endpoint);
      }
    });
  }
  
  obfuscateEndpoint(endpoint) {
    // Create time-based obfuscation that changes every 10 minutes
    const timeSlot = Math.floor(Date.now() / (1000 * 60 * 10));
    const hash = CryptoJS.MD5(endpoint + timeSlot + 'forgex-salt').toString().substring(0, 12);
    
    // Create obfuscated path
    const segments = endpoint.split('/').filter(s => s);
    const obfuscatedSegments = segments.map(segment => {
      if (segment === 'api') return 'x';
      return CryptoJS.MD5(segment + hash).toString().substring(0, 8);
    });
    
    return '/' + obfuscatedSegments.join('/') + '/';
  }
  
  setupInterceptors() {
    // Store original fetch
    this.originalFetch = window.fetch;
    
    // Override fetch with security features
    window.fetch = this.secureRequest.bind(this);
  }
  
  async secureRequest(url, options = {}) {
    // Check rate limiting
    if (!this.checkRateLimit(url)) {
      throw new Error('Rate limit exceeded');
    }
    
    // Apply security transformations
    const secureOptions = await this.applySecurityTransforms(url, options);
    const secureURL = this.transformURL(url);
    
    // Add security headers
    secureOptions.headers = {
      ...secureOptions.headers,
      'X-Requested-With': 'XMLHttpRequest',
      'X-Client-Version': '2.0.0',
      'X-Security-Token': this.generateSecurityToken()
    };
    
    try {
      // Make the actual request
      const response = await this.originalFetch(secureURL, secureOptions);
      
      // Process response
      return await this.processSecureResponse(response, url);
    } catch (error) {
      this.handleRequestError(error, url);
      throw error;
    }
  }
  
  checkRateLimit(url) {
    const now = Date.now();
    const key = this.getRateLimitKey(url);
    
    if (!this.rateLimiter.has(key)) {
      this.rateLimiter.set(key, { count: 1, resetTime: now + 60000 });
      return true;
    }
    
    const limit = this.rateLimiter.get(key);
    
    if (now > limit.resetTime) {
      // Reset the limit
      this.rateLimiter.set(key, { count: 1, resetTime: now + 60000 });
      return true;
    }
    
    if (limit.count >= 100) { // 100 requests per minute
      return false;
    }
    
    limit.count++;
    return true;
  }
  
  getRateLimitKey(url) {
    // Extract endpoint pattern for rate limiting
    const urlObj = new URL(url, window.location.origin);
    return urlObj.pathname.split('/').slice(0, 3).join('/');
  }
  
  async applySecurityTransforms(url, options) {
    const transformedOptions = { ...options };
    
    // Encrypt request body if needed
    if (this.encryptionEnabled && this.shouldEncryptRequest(url)) {
      transformedOptions.body = await this.encryptRequestBody(options.body);
      transformedOptions.headers = {
        ...transformedOptions.headers,
        'Content-Type': 'application/json',
        'X-Encrypted': 'true'
      };
    }
    
    // Add anti-CSRF token
    const csrfToken = this.getCSRFToken();
    if (csrfToken) {
      transformedOptions.headers = {
        ...transformedOptions.headers,
        'X-CSRFToken': csrfToken
      };
    }
    
    return transformedOptions;
  }
  
  transformURL(url) {
    // Convert endpoint keys to actual URLs
    for (const [key, endpoint] of this.endpoints.entries()) {
      if (url.includes(key)) {
        return url.replace(key, endpoint);
      }
    }
    
    // If it's already a full URL, apply obfuscation if needed
    if (this.obfuscationEnabled && this.shouldObfuscateURL(url)) {
      return this.obfuscateEndpoint(url);
    }
    
    return url;
  }
  
  shouldEncryptRequest(url) {
    const sensitivePatterns = [
      '/api/files/',
      '/api/chat/',
      '/api/profile/',
      '/api/sessions/',
      '/accounts/',
      '/learn/ai/',
      '/collaborate/api/',
      '/mentorship/api/'
    ];
    
    return sensitivePatterns.some(pattern => url.includes(pattern));
  }
  
  shouldObfuscateURL(url) {
    return url.includes('/api/') || url.includes('/accounts/') || 
           url.includes('/learn/') || url.includes('/collaborate/') || 
           url.includes('/mentorship/');
  }
  
  async encryptRequestBody(body) {
    if (!body) return body;
    
    let dataToEncrypt;
    if (typeof body === 'string') {
      dataToEncrypt = body;
    } else if (body instanceof FormData) {
      // Convert FormData to JSON for encryption
      const formObject = {};
      for (const [key, value] of body.entries()) {
        formObject[key] = value;
      }
      dataToEncrypt = JSON.stringify(formObject);
    } else {
      dataToEncrypt = JSON.stringify(body);
    }
    
    const encrypted = CryptoJS.AES.encrypt(dataToEncrypt, this.getEncryptionKey()).toString();
    return JSON.stringify({ 
      encrypted_data: encrypted,
      timestamp: Date.now()
    });
  }
  
  async processSecureResponse(response, originalURL) {
    // Clone response for processing
    const clonedResponse = response.clone();
    
    try {
      // Check if response is encrypted
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const data = await clonedResponse.json();
        
        if (data.encrypted_data) {
          // Decrypt the response
          const decrypted = this.decryptResponseData(data.encrypted_data);
          
          return new Response(decrypted, {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers
          });
        }
      }
    } catch (error) {
      // If processing fails, return original response
      console.warn('Failed to process secure response:', error);
    }
    
    return response;
  }
  
  decryptResponseData(encryptedData) {
    try {
      const decrypted = CryptoJS.AES.decrypt(encryptedData, this.getEncryptionKey());
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Failed to decrypt response data:', error);
      return '{}';
    }
  }
  
  getEncryptionKey() {
    // Use session-based encryption key
    if (window.ForgeXSecurity) {
      return window.ForgeXSecurity.getEncryptionKey();
    }
    
    // Fallback key generation
    let sessionKey = sessionStorage.getItem('forgex_session_key');
    if (!sessionKey) {
      sessionKey = CryptoJS.lib.WordArray.random(32).toString();
      sessionStorage.setItem('forgex_session_key', sessionKey);
    }
    return sessionKey;
  }
  
  getCSRFToken() {
    // Get CSRF token from cookie or meta tag
    const csrfCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('csrftoken='));
    
    if (csrfCookie) {
      return csrfCookie.split('=')[1];
    }
    
    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
    if (csrfMeta) {
      return csrfMeta.getAttribute('content');
    }
    
    return null;
  }
  
  generateSecurityToken() {
    // Generate a time-based security token
    const timestamp = Math.floor(Date.now() / 1000);
    const userAgent = navigator.userAgent;
    const origin = window.location.origin;
    
    return CryptoJS.MD5(`${timestamp}${userAgent}${origin}forgex`).toString().substring(0, 16);
  }
  
  handleRequestError(error, url) {
    // Log security-related errors
    if (window.ForgeXSecurity) {
      window.ForgeXSecurity.logSecurityEvent(`API Error: ${error.message} for ${url}`);
    }
    
    console.error('Secure API request failed:', error);
  }
  
  // Public API methods
  async get(endpoint, options = {}) {
    return this.secureRequest(endpoint, {
      method: 'GET',
      ...options
    });
  }
  
  async post(endpoint, data, options = {}) {
    return this.secureRequest(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
  }
  
  async put(endpoint, data, options = {}) {
    return this.secureRequest(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });
  }
  
  async delete(endpoint, options = {}) {
    return this.secureRequest(endpoint, {
      method: 'DELETE',
      ...options
    });
  }
  
  // Utility methods
  getEndpoint(key) {
    return this.endpoints.get(key) || key;
  }
  
  isSecureMode() {
    return this.encryptionEnabled && this.obfuscationEnabled;
  }
  
  // Admin methods (only work in debug mode)
  debugEndpoints() {
    if (window.ForgeXSecurity && window.ForgeXSecurity.canShowDebugInfo()) {
      console.table(Array.from(this.endpoints.entries()));
    }
  }
  
  debugRateLimits() {
    if (window.ForgeXSecurity && window.ForgeXSecurity.canShowDebugInfo()) {
      console.table(Array.from(this.rateLimiter.entries()));
    }
  }
}

// Initialize secure API client
const apiClient = new SecureAPIClient();

// Export for global use
window.SecureAPIClient = apiClient;
export default apiClient;
