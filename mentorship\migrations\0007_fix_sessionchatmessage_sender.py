# Generated manually to fix SessionChatMessage sender field

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('mentorship', '0006_sessionchatmessage_sessionchatattachment_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sessionchatmessage',
            name='sender',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
    ]
