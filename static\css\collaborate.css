/* Collaborate App CSS with Parallax Effects and Animations */

/* Base styles for collaborate pages */
.collaborate-page {
  position: relative;
  z-index: 1;
  padding: 50px 0;
  min-height: 100vh;
}

/* Particles background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
}

/* Card styling for collaborate pages */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.8);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(192, 255, 107, 0.3);
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.collaborate-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.2);
}

/* Heading styles */
.collaborate-card h1, .collaborate-card h2 {
  color: #ffffff;
  font-size: 32px;
  margin-bottom: 20px;
  position: relative;
}

.collaborate-card h1:after, .collaborate-card h2:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.collaborate-card:hover h1:after, .collaborate-card:hover h2:after {
  width: 100px;
}

/* Project list styling */
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.project-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.project-card.owned {
  border-left: 5px solid var(--color-border);
}

.project-card.joined {
  border-left: 5px solid #4CAF50;
}

.project-header {
  margin-bottom: 15px;
}

.project-header h3 {
  color: var(--color-border);
  font-size: 20px;
  margin-bottom: 5px;
}

.project-body {
  flex: 1;
  margin-bottom: 20px;
}

.project-body p {
  margin-bottom: 10px;
  line-height: 1.5;
}

.project-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

/* Button styling */
.btn {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid var(--color-border);
  border-radius: 30px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn:hover {
  background-color: var(--color-border);
  color: #000000 !important;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
  transform: translateY(-3px);
  text-decoration: none;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.btn:hover:before {
  left: 100%;
}

.btn-primary {
  background-color: rgba(192, 255, 107, 0.2);
  border-color: var(--color-border);
  color: var(--color-border);
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-danger {
  background-color: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
}

.btn-danger:hover {
  background-color: #f44336;
  color: #ffffff !important;
  box-shadow: 0 0 15px rgba(244, 67, 54, 0.5);
}

.btn-success {
  background-color: rgba(76, 175, 80, 0.2);
  border-color: #4CAF50;
  color: #4CAF50;
}

.btn-success:hover {
  background-color: #4CAF50;
  color: #ffffff !important;
  box-shadow: 0 0 15px rgba(76, 175, 80, 0.5);
}

.btn-warning {
  background-color: rgba(255, 193, 7, 0.2);
  border-color: #FFC107;
  color: #FFC107;
}

.btn-warning:hover {
  background-color: #FFC107;
  color: #000000 !important;
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.5);
}

.btn-info {
  background-color: rgba(33, 150, 243, 0.2);
  border-color: #2196F3;
  color: #2196F3;
}

.btn-info:hover {
  background-color: #2196F3;
  color: #ffffff !important;
  box-shadow: 0 0 15px rgba(33, 150, 243, 0.5);
}

/* Form styling */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #ffffff;
}

.form-control {
  width: 100%;
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 15px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--color-border);
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  outline: none;
}

.form-control:hover {
  border-color: rgba(192, 255, 107, 0.5);
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-right {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-left {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Project Detail Page Styling */
.project-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
  margin-bottom: 30px;
}

.description, .requirements, .project-skills, .meta {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.description:hover, .requirements:hover, .project-skills:hover, .meta:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.description h3, .requirements h3, .project-skills h3 {
  color: var(--color-border);
  font-size: 20px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.3);
}

.requirements h4 {
  color: var(--color-border);
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
}

.requirements-grid {
  display: grid;
  gap: 15px;
  margin-top: 15px;
}

.requirement-section {
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.description p, .requirements p, .project-skills p, .meta p {
  margin-bottom: 12px;
  line-height: 1.6;
  color: #e0e0e0;
}

.project-skills ul {
  list-style-type: none;
  padding-left: 0;
  margin-bottom: 20px;
}

.project-skills li {
  background-color: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 20px;
  padding: 6px 15px;
  margin-bottom: 8px;
  display: inline-block;
  margin-right: 8px;
  color: var(--color-border);
  font-size: 14px;
  transition: all 0.3s ease;
}

.project-skills li:hover {
  background-color: rgba(192, 255, 107, 0.2);
  transform: translateY(-2px);
}

.meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 15px 20px;
}

.meta p {
  margin: 0;
  padding: 5px 0;
  font-size: 14px;
  color: #b0b0b0;
}

.ai-pairing-buttons {
  margin: 30px 0;
}

.ai-pairing-buttons h3 {
  color: var(--color-border);
  font-size: 20px;
  margin-bottom: 15px;
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

/* Storage Usage Styling */
.storage-usage-container {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  margin: 25px 0;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.storage-usage-container label {
  display: block;
  color: var(--color-border);
  font-weight: 600;
  margin-bottom: 10px;
}

.storage-bar {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}

.used-bar {
  height: 100%;
  background: linear-gradient(90deg, rgba(192, 255, 107, 0.8), rgba(192, 255, 107, 0.4));
  border-radius: 5px;
}

.storage-label {
  font-size: 14px;
  color: #b0b0b0;
  margin: 5px 0;
}

.upgrade-link {
  color: var(--color-border);
  text-decoration: underline;
  margin-left: 5px;
  transition: all 0.3s ease;
}

.upgrade-link:hover {
  color: #ffffff;
  text-decoration: none;
}

/* Application Status Styling */
.application-status {
  margin: 25px 0;
}

.status-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border-left: 4px solid;
}

.status-card.status-pending {
  border-left-color: #FFC107;
}

.status-card.status-rejected {
  border-left-color: #f44336;
}

.status-icon {
  font-size: 24px;
  margin-right: 10px;
}

.status-date {
  font-size: 14px;
  color: #b0b0b0;
  margin-top: 10px;
}

.status-note {
  font-style: italic;
  color: #f44336;
  margin-top: 10px;
}

.application-form {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

/* Course List Specific Styles */

/* Messages */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  margin-bottom: 15px;
  border-radius: 10px;
  border-left: 4px solid;
  position: relative;
  animation: slideDown 0.5s ease-out;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-left-color: #4CAF50;
  color: #4CAF50;
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.1);
  border-left-color: #2196F3;
  color: #2196F3;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left-color: #FFC107;
  color: #FFC107;
}

.alert-error {
  background-color: rgba(244, 67, 54, 0.1);
  border-left-color: #f44336;
  color: #f44336;
}

.alert-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.alert-close {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.alert-close:hover {
  opacity: 1;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-down {
  animation: slideDown 0.5s ease-out;
}

.welcome-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px 0;
}

.welcome-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: glow 2s ease-in-out infinite alternate;
}

.welcome-subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  margin: 0;
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(192, 255, 107, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(192, 255, 107, 0.8);
  }
}

/* Course Stats */
.course-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  min-width: 120px;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: var(--color-border);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #b0b0b0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Admin Controls */
.admin-controls {
  text-align: center;
  margin-bottom: 30px;
}

/* Course Grid */
.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.course-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.4s ease;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.course-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.course-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.course-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.course-card:hover .course-image {
  transform: scale(1.1);
}

.course-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.2), rgba(160, 224, 102, 0.2));
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 3rem;
  opacity: 0.7;
}

.course-overlay {
  position: absolute;
  top: 15px;
  right: 15px;
  background-color: rgba(192, 255, 107, 0.9);
  color: #000;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.course-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.course-title {
  font-size: 1.4rem;
  color: var(--color-border);
  margin-bottom: 10px;
  font-weight: 600;
  line-height: 1.3;
}

.course-description {
  color: #e0e0e0;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.course-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.course-level,
.course-duration {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: #b0b0b0;
}

.course-actions {
  margin-top: auto;
}

.course-enroll-btn {
  width: 100%;
  margin-bottom: 15px;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.admin-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.btn-sm {
  padding: 8px 15px;
  font-size: 0.85rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #b0b0b0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.7;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 10px;
  color: #ffffff;
}

.empty-state p {
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* Animation Classes */
.slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Icon styling */
.icon {
  font-style: normal;
  font-size: 1em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .courses-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .course-stats {
    gap: 20px;
  }

  .stat-item {
    min-width: 100px;
    padding: 15px;
  }

  .welcome-header h1 {
    font-size: 2rem;
  }

  .course-meta {
    flex-direction: column;
    gap: 10px;
  }

  .admin-actions {
    flex-direction: column;
  }
}

/* Action Buttons Container */
.action-buttons-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

.action-buttons-container .btn {
  min-width: 150px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .collaborate-card {
    padding: 20px;
    margin: 0 15px;
  }

  .project-grid {
    grid-template-columns: 1fr;
  }

  .btn-group {
    flex-direction: column;
  }

  .action-buttons-container {
    flex-direction: column;
  }

  .action-buttons-container .btn {
    width: 100%;
  }
}
