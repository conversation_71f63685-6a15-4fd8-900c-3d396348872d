from django.core.management.base import BaseCommand
from collaborate.models import Skill, UserSkill
from django.db.models import Count
from django.db import transaction

class Command(BaseCommand):
    help = 'Deduplicate skills in the database by merging skills with the same name (case-insensitive)'

    def handle(self, *args, **kwargs):
        # Find duplicate skills (case-insensitive)
        duplicate_names = (
            Skill.objects.values('name')
            .annotate(name_count=Count('name'))
            .filter(name_count__gt=1)
            .values_list('name', flat=True)
        )
        
        # Find all skills with the same name (case-insensitive)
        case_insensitive_duplicates = {}
        for skill in Skill.objects.all():
            skill_lower = skill.name.lower()
            if skill_lower not in case_insensitive_duplicates:
                case_insensitive_duplicates[skill_lower] = []
            case_insensitive_duplicates[skill_lower].append(skill)
        
        # Filter to only include those with duplicates
        case_insensitive_duplicates = {
            name: skills for name, skills in case_insensitive_duplicates.items()
            if len(skills) > 1
        }
        
        if not case_insensitive_duplicates:
            self.stdout.write(self.style.SUCCESS('No duplicate skills found.'))
            return
        
        self.stdout.write(f'Found {len(case_insensitive_duplicates)} duplicate skill names.')
        
        # Process each set of duplicates
        for name_lower, skills in case_insensitive_duplicates.items():
            self.stdout.write(f'Processing duplicates for "{name_lower}":')
            for skill in skills:
                self.stdout.write(f'  - ID: {skill.id}, Name: "{skill.name}"')
            
            # Keep the first skill and merge the rest into it
            primary_skill = skills[0]
            duplicate_skills = skills[1:]
            
            with transaction.atomic():
                # Update all UserSkill references to point to the primary skill
                for dup_skill in duplicate_skills:
                    # Update UserSkill references
                    UserSkill.objects.filter(skill=dup_skill).update(skill=primary_skill)
                    
                    # Update any other references (add more as needed)
                    # For example, if skills are used in projects:
                    # Project.objects.filter(required_skills=dup_skill).update(required_skills=primary_skill)
                    
                    # Delete the duplicate skill
                    dup_skill.delete()
                    self.stdout.write(self.style.SUCCESS(f'  - Merged skill ID {dup_skill.id} into ID {primary_skill.id}'))
        
        self.stdout.write(self.style.SUCCESS('Skill deduplication complete.'))
