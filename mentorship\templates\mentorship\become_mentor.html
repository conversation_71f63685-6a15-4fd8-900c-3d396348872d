{% extends 'base.html' %}
{% load static %}

{% block title %}Become a Mentor - ForgeX{% endblock %}

{% block content %}
<div class="become-mentor-page">
    <!-- Hero Section -->
    <section class="mentor-hero">
        <div class="container">
            <h1>Become a Mentor</h1>
            <p>Share your expertise, help others grow, and earn money doing what you love</p>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="mentor-benefits">
        <div class="container">
            <h2>Why Become a Mentor?</h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3>Earn Money</h3>
                    <p>Set your own hourly rate and earn 85% of session fees. Top mentors earn $50-200+ per hour.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3>Make an Impact</h3>
                    <p>Help aspiring developers overcome challenges and accelerate their learning journey.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Flexible Schedule</h3>
                    <p>Set your own availability and work when it's convenient for you.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Build Network</h3>
                    <p>Connect with talented developers and expand your professional network.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Form -->
    <section class="mentor-application">
        <div class="container">
            <div class="application-form">
                <h2>Apply to Become a Mentor</h2>
                <p>Fill out the form below to start your mentorship journey</p>

                <form id="mentorApplicationForm">
                    {% csrf_token %}
                    <div class="form-group">
                        <label for="bio">Tell us about yourself</label>
                        <textarea id="bio" name="bio" rows="6"
                                  placeholder="Describe your experience, expertise, and what you can help learners with..."
                                  required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="hourlyRate">Hourly Rate (USD)</label>
                            <input type="number" id="hourlyRate" name="hourly_rate"
                                   min="5" max="500" step="0.01" value="25.00" required>
                            <small>Recommended: $25-75 for new mentors</small>
                        </div>

                        <div class="form-group">
                            <label for="specializations">Your Specializations</label>
                            <select id="specializations" name="specializations" multiple required>
                                {% for skill in all_skills %}
                                    <option value="{{ skill.id }}">{{ skill.name }}</option>
                                {% endfor %}
                            </select>
                            <small>Hold Ctrl/Cmd to select multiple skills</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Availability (Optional - you can set this later)</label>
                        <div class="availability-grid">
                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="monday" value="1">
                                    Monday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="monday_start" value="09:00">
                                    <span>to</span>
                                    <input type="time" name="monday_end" value="17:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="tuesday" value="1">
                                    Tuesday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="tuesday_start" value="09:00">
                                    <span>to</span>
                                    <input type="time" name="tuesday_end" value="17:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="wednesday" value="1">
                                    Wednesday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="wednesday_start" value="09:00">
                                    <span>to</span>
                                    <input type="time" name="wednesday_end" value="17:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="thursday" value="1">
                                    Thursday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="thursday_start" value="09:00">
                                    <span>to</span>
                                    <input type="time" name="thursday_end" value="17:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="friday" value="1">
                                    Friday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="friday_start" value="09:00">
                                    <span>to</span>
                                    <input type="time" name="friday_end" value="17:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="saturday" value="1">
                                    Saturday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="saturday_start" value="10:00">
                                    <span>to</span>
                                    <input type="time" name="saturday_end" value="16:00">
                                </div>
                            </div>

                            <div class="day-availability">
                                <label>
                                    <input type="checkbox" name="sunday" value="1">
                                    Sunday
                                </label>
                                <div class="time-inputs">
                                    <input type="time" name="sunday_start" value="10:00">
                                    <span>to</span>
                                    <input type="time" name="sunday_end" value="16:00">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary btn-large">
                            Submit Application
                        </button>
                        <a href="{% url 'mentorship:marketplace' %}" class="btn btn-secondary">
                            Back to Marketplace
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<style>
.become-mentor-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.mentor-hero {
    background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 4rem 0;
    text-align: center;
}

.mentor-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.mentor-hero p {
    font-size: 1.3rem;
    opacity: 0.8;
}

.mentor-benefits {
    padding: 4rem 0;
}

.mentor-benefits h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #C0ff6b;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.benefit-card {
    background: rgba(255,255,255,0.05);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(192,255,107,0.2);
    transition: all 0.3s ease;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(192,255,107,0.2);
    border-color: rgba(192,255,107,0.5);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
}

.benefit-icon i {
    font-size: 2rem;
    color: #1a1a1a;
}

.benefit-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #C0ff6b;
}

.benefit-card p {
    line-height: 1.6;
    opacity: 0.9;
}

.mentor-application {
    padding: 4rem 0;
    background: rgba(255,255,255,0.02);
}

.application-form {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255,255,255,0.05);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(192,255,107,0.2);
}

.application-form h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #C0ff6b;
}

.application-form > p {
    text-align: center;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.form-group {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 15px rgba(192,255,107,0.4);
    background: rgba(255,255,255,0.15);
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    opacity: 0.7;
    font-size: 0.9rem;
}

.availability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.day-availability {
    background: rgba(255,255,255,0.02);
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid rgba(192,255,107,0.1);
}

.day-availability label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.day-availability input[type="checkbox"] {
    width: auto;
}

.time-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-inputs input {
    flex: 1;
    padding: 0.5rem;
    font-size: 0.9rem;
}

.time-inputs span {
    font-size: 0.9rem;
    opacity: 0.7;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 3rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    color: #C0ff6b;
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .mentor-hero h1 {
        font-size: 2.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .availability-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>

<script>
document.getElementById('mentorApplicationForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = {
        bio: formData.get('bio'),
        hourly_rate: formData.get('hourly_rate'),
        specializations: Array.from(document.getElementById('specializations').selectedOptions).map(option => option.value),
        available_slots: []
    };

    // Collect availability data
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    days.forEach(day => {
        const checkbox = document.querySelector(`input[name="${day}"]`);
        if (checkbox && checkbox.checked) {
            const start = document.querySelector(`input[name="${day}_start"]`).value;
            const end = document.querySelector(`input[name="${day}_end"]`).value;
            data.available_slots.push({
                day: day.charAt(0).toUpperCase() + day.slice(1),
                start: start,
                end: end
            });
        }
    });

    console.log('Submitting data:', data);

    try {
        const response = await fetch('{% url "mentorship:become_mentor" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(data)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('Response result:', result);

        if (result.success) {
            alert('Application submitted successfully! Welcome to the ForgeX mentor community.');
            window.location.href = result.redirect_url;
        } else {
            alert('Error: ' + (result.error || 'Unknown error occurred'));
        }
    } catch (error) {
        console.error('Full error details:', error);
        alert('An error occurred. Please check the console for details and try again.');
    }
});
</script>
{% endblock %}
