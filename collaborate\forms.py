from django import forms
from .models import Project, ProjectMarketplacePost, MarketplaceApplication, ProjectComment
from django.core.exceptions import ValidationError

class ProjectForm(forms.ModelForm):
    required_languages = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'tag-input',
            'placeholder': 'Add languages...',
            'data-options': ','.join([lang[0] for lang in Project.LANGUAGE_CHOICES]),
        })
    )
    
    required_skills_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'tag-input',
            'placeholder': 'Add skills...',
            'id': 'id_required_skills_input',
        })
    )
    
    critical_skills_input = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'tag-input',
            'placeholder': 'Mark critical skills...',
            'id': 'id_critical_skills_input',
        })
    )
    
    class Meta:
        model = Project
        fields = ['title', 'description',  'team_size']  # Removed 'matching_approach'
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
         
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If we have an instance with existing skills, prepare them for the tag input
        if self.instance.pk and hasattr(self.instance, 'required_skills'):
            existing_skills = ','.join([skill.name for skill in self.instance.required_skills.all()])
            self.fields['required_skills_input'].initial = existing_skills
            
        # If we have an instance with existing critical skills, prepare them for the tag input
        if self.instance.pk and hasattr(self.instance, 'critical_skills'):
            existing_critical_skills = ','.join([skill.name for skill in self.instance.critical_skills.all()])
            self.fields['critical_skills_input'].initial = existing_critical_skills

    def clean_required_languages(self):
        data = self.cleaned_data.get('required_languages', '')
        if data:
            # Split the input by commas and strip whitespace
            return [lang.strip() for lang in data.split(',') if lang.strip()]
        return []
        
    def clean(self):
        cleaned_data = super().clean()
        # Convert the skills text input to actual skill objects during clean
        skills_input = cleaned_data.get('required_skills_input', '')
        if skills_input:
            # Will be handled in the view by using the skills text input
            # to set the many-to-many relationship
            pass
        return cleaned_data

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.ai_matching_enabled = True  # Enforce AI matching enabled
        if commit:
            instance.save()
            self.save_m2m()
        return instance


class PublishToMarketplaceForm(forms.ModelForm):
    """Form for publishing a project to the marketplace"""

    class Meta:
        model = ProjectMarketplacePost
        fields = ['featured_image', 'showcase_description', 'tags']
        widgets = {
            'featured_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'showcase_description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Describe what makes your project special and what kind of collaborators you\'re looking for...'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., web development, machine learning, mobile app, startup'
            })
        }

    def clean_featured_image(self):
        image = self.cleaned_data.get('featured_image')
        if image:
            # Check file size (max 5MB)
            if image.size > 5 * 1024 * 1024:
                raise ValidationError("Image file too large ( > 5MB )")

            # Check file type
            if not image.content_type.startswith('image/'):
                raise ValidationError("File must be an image")

        return image

    def clean_tags(self):
        tags = self.cleaned_data.get('tags', '')
        if tags:
            # Split by comma and clean up
            tag_list = [tag.strip().lower() for tag in tags.split(',') if tag.strip()]
            # Limit to 10 tags
            if len(tag_list) > 10:
                raise ValidationError("Maximum 10 tags allowed")
            return ', '.join(tag_list)
        return tags


class MarketplaceApplicationForm(forms.ModelForm):
    """Form for applying to join a project from marketplace"""

    class Meta:
        model = MarketplaceApplication
        fields = ['message', 'skills_offered', 'portfolio_link']
        widgets = {
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Tell the project owner why you want to join this project and what you can contribute...'
            }),
            'skills_offered': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'List the skills and experience you can bring to this project...'
            }),
            'portfolio_link': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://your-portfolio.com or GitHub profile'
            })
        }

    def clean_message(self):
        message = self.cleaned_data.get('message', '')
        if len(message.strip()) < 20:
            raise ValidationError("Please provide a more detailed message (at least 20 characters)")
        return message


class ProjectCommentForm(forms.ModelForm):
    """Form for commenting on marketplace posts"""

    class Meta:
        model = ProjectComment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Share your thoughts about this project...'
            })
        }

    def clean_content(self):
        content = self.cleaned_data.get('content', '')
        if len(content.strip()) < 10:
            raise ValidationError("Comment must be at least 10 characters long")
        return content