"""
Threat Management Views for ForgeX Security System
Handles security warnings, IP blocking, and threat response workflows
"""

import json
import logging
from datetime import datetime, timedelta
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.models import User
from django.http import JsonResponse, HttpResponseForbidden
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import (
    SecurityLog, SecurityWarning, IPBlock, UserThreatLevel, 
    SecurityAction, UserSecurityProfile
)

logger = logging.getLogger(__name__)

# Helper function to check if user is superuser
def is_superuser(user):
    return user.is_superuser

# Predefined warning templates
WARNING_TEMPLATES = {
    'devtools_detected': {
        'title': 'Developer Tools Access Detected',
        'message': 'We detected that you attempted to access browser developer tools. This is against our security policy. Please refrain from using developer tools while using ForgeX.',
        'severity': 'warning'
    },
    'console_access': {
        'title': 'Console Access Attempt',
        'message': 'An attempt to access the browser console was detected from your account. Console access is restricted for security reasons.',
        'severity': 'warning'
    },
    'repeated_violations': {
        'title': 'Repeated Security Violations',
        'message': 'Multiple security violations have been detected from your account. Continued violations may result in account restrictions.',
        'severity': 'final_warning'
    },
    'malicious_activity': {
        'title': 'Suspicious Activity Detected',
        'message': 'Potentially malicious activity has been detected from your account. Your access is being monitored.',
        'severity': 'critical'
    },
    'final_warning': {
        'title': 'Final Warning - Account at Risk',
        'message': 'This is your final warning. Any additional security violations will result in immediate account suspension.',
        'severity': 'final_warning'
    }
}

@user_passes_test(is_superuser)
def threat_management_dashboard(request):
    """Main threat management dashboard for superusers"""
    
    # Get recent security events
    recent_events = SecurityLog.objects.select_related('user').order_by('-timestamp')[:20]
    
    # Get pending warnings
    pending_warnings = SecurityWarning.objects.filter(status='pending').count()
    
    # Get active IP blocks
    active_blocks = IPBlock.objects.filter(is_active=True).count()
    
    # Get high-risk users
    high_risk_users = UserThreatLevel.objects.filter(
        level__in=['red', 'critical']
    ).select_related('user').order_by('-last_violation')[:10]
    
    # Get recent actions
    recent_actions = SecurityAction.objects.select_related(
        'admin', 'target_user'
    ).order_by('-created_at')[:15]
    
    # Statistics
    stats = {
        'total_events_today': SecurityLog.objects.filter(
            timestamp__gte=timezone.now().replace(hour=0, minute=0, second=0)
        ).count(),
        'warnings_sent_today': SecurityWarning.objects.filter(
            created_at__gte=timezone.now().replace(hour=0, minute=0, second=0)
        ).count(),
        'active_blocks': active_blocks,
        'pending_warnings': pending_warnings,
        'high_risk_users': high_risk_users.count(),
    }
    
    context = {
        'recent_events': recent_events,
        'high_risk_users': high_risk_users,
        'recent_actions': recent_actions,
        'stats': stats,
        'warning_templates': WARNING_TEMPLATES,
    }
    
    return render(request, 'accounts/threat_management/dashboard.html', context)

@user_passes_test(is_superuser)
def security_events_list(request):
    """List all security events with filtering and actions"""
    
    # Get filter parameters
    event_type = request.GET.get('event_type')
    severity = request.GET.get('severity')
    user_filter = request.GET.get('user')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    # Build query
    events = SecurityLog.objects.select_related('user').order_by('-timestamp')
    
    if event_type:
        events = events.filter(event_type=event_type)
    if severity:
        events = events.filter(severity=severity)
    if user_filter:
        events = events.filter(user__username__icontains=user_filter)
    if date_from:
        events = events.filter(timestamp__gte=date_from)
    if date_to:
        events = events.filter(timestamp__lte=date_to)
    
    # Pagination
    paginator = Paginator(events, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get event types for filter
    event_types = SecurityLog.EVENT_TYPES
    
    context = {
        'page_obj': page_obj,
        'event_types': event_types,
        'current_filters': {
            'event_type': event_type,
            'severity': severity,
            'user': user_filter,
            'date_from': date_from,
            'date_to': date_to,
        },
        'warning_templates': WARNING_TEMPLATES,
    }
    
    return render(request, 'accounts/threat_management/events_list.html', context)

@user_passes_test(is_superuser)
@require_http_methods(["POST"])
def send_warning(request):
    """Send a security warning to a user"""
    try:
        data = json.loads(request.body)
        
        user_id = data.get('user_id')
        template_key = data.get('template')
        custom_title = data.get('custom_title')
        custom_message = data.get('custom_message')
        severity = data.get('severity', 'warning')
        security_log_id = data.get('security_log_id')
        
        # Get target user
        user = get_object_or_404(User, id=user_id)
        
        # Get security log if provided
        security_log = None
        if security_log_id:
            security_log = get_object_or_404(SecurityLog, id=security_log_id)
        
        # Use template or custom message
        if template_key and template_key in WARNING_TEMPLATES:
            template = WARNING_TEMPLATES[template_key]
            title = template['title']
            message = template['message']
            severity = template['severity']
            template_used = template_key
        else:
            title = custom_title or 'Security Warning'
            message = custom_message or 'Please review your recent activity.'
            template_used = ''
        
        # Create warning
        warning = SecurityWarning.objects.create(
            user=user,
            admin=request.user,
            severity=severity,
            title=title,
            message=message,
            template_used=template_used,
            security_log=security_log,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        # Update user threat level
        threat_level, created = UserThreatLevel.objects.get_or_create(
            user=user,
            defaults={'level': 'green'}
        )
        threat_level.warnings_received += 1
        threat_level.calculate_threat_level()
        
        # Log the action
        SecurityAction.objects.create(
            admin=request.user,
            target_user=user,
            action_type='warning_sent',
            description=f'Warning sent: {title}',
            reason=f'Security violation: {security_log.event_type if security_log else "Manual"}',
            security_warning=warning,
            security_log=security_log,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        logger.warning(f"Security warning sent by {request.user.username} to {user.username}: {title}")
        
        return JsonResponse({
            'success': True,
            'message': f'Warning sent to {user.username}',
            'warning_id': warning.id
        })
        
    except Exception as e:
        logger.error(f"Failed to send warning: {e}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@user_passes_test(is_superuser)
@require_http_methods(["POST"])
def block_ip(request):
    """Block an IP address"""
    try:
        data = json.loads(request.body)
        
        ip_address = data.get('ip_address')
        block_type = data.get('block_type', 'temporary')
        reason = data.get('reason', 'security_violation')
        custom_reason = data.get('custom_reason', '')
        duration_hours = data.get('duration_hours', 24)
        user_id = data.get('user_id')
        security_log_ids = data.get('security_log_ids', [])
        
        if not ip_address:
            return JsonResponse({'success': False, 'error': 'IP address is required'}, status=400)
        
        # Check if IP is already blocked
        existing_block = IPBlock.objects.filter(ip_address=ip_address, is_active=True).first()
        if existing_block:
            return JsonResponse({'success': False, 'error': 'IP address is already blocked'}, status=400)
        
        # Calculate expiration
        expires_at = None
        if block_type == 'temporary':
            expires_at = timezone.now() + timedelta(hours=duration_hours)
        
        # Get user if provided
        user = None
        if user_id:
            user = get_object_or_404(User, id=user_id)
        
        # Create IP block
        ip_block = IPBlock.objects.create(
            ip_address=ip_address,
            block_type=block_type,
            reason=reason,
            custom_reason=custom_reason,
            admin=request.user,
            user=user,
            expires_at=expires_at,
        )
        
        # Add related security logs
        if security_log_ids:
            security_logs = SecurityLog.objects.filter(id__in=security_log_ids)
            ip_block.security_logs.set(security_logs)
        
        # Log the action
        SecurityAction.objects.create(
            admin=request.user,
            target_user=user,
            action_type='ip_blocked',
            description=f'IP {ip_address} blocked ({block_type})',
            reason=f'{reason}: {custom_reason}' if custom_reason else reason,
            ip_block=ip_block,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )
        
        logger.warning(f"IP {ip_address} blocked by {request.user.username} ({block_type})")
        
        return JsonResponse({
            'success': True,
            'message': f'IP {ip_address} has been blocked',
            'block_id': ip_block.id
        })
        
    except Exception as e:
        logger.error(f"Failed to block IP: {e}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@user_passes_test(is_superuser)
def ip_blocks_list(request):
    """List and manage IP blocks"""

    # Get filter parameters
    block_type = request.GET.get('block_type')
    is_active = request.GET.get('is_active')

    # Build query
    blocks = IPBlock.objects.select_related('admin', 'user').order_by('-created_at')

    if block_type:
        blocks = blocks.filter(block_type=block_type)
    if is_active == 'true':
        blocks = blocks.filter(is_active=True)
    elif is_active == 'false':
        blocks = blocks.filter(is_active=False)

    # Pagination
    paginator = Paginator(blocks, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'current_filters': {
            'block_type': block_type,
            'is_active': is_active,
        }
    }

    return render(request, 'accounts/threat_management/ip_blocks.html', context)

@user_passes_test(is_superuser)
@require_http_methods(["POST"])
def unblock_ip(request, block_id):
    """Unblock an IP address"""
    try:
        ip_block = get_object_or_404(IPBlock, id=block_id)

        ip_block.is_active = False
        ip_block.save()

        # Log the action
        SecurityAction.objects.create(
            admin=request.user,
            target_user=ip_block.user,
            action_type='ip_unblocked',
            description=f'IP {ip_block.ip_address} unblocked',
            reason='Manual unblock by admin',
            ip_block=ip_block,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )

        logger.info(f"IP {ip_block.ip_address} unblocked by {request.user.username}")

        return JsonResponse({
            'success': True,
            'message': f'IP {ip_block.ip_address} has been unblocked'
        })

    except Exception as e:
        logger.error(f"Failed to unblock IP: {e}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@user_passes_test(is_superuser)
def user_threat_levels(request):
    """View and manage user threat levels"""

    # Get filter parameters
    level_filter = request.GET.get('level')
    user_filter = request.GET.get('user_filter')
    min_violations = request.GET.get('min_violations')

    # Build query
    threat_levels = UserThreatLevel.objects.select_related('user').order_by('-last_violation')

    if level_filter:
        threat_levels = threat_levels.filter(level=level_filter)
    if user_filter:
        threat_levels = threat_levels.filter(user__username__icontains=user_filter)
    if min_violations:
        try:
            min_violations_int = int(min_violations)
            threat_levels = threat_levels.filter(total_violations__gte=min_violations_int)
        except ValueError:
            pass

    # Calculate statistics
    all_threat_levels = UserThreatLevel.objects.all()
    stats = {
        'low_threat': all_threat_levels.filter(level='green').count(),
        'medium_threat': all_threat_levels.filter(level='yellow').count(),
        'high_threat': all_threat_levels.filter(level='red').count(),
        'critical_threat': all_threat_levels.filter(level='critical').count(),
    }

    # Pagination
    paginator = Paginator(threat_levels, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages(),
        'stats': stats,
        'warning_templates': WARNING_TEMPLATES,
        'current_filters': {
            'level': level_filter,
            'user_filter': user_filter,
            'min_violations': min_violations,
        }
    }

    return render(request, 'accounts/threat_management/threat_levels.html', context)

@user_passes_test(is_superuser)
def user_security_profile(request, user_id):
    """View detailed security profile for a user"""

    user = get_object_or_404(User, id=user_id)

    # Get or create threat level
    threat_level, created = UserThreatLevel.objects.get_or_create(
        user=user,
        defaults={'level': 'green'}
    )

    # Get security events
    security_events = SecurityLog.objects.filter(user=user).order_by('-timestamp')[:20]

    # Get warnings
    warnings = SecurityWarning.objects.filter(user=user).order_by('-created_at')[:10]

    # Get IP blocks
    ip_blocks = IPBlock.objects.filter(user=user).order_by('-created_at')[:10]

    # Get security actions
    security_actions = SecurityAction.objects.filter(target_user=user).order_by('-created_at')[:15]

    # Statistics
    stats = {
        'total_violations': security_events.count(),
        'warnings_received': warnings.count(),
        'times_blocked': ip_blocks.count(),
        'last_violation': security_events.first().timestamp if security_events.exists() else None,
    }

    context = {
        'target_user': user,
        'threat_level': threat_level,
        'security_events': security_events,
        'warnings': warnings,
        'ip_blocks': ip_blocks,
        'security_actions': security_actions,
        'stats': stats,
        'warning_templates': WARNING_TEMPLATES,
    }

    return render(request, 'accounts/threat_management/user_profile.html', context)

@login_required
def user_warnings(request):
    """Show warnings for the current user"""

    # Get pending warnings for the user
    pending_warnings = SecurityWarning.objects.filter(
        user=request.user,
        status='pending'
    ).order_by('-created_at')

    # Mark warnings as delivered
    for warning in pending_warnings:
        warning.mark_delivered()

    # Get all warnings for history
    all_warnings = SecurityWarning.objects.filter(
        user=request.user
    ).order_by('-created_at')[:20]

    context = {
        'pending_warnings': pending_warnings,
        'all_warnings': all_warnings,
    }

    return render(request, 'accounts/user_warnings.html', context)

@login_required
@require_http_methods(["POST"])
def acknowledge_warning(request, warning_id):
    """Acknowledge a security warning"""
    try:
        warning = get_object_or_404(SecurityWarning, id=warning_id, user=request.user)
        warning.mark_acknowledged()

        return JsonResponse({
            'success': True,
            'message': 'Warning acknowledged'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

# Middleware function to check for IP blocks
def check_ip_block(get_response):
    """Middleware to check if IP is blocked"""

    def middleware(request):
        # Skip check for superusers and certain paths
        if (request.user.is_authenticated and request.user.is_superuser) or \
           request.path.startswith('/admin/') or \
           request.path.startswith('/static/') or \
           request.path.startswith('/media/'):
            return get_response(request)

        client_ip = get_client_ip(request)

        # Check if IP is blocked
        ip_block = IPBlock.objects.filter(
            ip_address=client_ip,
            is_active=True
        ).first()

        if ip_block and ip_block.is_blocked():
            # Record the attempt
            ip_block.record_attempt()

            # Return blocked page
            context = {
                'ip_address': client_ip,
                'block_reason': ip_block.get_reason_display(),
                'custom_reason': ip_block.custom_reason,
                'block_type': ip_block.get_block_type_display(),
                'expires_at': ip_block.expires_at,
                'admin_contact': '<EMAIL>',  # Configure this
            }

            return render(request, 'accounts/ip_blocked.html', context, status=403)

        return get_response(request)

    return middleware

@login_required
def get_pending_warnings(request):
    """API endpoint to get pending warnings for current user"""
    try:
        pending_warnings = SecurityWarning.objects.filter(
            user=request.user,
            status='pending'
        ).order_by('-created_at')

        warnings_data = []
        for warning in pending_warnings:
            warnings_data.append({
                'id': warning.id,
                'title': warning.title,
                'message': warning.message,
                'severity': warning.severity,
                'severity_display': warning.get_severity_display(),
                'created_at': warning.created_at.isoformat(),
                'admin': warning.admin.get_full_name() or warning.admin.username,
            })

        return JsonResponse({
            'success': True,
            'warnings': warnings_data
        })

    except Exception as e:
        logger.error(f"Failed to get pending warnings: {e}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@login_required
@require_http_methods(["POST"])
def mark_warning_delivered(request, warning_id):
    """Mark a warning as delivered"""
    try:
        warning = get_object_or_404(SecurityWarning, id=warning_id, user=request.user)
        if warning.status == 'pending':
            warning.mark_delivered()

        return JsonResponse({'success': True})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

def get_client_ip(request):
    """Get the real IP address of the client"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


@login_required
@require_http_methods(["POST"])
def update_threat_level(request):
    """Update a user's threat level"""
    if not request.user.is_superuser:
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        level = data.get('level')
        reason = data.get('reason', '')

        if not user_id or not level:
            return JsonResponse({'success': False, 'error': 'Missing required fields'})

        user = User.objects.get(id=user_id)

        # Get or create threat level
        threat_level, created = UserThreatLevel.objects.get_or_create(
            user=user,
            defaults={'level': level, 'admin': request.user}
        )

        if not created:
            # Update existing threat level
            old_level = threat_level.level
            threat_level.level = level
            threat_level.admin = request.user
            threat_level.save()

            # Log the change
            SecurityLog.objects.create(
                user=user,
                event_type='threat_level_updated',
                severity='warning',
                ip_address=get_client_ip(request),
                details=f'Threat level changed from {old_level} to {level}. Reason: {reason}'
            )
        else:
            # Log the creation
            SecurityLog.objects.create(
                user=user,
                event_type='threat_level_created',
                severity='warning',
                ip_address=get_client_ip(request),
                details=f'Initial threat level set to {level}. Reason: {reason}'
            )

        return JsonResponse({'success': True, 'message': 'Threat level updated successfully'})

    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_http_methods(["POST"])
def suspend_user(request):
    """Suspend a user account"""
    if not request.user.is_superuser:
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        reason = data.get('reason', 'Account suspended by administrator')

        if not user_id:
            return JsonResponse({'success': False, 'error': 'Missing user ID'})

        user = User.objects.get(id=user_id)

        # Suspend the user
        user.is_active = False
        user.save()

        # Log the suspension
        SecurityLog.objects.create(
            user=user,
            event_type='account_suspended',
            severity='critical',
            ip_address=get_client_ip(request),
            details=f'Account suspended by {request.user.username}. Reason: {reason}'
        )

        # Create security action record
        SecurityAction.objects.create(
            admin=request.user,
            action_type='user_suspended',
            target_user=user,
            description=f'Suspended user account: {user.username}. Reason: {reason}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
        )

        return JsonResponse({'success': True, 'message': 'User account suspended successfully'})

    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
