/**
 * Mentorship Session Room JavaScript
 * Handles Monaco editor integration, voice chat, and real-time collaboration
 */

class MentorshipSession {
    constructor(config) {
        this.config = config;
        this.editor = null;
        this.websocket = null;
        this.voiceConnection = null;
        this.sessionTimer = null;
        this.isConnected = false;
        this.canEdit = config.canEdit;

        this.init();
    }

    async init() {
        try {
            await this.initializeMonacoEditor();
            this.initializeWebSocket();
            this.initializeVoiceChat();
            this.initializeSessionTimer();
            this.initializeEventListeners();
            await this.loadChatHistory(); // Load chat history

            console.log('Mentorship session initialized successfully');
        } catch (error) {
            console.error('Failed to initialize mentorship session:', error);
        }
    }

    async initializeMonacoEditor() {
        // Wait for Monaco to be available
        if (typeof monaco === 'undefined') {
            await this.loadMonaco();
        }

        // Create Monaco editor instance
        this.editor = monaco.editor.create(document.getElementById('monaco-editor'), {
            value: '# Welcome to your mentorship session!\n# Start coding together...\n\n',
            language: 'python',
            theme: 'vs-dark',
            automaticLayout: true,
            readOnly: !this.canEdit,
            minimap: { enabled: true },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            wordWrap: 'on'
        });

        // Set up collaborative editing if YJS is available
        if (typeof Y !== 'undefined') {
            this.setupYjsCollaboration();
        }

        // Listen for content changes
        this.editor.onDidChangeModelContent((event) => {
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                this.websocket.send(JSON.stringify({
                    type: 'code_change',
                    content: this.editor.getValue(),
                    changes: event.changes
                }));
            }
        });
    }

    async loadMonaco() {
        return new Promise((resolve, reject) => {
            if (window.monaco) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = '/static/monaco-editor/min/vs/loader.js';
            script.onload = () => {
                require.config({ paths: { vs: '/static/monaco-editor/min/vs' } });
                require(['vs/editor/editor.main'], () => {
                    resolve();
                });
            };
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    setupYjsCollaboration() {
        try {
            // Create YJS document
            this.ydoc = new Y.Doc();
            this.ytext = this.ydoc.getText('monaco');

            // Set up WebSocket provider for YJS
            this.yjsProvider = new Y.WebsocketProvider(
                `ws://${window.location.host}/ws/yjs-room/${this.config.roomId}/`,
                this.config.roomId,
                this.ydoc
            );

            // Bind YJS to Monaco editor
            this.monacoBinding = new MonacoBinding(
                this.ytext,
                this.editor.getModel(),
                new Set([this.editor]),
                this.yjsProvider.awareness
            );

            console.log('YJS collaboration setup complete');
        } catch (error) {
            console.error('Failed to setup YJS collaboration:', error);
        }
    }

    initializeWebSocket() {
        // Use centralized WebSocket configuration if available
        let wsUrl;
        if (window.WEBSOCKET_CONFIG && window.WEBSOCKET_CONFIG.baseUrl) {
            wsUrl = `${window.WEBSOCKET_CONFIG.baseUrl}/ws/mentorship/session/${this.config.roomId}/`;
        } else {
            // Fallback to old method for backward compatibility
            wsUrl = `ws://${window.location.host}/ws/mentorship/session/${this.config.roomId}/`;
        }
        this.websocket = new WebSocket(wsUrl);

        this.websocket.onopen = () => {
            console.log('WebSocket connected');
            this.isConnected = true;
            this.updateConnectionStatus('connected');
        };

        this.websocket.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            } catch (error) {
                console.error('Failed to parse WebSocket message:', error);
            }
        };

        this.websocket.onclose = () => {
            console.log('WebSocket disconnected');
            this.isConnected = false;
            this.updateConnectionStatus('disconnected');

            // Attempt to reconnect after 3 seconds
            setTimeout(() => {
                if (!this.isConnected) {
                    this.initializeWebSocket();
                }
            }, 3000);
        };

        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.updateConnectionStatus('error');
        };
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'chat_message':
                this.addChatMessage(data);
                break;
            case 'session_control':
                this.handleSessionControl(data);
                break;
            case 'edit_permission':
                this.handleEditPermission(data);
                break;
            case 'voice_signal':
                this.handleVoiceSignal(data);
                break;
            case 'code_change':
                if (data.user_id !== this.config.userId) {
                    this.updateEditorContent(data);
                }
                break;
            case 'timer_sync_response':
                this.syncTimerWithServer(data);
                break;
            case 'timer_warning_dismissed':
                this.handleTimerWarningDismissed(data);
                break;
            default:
                console.log('Unknown message type:', data.type);
        }
    }

    initializeVoiceChat() {
        this.voiceConnection = new VoiceChat(this.config.roomId, this.websocket);

        // Set up voice control buttons
        document.getElementById('toggleMicBtn').addEventListener('click', () => {
            this.voiceConnection.toggleMicrophone();
        });

        document.getElementById('toggleSpeakerBtn').addEventListener('click', () => {
            this.voiceConnection.toggleSpeaker();
        });
    }

    initializeSessionTimer() {
        this.timerElement = document.querySelector('.timer-value');
        this.remainingSeconds = this.config.remainingTimeSeconds || (this.config.sessionDuration / 1000);
        this.sessionStarted = this.config.sessionStarted || false;
        this.timerWarningShown = false;
        this.sessionExpiredWarningShown = false;

        // Request timer sync from server on initialization
        this.requestTimerSync();

        // Update timer every second
        this.sessionTimer = setInterval(() => {
            this.updateTimerDisplay();
            this.checkTimerWarnings();
        }, 1000);

        // Sync with server every 30 seconds to prevent drift
        this.syncTimer = setInterval(() => {
            this.requestTimerSync();
        }, 30000);
    }

    requestTimerSync() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'timer_sync_request'
            }));
        }
    }

    updateTimerDisplay() {
        if (!this.sessionStarted) {
            // Show full duration if session hasn't started
            const totalMinutes = Math.floor(this.config.sessionDuration / 60000);
            this.timerElement.textContent = `${totalMinutes}:00`;
            return;
        }

        // Decrease remaining time
        this.remainingSeconds = Math.max(0, this.remainingSeconds - 1);

        // Update display
        const minutes = Math.floor(this.remainingSeconds / 60);
        const seconds = this.remainingSeconds % 60;
        this.timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        // Change color based on remaining time
        if (this.remainingSeconds <= 300) { // 5 minutes
            this.timerElement.style.color = '#ff4444';
        } else if (this.remainingSeconds <= 600) { // 10 minutes
            this.timerElement.style.color = '#ffaa00';
        } else {
            this.timerElement.style.color = '#C0ff6b';
        }
    }

    checkTimerWarnings() {
        if (!this.sessionStarted) return;

        // Show 10-minute warning
        if (this.remainingSeconds <= 600 && this.remainingSeconds > 300 && !this.timerWarningShown) {
            this.showTimeWarning('10 minutes remaining in session');
            this.timerWarningShown = true;
        }

        // Show 5-minute warning
        if (this.remainingSeconds <= 300 && this.remainingSeconds > 0 && this.timerWarningShown) {
            this.showTimeWarning('5 minutes remaining in session');
        }

        // Show session expired warning
        if (this.remainingSeconds <= 0 && !this.sessionExpiredWarningShown) {
            this.showSessionExpiredWarning();
            this.sessionExpiredWarningShown = true;
        }
    }

    syncTimerWithServer(data) {
        this.remainingSeconds = data.remaining_seconds;
        this.sessionStarted = data.session_started;

        if (data.session_expired && !this.sessionExpiredWarningShown) {
            this.showSessionExpiredWarning();
            this.sessionExpiredWarningShown = true;
        }
    }

    initializeEventListeners() {
        // Chat functionality
        document.getElementById('sendChatBtn').addEventListener('click', () => {
            this.sendChatMessage();
        });

        document.getElementById('chatInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendChatMessage();
            }
        });

        // Session controls
        document.getElementById('endSessionBtn').addEventListener('click', () => {
            this.showEndSessionModal();
        });

        document.getElementById('confirmEndSession').addEventListener('click', () => {
            this.endSession();
        });

        document.getElementById('cancelEndSession').addEventListener('click', () => {
            this.hideEndSessionModal();
        });

        // Edit permission toggle (mentor only)
        const toggleEditBtn = document.getElementById('toggleLearnerEdit');
        if (toggleEditBtn) {
            toggleEditBtn.addEventListener('click', () => {
                this.toggleLearnerEditPermission();
            });
        }

        // Code execution
        document.getElementById('runCodeBtn').addEventListener('click', () => {
            this.runCode();
        });

        // Save notes
        document.getElementById('saveNotesBtn').addEventListener('click', () => {
            this.saveSessionNotes();
        });
    }

    sendChatMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();

        if (message && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'chat_message',
                message: message,
                timestamp: new Date().toISOString()
            }));

            input.value = '';
        }
    }

    async loadChatHistory() {
        console.log('Loading chat history for room:', this.config.roomId);
        try {
            const response = await fetch(`/mentorship/api/chat-history/${this.config.roomId}/`, {
                method: 'GET',
                headers: {
                    'X-CSRFToken': this.getCsrfToken(),
                    'Content-Type': 'application/json',
                },
            });

            console.log('Chat history response status:', response.status);

            if (response.ok) {
                const data = await response.json();
                console.log('Chat history data:', data);
                if (data.success && data.messages) {
                    console.log('Displaying', data.messages.length, 'messages');
                    this.displayChatHistory(data.messages);
                } else {
                    console.log('No messages found or request failed');
                }
            } else {
                console.error('Failed to load chat history:', response.status, response.statusText);
                const errorText = await response.text();
                console.error('Error response:', errorText);
            }
        } catch (error) {
            console.error('Error loading chat history:', error);
        }
    }

    displayChatHistory(messages) {
        console.log('displayChatHistory called with', messages.length, 'messages');
        const chatMessages = document.getElementById('chatMessages');

        if (!chatMessages) {
            console.error('chatMessages element not found!');
            return;
        }

        // Clear existing messages
        chatMessages.innerHTML = '';
        console.log('Cleared existing messages');

        messages.forEach((message, index) => {
            console.log(`Adding message ${index + 1}:`, message);
            this.addChatMessageFromHistory(message);
        });

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
        console.log('Chat history display completed');
    }

    addChatMessageFromHistory(message) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');

        const isOwn = message.user_id === this.config.userId;
        const messageType = message.message_type;

        if (messageType === 'system' || messageType === 'user_joined' || messageType === 'user_left') {
            messageElement.className = 'system-message';
            const time = new Date(message.timestamp * 1000).toLocaleTimeString();
            messageElement.innerHTML = `
                <div class="system-message-content">
                    <span class="system-text">${this.escapeHtml(message.content)}</span>
                    <span class="system-time">${time}</span>
                </div>
            `;
        } else {
            messageElement.className = `chat-message ${isOwn ? 'own' : 'other'}`;
            const time = new Date(message.timestamp * 1000).toLocaleTimeString();
            messageElement.innerHTML = `
                <div class="message-header">
                    <span class="message-sender">${this.escapeHtml(message.username)}</span>
                    <span class="message-time">${time}</span>
                </div>
                <div class="message-content">${this.escapeHtml(message.content)}</div>
            `;
        }

        chatMessages.appendChild(messageElement);
    }

    addChatMessage(data) {
        const chatMessages = document.getElementById('chatMessages');
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';

        const time = new Date(data.timestamp).toLocaleTimeString();
        messageElement.innerHTML = `
            <div class="message-header">
                <strong>${data.username}</strong>
                <span class="message-time">${time}</span>
            </div>
            <div class="message-content">${this.escapeHtml(data.message)}</div>
        `;

        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    toggleLearnerEditPermission() {
        const newPermission = !this.canEdit;

        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'edit_permission',
                can_edit: newPermission,
                target_user: 'learner'
            }));
        }
    }

    handleEditPermission(data) {
        if (this.config.userRole === 'learner') {
            this.canEdit = data.can_edit;
            this.editor.updateOptions({ readOnly: !this.canEdit });

            const toggleText = document.getElementById('editToggleText');
            if (toggleText) {
                toggleText.textContent = this.canEdit ? 'Disable Learner Edit' : 'Allow Learner Edit';
            }
        }
    }

    async runCode() {
        const code = this.editor.getValue();
        const output = document.getElementById('codeOutput');

        output.textContent = 'Running code...\n';

        try {
            // This would typically send to a code execution service
            // For now, we'll simulate it
            setTimeout(() => {
                output.textContent = `Code executed successfully!\n\nOutput:\n${code}\n\n(This is a simulation - integrate with actual code execution service)`;
            }, 1000);
        } catch (error) {
            output.textContent = `Error: ${error.message}`;
        }
    }

    async saveSessionNotes() {
        const notes = document.getElementById('sessionNotes').value;

        try {
            const response = await fetch(`/mentorship/session/${this.config.sessionId}/notes/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify({ notes: notes })
            });

            if (response.ok) {
                this.showNotification('Notes saved successfully');
            } else {
                this.showNotification('Failed to save notes', 'error');
            }
        } catch (error) {
            console.error('Failed to save notes:', error);
            this.showNotification('Failed to save notes', 'error');
        }
    }

    showEndSessionModal() {
        document.getElementById('endSessionModal').classList.add('show');
    }

    hideEndSessionModal() {
        document.getElementById('endSessionModal').classList.remove('show');
    }

    endSession() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'session_control',
                action: 'end_session'
            }));
        }

        // Clean up
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
        }

        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }

        if (this.voiceConnection) {
            this.voiceConnection.disconnect();
        }

        // Redirect to feedback page or session summary
        window.location.href = '/mentorship/my-sessions/';
    }

    showTimeWarning(message) {
        this.showNotification(message, 'warning');
    }

    showSessionExpiredWarning() {
        if (this.config.userRole === 'mentor') {
            this.showSessionExpiredModal();
        } else {
            this.showNotification('Session time has expired. The mentor can choose to continue or end the session.', 'warning');
        }
    }

    showSessionExpiredModal() {
        // Create and show session expired modal for mentor
        const modal = document.createElement('div');
        modal.className = 'modal session-expired-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <h3>Session Time Expired</h3>
                <p>The scheduled session time has ended. You can continue teaching or end the session.</p>
                <div class="modal-actions">
                    <button id="continueSessionBtn" class="btn btn-primary">Continue Session</button>
                    <button id="endSessionFromModalBtn" class="btn btn-danger">End Session</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.classList.add('show');

        // Add event listeners
        document.getElementById('continueSessionBtn').addEventListener('click', () => {
            this.dismissSessionExpiredWarning();
            modal.remove();
        });

        document.getElementById('endSessionFromModalBtn').addEventListener('click', () => {
            modal.remove();
            this.endSession();
        });
    }

    dismissSessionExpiredWarning() {
        // Send message to dismiss warning for all participants
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'timer_warning_dismissed'
            }));
        }
    }

    handleTimerWarningDismissed(data) {
        // Handle when mentor dismisses the timer warning
        if (data.user_id !== this.config.userId) {
            this.showNotification(`${data.username} chose to continue the session beyond the scheduled time.`, 'info');
        }
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('voiceStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');

        switch (status) {
            case 'connected':
                indicator.className = 'status-indicator connected';
                text.textContent = 'Connected';
                break;
            case 'disconnected':
                indicator.className = 'status-indicator';
                text.textContent = 'Disconnected';
                break;
            case 'error':
                indicator.className = 'status-indicator error';
                text.textContent = 'Connection Error';
                break;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || '';
    }
}

// Voice Chat Class
class VoiceChat {
    constructor(roomId, websocket) {
        this.roomId = roomId;
        this.websocket = websocket;
        this.localStream = null;
        this.peerConnection = null;
        this.isMuted = false;
        this.isSpeakerOn = true;

        this.init();
    }

    async init() {
        try {
            await this.setupLocalStream();
            this.setupPeerConnection();
        } catch (error) {
            console.error('Failed to initialize voice chat:', error);
        }
    }

    async setupLocalStream() {
        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                audio: true,
                video: false
            });
            console.log('Local audio stream obtained');
        } catch (error) {
            console.error('Failed to get local stream:', error);
            throw error;
        }
    }

    setupPeerConnection() {
        this.peerConnection = new RTCPeerConnection({
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        });

        // Add local stream to peer connection
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });
        }

        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            const remoteAudio = document.createElement('audio');
            remoteAudio.srcObject = event.streams[0];
            remoteAudio.autoplay = true;
            document.body.appendChild(remoteAudio);
        };

        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate && this.websocket) {
                this.websocket.send(JSON.stringify({
                    type: 'voice_signal',
                    signal_type: 'ice_candidate',
                    signal_data: event.candidate
                }));
            }
        };
    }

    toggleMicrophone() {
        if (this.localStream) {
            const audioTrack = this.localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                this.isMuted = !audioTrack.enabled;

                const micBtn = document.getElementById('toggleMicBtn');
                const icon = micBtn.querySelector('i');
                const text = micBtn.querySelector('span');

                if (this.isMuted) {
                    icon.className = 'fas fa-microphone-slash';
                    text.textContent = 'Unmute';
                    micBtn.classList.add('active');
                } else {
                    icon.className = 'fas fa-microphone';
                    text.textContent = 'Mute';
                    micBtn.classList.remove('active');
                }
            }
        }
    }

    toggleSpeaker() {
        this.isSpeakerOn = !this.isSpeakerOn;

        const speakerBtn = document.getElementById('toggleSpeakerBtn');
        const icon = speakerBtn.querySelector('i');
        const text = speakerBtn.querySelector('span');

        // Mute/unmute all remote audio elements
        document.querySelectorAll('audio').forEach(audio => {
            audio.muted = !this.isSpeakerOn;
        });

        if (this.isSpeakerOn) {
            icon.className = 'fas fa-volume-up';
            text.textContent = 'Speaker';
            speakerBtn.classList.remove('active');
        } else {
            icon.className = 'fas fa-volume-mute';
            text.textContent = 'Unmuted';
            speakerBtn.classList.add('active');
        }
    }

    disconnect() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
        }

        if (this.peerConnection) {
            this.peerConnection.close();
        }
    }
}

// Initialize session when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof SESSION_CONFIG !== 'undefined') {
        window.mentorshipSession = new MentorshipSession(SESSION_CONFIG);
    }
});
