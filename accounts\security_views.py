"""
Security-related views for ForgeX application
Handles user role detection, security logging, and admin security management
"""

import json
import logging
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required, user_passes_test
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.models import User
from django.conf import settings
from django.utils import timezone
from .models import UserProfile, SecurityLog
import hashlib
import secrets

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["GET"])
def get_user_role(request):
    """
    Return user role and security settings for client-side security system
    """
    user = request.user
    
    # Determine user role and permissions
    role_data = {
        'role': 'user',
        'is_superuser': user.is_superuser,
        'is_staff': user.is_staff,
        'security_level': 'standard',
        'debug_enabled': False,
        'permissions': []
    }
    
    # Set role based on user status
    if user.is_superuser:
        role_data['role'] = 'superuser'
        role_data['debug_enabled'] = True
        role_data['security_level'] = 'minimal'
        role_data['permissions'] = ['debug', 'admin', 'security_management']
    elif user.is_staff:
        role_data['role'] = 'staff'
        role_data['debug_enabled'] = True
        role_data['security_level'] = 'standard'
        role_data['permissions'] = ['debug', 'content_management']
    else:
        # Check for special permissions in user profile
        try:
            profile = user.userprofile
            if hasattr(profile, 'security_clearance'):
                if profile.security_clearance == 'developer':
                    role_data['role'] = 'developer'
                    role_data['debug_enabled'] = True
                    role_data['security_level'] = 'standard'
                    role_data['permissions'] = ['debug']
                elif profile.security_clearance == 'tester':
                    role_data['role'] = 'tester'
                    role_data['debug_enabled'] = True
                    role_data['security_level'] = 'high'
                    role_data['permissions'] = ['debug', 'testing']
        except UserProfile.DoesNotExist:
            pass
    
    # Add additional security settings
    role_data.update({
        'session_timeout': getattr(settings, 'SESSION_COOKIE_AGE', 3600),
        'csrf_token': request.META.get('CSRF_COOKIE'),
        'client_ip': get_client_ip(request),
        'user_agent_hash': hashlib.md5(request.META.get('HTTP_USER_AGENT', '').encode()).hexdigest()[:16]
    })
    
    return JsonResponse(role_data)

@csrf_exempt
@require_http_methods(["POST"])
def log_security_event(request):
    """
    Log security events from client-side security system
    """
    try:
        data = json.loads(request.body)

        # Extract event information
        event_type = data.get('event_type', 'other')
        details = data.get('details', '')
        user_agent = data.get('user_agent', request.META.get('HTTP_USER_AGENT', ''))

        # Map event types to our model choices
        event_type_mapping = {
            'devtools_detected': 'devtools_open',
            'console_access_attempt': 'console_access',
            'blocked_action': 'permission_denied',
            'security_violation': 'security_threat',
            'right_click_blocked': 'permission_denied',
            'keyboard_shortcut_blocked': 'permission_denied',
            'source_view_blocked': 'permission_denied'
        }

        mapped_event_type = event_type_mapping.get(event_type, 'other')

        # Determine severity based on event type
        severity = 'medium'
        if 'devtools' in event_type.lower() or 'console' in event_type.lower():
            severity = 'high'
        elif 'blocked' in event_type.lower():
            severity = 'low'
        elif 'threat' in event_type.lower() or 'attack' in event_type.lower():
            severity = 'critical'

        # Get user (anonymous if not logged in)
        user = request.user if request.user.is_authenticated else None

        # Create comprehensive event data
        event_data = {
            'original_event_type': event_type,
            'details': details,
            'url': data.get('url', ''),
            'page_title': data.get('page_title', ''),
            'referrer': data.get('referrer', ''),
            'timestamp_client': data.get('timestamp', ''),
            'user_id': user.id if user else None,
            'username': user.username if user else 'anonymous',
            'session_key': request.session.session_key
        }

        # Create security log entry
        security_log = SecurityLog.objects.create(
            user=user,
            event_type=mapped_event_type,
            event_data=event_data,
            ip_address=get_client_ip(request),
            user_agent=user_agent,
            severity=severity,
            timestamp=timezone.now()
        )

        # Log to security files
        username = user.username if user else f"Anonymous"
        ip_address = get_client_ip(request)

        # Create security event logger
        security_logger = logging.getLogger('accounts.security_views')

        # Create detailed log message
        log_message = (
            f"USER: {username} | IP: {ip_address} | EVENT: {event_type} | "
            f"SEVERITY: {severity} | DETAILS: {details} | "
            f"URL: {data.get('url', 'unknown')} | "
            f"PAGE: {data.get('page_title', 'unknown')} | "
            f"USER_AGENT: {user_agent[:100]}..."
        )

        # Log with appropriate level based on severity
        if severity == 'critical':
            security_logger.critical(log_message)
        elif severity == 'high':
            security_logger.warning(log_message)
        elif severity == 'medium':
            security_logger.info(log_message)
        else:
            security_logger.info(log_message)

        # Also write to a simple text file for easy reading
        try:
            import os
            from datetime import datetime

            log_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
            os.makedirs(log_dir, exist_ok=True)

            simple_log_file = os.path.join(log_dir, 'security_simple.log')

            with open(simple_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{timestamp}] {severity.upper()} - {username} ({ip_address}) - {event_type}: {details}\n")

        except Exception as file_error:
            logger.error(f"Failed to write to simple security log: {file_error}")

        # Console log for debugging
        print(f"🔒 Security Event Logged: {event_type} by {username} - {details}")

        return JsonResponse({
            'status': 'logged',
            'log_id': security_log.id,
            'severity': severity,
            'message': f'Security event logged: {event_type}'
        })

    except json.JSONDecodeError:
        logger.error("Invalid JSON in security log request")
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"Failed to log security event: {e}")
        return JsonResponse({'error': 'Failed to log event'}, status=500)

@user_passes_test(lambda u: u.is_superuser)
@require_http_methods(["GET"])
def security_dashboard(request):
    """
    Security dashboard for administrators
    """
    # Get recent security events
    recent_events = SecurityLog.objects.filter(
        timestamp__gte=timezone.now() - timedelta(days=7)
    ).order_by('-timestamp')[:100]
    
    # Aggregate security statistics
    stats = {
        'total_events_week': recent_events.count(),
        'unique_users_week': recent_events.values('user').distinct().count(),
        'threat_events_week': recent_events.filter(
            event_type__icontains='threat'
        ).count(),
        'devtools_detections_week': recent_events.filter(
            event_type__icontains='devtools'
        ).count()
    }
    
    # Get top event types
    event_types = {}
    for event in recent_events:
        event_type = event.event_type
        event_types[event_type] = event_types.get(event_type, 0) + 1
    
    top_events = sorted(event_types.items(), key=lambda x: x[1], reverse=True)[:10]
    
    return JsonResponse({
        'stats': stats,
        'recent_events': [
            {
                'id': event.id,
                'user': event.user.username,
                'event_type': event.event_type,
                'timestamp': event.timestamp.isoformat(),
                'ip_address': event.ip_address,
                'user_agent': event.user_agent[:100] + '...' if len(event.user_agent) > 100 else event.user_agent
            }
            for event in recent_events[:20]
        ],
        'top_event_types': top_events
    })

@user_passes_test(lambda u: u.is_superuser)
@require_http_methods(["POST"])
def update_security_settings(request):
    """
    Update global security settings
    """
    try:
        data = json.loads(request.body)
        
        # Update security settings in database or cache
        # This would typically update a SecuritySettings model
        
        settings_to_update = {
            'obfuscation_enabled': data.get('obfuscation_enabled', True),
            'encryption_enabled': data.get('encryption_enabled', True),
            'devtools_protection': data.get('devtools_protection', True),
            'console_protection': data.get('console_protection', True),
            'rate_limiting_enabled': data.get('rate_limiting_enabled', True),
            'security_logging_level': data.get('security_logging_level', 'standard')
        }
        
        # Log the security settings change
        SecurityLog.objects.create(
            user=request.user,
            event_type='security_settings_updated',
            event_data=settings_to_update,
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            timestamp=timezone.now()
        )
        
        logger.info(f"Security settings updated by {request.user.username}")
        
        return JsonResponse({'status': 'updated', 'settings': settings_to_update})
    
    except Exception as e:
        logger.error(f"Failed to update security settings: {e}")
        return JsonResponse({'error': 'Failed to update settings'}, status=500)

@user_passes_test(lambda u: u.is_superuser)
@require_http_methods(["GET"])
def get_security_settings(request):
    """
    Get current security settings
    """
    # Default security settings
    default_settings = {
        'obfuscation_enabled': True,
        'encryption_enabled': True,
        'devtools_protection': True,
        'console_protection': True,
        'rate_limiting_enabled': True,
        'security_logging_level': 'standard',
        'session_timeout': getattr(settings, 'SESSION_COOKIE_AGE', 3600),
        'max_login_attempts': 5,
        'lockout_duration': 300  # 5 minutes
    }
    
    # In a real implementation, you would fetch these from a database
    # For now, return defaults
    
    return JsonResponse(default_settings)

@login_required
@require_http_methods(["POST"])
def generate_secure_token(request):
    """
    Generate secure tokens for API requests
    """
    try:
        # Generate a secure token for the current session
        token_data = {
            'user_id': request.user.id,
            'timestamp': timezone.now().timestamp(),
            'session_key': request.session.session_key,
            'random': secrets.token_hex(16)
        }
        
        # Create token hash
        token_string = json.dumps(token_data, sort_keys=True)
        token_hash = hashlib.sha256(token_string.encode()).hexdigest()
        
        # Store token in session for validation
        request.session['secure_token'] = token_hash
        request.session['token_timestamp'] = token_data['timestamp']
        
        return JsonResponse({
            'token': token_hash[:32],  # Return first 32 characters
            'expires_in': 3600  # 1 hour
        })
    
    except Exception as e:
        logger.error(f"Failed to generate secure token: {e}")
        return JsonResponse({'error': 'Failed to generate token'}, status=500)

@login_required
@require_http_methods(["POST"])
def validate_secure_token(request):
    """
    Validate secure tokens from API requests
    """
    try:
        data = json.loads(request.body)
        provided_token = data.get('token')
        
        if not provided_token:
            return JsonResponse({'valid': False, 'error': 'No token provided'})
        
        # Get stored token from session
        stored_token = request.session.get('secure_token')
        token_timestamp = request.session.get('token_timestamp')
        
        if not stored_token or not token_timestamp:
            return JsonResponse({'valid': False, 'error': 'No stored token'})
        
        # Check if token has expired (1 hour)
        if timezone.now().timestamp() - token_timestamp > 3600:
            return JsonResponse({'valid': False, 'error': 'Token expired'})
        
        # Validate token
        if stored_token.startswith(provided_token):
            return JsonResponse({'valid': True})
        else:
            # Log invalid token attempt
            SecurityLog.objects.create(
                user=request.user,
                event_type='invalid_token_attempt',
                event_data={'provided_token': provided_token[:16]},
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                timestamp=timezone.now()
            )
            return JsonResponse({'valid': False, 'error': 'Invalid token'})
    
    except Exception as e:
        logger.error(f"Failed to validate secure token: {e}")
        return JsonResponse({'error': 'Validation failed'}, status=500)

def get_client_ip(request):
    """
    Get the real IP address of the client
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

@user_passes_test(lambda u: u.is_superuser)
@require_http_methods(["GET"])
def export_security_logs(request):
    """
    Export security logs for analysis
    """
    try:
        # Get date range from query parameters
        days = int(request.GET.get('days', 7))
        start_date = timezone.now() - timedelta(days=days)
        
        logs = SecurityLog.objects.filter(
            timestamp__gte=start_date
        ).order_by('-timestamp')
        
        # Convert to exportable format
        export_data = []
        for log in logs:
            export_data.append({
                'timestamp': log.timestamp.isoformat(),
                'user': log.user.username,
                'event_type': log.event_type,
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'event_data': log.event_data
            })
        
        return JsonResponse({
            'logs': export_data,
            'total_count': len(export_data),
            'date_range': {
                'start': start_date.isoformat(),
                'end': timezone.now().isoformat()
            }
        })
    
    except Exception as e:
        logger.error(f"Failed to export security logs: {e}")
        return JsonResponse({'error': 'Export failed'}, status=500)

@user_passes_test(lambda u: u.is_superuser)
def security_logs_view(request):
    """
    Web view for security logs (admin only)
    """
    from django.shortcuts import render
    from django.core.paginator import Paginator

    # Get recent security logs
    logs = SecurityLog.objects.select_related('user').order_by('-timestamp')

    # Filter by event type if specified
    event_type_filter = request.GET.get('event_type')
    if event_type_filter:
        logs = logs.filter(event_type=event_type_filter)

    # Filter by user if specified
    user_filter = request.GET.get('user')
    if user_filter:
        logs = logs.filter(user__username__icontains=user_filter)

    # Filter by severity if specified
    severity_filter = request.GET.get('severity')
    if severity_filter:
        logs = logs.filter(severity=severity_filter)

    # Pagination
    paginator = Paginator(logs, 50)  # Show 50 logs per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get statistics
    total_logs = logs.count()
    devtools_attempts = logs.filter(event_type='devtools_open').count()
    console_attempts = logs.filter(event_type='console_access').count()
    high_severity = logs.filter(severity__in=['high', 'critical']).count()

    # Get unique event types for filter dropdown
    event_types = SecurityLog.EVENT_TYPES

    context = {
        'page_obj': page_obj,
        'total_logs': total_logs,
        'devtools_attempts': devtools_attempts,
        'console_attempts': console_attempts,
        'high_severity': high_severity,
        'event_types': event_types,
        'current_filters': {
            'event_type': event_type_filter,
            'user': user_filter,
            'severity': severity_filter,
        }
    }

    return render(request, 'accounts/security_logs.html', context)

def test_security_page(request):
    """
    Test page for security logging system
    """
    from django.shortcuts import render

    return render(request, 'accounts/test_security.html')
