class AudioWorkletProcessorJS extends AudioWorkletProcessor {
  constructor() {
    super();
    this.bufferSize = 2048;
    this.buffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
    this.noiseFloor = 0.01;
  }

  process(inputs) {
    const input = inputs[0];
    if (!input || !input[0]) return true;

    const inputChannel = input[0];
    
    // Fill the buffer with input samples
    for (let i = 0; i < inputChannel.length; i++) {
      // Apply noise gate
      let sample = inputChannel[i];
      if (Math.abs(sample) < this.noiseFloor) {
        sample = 0;
      }
      
      // Apply soft clipping for better audio quality
      if (sample > 0.8) {
        sample = 0.8 + (sample - 0.8) * 0.2;
      } else if (sample < -0.8) {
        sample = -0.8 + (sample + 0.8) * 0.2;
      }
      
      this.buffer[this.bufferIndex++] = sample;
      
      // When buffer is full, convert and send
      if (this.bufferIndex >= this.bufferSize) {
        // Convert to Int16 with improved quality
        const pcmData = new Int16Array(this.bufferSize);
        for (let j = 0; j < this.bufferSize; j++) {
          const s = Math.max(-1, Math.min(1, this.buffer[j]));
          pcmData[j] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        
        // Send the raw PCM data
        this.port.postMessage(pcmData.buffer, [pcmData.buffer]);
        
        // Reset buffer
        this.bufferIndex = 0;
      }
    }

    return true;
  }
}

registerProcessor("audio-worklet-processor", AudioWorkletProcessorJS);

function encodeWAV(samples, sampleRate) {
  const buffer = new ArrayBuffer(44 + samples.length * 2);
  const view = new DataView(buffer);

  // Write WAV header
  writeString(view, 0, "RIFF");
  view.setUint32(4, 36 + samples.length * 2, true); // File size - 8
  writeString(view, 8, "WAVE");
  writeString(view, 12, "fmt ");
  view.setUint32(16, 16, true); // Subchunk1Size (PCM)
  view.setUint16(20, 1, true); // AudioFormat (PCM)
  view.setUint16(22, 1, true); // NumChannels (Mono)
  view.setUint32(24, sampleRate, true); // SampleRate
  view.setUint32(28, sampleRate * 2, true); // ByteRate (SampleRate * NumChannels * BitsPerSample/8)
  view.setUint16(32, 2, true); // BlockAlign (NumChannels * BitsPerSample/8)
  view.setUint16(34, 16, true); // BitsPerSample
  writeString(view, 36, "data");
  view.setUint32(40, samples.length * 2, true); // Subchunk2Size (NumSamples * NumChannels * BitsPerSample/8)

  // Write PCM samples
  floatTo16BitPCM(view, 44, samples);

  return new Blob([buffer], { type: "audio/wav" });
}

function writeString(view, offset, string) {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

function floatTo16BitPCM(output, offset, input) {
  for (let i = 0; i < input.length; i++, offset += 2) {
    const s = Math.max(-1, Math.min(1, input[i]));
    output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);
  }
}