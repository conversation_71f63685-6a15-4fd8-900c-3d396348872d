/* Enhanced Notification System Styles */

/* Notifications container - positioned right-center */
.notifications-container {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    max-width: 380px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 15px;
    pointer-events: none; /* Allow clicks to pass through container */
}

/* Individual notification toast with enhanced animations */
.notification-toast {
    width: 100%;
    opacity: 0;
    transform: translateX(100px) scale(0.8);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: visible;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-radius: 12px;
    position: relative;
    pointer-events: auto; /* Enable clicks on notifications */
    backdrop-filter: blur(10px);
}

.notification-toast.notification-show {
    opacity: 1;
    transform: translateX(0) scale(1);
}

.notification-toast.notification-hide {
    opacity: 0;
    transform: translateX(100px) scale(0.8);
}

/* Glowing border animation that runs around the notification */
.notification-toast::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 14px;
    background: linear-gradient(45deg,
        transparent,
        rgba(192, 255, 107, 0.8),
        transparent,
        rgba(192, 255, 107, 0.8),
        transparent
    );
    background-size: 400% 400%;
    animation: notification-glow 5s linear;
    z-index: -1;
    opacity: 0;
}

.notification-toast.notification-show::before {
    opacity: 1;
}

@keyframes notification-glow {
    0% {
        background-position: 0% 0%;
        opacity: 1;
    }
    25% {
        background-position: 100% 0%;
    }
    50% {
        background-position: 100% 100%;
    }
    75% {
        background-position: 0% 100%;
    }
    100% {
        background-position: 0% 0%;
        opacity: 0;
    }
}

.notification-content {
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95), rgba(40, 40, 40, 0.95));
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid rgba(192, 255, 107, 0.3);
    position: relative;
}

/* Notification header */
.notification-header {
    display: flex;
    align-items: center;
    padding: 15px 18px;
    border-bottom: 1px solid rgba(192, 255, 107, 0.2);
    background: rgba(192, 255, 107, 0.05);
}

.notification-icon {
    margin-right: 12px;
    font-size: 20px;
    color: #ffffff;
}

.notification-title {
    flex-grow: 1;
    font-weight: 600;
    font-size: 16px;
    color: #ffffff;
}

.notification-close {
    background: none;
    border: none;
    font-size: 22px;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.3s ease;
    padding: 4px;
    margin-left: 10px;
    color: #ffffff;
    border-radius: 4px;
}

.notification-close:hover {
    opacity: 1;
    background: rgba(192, 255, 107, 0.2);
    transform: scale(1.1);
}

/* Notification body */
.notification-body {
    padding: 15px 18px;
    font-size: 14px;
    line-height: 1.6;
    color: #ffffff;
}

/* Enhanced progress bar with timer */
.notification-progress {
    height: 4px;
    width: 100%;
    background-color: rgba(192, 255, 107, 0.2);
    position: relative;
    overflow: hidden;
}

.notification-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg,
        rgba(192, 255, 107, 0.8),
        rgba(192, 255, 107, 1),
        rgba(192, 255, 107, 0.8)
    );
    animation: notification-progress 5s linear;
}

@keyframes notification-progress {
    from {
        width: 100%;
        opacity: 1;
    }
    to {
        width: 0%;
        opacity: 0.5;
    }
}

/* Enhanced notification types with dark theme support */
.notification-info::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(13, 110, 253, 0.8),
        transparent,
        rgba(13, 110, 253, 0.8),
        transparent
    );
}

.notification-info .notification-icon {
    color: #4dabf7;
}

.notification-info .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(13, 110, 253, 0.8),
        rgba(13, 110, 253, 1),
        rgba(13, 110, 253, 0.8)
    );
}

.notification-success::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(25, 135, 84, 0.8),
        transparent,
        rgba(25, 135, 84, 0.8),
        transparent
    );
}

.notification-success .notification-icon {
    color: #51cf66;
}

.notification-success .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(25, 135, 84, 0.8),
        rgba(25, 135, 84, 1),
        rgba(25, 135, 84, 0.8)
    );
}

.notification-warning::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(255, 193, 7, 0.8),
        transparent,
        rgba(255, 193, 7, 0.8),
        transparent
    );
}

.notification-warning .notification-icon {
    color: #ffd43b;
}

.notification-warning .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(255, 193, 7, 0.8),
        rgba(255, 193, 7, 1),
        rgba(255, 193, 7, 0.8)
    );
}

.notification-error::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(220, 53, 69, 0.8),
        transparent,
        rgba(220, 53, 69, 0.8),
        transparent
    );
}

.notification-error .notification-icon {
    color: #ff6b6b;
}

.notification-error .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(220, 53, 69, 0.8),
        rgba(220, 53, 69, 1),
        rgba(220, 53, 69, 0.8)
    );
}

.notification-system::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(111, 66, 193, 0.8),
        transparent,
        rgba(111, 66, 193, 0.8),
        transparent
    );
}

.notification-system .notification-icon {
    color: #9775fa;
}

.notification-system .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(111, 66, 193, 0.8),
        rgba(111, 66, 193, 1),
        rgba(111, 66, 193, 0.8)
    );
}

.notification-invitation::before {
    background: linear-gradient(45deg,
        transparent,
        rgba(32, 201, 151, 0.8),
        transparent,
        rgba(32, 201, 151, 0.8),
        transparent
    );
}

.notification-invitation .notification-icon {
    color: #3bc9db;
}

.notification-invitation .notification-progress::after {
    background: linear-gradient(90deg,
        rgba(32, 201, 151, 0.8),
        rgba(32, 201, 151, 1),
        rgba(32, 201, 151, 0.8)
    );
}

/* Connection status indicator */
#notification-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-left: 5px;
}

/* Notification list page styles */
.notification-list-item {
    border-left: 4px solid #ddd;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.notification-list-item:hover {
    transform: translateX(5px);
}

.notification-list-item.unread {
    background-color: rgba(13, 110, 253, 0.05);
}

.notification-list-item.info { border-left-color: #0d6efd; }
.notification-list-item.success { border-left-color: #198754; }
.notification-list-item.warning { border-left-color: #ffc107; }
.notification-list-item.error { border-left-color: #dc3545; }
.notification-list-item.system { border-left-color: #6f42c1; }
.notification-list-item.invitation { border-left-color: #20c997; }

/* Notification badge with animation */
@keyframes notification-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.notification-badge-animated {
    animation: notification-pulse 1s infinite;
}

/* Enhanced entrance animation with bounce effect */
@keyframes notification-entrance {
    0% {
        opacity: 0;
        transform: translateX(100px) scale(0.8) rotateY(90deg);
    }
    50% {
        opacity: 0.8;
        transform: translateX(-10px) scale(1.05) rotateY(0deg);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1) rotateY(0deg);
    }
}

.notification-toast.notification-show {
    animation: notification-entrance 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
    .notifications-container {
        right: 15px;
        left: 15px;
        max-width: none;
        top: 20px;
        transform: none;
    }

    .notification-toast {
        transform: translateY(-50px) scale(0.9);
    }

    .notification-toast.notification-show {
        transform: translateY(0) scale(1);
    }

    .notification-toast.notification-hide {
        transform: translateY(-50px) scale(0.9);
    }
}

@media (max-width: 480px) {
    .notification-header {
        padding: 12px 15px;
    }

    .notification-body {
        padding: 12px 15px;
        font-size: 13px;
    }

    .notification-title {
        font-size: 14px;
    }
}
