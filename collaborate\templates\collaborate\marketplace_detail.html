{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}{{ post.project.title }} - Project Marketplace{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Back Button -->
    <div style="margin-bottom: 2rem;">
      <a href="{% url 'collaborate:marketplace_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Marketplace
      </a>
    </div>
    
    <!-- Project Header -->
    <div class="dashboard-welcome">
      {% if post.featured_image %}
        <div style="text-align: center; margin-bottom: 2rem;">
          <img src="{{ post.featured_image.url }}" alt="{{ post.project.title }}" 
               style="width: 100%; max-width: 600px; height: 300px; object-fit: cover; border-radius: 15px;">
        </div>
      {% endif %}
      
      <h1 class="nunito-sans-header">{{ post.project.title }}</h1>
      <p class="welcome-subtitle">
        <i class="fas fa-user"></i> Created by {{ post.project.owner.username }} • 
        Published {{ post.published_at|date:"M d, Y" }}
      </p>
    </div>

    <!-- Project Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">👀</div>
        <div class="stat-content">
          <h3>{{ post.view_count }}</h3>
          <p>Views</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">❤️</div>
        <div class="stat-content">
          <h3>{{ reactions|length }}</h3>
          <p>Reactions</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">💬</div>
        <div class="stat-content">
          <h3>{{ comments|length }}</h3>
          <p>Comments</p>
        </div>
      </div>
    </div>
    
    <!-- Project Description -->
    <div class="dashboard-section">
      <h2><i class="fas fa-info-circle"></i> About This Project</h2>
      <p style="color: rgba(255, 255, 255, 0.9); line-height: 1.6; font-size: 1.1rem;">
        {{ post.showcase_description|linebreaks }}
      </p>
      
      {% if post.project.description != post.showcase_description %}
        <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.1);">
          <h3 style="color: #00d4ff; margin-bottom: 1rem;">Original Description:</h3>
          <p style="color: rgba(255, 255, 255, 0.8);">{{ post.project.description|linebreaks }}</p>
        </div>
      {% endif %}
    </div>
    
    <!-- Required Skills -->
    {% if post.project.required_skills.all %}
      <div class="dashboard-section">
        <h2><i class="fas fa-code"></i> Required Skills</h2>
        <div class="category-leaders">
          <div class="skills-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 1rem;">
            {% for skill in post.project.required_skills.all %}
              <div class="skill-card" style="background: rgba(0, 212, 255, 0.2); color: #00d4ff; padding: 1rem; border-radius: 10px; text-align: center; border: 1px solid rgba(0, 212, 255, 0.3);">
                {{ skill.name }}
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}
    
    <!-- Tags -->
    {% if post.tags %}
      <div class="dashboard-section">
        <h2><i class="fas fa-tags"></i> Project Tags</h2>
        <div class="tag-cloud" style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: center;">
          {% for tag in post.tags|split:"," %}
            <span class="tag-item" style="background: rgba(192, 255, 107, 0.2); color: #C0ff6b; padding: 0.5rem 1rem; border-radius: 20px; border: 1px solid rgba(192, 255, 107, 0.3);">
              {{ tag|trim }}
            </span>
          {% endfor %}
        </div>
      </div>
    {% endif %}
    
    <!-- Reactions -->
    {% if not is_owner %}
      <div class="dashboard-section">
        <h2><i class="fas fa-thumbs-up"></i> React to this project</h2>
        <!-- Hidden CSRF token for AJAX requests -->
        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
        <div class="quick-actions">
          <div class="action-grid" style="grid-template-columns: repeat(5, 1fr);">
            <button class="action-card reaction-btn {% if user_reaction and user_reaction.reaction_type == 'like' %}active{% endif %}"
                    onclick="reactToPost('like')" style="padding: 1rem; text-align: center;">
              <div class="action-icon">👍</div>
              <h3>Like</h3>
              <span class="reaction-count" id="like-count">
                {% for reaction in reactions %}
                  {% if reaction.reaction_type == 'like' %}{{ reaction.count }}{% endif %}
                {% endfor %}
              </span>
            </button>
            <button class="action-card reaction-btn {% if user_reaction and user_reaction.reaction_type == 'love' %}active{% endif %}"
                    onclick="reactToPost('love')" style="padding: 1rem; text-align: center;">
              <div class="action-icon">❤️</div>
              <h3>Love</h3>
              <span class="reaction-count" id="love-count">
                {% for reaction in reactions %}
                  {% if reaction.reaction_type == 'love' %}{{ reaction.count }}{% endif %}
                {% endfor %}
              </span>
            </button>
            <button class="action-card reaction-btn {% if user_reaction and user_reaction.reaction_type == 'wow' %}active{% endif %}"
                    onclick="reactToPost('wow')" style="padding: 1rem; text-align: center;">
              <div class="action-icon">😮</div>
              <h3>Wow</h3>
              <span class="reaction-count" id="wow-count">
                {% for reaction in reactions %}
                  {% if reaction.reaction_type == 'wow' %}{{ reaction.count }}{% endif %}
                {% endfor %}
              </span>
            </button>
            <button class="action-card reaction-btn {% if user_reaction and user_reaction.reaction_type == 'fire' %}active{% endif %}"
                    onclick="reactToPost('fire')" style="padding: 1rem; text-align: center;">
              <div class="action-icon">🔥</div>
              <h3>Fire</h3>
              <span class="reaction-count" id="fire-count">
                {% for reaction in reactions %}
                  {% if reaction.reaction_type == 'fire' %}{{ reaction.count }}{% endif %}
                {% endfor %}
              </span>
            </button>
            <button class="action-card reaction-btn {% if user_reaction and user_reaction.reaction_type == 'rocket' %}active{% endif %}"
                    onclick="reactToPost('rocket')" style="padding: 1rem; text-align: center;">
              <div class="action-icon">🚀</div>
              <h3>Rocket</h3>
              <span class="reaction-count" id="rocket-count">
                {% for reaction in reactions %}
                  {% if reaction.reaction_type == 'rocket' %}{{ reaction.count }}{% endif %}
                {% endfor %}
              </span>
            </button>
          </div>
        </div>
      </div>
    {% endif %}
    
    <!-- Application Section -->
    {% if not is_owner %}
      <div class="dashboard-section">
        <h2><i class="fas fa-handshake"></i> Join This Project</h2>
        
        {% if existing_application %}
          <div class="mentorship-content">
            <div class="session-card" style="background: {% if existing_application.status == 'pending' %}rgba(255, 193, 7, 0.2){% elif existing_application.status == 'accepted' %}rgba(40, 167, 69, 0.2){% else %}rgba(220, 53, 69, 0.2){% endif %};">
              <div class="session-info">
                <h4>Application Status: {{ existing_application.get_status_display }}</h4>
                <div class="session-details">
                  <span class="session-date">
                    <i class="fas fa-calendar"></i>
                    Applied {{ existing_application.created_at|date:"M d, Y" }}
                  </span>
                  {% if existing_application.status == 'pending' %}
                    <span style="color: rgba(255, 255, 255, 0.8);">Your application is being reviewed</span>
                  {% elif existing_application.status == 'accepted' %}
                    <span style="color: #28a745;">Welcome to the team!</span>
                  {% elif existing_application.status == 'rejected' %}
                    <span style="color: rgba(255, 255, 255, 0.8);">Better luck next time</span>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <form method="post" style="background: rgba(255, 255, 255, 0.05); padding: 2rem; border-radius: 15px;" id="applicationForm">
            {% csrf_token %}

            <!-- Show form errors if any -->
            {% if application_form.errors %}
              <div style="background: rgba(220, 53, 69, 0.1); border: 1px solid #dc3545; border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem;">
                <h5 style="color: #dc3545; margin-bottom: 0.5rem;">Please fix the following errors:</h5>
                {% for field, errors in application_form.errors.items %}
                  {% for error in errors %}
                    <p style="color: #dc3545; margin: 0.25rem 0; font-size: 0.9rem;">{{ field|title }}: {{ error }}</p>
                  {% endfor %}
                {% endfor %}
              </div>
            {% endif %}

            <div class="form-group" style="margin-bottom: 1.5rem;">
              <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.5rem; display: block;">Why do you want to join? <span style="color: #dc3545;">*</span></label>
              {{ application_form.message }}
              <div style="text-align: right; color: rgba(255, 255, 255, 0.6); font-size: 0.8rem; margin-top: 0.5rem;">
                <span id="messageCounter">0</span>/500 characters (minimum 20)
              </div>
            </div>
            <div class="form-group" style="margin-bottom: 1.5rem;">
              <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.5rem; display: block;">Skills you can contribute:</label>
              {{ application_form.skills_offered }}
              <div style="text-align: right; color: rgba(255, 255, 255, 0.6); font-size: 0.8rem; margin-top: 0.5rem;">
                <span id="skillsCounter">0</span>/500 characters
              </div>
            </div>
            <div class="form-group" style="margin-bottom: 1.5rem;">
              <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.5rem; display: block;">Portfolio/GitHub (optional):</label>
              {{ application_form.portfolio_link }}
            </div>
            <button type="submit" name="apply" class="btn btn-primary" style="width: 100%;" id="submitApplicationBtn">
              <i class="fas fa-paper-plane"></i> Submit Application
            </button>
          </form>
        {% endif %}
      </div>
    {% else %}
      <div class="dashboard-section">
        <h2><i class="fas fa-cog"></i> Project Management</h2>
        <div class="mentorship-actions">
          <a href="{% url 'collaborate:manage_marketplace_applications' post.project.hash %}" class="btn btn-primary">
            <i class="fas fa-users"></i> Manage Applications
          </a>
          <a href="{% url 'collaborate:project_detail' post.project.hash %}" class="btn btn-secondary">
            <i class="fas fa-edit"></i> Edit Project
          </a>
        </div>
      </div>
    {% endif %}

    <!-- Comments Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-comments"></i> Comments ({{ comments|length }})</h2>
      
      <!-- Add Comment Form -->
      <form method="post" style="background: rgba(255, 255, 255, 0.05); padding: 1.5rem; border-radius: 10px; margin-bottom: 2rem;" id="commentForm">
        {% csrf_token %}
        <div class="form-group">
          <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.5rem; display: block;">Add a comment:</label>
          {{ comment_form.content }}
          <div style="text-align: right; color: rgba(255, 255, 255, 0.6); font-size: 0.8rem; margin-top: 0.5rem;">
            <span id="commentCounter">0</span>/500 characters
          </div>
        </div>
        <button type="submit" name="comment" class="btn btn-primary" id="submitCommentBtn">
          <i class="fas fa-comment"></i> Post Comment
        </button>
      </form>
      
      <!-- Comments List -->
      {% if comments %}
        <div class="mentorship-content">
          {% for comment in comments %}
            <div class="session-card">
              <div class="session-info">
                <h4>{{ comment.author.username }}</h4>
                <div class="session-details">
                  <span class="session-date">
                    <i class="fas fa-calendar"></i>
                    {{ comment.created_at|date:"M d, Y H:i" }}
                  </span>
                </div>
                <p style="color: rgba(255, 255, 255, 0.9); margin-top: 1rem;">{{ comment.content|linebreaks }}</p>
              </div>
              
              <!-- Replies -->
              {% for reply in comment.replies.all %}
                <div class="session-card" style="margin-left: 2rem; margin-top: 1rem; border-left: 3px solid #00d4ff;">
                  <div class="session-info">
                    <h4>{{ reply.author.username }}</h4>
                    <div class="session-details">
                      <span class="session-date">
                        <i class="fas fa-calendar"></i>
                        {{ reply.created_at|date:"M d, Y H:i" }}
                      </span>
                    </div>
                    <p style="color: rgba(255, 255, 255, 0.9); margin-top: 1rem;">{{ reply.content|linebreaks }}</p>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="no-projects-message">
          <div class="no-projects-icon">💬</div>
          <h3>No comments yet</h3>
          <p>Be the first to share your thoughts about this project!</p>
        </div>
      {% endif %}
    </div>

    <!-- Project Details -->
    <div class="dashboard-section">
      <h2><i class="fas fa-info"></i> Project Details</h2>
      <div class="mentorship-stats">
        <div class="mentorship-stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <h3>{{ post.project.team_size }}</h3>
            <p>Team Size</p>
          </div>
        </div>
        <div class="mentorship-stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-content">
            <h3>{{ post.project.created_at|date:"M d" }}</h3>
            <p>Created</p>
          </div>
        </div>
        <div class="mentorship-stat-card">
          <div class="stat-icon">🔧</div>
          <div class="stat-content">
            <h3>{{ post.project.required_skills.count }}</h3>
            <p>Skills Required</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .stat-card, .session-card, .mentorship-stat-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds (similar to dashboard)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });

    // Comment form enhancements
    const commentTextarea = document.querySelector('#id_content');
    const commentCounter = document.querySelector('#commentCounter');
    const submitBtn = document.querySelector('#submitCommentBtn');

    if (commentTextarea && commentCounter) {
      // Update character counter
      function updateCounter() {
        const length = commentTextarea.value.length;
        commentCounter.textContent = length;

        // Change color based on length
        if (length < 10) {
          commentCounter.style.color = '#dc3545'; // Red
          submitBtn.disabled = true;
          submitBtn.style.opacity = '0.6';
        } else if (length > 450) {
          commentCounter.style.color = '#ffc107'; // Yellow
          submitBtn.disabled = false;
          submitBtn.style.opacity = '1';
        } else {
          commentCounter.style.color = '#28a745'; // Green
          submitBtn.disabled = false;
          submitBtn.style.opacity = '1';
        }

        // Disable if too long
        if (length > 500) {
          submitBtn.disabled = true;
          submitBtn.style.opacity = '0.6';
          commentCounter.style.color = '#dc3545';
        }
      }

      // Initial update
      updateCounter();

      // Update on input
      commentTextarea.addEventListener('input', updateCounter);
      commentTextarea.addEventListener('keyup', updateCounter);
    }

    // Application form enhancements
    const messageTextarea = document.querySelector('#id_message');
    const skillsTextarea = document.querySelector('#id_skills_offered');
    const messageCounter = document.querySelector('#messageCounter');
    const skillsCounter = document.querySelector('#skillsCounter');
    const submitAppBtn = document.querySelector('#submitApplicationBtn');

    if (messageTextarea && messageCounter) {
      function updateApplicationCounters() {
        const messageLength = messageTextarea.value.length;
        const skillsLength = skillsTextarea ? skillsTextarea.value.length : 0;

        messageCounter.textContent = messageLength;
        if (skillsCounter) skillsCounter.textContent = skillsLength;

        // Update message counter color
        if (messageLength < 20) {
          messageCounter.style.color = '#dc3545'; // Red
          submitAppBtn.disabled = true;
          submitAppBtn.style.opacity = '0.6';
        } else if (messageLength > 450) {
          messageCounter.style.color = '#ffc107'; // Yellow
          submitAppBtn.disabled = false;
          submitAppBtn.style.opacity = '1';
        } else {
          messageCounter.style.color = '#28a745'; // Green
          submitAppBtn.disabled = false;
          submitAppBtn.style.opacity = '1';
        }

        // Disable if too long
        if (messageLength > 500) {
          submitAppBtn.disabled = true;
          submitAppBtn.style.opacity = '0.6';
          messageCounter.style.color = '#dc3545';
        }
      }

      // Initial update
      updateApplicationCounters();

      // Update on input
      messageTextarea.addEventListener('input', updateApplicationCounters);
      messageTextarea.addEventListener('keyup', updateApplicationCounters);
      if (skillsTextarea) {
        skillsTextarea.addEventListener('input', updateApplicationCounters);
        skillsTextarea.addEventListener('keyup', updateApplicationCounters);
      }
    }
  });

  // Show message function for dynamic feedback
  function showMessage(message, type = 'info') {
    // Remove existing dynamic messages
    const existingMessages = document.querySelectorAll('.dynamic-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert alert-${type} slide-down dynamic-message`;
    messageDiv.innerHTML = `
      <span class="alert-icon">
        ${type === 'success' ? '🎉' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'}
      </span>
      ${message}
      <button class="alert-close" onclick="this.parentElement.remove()">×</button>
    `;

    // Insert at the top of the collaborate-card
    const collaborateCard = document.querySelector('.collaborate-card');
    if (collaborateCard) {
      collaborateCard.insertBefore(messageDiv, collaborateCard.firstChild);
    }

    // Auto-hide after 5 seconds
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          messageDiv.remove();
        }, 300);
      }
    }, 5000);
  }

function reactToPost(reactionType) {
    // Get CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    if (!csrfToken) {
        showMessage('Security token not found. Please refresh the page.', 'error');
        return;
    }

    // Add loading state
    const clickedButton = document.querySelector(`.reaction-btn[onclick="reactToPost('${reactionType}')"]`);
    if (clickedButton) {
        clickedButton.style.opacity = '0.6';
        clickedButton.style.pointerEvents = 'none';
        const originalText = clickedButton.querySelector('h3').textContent;
        clickedButton.querySelector('h3').textContent = 'Loading...';

        // Reset after timeout
        setTimeout(() => {
            clickedButton.style.opacity = '1';
            clickedButton.style.pointerEvents = 'auto';
            clickedButton.querySelector('h3').textContent = originalText;
        }, 3000);
    }

    fetch(`{% url 'collaborate:marketplace_react' post.id %}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({
            reaction_type: reactionType
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Reset loading state
        const clickedButton = document.querySelector(`.reaction-btn[onclick="reactToPost('${reactionType}')"]`);
        if (clickedButton) {
            clickedButton.style.opacity = '1';
            clickedButton.style.pointerEvents = 'auto';
            const reactionName = reactionType.charAt(0).toUpperCase() + reactionType.slice(1);
            clickedButton.querySelector('h3').textContent = reactionName;
        }

        if (data.success) {
            // Update UI based on reaction
            document.querySelectorAll('.reaction-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            if (data.action !== 'removed') {
                // Find the button that was clicked and mark it as active
                if (clickedButton) {
                    clickedButton.classList.add('active');
                }
            }

            // Update reaction counts (refresh the page to get updated counts)
            // In a production app, you might want to fetch updated counts via AJAX
            setTimeout(() => {
                location.reload();
            }, 1000);

            // Show success message
            const actionText = data.action === 'removed' ? 'Reaction removed!' : `Reacted with ${reactionType}!`;
            showMessage(actionText, 'success');
        } else {
            showMessage(data.error || 'Error updating reaction', 'error');
        }
    })
    .catch(error => {
        // Reset loading state on error
        const clickedButton = document.querySelector(`.reaction-btn[onclick="reactToPost('${reactionType}')"]`);
        if (clickedButton) {
            clickedButton.style.opacity = '1';
            clickedButton.style.pointerEvents = 'auto';
            const reactionName = reactionType.charAt(0).toUpperCase() + reactionType.slice(1);
            clickedButton.querySelector('h3').textContent = reactionName;
        }

        console.error('Error:', error);
        showMessage('Network error. Please check your connection and try again.', 'error');
    });
}


</script>

<style>
/* Override collaborate.css with dashboard styling */
:root {
  --color-border: #C0ff6b;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent !important;
  padding: 50px 0 !important;
  min-height: 100vh !important;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Dashboard welcome section */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
  background: linear-gradient(135deg, var(--color-border), #a0e066) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

.welcome-subtitle {
  font-size: 1.2rem !important;
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard stats */
.dashboard-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 40px !important;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 25px !important;
  text-align: center !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
}

.stat-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.stat-icon {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
}

.stat-content h3 {
  font-size: 2rem !important;
  color: var(--color-border) !important;
  margin-bottom: 5px !important;
}

.stat-content p {
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard sections */
.dashboard-section {
  margin-bottom: 40px !important;
}

.dashboard-section h2 {
  color: #ffffff !important;
  margin-bottom: 25px !important;
  text-align: center !important;
  font-size: 1.8rem !important;
}

/* Quick actions */
.quick-actions {
  margin-bottom: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 25px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 15px !important;
  padding: 20px !important;
  text-align: center !important;
  text-decoration: none !important;
  color: inherit !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.4s ease !important;
  display: block !important;
}

.action-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
  border-color: rgba(192, 255, 107, 0.5) !important;
  text-decoration: none !important;
  color: inherit !important;
}

.action-icon {
  font-size: 2rem !important;
  margin-bottom: 15px !important;
}

.action-card h3 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

/* Mentorship content */
.mentorship-content {
  margin-bottom: 30px;
}

.session-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 20px !important;
}

.session-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.session-info h4 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

.session-details {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
  margin-bottom: 10px !important;
  font-size: 0.9rem !important;
  color: #b0b0b0 !important;
}

.session-date {
  display: flex !important;
  align-items: center !important;
  gap: 5px !important;
}

/* Mentorship stats */
.mentorship-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 30px !important;
}

.mentorship-stat-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  text-align: center !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
}

.mentorship-stat-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

/* Mentorship actions */
.mentorship-actions {
  display: flex !important;
  gap: 15px !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
}

/* Category leaders */
.category-leaders {
  margin-top: 30px;
}

/* No projects message */
.no-projects-message {
  text-align: center !important;
  padding: 40px 20px !important;
  background-color: rgba(40, 40, 40, 0.3) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
}

.no-projects-icon {
  font-size: 3rem !important;
  margin-bottom: 20px !important;
  opacity: 0.8 !important;
}

.no-projects-message h3 {
  color: var(--color-border) !important;
  margin-bottom: 15px !important;
  font-size: 1.3rem !important;
}

.no-projects-message p {
  color: #e0e0e0 !important;
  margin-bottom: 25px !important;
  line-height: 1.6 !important;
  max-width: 400px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Marketplace detail specific styles */
.reaction-btn.active {
  background: linear-gradient(45deg, #C0ff6b, #00d4ff) !important;
  color: #1a1a2e !important;
  border-color: transparent !important;
}

.reaction-btn:hover {
  background: rgba(192, 255, 107, 0.2) !important;
  border-color: #C0ff6b !important;
  transform: translateY(-2px);
}

.reaction-count {
  display: block;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.5rem;
  font-weight: normal;
}

.reaction-btn.active .reaction-count {
  color: #1a1a2e;
  font-weight: bold;
}

/* Reaction button animations */
.reaction-btn {
  position: relative;
  overflow: hidden;
}

.reaction-btn:active {
  transform: scale(0.95);
}

.reaction-btn.active {
  animation: reactionPulse 0.6s ease-out;
}

@keyframes reactionPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Reaction icon animations */
.reaction-btn:hover .action-icon {
  animation: iconBounce 0.5s ease-in-out;
}

@keyframes iconBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Comment form improvements */
#id_content {
  min-height: 100px !important;
  resize: vertical !important;
  font-family: inherit !important;
}

#id_content:focus {
  box-shadow: 0 0 0 0.2rem rgba(192, 255, 107, 0.25) !important;
  border-color: var(--color-border) !important;
}

/* Comment cards improvements */
.session-card {
  position: relative;
}

.session-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-border), #00d4ff);
  border-radius: 12px 12px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.session-card:hover::before {
  opacity: 1;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  background-color: rgba(40, 40, 40, 0.8);
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 15px;
  border: none;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-info {
  border-left: 4px solid #17a2b8;
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-warning {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-danger {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.alert-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard-style animations */
.stat-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.action-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.action-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.session-card {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.session-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.mentorship-stat-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.mentorship-stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Button styling consistency */
.btn {
  background-color: transparent !important;
  color: #ffffff !important;
  border: 2px solid var(--color-border) !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  min-width: 120px !important;
}

.btn:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3) !important;
  transform: translateY(-2px) !important;
  text-decoration: none !important;
}

.btn-primary {
  background-color: rgba(192, 255, 107, 0.2) !important;
  border-color: var(--color-border) !important;
  color: var(--color-border) !important;
}

.btn-primary:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: #ffffff !important;
}

.btn-sm {
  padding: 8px 16px !important;
  font-size: 0.9rem !important;
  min-width: 100px !important;
}

/* Form styling consistency */
.form-control, textarea, input[type="text"], input[type="url"] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 0.8rem !important;
  color: white !important;
  font-size: 1rem !important;
  width: 100% !important;
  resize: vertical !important;
}

.form-control:focus, textarea:focus, input[type="text"]:focus, input[type="url"]:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--color-border) !important;
  box-shadow: 0 0 0 0.2rem rgba(192, 255, 107, 0.25) !important;
  outline: none !important;
}

.form-control::placeholder, textarea::placeholder, input::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Skills grid styling */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.skill-card {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
  border: 1px solid rgba(0, 212, 255, 0.3);
  transition: all 0.3s ease;
}

.skill-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Tag styling */
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.tag-item {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
}

.tag-item:hover {
  background: rgba(192, 255, 107, 0.3);
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .mentorship-stats {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .mentorship-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .mentorship-actions .btn {
    width: 100%;
  }
}
</style>
{% endblock %}
