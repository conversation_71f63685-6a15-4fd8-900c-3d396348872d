{% extends 'base.html' %}
{% load static %}
{% block title %}Dashboard - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Welcome Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">Welcome to Forge X, {{ user.username }}! 🚀</h1>
      <p class="welcome-subtitle">Your journey to mastering new skills starts here</p>
    </div>

    <!-- Quick Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-content">
          <h3>{{ course_count }}</h3>
          <p>Courses Available</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ project_count }}</h3>
          <p>Your Projects</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <h3>{{ user_skills_count }}</h3>
          <p>Your Skills</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>What would you like to do?</h2>
      <div class="action-grid">
        <a href="{% url 'learn:course_list' %}" class="action-card">
          <div class="action-icon">📖</div>
          <h3>Start Learning</h3>
          <p>Explore our courses and begin your learning journey</p>
        </a>
        <a href="{% url 'collaborate:project_list' %}" class="action-card">
          <div class="action-icon">🤝</div>
          <h3>Collaborate</h3>
          <p>Join projects and work with other developers</p>
        </a>
        <a href="{% url 'accounts:profile' %}" class="action-card">
          <div class="action-icon">👤</div>
          <h3>Setup Profile</h3>
          <p>Complete your profile and add your skills</p>
        </a>
        <a href="{% url 'mentorship:marketplace' %}" class="action-card">
          <div class="action-icon">🎓</div>
          <h3>Find Mentors</h3>
          <p>Get personalized guidance from experienced developers</p>
        </a>
      </div>
    </div>

    <!-- Mentorship Overview -->
    <div class="dashboard-section">
      <h2>🎓 Mentorship Overview</h2>

      <!-- Mentorship Stats -->
      <div class="mentorship-stats">
        <div class="mentorship-stat-card">
          <div class="stat-icon">👨‍🏫</div>
          <div class="stat-content">
            <h3>{{ mentorship_data.total_mentors }}</h3>
            <p>Active Mentors</p>
          </div>
        </div>
        <div class="mentorship-stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-content">
            <h3>{{ mentorship_data.sessions_today }}</h3>
            <p>Sessions Today</p>
          </div>
        </div>
        {% if mentorship_data.is_mentor %}
        <div class="mentorship-stat-card">
          <div class="stat-icon">💰</div>
          <div class="stat-content">
            <h3>${{ mentorship_data.monthly_earnings }}</h3>
            <p>This Month</p>
          </div>
        </div>
        {% endif %}
      </div>

      <!-- Mentorship Content Based on User Type -->
      {% if mentorship_data.is_mentor %}
        <!-- Mentor View -->
        <div class="mentorship-content">
          <div class="mentorship-section">
            <h3>📋 Your Upcoming Sessions</h3>
            {% if mentorship_data.mentor_upcoming_sessions %}
              <div class="sessions-list">
                {% for session in mentorship_data.mentor_upcoming_sessions %}
                  <div class="session-card">
                    <div class="session-info">
                      <h4>{{ session.learner.get_full_name|default:session.learner.username }}</h4>
                      <div class="session-details">
                        <span class="session-date">
                          <i class="fas fa-calendar"></i>
                          {{ session.scheduled_time|date:"M d, Y" }}
                        </span>
                        <span class="session-time">
                          <i class="fas fa-clock"></i>
                          {{ session.scheduled_time|time:"g:i A" }}
                        </span>
                        <span class="session-duration">{{ session.duration_minutes }} min</span>
                      </div>
                    </div>
                    <div class="session-actions">
                      <a href="{% url 'mentorship:session_room' session.room_id %}" class="btn btn-primary btn-sm">Join Session</a>
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="empty-state">
                <p>No upcoming sessions scheduled</p>
              </div>
            {% endif %}
          </div>

          <div class="mentorship-section">
            <h3>⭐ Recent Feedback</h3>
            {% if mentorship_data.recent_feedback %}
              <div class="feedback-list">
                {% for feedback in mentorship_data.recent_feedback %}
                  <div class="feedback-card">
                    <div class="feedback-header">
                      <span class="learner-name">{{ feedback.session.learner.get_full_name|default:feedback.session.learner.username }}</span>
                      <div class="rating">
                        {% for i in "12345" %}
                          <span class="star {% if feedback.get_average_rating >= i|add:0 %}filled{% endif %}">★</span>
                        {% endfor %}
                      </div>
                    </div>
                    {% if feedback.what_went_well %}
                      <p class="feedback-text">{{ feedback.what_went_well|truncatewords:15 }}</p>
                    {% endif %}
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="empty-state">
                <p>No feedback received yet</p>
              </div>
            {% endif %}
          </div>

          <div class="mentorship-actions">
            <a href="{% url 'mentorship:mentor_dashboard' %}" class="btn btn-primary">
              <i class="fas fa-tachometer-alt"></i>
              Mentor Dashboard
            </a>
            <a href="{% url 'mentorship:marketplace' %}" class="btn btn-secondary">
              <i class="fas fa-store"></i>
              View Marketplace
            </a>
          </div>
        </div>
      {% else %}
        <!-- Learner/Non-Mentor View -->
        <div class="mentorship-content">
          {% if mentorship_data.learner_upcoming_sessions %}
            <div class="mentorship-section">
              <h3>📚 Your Upcoming Sessions</h3>
              <div class="sessions-list">
                {% for session in mentorship_data.learner_upcoming_sessions %}
                  <div class="session-card">
                    <div class="session-info">
                      <h4>{{ session.mentor.get_full_name|default:session.mentor.username }}</h4>
                      <div class="session-details">
                        <span class="session-date">
                          <i class="fas fa-calendar"></i>
                          {{ session.scheduled_time|date:"M d, Y" }}
                        </span>
                        <span class="session-time">
                          <i class="fas fa-clock"></i>
                          {{ session.scheduled_time|time:"g:i A" }}
                        </span>
                        <span class="session-duration">{{ session.duration_minutes }} min</span>
                      </div>
                    </div>
                    <div class="session-actions">
                      <a href="{% url 'mentorship:session_room' session.room_id %}" class="btn btn-primary btn-sm">Join Session</a>
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          {% endif %}

          {% if mentorship_data.learner_recent_sessions %}
            <div class="mentorship-section">
              <h3>📖 Recent Sessions</h3>
              <div class="sessions-list">
                {% for session in mentorship_data.learner_recent_sessions %}
                  <div class="session-card completed">
                    <div class="session-info">
                      <h4>{{ session.mentor.get_full_name|default:session.mentor.username }}</h4>
                      <div class="session-details">
                        <span class="session-date">
                          <i class="fas fa-calendar"></i>
                          {{ session.ended_at|date:"M d, Y" }}
                        </span>
                        {% if session.rating %}
                          <div class="session-rating">
                            {% for i in "12345" %}
                              <span class="star {% if session.rating >= i|add:0 %}filled{% endif %}">★</span>
                            {% endfor %}
                          </div>
                        {% endif %}
                      </div>
                    </div>
                  </div>
                {% endfor %}
              </div>
            </div>
          {% endif %}

          <div class="mentorship-actions">
            <a href="{% url 'mentorship:marketplace' %}" class="btn btn-primary">
              <i class="fas fa-search"></i>
              Find a Mentor
            </a>
            <a href="{% url 'mentorship:my_sessions' %}" class="btn btn-secondary">
              <i class="fas fa-history"></i>
              My Sessions
            </a>
            <a href="{% url 'mentorship:become_mentor' %}" class="btn btn-outline">
              <i class="fas fa-graduation-cap"></i>
              Become a Mentor
            </a>
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Leaderboard Section -->
    <div class="dashboard-section">
      <h2>🏆 Top Developers Leaderboard</h2>

      <!-- Overall Top Developers -->
      <div class="leaderboard-container">
        <div class="leaderboard-header">
          <h3>🌟 Overall Top Performers</h3>
          <p>Based on projects, skills, and mentorship activity</p>
        </div>

        <div class="leaderboard-grid">
          {% for developer in leaderboard_data.top_developers|slice:":5" %}
          <div class="leaderboard-card {% if forloop.counter == 1 %}rank-1{% elif forloop.counter == 2 %}rank-2{% elif forloop.counter == 3 %}rank-3{% endif %}">
            <div class="rank-badge">
              {% if forloop.counter == 1 %}🥇
              {% elif forloop.counter == 2 %}🥈
              {% elif forloop.counter == 3 %}🥉
              {% else %}{{ forloop.counter }}{% endif %}
            </div>
            <div class="developer-info">
              <div class="developer-avatar">
                {% if developer.profile_picture %}
                  {% if developer.profile_picture.url %}
                    <img src="{{ developer.profile_picture.url }}" alt="{{ developer.user.username }}" class="avatar-img">
                  {% else %}
                    <img src="{{ developer.profile_picture }}" alt="{{ developer.user.username }}" class="avatar-img">
                  {% endif %}
                {% else %}
                  <div class="avatar-placeholder">{{ developer.user.username|first|upper }}</div>
                {% endif %}
              </div>
              <div class="developer-details">
                <h4>{{ developer.user.get_full_name|default:developer.user.username }}</h4>
                <div class="developer-stats">
                  <span class="stat-item">📚 {{ developer.project_count }} project{{ developer.project_count|pluralize }}</span>
                  <span class="stat-item">🎯 {{ developer.skills_count }} skill{{ developer.skills_count|pluralize }}</span>
                  {% if developer.is_mentor %}
                    <span class="stat-item">🎓 {{ developer.mentor_sessions }} session{{ developer.mentor_sessions|pluralize }}</span>
                    <span class="stat-item">⭐ {{ developer.mentor_rating|floatformat:1 }}/5.0</span>
                  {% endif %}
                </div>
                <div class="score-badge">Score: {{ developer.score|floatformat:0 }}</div>
              </div>
            </div>
          </div>
          {% empty %}
          <div class="no-leaderboard-data">
            <p>🚀 Be the first to appear on the leaderboard! Start by joining projects and adding skills to your profile.</p>
          </div>
          {% endfor %}
        </div>
      </div>

      <!-- Category Leaders -->
      <div class="category-leaders">
        <div class="category-grid">
          <!-- Top by Projects -->
          <div class="category-section">
            <h4>📚 Most Projects</h4>
            <div class="mini-leaderboard">
              {% for developer in leaderboard_data.top_by_projects|slice:":3" %}
              <div class="mini-leader-item">
                <div class="mini-avatar">
                  {% if developer.profile_picture %}
                    {% if developer.profile_picture.url %}
                      <img src="{{ developer.profile_picture.url }}" alt="{{ developer.user.username }}">
                    {% else %}
                      <img src="{{ developer.profile_picture }}" alt="{{ developer.user.username }}">
                    {% endif %}
                  {% else %}
                    <div class="mini-avatar-placeholder">{{ developer.user.username|first|upper }}</div>
                  {% endif %}
                </div>
                <div class="mini-info">
                  <span class="mini-name">{{ developer.user.get_full_name|default:developer.user.username|truncatechars:15 }}</span>
                  <span class="mini-stat">{{ developer.project_count }} projects</span>6+
                </div>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Top by Skills -->
          <div class="category-section">
            <h4>🎯 Most Skills</h4>
            <div class="mini-leaderboard">
              {% for developer in leaderboard_data.top_by_skills|slice:":3" %}
              <div class="mini-leader-item">
                <div class="mini-avatar">
                  {% if developer.profile_picture %}
                    {% if developer.profile_picture.url %}
                      <img src="{{ developer.profile_picture.url }}" alt="{{ developer.user.username }}">
                    {% else %}
                      <img src="{{ developer.profile_picture }}" alt="{{ developer.user.username }}">
                    {% endif %}
                  {% else %}
                    <div class="mini-avatar-placeholder">{{ developer.user.username|first|upper }}</div>
                  {% endif %}
                </div>
                <div class="mini-info">
                  <span class="mini-name">{{ developer.user.get_full_name|default:developer.user.username|truncatechars:15 }}</span>
                  <span class="mini-stat">{{ developer.skills_count }} skills</span>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>

          <!-- Top Mentors -->
          <div class="category-section">
            <h4>🎓 Top Mentors</h4>
            <div class="mini-leaderboard">
              {% for developer in leaderboard_data.top_mentors|slice:":3" %}
              <div class="mini-leader-item">
                <div class="mini-avatar">
                  {% if developer.profile_picture %}
                    {% if developer.profile_picture.url %}
                      <img src="{{ developer.profile_picture.url }}" alt="{{ developer.user.username }}">
                    {% else %}
                      <img src="{{ developer.profile_picture }}" alt="{{ developer.user.username }}">
                    {% endif %}
                  {% else %}
                    <div class="mini-avatar-placeholder">{{ developer.user.username|first|upper }}</div>
                  {% endif %}
                </div>
                <div class="mini-info">
                  <span class="mini-name">{{ developer.user.get_full_name|default:developer.user.username|truncatechars:15 }}</span>
                  <span class="mini-stat">⭐ {{ developer.mentor_rating|floatformat:1 }}/5.0</span>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Projects -->
    {% if recent_projects %}
    <div class="dashboard-section">
      <h2>Your Recent Projects</h2>
      <div class="recent-projects-grid">
        {% for project in recent_projects %}
        <div class="project-card-mini">
          <div class="project-header">
            <h4>{{ project.title }}</h4>
            <span class="project-status">
              {% if project.owner == user %}
                <span class="badge badge-owner">Owner</span>
              {% else %}
                <span class="badge badge-member">Member</span>
              {% endif %}
            </span>
          </div>
          <p class="project-description">{{ project.description|truncatewords:15 }}</p>
          <div class="project-meta">
            <span class="project-date">{{ project.created_at|date:"M d, Y" }}</span>
            <span class="project-members">{{ project.memberships.count }} members</span>
          </div>
          <div class="project-actions">
            <a href="{% url 'collaborate:project_detail' project.id %}" class="btn btn-primary btn-sm">View Project</a>
          </div>
        </div>
        {% endfor %}
      </div>
      <div class="view-all-projects">
        <a href="{% url 'collaborate:project_list' %}" class="btn btn-secondary">View All Projects</a>
      </div>
    </div>
    {% elif project_count == 0 %}
    <div class="dashboard-section">
      <div class="no-projects-message">
        <div class="no-projects-icon">🚀</div>
        <h3>Ready to Start Collaborating?</h3>
        <p>You haven't joined any projects yet. Explore available projects and start collaborating with other developers!</p>
        <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary">Explore Projects</a>
      </div>
    </div>
    {% endif %}

    <!-- Getting Started -->
    <div class="dashboard-section">
      <h2>{% if recent_projects %}Continue Your Journey{% else %}Getting Started{% endif %}</h2>
      <div class="getting-started">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>Complete Your Profile</h4>
            <p>Add your skills, bio, and upload your CV to get better project matches</p>
            <a href="{% url 'accounts:profile_setup' %}" class="btn btn-primary btn-sm">Complete Profile</a>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>Explore Courses</h4>
            <p>Browse our learning materials and start building your skills</p>
            <a href="{% url 'learn:course_list' %}" class="btn btn-primary btn-sm">Browse Courses</a>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>{% if recent_projects %}Find More Projects{% else %}Join Projects{% endif %}</h4>
            <p>Find collaborative projects that match your interests and skills</p>
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary btn-sm">{% if recent_projects %}Explore More{% else %}Find Projects{% endif %}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .step-item, .project-card-mini, .leaderboard-card, .mini-leader-item');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds (similar to course list)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
  });
</script>

<style>
/* Dashboard specific styles */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-content h3 {
  font-size: 2rem;
  color: var(--color-border);
  margin-bottom: 5px;
}

.stat-content p {
  color: #b0b0b0;
  margin: 0;
}

.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
}

.action-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.action-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
  text-decoration: none;
  color: inherit;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.action-card h3 {
  color: var(--color-border);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.action-card p {
  color: #e0e0e0;
  line-height: 1.5;
  margin: 0;
}

.dashboard-section h2 {
  color: #ffffff;
  margin-bottom: 25px;
  text-align: center;
}

.getting-started {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.5s ease;
}

.step-item.visible {
  opacity: 1;
  transform: translateX(0);
}

.step-number {
  background-color: var(--color-border);
  color: #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  color: var(--color-border);
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.step-content p {
  color: #e0e0e0;
  margin-bottom: 15px;
  line-height: 1.5;
}

/* Recent Projects Styles */
.recent-projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.project-card-mini {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
}

.project-card-mini.visible {
  opacity: 1;
  transform: translateY(0);
}

.project-card-mini:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.project-header h4 {
  color: var(--color-border);
  margin: 0;
  font-size: 1.1rem;
  flex: 1;
  margin-right: 10px;
}

.project-status {
  flex-shrink: 0;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-owner {
  background-color: rgba(192, 255, 107, 0.2);
  color: var(--color-border);
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.badge-member {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

.project-description {
  color: #e0e0e0;
  line-height: 1.5;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 0.8rem;
  color: #b0b0b0;
}

.project-date,
.project-members {
  display: flex;
  align-items: center;
  gap: 5px;
}

.project-date:before {
  content: "📅";
}

.project-members:before {
  content: "👥";
}

.project-actions {
  text-align: center;
}

.view-all-projects {
  text-align: center;
  margin-top: 20px;
}

/* No Projects Message */
.no-projects-message {
  text-align: center;
  padding: 40px 20px;
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.no-projects-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  opacity: 0.8;
}

.no-projects-message h3 {
  color: var(--color-border);
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.no-projects-message p {
  color: #e0e0e0;
  margin-bottom: 25px;
  line-height: 1.6;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Leaderboard Styles */
.leaderboard-container {
  margin-bottom: 40px;
}

.leaderboard-header {
  text-align: center;
  margin-bottom: 30px;
}

.leaderboard-header h3 {
  color: var(--color-border);
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.leaderboard-header p {
  color: #b0b0b0;
  margin: 0;
  font-size: 0.9rem;
}

.leaderboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.leaderboard-card {
  background-color: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px);
}

.leaderboard-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.leaderboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
  border-color: rgba(192, 255, 107, 0.4);
}

.leaderboard-card.rank-1 {
  border-color: #FFD700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(40, 40, 40, 0.6));
}

.leaderboard-card.rank-2 {
  border-color: #C0C0C0;
  background: linear-gradient(135deg, rgba(192, 192, 192, 0.1), rgba(40, 40, 40, 0.6));
}

.leaderboard-card.rank-3 {
  border-color: #CD7F32;
  background: linear-gradient(135deg, rgba(205, 127, 50, 0.1), rgba(40, 40, 40, 0.6));
}

.rank-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 1.5rem;
  font-weight: bold;
}

.developer-info {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.developer-avatar {
  flex-shrink: 0;
}

.avatar-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(192, 255, 107, 0.3);
}

.avatar-placeholder {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-border);
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.developer-details {
  flex: 1;
}

.developer-details h4 {
  color: var(--color-border);
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.developer-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 10px;
}

.stat-item {
  background-color: rgba(192, 255, 107, 0.1);
  color: #e0e0e0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.score-badge {
  background-color: rgba(192, 255, 107, 0.2);
  color: var(--color-border);
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  display: inline-block;
}

.no-leaderboard-data {
  text-align: center;
  padding: 40px 20px;
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  grid-column: 1 / -1;
}

.no-leaderboard-data p {
  color: #e0e0e0;
  margin: 0;
  font-size: 1rem;
}

/* Category Leaders */
.category-leaders {
  margin-top: 30px;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.category-section {
  background-color: rgba(40, 40, 40, 0.4);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.category-section h4 {
  color: var(--color-border);
  margin: 0 0 15px 0;
  font-size: 1rem;
  text-align: center;
}

.mini-leaderboard {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.mini-leader-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 8px;
  transition: all 0.2s ease;
  opacity: 0;
  transform: translateX(-20px);
}

.mini-leader-item.visible {
  opacity: 1;
  transform: translateX(0);
}

.mini-leader-item:hover {
  background-color: rgba(40, 40, 40, 0.6);
  transform: translateX(5px);
}

.mini-avatar {
  flex-shrink: 0;
}

.mini-avatar img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.mini-avatar-placeholder {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: var(--color-border);
  color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: bold;
}

.mini-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.mini-name {
  color: #e0e0e0;
  font-size: 0.85rem;
  font-weight: 500;
}

.mini-stat {
  color: #b0b0b0;
  font-size: 0.75rem;
}

/* Dashboard Logout Styles */
.dashboard-logout {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(244, 67, 54, 0.2);
}

.logout-form-dashboard {
  margin: 0;
}

.logout-btn-dashboard {
  background-color: rgba(244, 67, 54, 0.2) !important;
  border: 2px solid #f44336 !important;
  color: #f44336 !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  min-width: 150px;
}

.logout-btn-dashboard:hover {
  background-color: #f44336 !important;
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
}

.logout-icon {
  font-style: normal;
  font-size: 1.1em;
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .leaderboard-grid {
    grid-template-columns: 1fr;
  }

  .category-grid {
    grid-template-columns: 1fr;
  }

  .developer-info {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .developer-stats {
    justify-content: center;
  }

  .rank-badge {
    position: static;
    margin-bottom: 10px;
    font-size: 2rem;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .dashboard-welcome h1 {
    font-size: 2rem;
  }

  .recent-projects-grid {
    grid-template-columns: 1fr;
  }

  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .project-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
{% endblock %}
