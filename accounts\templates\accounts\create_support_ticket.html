{% extends 'base.html' %}
{% load static %}

{% block title %}Create Support Ticket - Forge X{% endblock %}

{% block content %}
<style>
/* Create Ticket Styles */
.create-ticket-container {
  padding: 40px 0;
  min-height: 80vh;
}

.form-container {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
  backdrop-filter: blur(10px);
}

.form-title {
  color: #C0ff6b;
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 30px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-group {
  margin-bottom: 25px;
}

.form-label {
  display: block;
  color: #C0ff6b;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
}

.form-control {
  width: 100%;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 12px 15px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 0 3px rgba(192, 255, 107, 0.2);
}

.form-control::placeholder {
  color: #999;
}

textarea.form-control {
  resize: vertical;
  min-height: 120px;
}

.help-text {
  color: #999;
  font-size: 0.85rem;
  margin-top: 5px;
}

.submit-btn {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #1a1a1a;
  border: none;
  border-radius: 12px;
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 20px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(192, 255, 107, 0.3);
}

.back-link {
  display: inline-block;
  color: #C0ff6b;
  text-decoration: none;
  margin-bottom: 20px;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #ffffff;
  text-decoration: none;
}

.back-link i {
  margin-right: 8px;
}

/* Error Styles */
.errorlist {
  list-style: none;
  padding: 0;
  margin: 5px 0 0 0;
}

.errorlist li {
  color: #F44336;
  font-size: 0.85rem;
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  border-radius: 4px;
  padding: 5px 10px;
  margin-bottom: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-ticket-container {
    padding: 20px 10px;
  }

  .form-container {
    padding: 30px 20px;
    margin: 0 10px;
  }

  .form-title {
    font-size: 2rem;
  }
}
</style>

<div class="create-ticket-container">
  <div class="tile-wrap">
    <div class="form-container">
      <a href="{% url 'accounts:help_center' %}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Help Center
      </a>
      
      <h1 class="form-title">Create Support Ticket</h1>
      
      <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <div class="form-group">
          <label for="{{ form.category.id_for_label }}" class="form-label">
            {{ form.category.label }}
          </label>
          {{ form.category }}
          {% if form.category.help_text %}
            <div class="help-text">{{ form.category.help_text }}</div>
          {% endif %}
          {{ form.category.errors }}
        </div>
        
        <div class="form-group">
          <label for="{{ form.subject.id_for_label }}" class="form-label">
            {{ form.subject.label }}
          </label>
          {{ form.subject }}
          {% if form.subject.help_text %}
            <div class="help-text">{{ form.subject.help_text }}</div>
          {% endif %}
          {{ form.subject.errors }}
        </div>
        
        <div class="form-group">
          <label for="{{ form.description.id_for_label }}" class="form-label">
            {{ form.description.label }}
          </label>
          {{ form.description }}
          {% if form.description.help_text %}
            <div class="help-text">{{ form.description.help_text }}</div>
          {% endif %}
          {{ form.description.errors }}
        </div>
        
        <div class="form-group">
          <label for="{{ form.priority.id_for_label }}" class="form-label">
            {{ form.priority.label }}
          </label>
          {{ form.priority }}
          {% if form.priority.help_text %}
            <div class="help-text">{{ form.priority.help_text }}</div>
          {% endif %}
          {{ form.priority.errors }}
        </div>
        
        <button type="submit" class="submit-btn">
          <i class="fas fa-paper-plane"></i> Create Ticket
        </button>
      </form>
      
      <div style="text-align: center; margin-top: 30px; color: #999;">
        <p>
          <i class="fas fa-info-circle"></i>
          You'll receive email notifications about updates to your ticket.
          <br>
          Our support team typically responds within 24-48 hours.
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
