"""
Performance optimization utilities for the matching engine.
This module provides functions to optimize the performance of the matching engine
through caching, query optimization, and background processing.
"""
import time
import hashlib
import json
from functools import wraps
from django.core.cache import cache
from django.db import connection, reset_queries
from django.db.models import Prefetch, Count, Q
from ..models import UserSkill, Project, ProjectMembership, Notification
from accounts.models import UserProfile
from django.contrib.auth import get_user_model

# Cache timeout in seconds (10 minutes)
CACHE_TIMEOUT = 600

def query_debugger(func):
    """
    Debug decorator to track database queries.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        reset_queries()
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()

        query_info = connection.queries
        print(f"Function : {func.__name__}")
        print(f"Number of Queries : {len(query_info)}")
        print(f"Execution Time : {(end - start):.2f}s")

        return result
    return wrapper

def cache_match_results(project_id, user_ids, timeout=CACHE_TIMEOUT):
    """
    Generate a cache key for match results.

    Args:
        project_id: ID of the project
        user_ids: List of user IDs to match
        timeout: Cache timeout in seconds

    Returns:
        str: Cache key
    """
    # Sort user IDs to ensure consistent cache keys
    sorted_user_ids = sorted(user_ids)

    # Create a string representation of the parameters
    param_str = f"project_{project_id}_users_{'_'.join(map(str, sorted_user_ids))}"

    # Generate a hash of the parameter string
    cache_key = f"match_results_{hashlib.md5(param_str.encode()).hexdigest()}"

    return cache_key

def get_cached_match_results(project, available_users, match_function):
    """
    Get cached match results or compute and cache them.

    Args:
        project: Project object
        available_users: List of User objects
        match_function: Function to use for matching

    Returns:
        dict: Match results
    """
    # Get user IDs
    user_ids = [user.id for user in available_users]

    # Generate cache key
    cache_key = cache_match_results(project.id, user_ids)

    # Try to get results from cache
    cached_results = cache.get(cache_key)
    if cached_results:
        # Convert user IDs back to User objects
        User = get_user_model()
        team_member_ids = cached_results.pop('team_member_ids')
        cached_results['team_members'] = list(User.objects.filter(id__in=team_member_ids))
        return cached_results

    # Compute results
    results = match_function(project, available_users)

    # Cache results (store user IDs instead of User objects)
    cache_data = results.copy()
    cache_data['team_member_ids'] = [user.id for user in results['team_members']]
    del cache_data['team_members']

    cache.set(cache_key, cache_data, CACHE_TIMEOUT)

    return results

def optimize_user_query(project):
    """
    Optimize the query for retrieving available users.

    Args:
        project: Project object

    Returns:
        QuerySet: Optimized query for available users
    """
    User = get_user_model()

    # Get IDs of users to exclude
    project_owner_id = project.owner.id

    # Get IDs of users who are already members
    member_ids = ProjectMembership.objects.filter(
        project=project
    ).values_list('user_id', flat=True)

    # Get IDs of users who have pending invitations
    invited_ids = Notification.objects.filter(
        project=project,
        is_accepted__isnull=True
    ).values_list('user_id', flat=True)

    # Combine all IDs to exclude
    exclude_ids = list(member_ids) + list(invited_ids) + [project_owner_id]

    # Create an optimized query with prefetching
    optimized_query = User.objects.filter(
        is_active=True
    ).exclude(
        id__in=exclude_ids
    ).prefetch_related(
        Prefetch(
            'user_skills',
            queryset=UserSkill.objects.select_related('skill')
        ),
        'profile'
    )

    return optimized_query

def optimize_project_query(project_id):
    """
    Optimize the query for retrieving a project with related data.

    Args:
        project_id: ID of the project

    Returns:
        Project: Project object with prefetched data
    """
    return Project.objects.filter(
        id=project_id
    ).prefetch_related(
        'required_skills',
        'critical_skills',
        Prefetch(
            'memberships',
            queryset=ProjectMembership.objects.select_related('user')
        )
    ).select_related(
        'owner',
        'owner__profile'
    ).first()

def get_user_skill_matrix(users, skills):
    """
    Create a matrix of users and their skills for efficient lookup.

    Args:
        users: List of User objects
        skills: List of Skill objects

    Returns:
        dict: Matrix of user skills with proficiency levels
    """
    # Get all user skills in one query
    user_ids = [user.id for user in users]
    skill_ids = [skill.id for skill in skills]

    user_skills = UserSkill.objects.filter(
        user_id__in=user_ids,
        skill_id__in=skill_ids
    ).select_related('skill')

    # Create a matrix for O(1) lookup
    skill_matrix = {}
    for user in users:
        skill_matrix[user.id] = {}
        for skill in skills:
            skill_matrix[user.id][skill.id] = None

    # Fill in the matrix with proficiency levels
    for us in user_skills:
        skill_matrix[us.user_id][us.skill_id] = us.proficiency

    return skill_matrix
