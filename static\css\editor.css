/* VS Code-like Editor Styles */
/* Color Variables - VS Code Dark Theme */
:root {
  /* Main Colors */
  --vscode-bg: #1e1e1e;
  --vscode-sidebar-bg: #252526;
  --vscode-panel-bg: #181818;
  --vscode-border: #3e3e42;
  --vscode-hover: #2a2d2e;
  --vscode-active: #37373d;
  --vscode-selection: #264f78;

  /* Text Colors */
  --vscode-text: #cccccc;
  --vscode-text-muted: #969696;
  --vscode-text-disabled: #656565;

  /* Accent Colors */
  --vscode-blue: #007acc;
  --vscode-blue-hover: #1177bb;
  --vscode-green: #28a745;
  --vscode-red: #f48771;
  --vscode-orange: #ff8c00;
  --vscode-yellow: #ffcc02;

  /* Status Bar */
  --vscode-statusbar-bg: #007acc;
  --vscode-statusbar-text: #ffffff;

  /* Activity Bar */
  --vscode-activitybar-bg: #333333;
  --vscode-activitybar-active: #007acc;

  /* Scrollbar */
  --vscode-scrollbar-bg: #1e1e1e;
  --vscode-scrollbar-thumb: #464647;
  --vscode-scrollbar-thumb-hover: #5a5a5a;

  /* Shadows */
  --vscode-shadow: 0 2px 8px rgba(0, 0, 0, 0.16);
  --vscode-shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.24);

  /* Transitions */
  --vscode-transition: all 0.2s ease;
  --vscode-transition-fast: all 0.1s ease;
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', 'Segoe WPC', 'Segoe UI Variable', system-ui, -apple-system, sans-serif;
  font-size: 13px;
  line-height: 1.4;
  color: var(--vscode-text);
  background: var(--vscode-bg);
  overflow: hidden;
}

/* Main Editor Container */
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--vscode-bg);
  color: var(--vscode-text);
  padding-bottom: 22px; /* Account for status bar */
}

/* Editor Header */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--vscode-sidebar-bg);
  border-bottom: 1px solid var(--vscode-border);
  min-height: 35px;
  z-index: 100;
}

.editor-header h1 {
  margin: 0;
  font-size: 13px;
  font-weight: 400;
  color: var(--vscode-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.editor-controls {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* Button Styles */
.btn-primary, .btn-info, .btn-success, .btn-warning, .btn-secondary, .btn-danger {
  padding: 4px 8px;
  border: none;
  border-radius: 2px;
  font-size: 11px;
  font-weight: 400;
  cursor: pointer;
  transition: var(--vscode-transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 4px;
  min-height: 22px;
  background: transparent;
  color: var(--vscode-text);
  border: 1px solid transparent;
}

.btn-primary:hover, .btn-info:hover, .btn-success:hover,
.btn-warning:hover, .btn-secondary:hover, .btn-danger:hover {
  background: var(--vscode-hover);
  border-color: var(--vscode-border);
}

.btn-primary:active, .btn-info:active, .btn-success:active,
.btn-warning:active, .btn-secondary:active, .btn-danger:active {
  background: var(--vscode-active);
}

.btn-primary { color: var(--vscode-blue); }
.btn-success { color: var(--vscode-green); }
.btn-warning { color: var(--vscode-orange); }
.btn-danger { color: var(--vscode-red); }

/* Main Editor Layout */
.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* File Explorer */
.file-explorer {
  width: 240px;
  min-width: 170px;
  max-width: 400px;
  background: var(--vscode-sidebar-bg);
  border-right: 1px solid var(--vscode-border);
  display: flex;
  flex-direction: column;
  resize: horizontal;
  overflow: hidden;
}

.file-explorer-header {
  padding: 8px 12px;
  border-bottom: 1px solid var(--vscode-border);
  background: var(--vscode-sidebar-bg);
}

.file-explorer-header h3 {
  margin: 0;
  font-size: 11px;
  font-weight: 600;
  color: var(--vscode-text);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-controls {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  align-items: center;
}

.file-search {
  flex: 1;
  padding: 4px 8px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  color: var(--vscode-text);
  font-size: 11px;
}

.file-search:focus {
  outline: none;
  border-color: var(--vscode-blue);
  box-shadow: 0 0 0 1px var(--vscode-blue);
}

.btn-sm {
  padding: 2px 6px;
  font-size: 10px;
  min-height: 20px;
}

/* File Tree */
#file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 4px 0;
}

.file-item, .folder-item {
  display: flex;
  align-items: center;
  padding: 2px 8px 2px 12px;
  cursor: pointer;
  font-size: 13px;
  line-height: 22px;
  color: var(--vscode-text);
  transition: var(--vscode-transition-fast);
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-item:hover, .folder-item:hover {
  background: var(--vscode-hover);
}

.file-item.selected, .folder-item.selected {
  background: var(--vscode-selection);
  color: white;
}

.file-item i, .folder-item i {
  margin-right: 6px;
  width: 16px;
  text-align: center;
  font-size: 12px;
}

.folder-children {
  margin-left: 16px;
  display: none;
}

.folder-children.expanded {
  display: block;
}

.toggle-icon {
  transition: transform 0.15s ease;
  margin-right: 2px;
  font-size: 10px;
}

.toggle-icon.expanded {
  transform: rotate(90deg);
}

/* File Tabs */
.file-tabs {
  display: flex;
  background: var(--vscode-sidebar-bg);
  border-top: 1px solid var(--vscode-border);
  overflow-x: auto;
  min-height: 35px;
}

.file-tab {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: var(--vscode-bg);
  border-right: 1px solid var(--vscode-border);
  cursor: pointer;
  font-size: 13px;
  color: var(--vscode-text-muted);
  transition: var(--vscode-transition-fast);
  min-width: 120px;
  max-width: 200px;
}

.file-tab:hover {
  color: var(--vscode-text);
}

.file-tab.active {
  background: var(--vscode-bg);
  color: var(--vscode-text);
  border-bottom: 2px solid var(--vscode-blue);
}

.file-tab .close-btn {
  margin-left: 8px;
  opacity: 0;
  transition: var(--vscode-transition-fast);
}

.file-tab:hover .close-btn {
  opacity: 1;
}

/* Editor Content */
.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--vscode-bg);
  position: relative;
}

#monaco-editor {
  flex: 1;
  width: 100%;
  height: 100%;
}

/* Panel Container */
.panel-container {
  height: 200px;
  min-height: 100px;
  max-height: 400px;
  background: var(--vscode-panel-bg);
  border-top: 1px solid var(--vscode-border);
  display: flex;
  flex-direction: column;
  resize: vertical;
  overflow: hidden;
}

.panel-tabs {
  display: flex;
  background: var(--vscode-panel-bg);
  border-bottom: 1px solid var(--vscode-border);
  min-height: 35px;
}

.panel-tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 13px;
  color: var(--vscode-text-muted);
  transition: var(--vscode-transition-fast);
  border-bottom: 2px solid transparent;
}

.panel-tab:hover {
  color: var(--vscode-text);
  background: var(--vscode-hover);
}

.panel-tab.active {
  color: var(--vscode-text);
  border-bottom-color: var(--vscode-blue);
}

.panel-tab i {
  margin-right: 6px;
  font-size: 12px;
}

.panel-content {
  flex: 1;
  display: none;
  flex-direction: column;
  overflow: hidden;
}

.panel-content.active {
  display: flex;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: var(--vscode-panel-bg);
  border-bottom: 1px solid var(--vscode-border);
}

.panel-header h3 {
  margin: 0;
  font-size: 13px;
  font-weight: 400;
  color: var(--vscode-text);
}

/* Output Panel */
#output-content {
  flex: 1;
  padding: 8px 12px;
  background: var(--vscode-bg);
  color: var(--vscode-text);
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-y: auto;
  white-space: pre-wrap;
}

/* Terminal Panel */
#terminal-container {
  flex: 1;
  background: var(--vscode-bg);
  color: var(--vscode-text);
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
  overflow: hidden;
}

#terminal-input-div {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background: var(--vscode-bg);
  border-top: 1px solid var(--vscode-border);
}

#terminal-prompt {
  color: var(--vscode-green);
  margin-right: 8px;
  font-family: 'Consolas', 'Courier New', monospace;
}

#terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--vscode-text);
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
  outline: none;
}

.terminal-tabs-container {
  display: flex;
  background: var(--vscode-panel-bg);
  border-top: 1px solid var(--vscode-border);
  padding: 4px 8px;
}

.terminal-tab-add-btn {
  padding: 4px 8px;
  background: transparent;
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  color: var(--vscode-text);
  font-size: 11px;
  cursor: pointer;
  transition: var(--vscode-transition-fast);
}

.terminal-tab-add-btn:hover {
  background: var(--vscode-hover);
}

/* Extensions Sidebar */
.extensions-sidebar {
  width: 300px;
  background: var(--vscode-sidebar-bg);
  border-right: 1px solid var(--vscode-border);
  display: none;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 200;
  box-shadow: var(--vscode-shadow-heavy);
}

.extensions-sidebar.active {
  display: flex;
}

.extensions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-border);
  background: var(--vscode-sidebar-bg);
}

.extensions-header h3 {
  margin: 0;
  color: var(--vscode-text);
  font-size: 13px;
  font-weight: 600;
}

.extensions-search {
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-border);
}

.extensions-search input {
  width: 100%;
  padding: 6px 10px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  color: var(--vscode-text);
  font-size: 13px;
}

.extensions-search input:focus {
  outline: none;
  border-color: var(--vscode-blue);
  box-shadow: 0 0 0 1px var(--vscode-blue);
}

.extensions-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--vscode-border);
}

.category-btn {
  padding: 4px 8px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  color: var(--vscode-text);
  font-size: 11px;
  cursor: pointer;
  transition: var(--vscode-transition);
}

.category-btn.active,
.category-btn:hover {
  background: var(--vscode-blue);
  border-color: var(--vscode-blue);
  color: white;
}

.extensions-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.extension-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--vscode-bg);
  border-radius: 4px;
  cursor: pointer;
  transition: var(--vscode-transition);
  border: 1px solid transparent;
}

.extension-item:hover {
  background: var(--vscode-hover);
  border-color: var(--vscode-border);
}

.extension-item.installed {
  border-color: var(--vscode-blue);
  background: rgba(0, 122, 204, 0.1);
}

.extension-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  border-radius: 4px;
  background: var(--vscode-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.extension-info {
  flex: 1;
  min-width: 0;
}

.extension-name {
  font-weight: 600;
  color: var(--vscode-text);
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.2;
}

.extension-description {
  color: var(--vscode-text-muted);
  font-size: 12px;
  line-height: 1.3;
  margin-bottom: 8px;
}

.extension-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.extension-btn {
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 2px;
  border: 1px solid var(--vscode-border);
  cursor: pointer;
  transition: var(--vscode-transition);
  background: var(--vscode-bg);
  color: var(--vscode-text);
}

.extension-btn.install {
  background: var(--vscode-blue);
  border-color: var(--vscode-blue);
  color: white;
}

.extension-btn.installed {
  background: var(--vscode-green);
  border-color: var(--vscode-green);
  color: white;
}

.extension-btn.disable {
  background: var(--vscode-text-muted);
  border-color: var(--vscode-text-muted);
  color: white;
}

.extension-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* AI Assistant Panel */
.ai-assistant-panel {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 400px;
  height: 500px;
  background: var(--vscode-sidebar-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 6px;
  box-shadow: var(--vscode-shadow-heavy);
  display: none;
  flex-direction: column;
  z-index: 1000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.ai-assistant-panel.active {
  display: flex;
  animation: slideInFromRight 0.3s ease;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vscode-panel-bg);
  border-bottom: 1px solid var(--vscode-border);
  border-radius: 6px 6px 0 0;
}

.ai-header h3 {
  margin: 0;
  color: var(--vscode-text);
  font-size: 14px;
  font-weight: 600;
}

.ai-chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: var(--vscode-bg);
}

.ai-message {
  margin-bottom: 16px;
  padding: 10px 12px;
  border-radius: 6px;
  max-width: 85%;
  word-wrap: break-word;
  font-size: 13px;
  line-height: 1.4;
}

.ai-message.user {
  background: var(--vscode-blue);
  color: white;
  margin-left: auto;
  text-align: right;
}

.ai-message.assistant {
  background: var(--vscode-panel-bg);
  color: var(--vscode-text);
  border: 1px solid var(--vscode-border);
}

.ai-message.system {
  background: var(--vscode-hover);
  color: var(--vscode-text-muted);
  font-style: italic;
  text-align: center;
  margin: 10px auto;
  max-width: 90%;
}

.ai-input-container {
  padding: 16px;
  border-top: 1px solid var(--vscode-border);
  background: var(--vscode-sidebar-bg);
}

.ai-input-container textarea {
  width: 100%;
  padding: 8px 12px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 4px;
  color: var(--vscode-text);
  font-size: 13px;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.ai-input-container textarea:focus {
  outline: none;
  border-color: var(--vscode-blue);
  box-shadow: 0 0 0 1px var(--vscode-blue);
}

.ai-controls {
  display: flex;
  gap: 6px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.ai-controls button {
  padding: 6px 10px;
  font-size: 11px;
  border-radius: 3px;
  border: 1px solid var(--vscode-border);
  cursor: pointer;
  transition: var(--vscode-transition);
  display: flex;
  align-items: center;
  gap: 4px;
  background: var(--vscode-bg);
  color: var(--vscode-text);
}

.ai-controls .btn-primary {
  background: var(--vscode-blue);
  border-color: var(--vscode-blue);
  color: white;
}

.ai-controls .btn-secondary {
  background: var(--vscode-text-muted);
  border-color: var(--vscode-text-muted);
  color: white;
}

.ai-controls .btn-warning {
  background: var(--vscode-orange);
  border-color: var(--vscode-orange);
  color: white;
}

.ai-controls .btn-success {
  background: var(--vscode-green);
  border-color: var(--vscode-green);
  color: white;
}

.ai-controls button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

/* Status Bar */
.status-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  background: var(--vscode-statusbar-bg);
  color: var(--vscode-statusbar-text);
  font-size: 12px;
  min-height: 22px;
  border-top: 1px solid var(--vscode-border);
  z-index: 100;
}

.status-left, .status-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 2px;
  transition: var(--vscode-transition-fast);
}

.status-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.status-item i {
  font-size: 11px;
}

/* Activity Bar */
.activity-bar {
  width: 48px;
  background: var(--vscode-activitybar-bg);
  border-right: 1px solid var(--vscode-border);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 0;
  z-index: 100;
}

.activity-item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  cursor: pointer;
  border-radius: 4px;
  color: var(--vscode-text-muted);
  transition: var(--vscode-transition);
  position: relative;
}

.activity-item:hover {
  color: var(--vscode-text);
  background: var(--vscode-hover);
}

.activity-item.active {
  color: var(--vscode-activitybar-active);
  background: var(--vscode-hover);
}

.activity-item.active::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--vscode-activitybar-active);
}

.activity-item i {
  font-size: 16px;
}

/* Custom Scrollbars */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--vscode-scrollbar-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--vscode-scrollbar-thumb);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--vscode-scrollbar-thumb-hover);
}

::-webkit-scrollbar-corner {
  background: var(--vscode-scrollbar-bg);
}

/* Monaco Editor Customizations */
.monaco-editor {
  font-family: 'Consolas', 'Courier New', monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.monaco-editor .margin {
  background: var(--vscode-sidebar-bg) !important;
}

.monaco-editor .monaco-editor-background {
  background: var(--vscode-bg) !important;
}

.monaco-editor .current-line {
  background: rgba(255, 255, 255, 0.04) !important;
}

.monaco-editor .selected-text {
  background: var(--vscode-selection) !important;
}

/* Breakpoint Styles */
.breakpoint-glyph {
  background: #e51400 !important;
  border-radius: 50% !important;
  width: 12px !important;
  height: 12px !important;
  margin-left: 2px !important;
  margin-top: 2px !important;
}

.breakpoint-glyph:hover {
  background: #ff4444 !important;
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 4px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  box-shadow: var(--vscode-shadow);
  z-index: 10000;
  max-width: 400px;
  word-wrap: break-word;
  animation: slideInRight 0.3s ease;
}

.notification.success {
  background: var(--vscode-green);
}

.notification.error {
  background: var(--vscode-red);
}

.notification.warning {
  background: var(--vscode-orange);
  color: #212529;
}

.notification.info {
  background: var(--vscode-blue);
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading States */
.loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid var(--vscode-border);
  border-radius: 50%;
  border-top-color: var(--vscode-blue);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Context Menu */
.context-menu {
  position: fixed;
  background: var(--vscode-sidebar-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 4px;
  box-shadow: var(--vscode-shadow);
  padding: 4px 0;
  z-index: 10000;
  min-width: 160px;
  font-size: 13px;
}

.context-menu-item {
  padding: 6px 12px;
  cursor: pointer;
  color: var(--vscode-text);
  transition: var(--vscode-transition-fast);
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu-item:hover {
  background: var(--vscode-hover);
}

.context-menu-item.disabled {
  color: var(--vscode-text-disabled);
  cursor: not-allowed;
}

.context-menu-item.disabled:hover {
  background: transparent;
}

.context-menu-separator {
  height: 1px;
  background: var(--vscode-border);
  margin: 4px 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .file-explorer {
    width: 200px;
  }

  .ai-assistant-panel {
    width: 350px;
  }
}

@media (max-width: 768px) {
  .file-explorer {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    z-index: 200;
    box-shadow: var(--vscode-shadow-heavy);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .file-explorer.mobile-open {
    transform: translateX(0);
  }

  .ai-assistant-panel {
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
    border-radius: 0;
  }

  .editor-header {
    padding: 8px 12px;
  }

  .editor-header h1 {
    font-size: 12px;
  }

  .btn-primary, .btn-info, .btn-success, .btn-warning, .btn-secondary, .btn-danger {
    padding: 6px 8px;
    font-size: 10px;
  }
}

/* Print Styles */
@media print {
  .file-explorer,
  .panel-container,
  .extensions-sidebar,
  .ai-assistant-panel,
  .status-bar,
  .activity-bar {
    display: none !important;
  }

  .editor-content {
    width: 100% !important;
    height: auto !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --vscode-bg: #000000;
    --vscode-sidebar-bg: #000000;
    --vscode-panel-bg: #000000;
    --vscode-border: #ffffff;
    --vscode-text: #ffffff;
    --vscode-text-muted: #ffffff;
    --vscode-blue: #0078d4;
    --vscode-hover: #333333;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Collaboration Panel Styles */
.collab-panel {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 360px;
  height: 500px;
  background: var(--vscode-sidebar-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 6px;
  box-shadow: var(--vscode-shadow-heavy);
  display: flex;
  flex-direction: column;
  z-index: 999;
  color: var(--vscode-text);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.collab-tabs {
  display: flex;
  padding: 8px;
  border-bottom: 1px solid var(--vscode-border);
  background: var(--vscode-panel-bg);
}

.tab-btn {
  flex: 1;
  padding: 6px 8px;
  background: none;
  border: none;
  color: var(--vscode-text-muted);
  cursor: pointer;
  font-size: 13px;
  transition: var(--vscode-transition);
  border-radius: 2px;
}

.tab-btn.active,
.tab-btn:hover {
  color: var(--vscode-blue);
  background: var(--vscode-hover);
}

.tab-content {
  display: none;
  flex: 1;
  overflow: hidden;
}

.tab-content.active {
  display: flex;
  flex-direction: column;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 8px;
  padding: 8px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 4px;
}

.chat-messages div {
  margin-bottom: 6px;
  padding: 6px 8px;
  border-radius: 4px;
  background: var(--vscode-hover);
  word-wrap: break-word;
  font-size: 13px;
}

.chat-input-container {
  display: flex;
  gap: 6px;
}

#chatInput {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  background: var(--vscode-bg);
  color: var(--vscode-text);
  outline: none;
  font-size: 13px;
}

#chatInput:focus {
  border-color: var(--vscode-blue);
  box-shadow: 0 0 0 1px var(--vscode-blue);
}

.send-btn {
  padding: 6px 10px;
  background: var(--vscode-blue);
  border: none;
  border-radius: 2px;
  color: white;
  cursor: pointer;
  transition: var(--vscode-transition);
  font-size: 12px;
}

.send-btn:hover {
  background: var(--vscode-blue-hover);
}

.voice-container {
  padding: 12px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 4px;
  margin: 8px;
}

.voice-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.voice-btn {
  flex: 1;
  padding: 8px;
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  background: var(--vscode-blue);
  color: white;
  cursor: pointer;
  transition: var(--vscode-transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 12px;
}

.voice-btn:hover {
  background: var(--vscode-blue-hover);
}

.voice-status {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.mic-status,
.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--vscode-text-disabled);
}

.status-dot.active {
  background: var(--vscode-green);
}

.user-list {
  padding: 8px;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 4px;
  margin: 8px;
}

.user-list h3 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--vscode-text-muted);
  font-weight: 600;
}

.user-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-list li {
  padding: 4px 6px;
  border-radius: 2px;
  background: var(--vscode-hover);
  margin-bottom: 2px;
  font-size: 12px;
  color: var(--vscode-text);
}

.hidden {
  display: none;
}

/* Collaboration Button */
.collab-button {
  position: fixed;
  bottom: 30px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--vscode-blue);
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  box-shadow: var(--vscode-shadow);
  transition: var(--vscode-transition);
  z-index: 998;
}

.collab-button:hover {
  background: var(--vscode-blue-hover);
  transform: scale(1.1);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 10001;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: var(--vscode-sidebar-bg);
  margin: 15% auto;
  padding: 20px;
  border: 1px solid var(--vscode-border);
  border-radius: 6px;
  width: 400px;
  color: var(--vscode-text);
}

.close-modal {
  color: var(--vscode-text-muted);
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-modal:hover {
  color: var(--vscode-text);
}

.modal-buttons {
  display: flex;
  gap: 8px;
  margin-top: 15px;
  justify-content: flex-end;
}

#file-name-input {
  width: 100%;
  padding: 8px 12px;
  margin: 10px 0;
  background: var(--vscode-bg);
  border: 1px solid var(--vscode-border);
  border-radius: 2px;
  color: var(--vscode-text);
  font-size: 13px;
}

#file-name-input:focus {
  outline: none;
  border-color: var(--vscode-blue);
  box-shadow: 0 0 0 1px var(--vscode-blue);
}

/* File Icons */
.fa-file-code { color: var(--vscode-blue); }
.fa-file-image { color: var(--vscode-green); }
.fa-file-pdf { color: var(--vscode-red); }
.fa-file-word { color: var(--vscode-blue); }
.fa-file-excel { color: var(--vscode-green); }
.fa-file-powerpoint { color: var(--vscode-orange); }
.fa-file-archive { color: var(--vscode-yellow); }
.fa-file-audio { color: #be4bdb; }
.fa-file-video { color: #e64980; }
.fa-gem { color: var(--vscode-red); }
.fa-terminal { color: var(--vscode-green); }
.fa-python { color: var(--vscode-yellow); }
.fa-js { color: var(--vscode-yellow); }
.fa-react { color: var(--vscode-blue); }
.fa-html5 { color: var(--vscode-red); }
.fa-css3-alt { color: var(--vscode-blue); }
.fa-java { color: var(--vscode-red); }
.fa-php { color: #845ef7; }

/* Terminal Container */
.terminal-container {
  height: 300px;
  overflow-y: auto;
  background: var(--vscode-bg);
  color: var(--vscode-text);
  padding: 10px;
  font-family: 'Consolas', 'Courier New', monospace;
  font-size: 12px;
}
