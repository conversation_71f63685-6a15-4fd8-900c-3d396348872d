import json
from channels.generic.websocket import AsyncWebsocketConsumer
from django.utils import timezone

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.user = self.scope["user"]
        if self.user.is_authenticated:
            await self.channel_layer.group_add(
                f"user_{self.user.id}",
                self.channel_name
            )
            await self.accept()
        else:
            await self.close()

    async def disconnect(self, close_code):
        if self.user.is_authenticated:
            await self.channel_layer.group_discard(
                f"user_{self.user.id}",
                self.channel_name
            )

    async def send_notification(self, event):
        await self.send(text_data=json.dumps({
            'title': event['title'],
            'message': event['message'],
        }))


class SupportChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for support chat functionality"""

    async def connect(self):
        self.ticket_id = self.scope['url_route']['kwargs']['ticket_id']
        self.room_group_name = f'support_chat_{self.ticket_id}'
        self.user = self.scope["user"]

        if not self.user.is_authenticated:
            await self.close()
            return

        # Check if user has access to this ticket
        from .models import SupportTicket
        try:
            ticket = await self.get_ticket(self.ticket_id)
            has_access = await self.has_ticket_access(ticket)

            if not has_access:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Access denied to this ticket'
                }))
                await self.close()
                return
        except SupportTicket.DoesNotExist:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Ticket not found'
            }))
            await self.close()
            return
        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Error checking access: {str(e)}'
            }))
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': f'Connected to support chat for ticket #{self.ticket_id}',
            'user_id': self.user.id,
            'username': self.user.username,
            'is_staff': self.user.is_staff or self.user.is_superuser
        }))

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type', 'chat_message')

            if message_type == 'chat_message':
                await self.handle_chat_message(text_data_json)
            elif message_type == 'typing_indicator':
                await self.handle_typing_indicator(text_data_json)
            elif message_type == 'status_update' and (self.user.is_staff or self.user.is_superuser):
                await self.handle_status_update(text_data_json)

        except json.JSONDecodeError:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Invalid JSON format'
            }))

    async def handle_chat_message(self, data):
        message = data.get('message', '').strip()
        is_internal = data.get('is_internal', False)

        if not message:
            return

        # Ensure is_internal is a proper boolean
        if is_internal is None:
            is_internal = False
        elif isinstance(is_internal, str):
            is_internal = is_internal.lower() in ('true', '1', 'yes', 'on')
        else:
            is_internal = bool(is_internal)

        # Only staff can send internal messages
        if is_internal and not (self.user.is_staff or self.user.is_superuser):
            is_internal = False

        # Save message to database
        try:
            saved_message = await self.save_message(message, is_internal)

            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'username': self.user.username,
                    'user_id': self.user.id,
                    'timestamp': saved_message.created_at.isoformat(),
                    'is_internal': is_internal,
                    'is_staff': self.user.is_staff or self.user.is_superuser
                }
            )

        except Exception as e:
            print(f"Error saving message: {e}")
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': f'Failed to send message: {str(e)}'
            }))

    async def handle_typing_indicator(self, data):
        is_typing = data.get('is_typing', False)

        # Send typing indicator to room group (excluding sender)
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'typing_indicator',
                'username': self.user.username,
                'user_id': self.user.id,
                'is_typing': is_typing,
                'sender_channel': self.channel_name
            }
        )

    async def handle_status_update(self, data):
        new_status = data.get('status')
        if not new_status:
            return

        try:
            # Update ticket status
            await self.update_ticket_status(new_status)

            # Notify all participants
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'status_update',
                    'status': new_status,
                    'updated_by': self.user.username,
                    'timestamp': timezone.now().isoformat()
                }
            )

        except Exception as e:
            await self.send(text_data=json.dumps({
                'type': 'error',
                'message': 'Failed to update status'
            }))

    # WebSocket message handlers
    async def chat_message(self, event):
        # Send message to all participants including sender
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'username': event['username'],
            'user_id': event['user_id'],
            'timestamp': event['timestamp'],
            'is_internal': event.get('is_internal', False),
            'is_staff': event.get('is_staff', False)
        }))

    async def typing_indicator(self, event):
        # Don't send typing indicator back to sender
        if event.get('sender_channel') == self.channel_name:
            return

        await self.send(text_data=json.dumps({
            'type': 'typing_indicator',
            'username': event['username'],
            'user_id': event['user_id'],
            'is_typing': event['is_typing']
        }))

    async def status_update(self, event):
        await self.send(text_data=json.dumps({
            'type': 'status_update',
            'status': event['status'],
            'updated_by': event['updated_by'],
            'timestamp': event['timestamp']
        }))

    # Database operations
    async def get_ticket(self, ticket_id):
        from .models import SupportTicket
        from channels.db import database_sync_to_async

        @database_sync_to_async
        def _get_ticket():
            return SupportTicket.objects.get(ticket_id=ticket_id)

        return await _get_ticket()

    async def has_ticket_access(self, ticket):
        from channels.db import database_sync_to_async

        @database_sync_to_async
        def _has_access():
            # User can access if they created the ticket or are staff
            return (ticket.user == self.user or
                   self.user.is_staff or
                   self.user.is_superuser or
                   ticket.assigned_to == self.user)

        return await _has_access()

    async def save_message(self, message, is_internal=False):
        from .models import SupportMessage, SupportTicket
        from channels.db import database_sync_to_async
        from django.utils import timezone
        from django.db import transaction

        @database_sync_to_async
        def _save_message():
            try:
                with transaction.atomic():
                    ticket = SupportTicket.objects.select_for_update().get(ticket_id=self.ticket_id)

                    # Ensure is_internal is a proper boolean for database
                    internal_flag = bool(is_internal) if is_internal is not None else False

                    message_obj = SupportMessage.objects.create(
                        ticket=ticket,
                        sender=self.user,
                        message=message,
                        is_internal=internal_flag
                    )
                    # Update ticket's updated_at timestamp
                    ticket.save(update_fields=['updated_at'])
                    return message_obj
            except SupportTicket.DoesNotExist:
                raise Exception(f"Ticket {self.ticket_id} not found")
            except Exception as e:
                raise Exception(f"Database error: {str(e)}")

        return await _save_message()

    async def update_ticket_status(self, new_status):
        from .models import SupportTicket
        from channels.db import database_sync_to_async
        from django.utils import timezone

        @database_sync_to_async
        def _update_status():
            ticket = SupportTicket.objects.get(ticket_id=self.ticket_id)
            ticket.status = new_status
            if new_status == 'resolved':
                ticket.resolved_at = timezone.now()
            ticket.save()
            return ticket

        return await _update_status()