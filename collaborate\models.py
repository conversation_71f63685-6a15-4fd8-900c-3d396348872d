from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from django.contrib.auth import get_user_model
from multiselectfield import MultiSelectField
from django.utils import timezone
import hashlib
import uuid

class Skill(models.Model):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']

class UserSkill(models.Model):
    PROFICIENCY_CHOICES = [
        (1, 'Beginner'),
        (2, 'Intermediate'),
        (3, 'Advanced'),
        (4, 'Expert'),
        (5, 'Master'),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='user_skills'
    )
    skill = models.ForeignKey(
        'Skill',
        on_delete=models.CASCADE,
        related_name='user_skills'
    )
    proficiency = models.IntegerField(
        choices=PROFICIENCY_CHOICES,
        default=2  # Default to Intermediate
    )
    years_experience = models.DecimalField(
        max_digits=4,
        decimal_places=1,
        blank=True,
        null=True
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'skill')

    def __str__(self):
        return f"{self.user.username} - {self.skill.name} ({self.get_proficiency_display()})"

class Project(models.Model):
    TYPE_CHOICES = [
        ('admin', 'Official'),
        ('user', 'User Project'),
    ]

    LANGUAGE_CHOICES = [
        ('python', 'Python'),
        ('javascript', 'JavaScript'),
        ('java', 'Java'),
        ('cpp', 'C++'),
        ('csharp', 'C#'),
        ('php', 'PHP'),
        ('ruby', 'Ruby'),
        ('swift', 'Swift'),
        ('go', 'Go'),
        ('rust', 'Rust'),
        ('typescript', 'TypeScript'),
        ('other', 'Other'),
    ]

    MATCHING_CHOICES = (
        ('application_based', 'Application Based'),
        ('ai_based', 'AI Based'),
    )

    title = models.CharField(max_length=255)
    description = models.TextField()
    hash = models.CharField(max_length=64, unique=True, blank=True, help_text="Unique hash for secure URL access")
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='owned_projects'
    )
    type = models.CharField(
        max_length=20,
        choices=TYPE_CHOICES,
        default='user'
    )
    required_languages = MultiSelectField(
        choices=LANGUAGE_CHOICES,
        max_choices=len(LANGUAGE_CHOICES),
        max_length=255,
        null=True,
        blank=True
    )
    required_skills = models.ManyToManyField('Skill', blank=True, related_name='required_for_projects')
    critical_skills = models.ManyToManyField('Skill', related_name='critical_for_projects', blank=True)
    required_skills_json = models.JSONField(default=list, blank=True)  # Adding JSONField for required skills
    missing_skills = models.JSONField(default=list, blank=True)  # For storing missing skills after team matching
    team_size = models.IntegerField(
        default=1,
        help_text="Maximum number of team members needed"
    )
    accepted_teammates = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='accepted_projects', blank=True)
    declined_teammates = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='declined_projects', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    ai_matching_enabled = models.BooleanField(default=True)

    # Marketplace fields
    is_published_to_marketplace = models.BooleanField(default=False, help_text="Whether this project is published to the marketplace")
    marketplace_image = models.ImageField(upload_to='marketplace/images/', blank=True, null=True, help_text="Project showcase image for marketplace")
    marketplace_description = models.TextField(blank=True, help_text="Enhanced description for marketplace display")
    marketplace_published_at = models.DateTimeField(null=True, blank=True, help_text="When the project was published to marketplace")

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        """Override save to ensure a project always has an owner and generate hash"""
        if not self.owner_id and hasattr(self, '_owner_from_request'):
            self.owner = self._owner_from_request

        # Generate hash if not exists
        if not self.hash:
            self.hash = self.generate_hash()

        super().save(*args, **kwargs)

    def generate_hash(self):
        """Generate a unique hash for the project"""
        # Create a unique string using UUID and timestamp
        unique_string = f"{uuid.uuid4()}-{self.title}-{timezone.now().isoformat()}"
        # Generate SHA256 hash
        hash_object = hashlib.sha256(unique_string.encode())
        return hash_object.hexdigest()[:32]  # Use first 32 characters for shorter URLs

    def can_access(self, user):
        """
        Check if a user has permission to access this project.
        Returns True if:
        - User is a superuser
        - User is the project owner
        - User is a member of the project
        """
        if user.is_superuser:
            return True

        # Check if user is the project owner
        if self.owner == user:
            return True

        # Check if user is a member of the project
        return ProjectMembership.objects.filter(user=user, project=self).exists()

    def match_users(self):
        """Match users to this project based on skills."""
        User = get_user_model()  # Get the user model
        all_users = User.objects.all()

        # Get all skills required for the project
        all_skills = list(self.required_skills.values_list('id', flat=True))

        # If the project has no required skills, return empty list or all users
        if not all_skills:
            return []  # Or return [(user, 0) for user in all_users] if you want to return all users with zero score

        project_skills_vector = np.array([1 if skill_id in all_skills else 0 for skill_id in all_skills])
        user_scores = []

        for user in all_users:
            user_skills = UserSkill.objects.filter(user=user).values_list('skill_id', flat=True)
            user_skills_vector = np.array([1 if skill_id in user_skills else 0 for skill_id in all_skills])
            similarity = cosine_similarity([project_skills_vector], [user_skills_vector])[0][0]
            user_scores.append((user, similarity))

        return sorted(user_scores, key=lambda x: x[1], reverse=True)

class ProjectRequiredSkill(models.Model):
    IMPORTANCE_CHOICES = [
        (1, 'Nice to Have'),
        (2, 'Important'),
        (3, 'Critical'),
    ]

    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='required_skill_details'
    )
    skill = models.ForeignKey(
        'Skill',
        on_delete=models.CASCADE,
    )
    importance = models.IntegerField(
        choices=IMPORTANCE_CHOICES,
        default=2  # Default to Important
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('project', 'skill')

    def __str__(self):
        return f"{self.project.title} - {self.skill.name} ({self.get_importance_display()})"

class UserPairing(models.Model):
    PAIRING_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('canceled', 'Canceled'),
    ]

    user1 = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pairings_as_user1'
    )
    user2 = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='pairings_as_user2'
    )
    # New field for flexible team size support
    team_members = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='team_pairings',
        blank=True,
        help_text="All team members in this pairing (including user1 and user2 for backward compatibility)"
    )
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='user_pairings'
    )
    successful = models.BooleanField(
        null=True,
        blank=True,
        help_text="Whether the pairing was successful (used as label for ML)"
    )
    status = models.CharField(
        max_length=20,
        choices=PAIRING_STATUS_CHOICES,
        default='pending'
    )
    feedback = models.TextField(
        blank=True,
        help_text="Feedback about the pairing experience"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user1', 'user2', 'project')

    def __str__(self):
        if self.team_members.exists() and self.team_members.count() > 2:
            member_count = self.team_members.count()
            success_str = "✓" if self.successful else "✗" if self.successful is False else "?"
            return f"{self.user1.username} & {member_count-1} others - {self.project.title} [{success_str}]"
        else:
            success_str = "✓" if self.successful else "✗" if self.successful is False else "?"
            return f"{self.user1.username} & {self.user2.username} - {self.project.title} [{success_str}]"

    def save(self, *args, **kwargs):
        """Override save to ensure user1 and user2 are in team_members after saving"""
        super().save(*args, **kwargs)
        # Add user1 and user2 to team_members if needed
        # This ensures backward compatibility
        if self.user1 and self.user2:
            self.team_members.add(self.user1, self.user2)

    def get_skill_overlap(self):
        """Calculate the number of overlapping skills between team members"""
        if self.team_members.exists() and self.team_members.count() > 2:
            # Calculate overlap for all team members
            skill_sets = [set(UserSkill.objects.filter(user=u).values_list('skill__id', flat=True))
                         for u in self.team_members.all()]
            if skill_sets:
                return len(set.intersection(*skill_sets))
            return 0
        else:
            # Fall back to original behavior for compatibility
            user1_skills = UserSkill.objects.filter(user=self.user1).values_list('skill__id', flat=True)
            user2_skills = UserSkill.objects.filter(user=self.user2).values_list('skill__id', flat=True)
            overlap = set(user1_skills).intersection(set(user2_skills))
            return len(overlap)

    def get_project_skill_coverage(self):
        """Calculate how many project required skills are covered by the team"""
        if self.team_members.exists():
            users = list(self.team_members.all())
        else:
            users = [self.user1, self.user2]

        all_skills = set().union(*[set(UserSkill.objects.filter(user=u).values_list('skill__id', flat=True)) for u in users])
        project_skills = set(self.project.required_skills.values_list('id', flat=True))

        if not project_skills:
            return {
                'covered_count': 0,
                'total_count': 0,
                'all_covered': True,
                'coverage_percent': 0,
                'coverage_score': 0.0
            }

        covered_skills = all_skills.intersection(project_skills)
        coverage_score = round(len(covered_skills) / len(project_skills), 2) if project_skills else 0.0

        return {
            'covered_count': len(covered_skills),
            'total_count': len(project_skills),
            'all_covered': len(covered_skills) == len(project_skills),
            'coverage_percent': (len(covered_skills) / len(project_skills) * 100) if project_skills else 0,
            'coverage_score': coverage_score
        }

    def extract_features(self):
        """Extract features for machine learning with flexible team sizes"""
        if self.team_members.exists():
            users = list(self.team_members.all())
        else:
            users = [self.user1, self.user2]  # if more users are added in the future, use a list field like self.users.all()

        # Get user skills for each team member
        all_user_skills = [set(UserSkill.objects.filter(user=u).values_list('skill__id', flat=True)) for u in users]
        combined_skills = set().union(*all_user_skills) if all_user_skills else set()

        # Calculate shared skills (intersection of all member skills)
        shared_skills = set.intersection(*all_user_skills) if len(all_user_skills) > 1 else combined_skills

        # Get project skills
        project_skills = set(self.project.required_skills.values_list('id', flat=True))

        # Calculate individual skill overlaps with the project
        individual_overlaps = [len(skills & project_skills) for skills in all_user_skills]

        # Calculate coverage and missing skills
        coverage = combined_skills & project_skills
        missing_skills = project_skills - combined_skills

        # Get proficiency data for all users
        user_proficiencies = []
        for user in users:
            user_prof = {us.skill_id: us.proficiency for us in UserSkill.objects.filter(user=user)}
            user_proficiencies.append(user_prof)

        # Calculate best proficiency for each project skill
        project_skill_proficiencies = []
        for skill_id in project_skills:
            best_proficiency = max([prof.get(skill_id, 0) for prof in user_proficiencies]) if user_proficiencies else 0
            project_skill_proficiencies.append(best_proficiency)

        avg_proficiency = sum(project_skill_proficiencies) / len(project_skill_proficiencies) if project_skill_proficiencies else 0

        # Calculate individual proficiency averages
        user_prof_avgs = [sum(prof.values()) / len(prof) if prof else 0 for prof in user_proficiencies]

        # Create feature dictionary
        features = {
            'team_size': len(users),
            'shared_skills_count': len(shared_skills),
            'combined_coverage_count': len(coverage),
            'missing_project_skills': len(missing_skills),
            'total_project_skills': len(project_skills),
            'average_user_overlap': sum(individual_overlaps) / len(users) if users else 0,
            'total_skill_count': sum(len(s) for s in all_user_skills),
            'unique_combined_skills': len(combined_skills),
            'avg_proficiency': avg_proficiency,
            'skill_complementary_ratio': len(coverage) / (len(combined_skills) or 1),
            'all_skills_covered': int(len(missing_skills) == 0),
        }

        # Add individual user skill counts and proficiency for the first two users
        # (for backward compatibility with existing models)
        if len(all_user_skills) > 0:
            features['user1_skill_count'] = len(all_user_skills[0])
            features['user1_skill_proficiency_avg'] = user_prof_avgs[0] if user_prof_avgs else 0
        else:
            features['user1_skill_count'] = 0
            features['user1_skill_proficiency_avg'] = 0

        if len(all_user_skills) > 1:
            features['user2_skill_count'] = len(all_user_skills[1])
            features['user2_skill_proficiency_avg'] = user_prof_avgs[1] if len(user_prof_avgs) > 1 else 0
        else:
            features['user2_skill_count'] = 0
            features['user2_skill_proficiency_avg'] = 0

        return features

class ProjectMembership(models.Model):
    ROLE_CHOICES = [
        ('owner', 'Owner'),
        ('admin', 'Admin'),
        ('member', 'Member'),
        ('viewer', 'Viewer'),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='project_memberships'
    )
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='memberships'  # Add related_name for reverse relation
    )
    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='member'
    )
    joined_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'project')

    def __str__(self):
        return f"{self.user.username} - {self.project.title} ({self.role})"

class ProjectApplication(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='project_applications'
    )
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='applications'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'project')

    def __str__(self):
        return f"{self.user.username} - {self.project.title} ({self.status})"

class ProjectAsset(models.Model):
    project = models.ForeignKey(
        Project,
        on_delete=models.CASCADE,
        related_name='assets'
    )
    name = models.CharField(max_length=255)
    path = models.CharField(max_length=1000)
    is_folder = models.BooleanField(default=False)
    content = models.TextField(null=True, blank=True)
    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='children'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('project', 'path')
        ordering = ['path']

    def __str__(self):
        return f"{self.project.title} - {self.path}"

class Notification(models.Model):
    NOTIFICATION_TYPES = [
        ('info', 'Information'),
        ('success', 'Success'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('system', 'System Announcement'),
        ('invitation', 'Project Invitation'),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    project = models.ForeignKey(Project, on_delete=models.CASCADE, null=True, blank=True)
    title = models.CharField(max_length=255, default="Notification")
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    is_accepted = models.BooleanField(null=True, blank=True)  # None = no action yet, True/False = action taken
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES, default='info')

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.title}"

    def get_notification_color(self):
        """Returns the appropriate Bootstrap color class based on notification type"""
        color_map = {
            'info': 'primary',
            'success': 'success',
            'warning': 'warning',
            'error': 'danger',
            'system': 'dark',
            'invitation': 'info'
        }
        return color_map.get(self.notification_type, 'primary')

    def get_is_accepted_display(self):
        if self.is_accepted is None:
            return "not responded"
        return "accepted" if self.is_accepted else "declined"

class TeamRejectionLog(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    project = models.ForeignKey('Project', on_delete=models.CASCADE)
    reason = models.TextField(blank=True, null=True)
    rejected_skills_overlap = models.JSONField(default=dict)
    availability_mismatch = models.BooleanField(default=False)
    timezone_difference = models.IntegerField(default=0)

class TeamMatchAnalysisLog(models.Model):
    """Log of team matching operations for debugging and analysis."""
    project = models.ForeignKey('Project', on_delete=models.CASCADE)
    timestamp = models.DateTimeField(auto_now_add=True)
    match_data = models.JSONField(default=dict)
    algorithm_used = models.CharField(max_length=100, default="find_best_team_with_availability_priority")
    team_members = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='match_analysis_logs')

    def __str__(self):
        return f"Match for {self.project.title} at {self.timestamp}"

class TeamMatchFeedback(models.Model):
    """Feedback on team match quality."""
    project = models.ForeignKey('Project', on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    rating = models.IntegerField(
        choices=[(1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')],
        help_text="Rating from 1-5 on match quality"
    )
    feedback_text = models.TextField(blank=True, null=True)
    match_aspects = models.JSONField(
        default=dict,
        help_text="Ratings for specific aspects of the match (e.g., skill_match, timezone_compatibility)"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('project', 'user')

    def __str__(self):
        return f"{self.user.username}'s feedback on {self.project.title} ({self.rating}/5)"


class ProjectChatMessage(models.Model):
    """Chat messages in collaborative editor projects"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='chat_messages')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True)
    message = models.TextField()
    message_type = models.CharField(
        max_length=20,
        choices=[
            ('chat', 'Chat Message'),
            ('system', 'System Message'),
            ('user_joined', 'User Joined'),
            ('user_left', 'User Left'),
        ],
        default='chat'
    )
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['project', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
        ]

    def __str__(self):
        return f"Message in {self.project.title} by {self.sender.username if self.sender else 'System'}: {self.message[:50]}"


class ProjectCommit(models.Model):
    """Version control commits for projects"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='commits')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    commit_hash = models.CharField(max_length=64, unique=True, help_text="Unique hash for this commit")
    message = models.TextField(help_text="Commit message describing the changes")
    parent_commit = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='child_commits')
    branch_name = models.CharField(max_length=100, default='main', help_text="Branch name for this commit")
    created_at = models.DateTimeField(default=timezone.now)

    # Metadata about the commit
    files_changed = models.PositiveIntegerField(default=0, help_text="Number of files changed in this commit")
    lines_added = models.PositiveIntegerField(default=0, help_text="Number of lines added")
    lines_removed = models.PositiveIntegerField(default=0, help_text="Number of lines removed")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['project', 'created_at']),
            models.Index(fields=['project', 'branch_name', 'created_at']),
            models.Index(fields=['commit_hash']),
        ]

    def __str__(self):
        return f"{self.project.title} - {self.commit_hash[:8]}: {self.message[:50]}"

    def save(self, *args, **kwargs):
        """Generate commit hash if not provided"""
        if not self.commit_hash:
            import hashlib
            import uuid
            unique_string = f"{uuid.uuid4()}-{self.project.id}-{self.message}-{timezone.now().isoformat()}"
            hash_object = hashlib.sha256(unique_string.encode())
            self.commit_hash = hash_object.hexdigest()
        super().save(*args, **kwargs)


class ProjectFileVersion(models.Model):
    """File versions associated with commits"""
    commit = models.ForeignKey(ProjectCommit, on_delete=models.CASCADE, related_name='file_versions')
    file_path = models.CharField(max_length=1000, help_text="Relative path to the file within the project")
    content = models.TextField(help_text="File content at this version")
    content_hash = models.CharField(max_length=64, help_text="Hash of the file content for deduplication")
    file_size = models.PositiveIntegerField(default=0, help_text="File size in bytes")
    is_deleted = models.BooleanField(default=False, help_text="True if file was deleted in this commit")
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        unique_together = ('commit', 'file_path')
        indexes = [
            models.Index(fields=['commit', 'file_path']),
            models.Index(fields=['content_hash']),
            models.Index(fields=['file_path', 'created_at']),
        ]

    def __str__(self):
        return f"{self.commit.commit_hash[:8]} - {self.file_path}"

    def save(self, *args, **kwargs):
        """Generate content hash and calculate file size"""
        if self.content:
            import hashlib
            self.content_hash = hashlib.sha256(self.content.encode()).hexdigest()
            self.file_size = len(self.content.encode('utf-8'))
        super().save(*args, **kwargs)


class ProjectBranch(models.Model):
    """Branches for version control"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='branches')
    name = models.CharField(max_length=100, help_text="Branch name")
    head_commit = models.ForeignKey(ProjectCommit, on_delete=models.CASCADE, null=True, blank=True, related_name='branch_heads')
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(default=timezone.now)
    is_default = models.BooleanField(default=False, help_text="True if this is the default branch")

    class Meta:
        unique_together = ('project', 'name')
        indexes = [
            models.Index(fields=['project', 'name']),
            models.Index(fields=['project', 'is_default']),
        ]

    def __str__(self):
        return f"{self.project.title} - {self.name}"

class SkillAlias(models.Model):
    """Aliases for skills to handle variations in naming."""
    skill = models.ForeignKey('Skill', on_delete=models.CASCADE, related_name='aliases')
    alias = models.CharField(max_length=100, unique=True)

    def __str__(self):
        return f"{self.alias} → {self.skill.name}"

class SkillRelationship(models.Model):
    """Relationships between skills (parent-child, related)."""
    RELATIONSHIP_TYPES = [
        ('parent', 'Parent'),
        ('child', 'Child'),
        ('related', 'Related'),
    ]

    from_skill = models.ForeignKey('Skill', on_delete=models.CASCADE, related_name='relationships_from')
    to_skill = models.ForeignKey('Skill', on_delete=models.CASCADE, related_name='relationships_to')
    relationship_type = models.CharField(max_length=20, choices=RELATIONSHIP_TYPES)

    class Meta:
        unique_together = ('from_skill', 'to_skill', 'relationship_type')

    def __str__(self):
        return f"{self.from_skill.name} {self.get_relationship_type_display()} of {self.to_skill.name}"
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} rejected {self.project.title}"

class TeamMatchLog(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name="match_logs")
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    match_score = models.FloatField()
    overlap_skills = models.JSONField(default=list)
    missing_skills = models.JSONField(default=list)
    is_backup = models.BooleanField(default=False)
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username} - {self.project.title} ({self.match_score:.2f})"

    class Meta:
        ordering = ['-timestamp', '-match_score']


# Marketplace Models

class ProjectMarketplacePost(models.Model):
    """Marketplace posts for showcasing projects"""
    project = models.OneToOneField(Project, on_delete=models.CASCADE, related_name='marketplace_post')
    featured_image = models.ImageField(upload_to='marketplace/featured/', blank=True, null=True)
    showcase_description = models.TextField(help_text="Enhanced description for marketplace showcase")
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags for better discovery")
    is_featured = models.BooleanField(default=False, help_text="Featured posts appear at the top")
    view_count = models.PositiveIntegerField(default=0)
    published_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_featured', '-published_at']
        indexes = [
            models.Index(fields=['is_featured', '-published_at']),
            models.Index(fields=['published_at']),
        ]

    def __str__(self):
        return f"Marketplace: {self.project.title}"

    def increment_view_count(self):
        """Increment view count"""
        self.view_count += 1
        self.save(update_fields=['view_count'])


class ProjectReaction(models.Model):
    """Reactions (likes, hearts, etc.) on marketplace posts"""
    REACTION_CHOICES = [
        ('like', '👍 Like'),
        ('love', '❤️ Love'),
        ('wow', '😮 Wow'),
        ('fire', '🔥 Fire'),
        ('rocket', '🚀 Rocket'),
    ]

    post = models.ForeignKey(ProjectMarketplacePost, on_delete=models.CASCADE, related_name='reactions')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    reaction_type = models.CharField(max_length=10, choices=REACTION_CHOICES, default='like')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('post', 'user')  # One reaction per user per post
        indexes = [
            models.Index(fields=['post', 'reaction_type']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f"{self.user.username} {self.get_reaction_type_display()} {self.post.project.title}"


class ProjectComment(models.Model):
    """Comments on marketplace posts"""
    post = models.ForeignKey(ProjectMarketplacePost, on_delete=models.CASCADE, related_name='comments')
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    content = models.TextField()
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_edited = models.BooleanField(default=False)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['post', 'created_at']),
            models.Index(fields=['author', 'created_at']),
            models.Index(fields=['parent']),
        ]

    def __str__(self):
        return f"Comment by {self.author.username} on {self.post.project.title}"

    def save(self, *args, **kwargs):
        if self.pk:  # If updating existing comment
            self.is_edited = True
        super().save(*args, **kwargs)


class MarketplaceApplication(models.Model):
    """Applications to join projects from the marketplace"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
    ]

    post = models.ForeignKey(ProjectMarketplacePost, on_delete=models.CASCADE, related_name='marketplace_applications')
    applicant = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='marketplace_applications')
    message = models.TextField(help_text="Why do you want to join this project?")
    skills_offered = models.TextField(blank=True, help_text="What skills can you bring to this project?")
    portfolio_link = models.URLField(blank=True, help_text="Link to your portfolio or relevant work")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    reviewed_at = models.DateTimeField(null=True, blank=True)
    reviewer_notes = models.TextField(blank=True, help_text="Notes from the project owner")

    class Meta:
        unique_together = ('post', 'applicant')  # One application per user per post
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', 'status']),
            models.Index(fields=['applicant', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"{self.applicant.username} applied to {self.post.project.title} ({self.status})"