{% extends "base.html" %}
{% load static %}
{% block title %}Learning Dashboard - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="collaborate-card fade-in visible">
  <!-- Dashboard Header -->
  <div class="dashboard-hero">
    <h1>
      {% if user_role == 'admin' %}🛠️ Admin Learning Dashboard
      {% elif user_role == 'mentor' %}👨‍🏫 Mentor Dashboard
      {% else %}🎓 Learning Dashboard{% endif %}
    </h1>
    <p>
      {% if user_role == 'admin' %}Manage courses, monitor progress, and oversee the learning platform
      {% elif user_role == 'mentor' %}Create and manage your courses while tracking your learning journey
      {% else %}Your personalized learning journey with AI-powered insights{% endif %}
    </p>
    {% if setup_required %}
    <div class="setup-notice">
      <p>⚠️ AI features are being set up. Please run migrations to enable full functionality.</p>
    </div>
    {% endif %}
  </div>

  <!-- Dashboard Navigation Tabs -->
  <div class="dashboard-tabs">
    <button class="tab-btn active" onclick="switchTab('learning')">
      📊 Learning Analytics
    </button>
    {% if can_manage %}
    <button class="tab-btn" onclick="switchTab('management')">
      {% if user_role == 'admin' %}🛠️ Course Management
      {% else %}📚 My Courses{% endif %}
    </button>
    {% endif %}
    <button class="tab-btn" onclick="switchTab('ai-tools')">
      🤖 AI Tools
    </button>
  </div>

  <!-- Learning Analytics Tab -->
  <div id="learning-tab" class="tab-content active">
    <!-- Learning Analytics Overview -->
    <div class="analytics-overview">
      <h2>📊 Your Learning Analytics</h2>
      <div class="analytics-grid">
        <div class="analytics-card">
          <div class="analytics-icon">📚</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.total_lessons }}</div>
            <div class="analytics-label">Total Lessons</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="analytics-icon">✅</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.completed_lessons }}</div>
            <div class="analytics-label">Completed</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="analytics-icon">🏆</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.mastered_lessons }}</div>
            <div class="analytics-label">Mastered</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="analytics-icon">📈</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.completion_rate }}%</div>
            <div class="analytics-label">Completion Rate</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="analytics-icon">⭐</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.average_score }}</div>
            <div class="analytics-label">Avg Score</div>
          </div>
        </div>
        
        <div class="analytics-card">
          <div class="analytics-icon">⏱️</div>
          <div class="analytics-content">
            <div class="analytics-value">{{ progress_analytics.total_study_time }}m</div>
            <div class="analytics-label">Study Time</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Learning Profile -->
    <div class="learning-profile-section">
      <h2>👤 Your Learning Profile</h2>
      <div class="profile-content">
        <div class="profile-info">
          <div class="profile-item">
            <span class="profile-label">Learning Style:</span>
            <span class="profile-value">{{ learning_profile.learning_style|title|default:"Visual" }}</span>
          </div>
          <div class="profile-item">
            <span class="profile-label">Current Level:</span>
            <span class="profile-value">{{ learning_profile.current_skill_level|title|default:"Beginner" }}</span>
          </div>
          <div class="profile-item">
            <span class="profile-label">Preferred Difficulty:</span>
            <span class="profile-value">{{ learning_profile.preferred_difficulty|title|default:"Beginner" }}</span>
          </div>
        </div>
        <div class="profile-goals">
          <h4>🎯 Learning Goals</h4>
          {% if learning_profile %}
          <p>{{ learning_profile.learning_goals|default:"Set your learning goals to get personalized recommendations!" }}</p>
          {% else %}
          <p>Complete the setup to set your learning goals and get personalized recommendations!</p>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- AI Recommendations -->
    {% if recommendations %}
    <div class="ai-recommendations-section">
      <h2>🎯 AI-Powered Recommendations</h2>
      <div class="recommendations-grid">
        {% for rec in recommendations %}
        <div class="recommendation-card">
          <div class="rec-header">
            <div class="rec-type">{{ rec.type|title }}</div>
            <div class="rec-confidence">{{ rec.confidence|floatformat:0 }}% match</div>
          </div>
          <h3>{{ rec.title }}</h3>
          <p>{{ rec.description }}</p>
          <div class="rec-actions">
            {% if rec.lesson %}
            <a href="{% url 'learn:lesson_details' rec.lesson.id %}" class="rec-action-btn primary">
              Start Learning →
            </a>
            {% elif rec.course %}
            <a href="{% url 'learn:course_details' rec.course.id %}" class="rec-action-btn primary">
              Explore Course →
            </a>
            {% endif %}
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Recent Activity -->
    <div class="recent-activity-section">
      <h2>📈 Recent Learning Activity</h2>
      <div class="activity-content">
        <div class="activity-lessons">
          <h3>Recent Lessons</h3>
          {% if recent_progress %}
          <div class="lessons-list">
            {% for progress in recent_progress %}
            <div class="lesson-item">
              <div class="lesson-info">
                <div class="lesson-title">
                  <a href="{% url 'learn:lesson_details' progress.lesson.id %}">
                    {{ progress.lesson.name }}
                  </a>
                </div>
                <div class="lesson-meta">
                  {{ progress.lesson.chapter.course.name }} • {{ progress.lesson.chapter.name }}
                </div>
              </div>
              <div class="lesson-progress">
                <div class="progress-status status-{{ progress.status }}">
                  {% if progress.status == 'completed' %}✅
                  {% elif progress.status == 'in_progress' %}🔄
                  {% elif progress.status == 'mastered' %}🏆
                  {% else %}📝{% endif %}
                </div>
                {% if progress.score %}
                <div class="progress-score">{{ progress.score|floatformat:0 }}%</div>
                {% endif %}
              </div>
            </div>
            {% endfor %}
          </div>
          {% else %}
          <p class="no-activity">No recent lesson activity. Start learning to see your progress here!</p>
          {% endif %}
        </div>
        
        <div class="activity-conversations">
          <h3>Recent AI Conversations</h3>
          {% if recent_conversations %}
          <div class="conversations-list">
            {% for conversation in recent_conversations %}
            <div class="conversation-item">
              <div class="conversation-type">{{ conversation.interaction_type|title }}</div>
              <div class="conversation-preview">{{ conversation.user_message|truncatechars:100 }}</div>
              <div class="conversation-time">{{ conversation.created_at|timesince }} ago</div>
            </div>
            {% endfor %}
          </div>
          {% else %}
          <p class="no-activity">No recent AI conversations. Try asking the AI assistant for help!</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Course Management Tab -->
  {% if can_manage %}
  <div id="management-tab" class="tab-content">
    <div class="management-overview">
      <h2>
        {% if user_role == 'admin' %}🛠️ Course Management Overview
        {% else %}📚 My Courses Overview{% endif %}
      </h2>
      <div class="management-stats">
        <div class="stat-card">
          <div class="stat-icon">📚</div>
          <div class="stat-content">
            <div class="stat-value">{{ total_courses }}</div>
            <div class="stat-label">
              {% if user_role == 'admin' %}Total Courses
              {% else %}My Courses{% endif %}
            </div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-value">{{ published_courses }}</div>
            <div class="stat-label">Published</div>
          </div>
        </div>
        
        {% if user_role == 'admin' %}
        <div class="stat-card">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <div class="stat-value">{{ pending_courses }}</div>
            <div class="stat-label">Pending Approval</div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section">
      <h3>🚀 Quick Actions</h3>
      <div class="actions-grid">
        <a href="{% url 'learn:create_course' %}" class="action-card">
          <div class="action-icon">➕</div>
          <div class="action-title">Create New Course</div>
          <div class="action-description">Start building a new course</div>
        </a>
        
        {% if user_role == 'admin' %}
        <a href="{% url 'learn:manage_course_approvals' %}" class="action-card">
          <div class="action-icon">✅</div>
          <div class="action-title">Manage Approvals</div>
          <div class="action-description">Review pending course submissions</div>
        </a>
        {% endif %}
        
        <a href="{% url 'learn:course_list' %}" class="action-card">
          <div class="action-icon">📋</div>
          <div class="action-title">View All Courses</div>
          <div class="action-description">Browse the complete course catalog</div>
        </a>
      </div>
    </div>

    <!-- Recent Courses -->
    <div class="recent-courses-section">
      <h3>
        {% if user_role == 'admin' %}📚 Recent Courses
        {% else %}📚 My Recent Courses{% endif %}
      </h3>
      {% if user_courses %}
      <div class="courses-list">
        {% for course in user_courses %}
        <div class="course-item">
          <div class="course-info">
            <div class="course-title">
              <a href="{% url 'learn:course_details' course.id %}">{{ course.name }}</a>
            </div>
            <div class="course-meta">
              Created {{ course.created_at|timesince }} ago • 
              Status: {{ course.status|title }} • 
              {% if course.is_approved %}Approved{% else %}Pending{% endif %}
            </div>
          </div>
          <div class="course-actions">
            <a href="{% url 'learn:edit_course' course.id %}" class="btn btn-sm btn-secondary">Edit</a>
            <a href="{% url 'learn:course_details' course.id %}" class="btn btn-sm btn-primary">View</a>
          </div>
        </div>
        {% endfor %}
      </div>
      {% else %}
      <p class="no-activity">
        {% if user_role == 'admin' %}No courses found.
        {% else %}You haven't created any courses yet. <a href="{% url 'learn:create_course' %}">Create your first course!</a>{% endif %}
      </p>
      {% endif %}
    </div>
  </div>
  {% endif %}

  <!-- AI Tools Tab -->
  <div id="ai-tools-tab" class="tab-content">
    <div class="ai-tools-section">
      <h2>🤖 AI Learning Tools</h2>

      <!-- AI Assistant -->
      <div class="ai-tool-card">
        <div class="tool-header">
          <h3>💬 AI Learning Assistant</h3>
          <button class="tool-toggle" onclick="toggleTool('ai-assistant')">
            <span id="assistant-toggle">Show Assistant</span>
          </button>
        </div>
        <div class="tool-content" id="ai-assistant" style="display: none;">
          <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
              {% for conversation in recent_conversations %}
              <div class="message user-message">
                <div class="message-content">{{ conversation.user_message }}</div>
                <div class="message-time">{{ conversation.created_at|timesince }} ago</div>
              </div>
              <div class="message ai-message">
                <div class="message-content">{{ conversation.ai_response|safe }}</div>
              </div>
              {% endfor %}
            </div>
            <div class="chat-input-container">
              <textarea id="chat-input" placeholder="Ask me anything about programming or learning..." rows="3"></textarea>
              <button onclick="sendMessage()" class="send-btn">Send 🚀</button>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Analyzer -->
      <div class="ai-tool-card">
        <div class="tool-header">
          <h3>🔍 AI Code Analyzer</h3>
          <button class="tool-toggle" onclick="toggleTool('code-analyzer')">
            <span id="analyzer-toggle">Show Analyzer</span>
          </button>
        </div>
        <div class="tool-content" id="code-analyzer" style="display: none;">
          <div class="analyzer-container">
            <div class="analyzer-controls">
              <select id="code-language">
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="java">Java</option>
                <option value="cpp">C++</option>
                <option value="html">HTML</option>
                <option value="css">CSS</option>
              </select>
              <select id="analysis-type">
                <option value="comprehensive">Comprehensive Analysis</option>
                <option value="syntax_check">Syntax Check</option>
                <option value="best_practices">Best Practices</option>
                <option value="performance">Performance</option>
                <option value="security">Security</option>
              </select>
              <button onclick="analyzeCode()" class="analyze-btn">Analyze Code 🔍</button>
            </div>
            <textarea id="code-input" placeholder="Paste your code here for AI analysis..." rows="15"></textarea>
            <div id="analysis-results" class="analysis-results" style="display: none;">
              <div class="score-display">
                <div class="score-circle" id="code-score">0</div>
                <div class="score-label">Code Quality Score</div>
              </div>
              <div class="feedback-content" id="ai-feedback"></div>
              <div class="suggestions-list" id="suggestions-list"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Learning Progress Tracker -->
      <div class="ai-tool-card">
        <div class="tool-header">
          <h3>📊 Learning Progress Insights</h3>
        </div>
        <div class="tool-content">
          <div class="progress-insights">
            <div class="insight-item">
              <div class="insight-icon">🎯</div>
              <div class="insight-content">
                <h4>Learning Style</h4>
                <p>You learn best through {{ learning_profile.learning_style|default:"visual" }} methods.
                   {% if learning_profile.learning_style == 'visual' %}Try creating diagrams and flowcharts.
                   {% elif learning_profile.learning_style == 'auditory' %}Consider explaining concepts out loud.
                   {% elif learning_profile.learning_style == 'kinesthetic' %}Focus on hands-on coding exercises.
                   {% else %}Take detailed notes and read documentation.{% endif %}
                </p>
              </div>
            </div>

            <div class="insight-item">
              <div class="insight-icon">📈</div>
              <div class="insight-content">
                <h4>Progress Trend</h4>
                <p>
                  {% if progress_analytics.completion_rate > 75 %}
                    Excellent progress! You're completing lessons consistently.
                  {% elif progress_analytics.completion_rate > 50 %}
                    Good momentum! Keep up the steady learning pace.
                  {% elif progress_analytics.completion_rate > 25 %}
                    You're making progress! Try setting aside regular study time.
                  {% else %}
                    Just getting started! Every expert was once a beginner.
                  {% endif %}
                </p>
              </div>
            </div>

            <div class="insight-item">
              <div class="insight-icon">⏰</div>
              <div class="insight-content">
                <h4>Study Time Analysis</h4>
                <p>
                  You've studied for {{ progress_analytics.total_study_time }} minutes total.
                  {% if progress_analytics.total_study_time > 300 %}
                    Great dedication to learning!
                  {% elif progress_analytics.total_study_time > 120 %}
                    Building good study habits!
                  {% else %}
                    Consider setting aside 15-30 minutes daily for consistent progress.
                  {% endif %}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Access Actions -->
  <div class="quick-access-section">
    <h2>🚀 Quick Access</h2>
    <div class="quick-actions-grid">
      <a href="{% url 'learn:course_list' %}" class="quick-action-card">
        <div class="action-icon">📚</div>
        <div class="action-title">Browse Courses</div>
        <div class="action-description">Explore available courses and start learning</div>
      </a>

      {% if can_manage %}
      <a href="{% url 'learn:create_course' %}" class="quick-action-card">
        <div class="action-icon">➕</div>
        <div class="action-title">Create Course</div>
        <div class="action-description">Build and share your knowledge</div>
      </a>
      {% endif %}

      <a href="#" onclick="switchTab('ai-tools')" class="quick-action-card">
        <div class="action-icon">🤖</div>
        <div class="action-title">AI Assistant</div>
        <div class="action-description">Get help with programming questions</div>
      </a>

      <a href="{% url 'accounts:profile' %}" class="quick-action-card">
        <div class="action-icon">⚙️</div>
        <div class="action-title">Profile Settings</div>
        <div class="action-description">Customize your learning preferences</div>
      </a>
    </div>
  </div>
</div>
</section>

<!-- Include CSS and JavaScript -->
<link rel="stylesheet" href="{% static 'css/ai_learning.css' %}">
<link rel="stylesheet" href="{% static 'css/unified_dashboard.css' %}">
<script src="{% static 'js/ai_learning.js' %}"></script>
<script src="{% static 'js/unified_dashboard.js' %}"></script>

<!-- Particles.js -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeUnifiedDashboard();
});

// Initialize lesson data for AI features
{% if ai_enabled %}
window.lessonData = {
    lessonId: null,
    startTime: Date.now(),
    timeSpent: 0,
    status: 'dashboard'
};
{% endif %}
</script>

{% endblock %}
