{% extends 'base.html' %}
{% load static %}
{% block title %}Delete Event - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Delete Event Header -->
    <div class="dashboard-welcome">
      <h1>🗑️ Delete Event</h1>
      <p class="welcome-subtitle">Permanently remove "{{ event.title }}" from the system</p>
    </div>

    <!-- Navigation Actions -->
    <div class="quick-actions" style="margin-bottom: 30px;">
      <div class="action-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
        <a href="{% url 'accounts:events' %}" class="action-card">
          <div class="action-icon">⬅️</div>
          <h3>Back to Events</h3>
          <p>Return to events list</p>
        </a>
        <a href="{% url 'accounts:edit_event' event.id %}" class="action-card" style="border-color: rgba(33, 150, 243, 0.5);">
          <div class="action-icon" style="color: #2196F3;">✏️</div>
          <h3 style="color: #2196F3;">Edit Instead</h3>
          <p>Modify this event instead of deleting</p>
        </a>
      </div>
    </div>

    <!-- Warning Section -->
    <div class="dashboard-section">
      <div class="warning-container">
        <div class="warning-card">
          <div class="warning-icon">⚠️</div>
          <h2>Are you sure you want to delete this event?</h2>
          <p>This action cannot be undone. The event will be permanently removed from the system and all associated data will be lost.</p>
        </div>
      </div>
    </div>

    <!-- Event Preview -->
    <div class="dashboard-section">
      <h2>📋 Event to be Deleted</h2>
      <div class="preview-container">
        <div class="project-card" style="border-left: 3px solid #f44336;">
          <div class="project-header">
            <h3>{{ event.title }}</h3>
            <div class="status-badge">
              {% if event.is_upcoming %}
                <span class="badge-upcoming">🟢 Upcoming</span>
              {% else %}
                <span class="badge-past">🔴 Past</span>
              {% endif %}
            </div>
          </div>

          <div class="project-body">
            <div class="event-meta">
              <p><i class="fas fa-clock"></i> {{ event.event_date|date:"F d, Y g:i A" }}</p>
              {% if event.location %}
                <p><i class="fas fa-map-marker-alt"></i> {{ event.location }}</p>
              {% endif %}
            </div>
            <p>{{ event.description }}</p>
          </div>

          <div class="project-actions">
            <small style="color: #b0b0b0;">
              Created by {{ event.created_by.get_full_name|default:event.created_by.username }}
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Event Information Stats -->
    <div class="dashboard-section">
      <h2>📊 Event Information</h2>
      <div class="dashboard-stats">
        <div class="stat-card">
          <div class="stat-icon">👤</div>
          <div class="stat-content">
            <h3>{{ event.created_by.get_full_name|default:event.created_by.username }}</h3>
            <p>Created By</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-content">
            <h3>{{ event.created_at|date:"M d, Y" }}</h3>
            <p>Created On</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🔄</div>
          <div class="stat-content">
            <h3>{{ event.updated_at|date:"M d, Y" }}</h3>
            <p>Last Updated</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            {% if event.is_upcoming %}🟢{% else %}🔴{% endif %}
          </div>
          <div class="stat-content">
            <h3>
              {% if event.is_upcoming %}
                Upcoming
              {% else %}
                Past
              {% endif %}
            </h3>
            <p>Status</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation -->
    <div class="dashboard-section">
      <h2>⚡ Confirm Deletion</h2>
      <div class="delete-form-container">
        <form method="post" class="delete-form">
          {% csrf_token %}
          <div class="form-actions">
            <button type="submit" class="btn btn-danger btn-large">
              <i class="fas fa-trash"></i> Yes, Delete Event Permanently
            </button>
            <a href="{% url 'accounts:events' %}" class="btn btn-secondary btn-large">
              <i class="fas fa-times"></i> Cancel & Keep Event
            </a>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .project-card, .stat-card, .warning-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });

    // Add confirmation dialog for delete button
    const deleteButton = document.querySelector('.btn-danger');
    if (deleteButton) {
      deleteButton.addEventListener('click', function(e) {
        const confirmed = confirm('Are you absolutely sure you want to delete this event? This action cannot be undone.');
        if (!confirmed) {
          e.preventDefault();
        }
      });
    }
  });
</script>

<style>
/* Dashboard specific styles for delete event page */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, #f44336, #d32f2f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  color: #b0b0b0;
  font-size: 1.1rem;
  margin: 0;
}

.dashboard-section {
  margin-bottom: 40px;
}

.dashboard-section h2 {
  color: #ffffff;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-content h3 {
  font-size: 1.5rem;
  color: var(--color-border);
  margin-bottom: 5px;
  word-break: break-word;
}

.stat-content p {
  color: #b0b0b0;
  margin: 0;
}

.quick-actions {
  margin-bottom: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
  text-decoration: none;
  color: inherit;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--color-border);
}

.action-card h3 {
  color: var(--color-border);
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.action-card p {
  color: #b0b0b0;
  margin: 0;
  font-size: 0.9rem;
}

/* Warning section */
.warning-container {
  max-width: 800px;
  margin: 0 auto;
}

.warning-card {
  background-color: rgba(244, 67, 54, 0.1);
  border: 2px solid rgba(244, 67, 54, 0.3);
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
}

.warning-card:hover {
  border-color: rgba(244, 67, 54, 0.5);
  box-shadow: 0 10px 30px rgba(244, 67, 54, 0.2);
}

.warning-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  color: #f44336;
}

.warning-card h2 {
  color: #f44336;
  margin-bottom: 20px;
  font-size: 1.8rem;
  font-weight: 600;
}

.warning-card p {
  color: #e0e0e0;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Preview styling */
.preview-container {
  max-width: 600px;
  margin: 0 auto;
}

.status-badge {
  display: flex;
  align-items: center;
}

.badge-upcoming {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.badge-past {
  background-color: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.event-meta p {
  margin-bottom: 8px;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.event-meta i {
  color: var(--color-border);
  margin-right: 8px;
  width: 16px;
}

/* Delete form styling */
.delete-form-container {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(244, 67, 54, 0.3);
  max-width: 600px;
  margin: 0 auto;
}

.form-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 600;
  min-width: 200px;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #2196F3;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.5);
  color: #FFC107;
}

.alert-error {
  background-color: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .form-actions {
    flex-direction: column;
  }

  .btn-large {
    min-width: auto;
    width: 100%;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .stat-content h3 {
    font-size: 1.2rem;
  }

  .warning-card {
    padding: 30px 20px;
  }

  .warning-icon {
    font-size: 3rem;
  }

  .warning-card h2 {
    font-size: 1.5rem;
  }

  .warning-card p {
    font-size: 1rem;
  }
}
</style>

{% endblock %}
