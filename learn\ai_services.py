"""
AI Services for ForgeX Learning Platform
Provides intelligent learning assistance, code analysis, and personalized recommendations
"""

import json
import re
import random
from typing import Dict, List, Any, Optional
from django.contrib.auth.models import User
from django.db import models
from .models import (
    UserLearningProfile, LessonProgress, AILearningAssistant,
    SmartRecommendation, CodeAnalysis, Lesson, Course
)


class AILearningService:
    """Main AI service for learning assistance and personalization"""
    
    def __init__(self):
        self.programming_concepts = {
            'python': ['variables', 'functions', 'classes', 'loops', 'conditionals', 'data structures'],
            'javascript': ['variables', 'functions', 'objects', 'arrays', 'promises', 'dom manipulation'],
            'java': ['classes', 'objects', 'inheritance', 'polymorphism', 'interfaces', 'collections'],
            'general': ['algorithms', 'data structures', 'design patterns', 'debugging', 'testing']
        }
        
        self.learning_tips = {
            'visual': "Try creating diagrams and flowcharts to visualize concepts",
            'auditory': "Consider explaining concepts out loud or discussing with peers",
            'kinesthetic': "Practice coding exercises and hands-on projects",
            'reading': "Take detailed notes and read documentation thoroughly"
        }
    
    def get_or_create_learning_profile(self, user: User) -> UserLearningProfile:
        """Get or create a learning profile for the user"""
        profile, created = UserLearningProfile.objects.get_or_create(
            user=user,
            defaults={
                'learning_style': 'visual',
                'current_skill_level': 'beginner',
                'preferred_difficulty': 'beginner',
                'learning_goals': 'Improve programming skills',
                'strengths': [],
                'weaknesses': []
            }
        )
        return profile
    
    def analyze_user_progress(self, user: User) -> Dict[str, Any]:
        """Analyze user's learning progress and generate insights"""
        profile = self.get_or_create_learning_profile(user)
        progress_records = LessonProgress.objects.filter(user=user)
        
        total_lessons = progress_records.count()
        completed_lessons = progress_records.filter(status='completed').count()
        mastered_lessons = progress_records.filter(status='mastered').count()
        
        completion_rate = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
        mastery_rate = (mastered_lessons / total_lessons * 100) if total_lessons > 0 else 0
        
        avg_score = progress_records.exclude(score__isnull=True).aggregate(
            avg_score=models.Avg('score')
        )['avg_score'] or 0
        
        return {
            'total_lessons': total_lessons,
            'completed_lessons': completed_lessons,
            'mastered_lessons': mastered_lessons,
            'completion_rate': round(completion_rate, 1),
            'mastery_rate': round(mastery_rate, 1),
            'average_score': round(avg_score, 1),
            'total_study_time': profile.total_study_time,
            'current_level': profile.current_skill_level,
            'learning_style': profile.learning_style
        }
    
    def generate_ai_response(self, user_message: str, context: Dict[str, Any] = None) -> str:
        """Generate AI response based on user message and context"""
        message_lower = user_message.lower()
        
        # Code-related questions
        if any(keyword in message_lower for keyword in ['code', 'function', 'variable', 'error', 'bug']):
            return self._generate_code_help_response(user_message, context)
        
        # Concept explanations
        elif any(keyword in message_lower for keyword in ['what is', 'explain', 'how does', 'why']):
            return self._generate_concept_explanation(user_message, context)
        
        # Learning guidance
        elif any(keyword in message_lower for keyword in ['learn', 'study', 'practice', 'improve']):
            return self._generate_learning_guidance(user_message, context)
        
        # General encouragement and help
        else:
            return self._generate_general_response(user_message, context)
    
    def _generate_code_help_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate response for code-related questions"""
        responses = [
            "I'd be happy to help with your code! Here are some general debugging tips:\n\n"
            "1. Check for syntax errors (missing brackets, semicolons)\n"
            "2. Verify variable names and scope\n"
            "3. Use print statements to trace execution\n"
            "4. Break down complex problems into smaller parts\n\n"
            "Can you share the specific code you're working on?",
            
            "Great question about coding! Here's how I can help:\n\n"
            "• Code review and suggestions\n"
            "• Debugging assistance\n"
            "• Best practices recommendations\n"
            "• Performance optimization tips\n\n"
            "What specific coding challenge are you facing?",
            
            "Let's solve this coding problem together! Here's my approach:\n\n"
            "1. Understand the problem requirements\n"
            "2. Plan the solution step by step\n"
            "3. Write clean, readable code\n"
            "4. Test with different inputs\n\n"
            "What programming language are you using?"
        ]
        return random.choice(responses)
    
    def _generate_concept_explanation(self, message: str, context: Dict[str, Any]) -> str:
        """Generate explanations for programming concepts"""
        responses = [
            "That's an excellent question! Understanding core concepts is crucial for programming success.\n\n"
            "Here's how I like to explain complex topics:\n"
            "• Start with the basic definition\n"
            "• Provide real-world analogies\n"
            "• Show practical examples\n"
            "• Practice with hands-on exercises\n\n"
            "Which specific concept would you like me to explain?",
            
            "I love explaining programming concepts! Let me break this down for you:\n\n"
            "The key to understanding any concept is to:\n"
            "1. Learn the 'what' (definition)\n"
            "2. Understand the 'why' (purpose)\n"
            "3. Practice the 'how' (implementation)\n\n"
            "What concept are you curious about?",
            
            "Great question! Conceptual understanding is the foundation of good programming.\n\n"
            "I can help explain:\n"
            "• Programming fundamentals\n"
            "• Data structures and algorithms\n"
            "• Design patterns\n"
            "• Best practices\n\n"
            "What would you like to explore?"
        ]
        return random.choice(responses)
    
    def _generate_learning_guidance(self, message: str, context: Dict[str, Any]) -> str:
        """Generate learning and study guidance"""
        responses = [
            "I'm here to help optimize your learning journey! Here's my recommended approach:\n\n"
            "📚 **Study Strategy:**\n"
            "• Set clear, achievable goals\n"
            "• Practice regularly (consistency beats intensity)\n"
            "• Build projects to apply knowledge\n"
            "• Join coding communities for support\n\n"
            "What's your current learning goal?",
            
            "Excellent mindset! Continuous learning is key to programming success.\n\n"
            "🎯 **Learning Tips:**\n"
            "• Focus on understanding, not memorizing\n"
            "• Learn by doing (build projects)\n"
            "• Teach others to reinforce knowledge\n"
            "• Embrace mistakes as learning opportunities\n\n"
            "What skill would you like to develop?",
            
            "I love your enthusiasm for learning! Here's how to maximize your progress:\n\n"
            "🚀 **Effective Learning:**\n"
            "• Break complex topics into smaller chunks\n"
            "• Use multiple learning resources\n"
            "• Practice coding daily\n"
            "• Review and refactor your code\n\n"
            "What's your biggest learning challenge right now?"
        ]
        return random.choice(responses)
    
    def _generate_general_response(self, message: str, context: Dict[str, Any]) -> str:
        """Generate general helpful responses"""
        responses = [
            "Hello! I'm your AI learning assistant, here to help you master programming concepts.\n\n"
            "I can help you with:\n"
            "• Code review and debugging\n"
            "• Concept explanations\n"
            "• Learning strategies\n"
            "• Project guidance\n\n"
            "What would you like to learn today?",
            
            "Hi there! I'm excited to help you on your coding journey.\n\n"
            "Whether you're a beginner or looking to advance your skills, I'm here to:\n"
            "• Answer your questions\n"
            "• Provide personalized guidance\n"
            "• Help you overcome challenges\n"
            "• Celebrate your progress!\n\n"
            "How can I assist you today?",
            
            "Great to meet you! I'm your dedicated AI learning companion.\n\n"
            "My goal is to make your learning experience:\n"
            "✨ Personalized and adaptive\n"
            "🎯 Goal-oriented and practical\n"
            "🤝 Supportive and encouraging\n"
            "📈 Continuously improving\n\n"
            "What programming topic interests you most?"
        ]
        return random.choice(responses)
    
    def generate_personalized_recommendations(self, user: User, limit: int = 5) -> List[Dict[str, Any]]:
        """Generate personalized learning recommendations"""
        profile = self.get_or_create_learning_profile(user)
        progress_records = LessonProgress.objects.filter(user=user)
        
        recommendations = []
        
        # Recommend next lessons based on progress
        completed_lessons = progress_records.filter(status__in=['completed', 'mastered']).values_list('lesson_id', flat=True)
        next_lessons = Lesson.objects.exclude(id__in=completed_lessons)[:3]
        
        for lesson in next_lessons:
            recommendations.append({
                'type': 'next_lesson',
                'title': f"Continue with: {lesson.name}",
                'description': f"Based on your progress, this {lesson.lesson_type} lesson is perfect for you!",
                'lesson': lesson,
                'confidence': 0.8,
                'priority': 5
            })
        
        # Recommend review for low-scoring lessons
        low_score_lessons = progress_records.filter(score__lt=70, score__isnull=False)[:2]
        for progress in low_score_lessons:
            recommendations.append({
                'type': 'review_content',
                'title': f"Review: {progress.lesson.name}",
                'description': "Your score suggests reviewing this topic could be beneficial.",
                'lesson': progress.lesson,
                'confidence': 0.7,
                'priority': 4
            })
        
        return recommendations[:limit]


class CodeAnalysisService:
    """AI-powered code analysis and feedback service"""

    def __init__(self):
        self.syntax_patterns = {
            'python': {
                'missing_colon': r'(if|for|while|def|class|try|except|with|elif|else)\s+[^:]*$',
                'missing_parentheses': r'(if|while|for)\s+[^()]*[^():]\s*:',
                'indentation_error': r'^[ ]*[^ ]',
                'unused_import': r'^import\s+(\w+)(?:\s+as\s+\w+)?$',
            },
            'javascript': {
                'missing_semicolon': r'[^;{}]\s*$',
                'missing_brackets': r'(if|for|while)\s*\([^)]*\)\s*[^{]',
                'var_declaration': r'^\s*(var|let|const)\s+\w+',
                'function_declaration': r'function\s+\w+\s*\([^)]*\)',
            },
            'java': {
                'missing_semicolon': r'[^;{}]\s*$',
                'class_declaration': r'(public|private|protected)?\s*class\s+\w+',
                'method_declaration': r'(public|private|protected)\s+\w+\s+\w+\s*\([^)]*\)',
            }
        }

        self.best_practices = {
            'python': [
                "Use descriptive variable names",
                "Follow PEP 8 style guidelines",
                "Add docstrings to functions and classes",
                "Use list comprehensions when appropriate",
                "Handle exceptions properly with try-except blocks"
            ],
            'javascript': [
                "Use const for variables that don't change",
                "Use meaningful function and variable names",
                "Avoid global variables",
                "Use strict mode ('use strict')",
                "Handle promises properly with async/await"
            ],
            'java': [
                "Follow Java naming conventions",
                "Use proper access modifiers",
                "Implement proper exception handling",
                "Use interfaces for abstraction",
                "Follow SOLID principles"
            ]
        }

    def analyze_code(self, code: str, language: str, analysis_type: str = 'comprehensive') -> Dict[str, Any]:
        """Analyze code and provide AI feedback"""
        analysis_result = {
            'syntax_issues': [],
            'best_practice_suggestions': [],
            'performance_tips': [],
            'security_concerns': [],
            'style_improvements': [],
            'overall_score': 0,
            'feedback': ""
        }

        # Syntax analysis
        if analysis_type in ['syntax_check', 'comprehensive']:
            analysis_result['syntax_issues'] = self._check_syntax(code, language)

        # Best practices analysis
        if analysis_type in ['best_practices', 'comprehensive']:
            analysis_result['best_practice_suggestions'] = self._check_best_practices(code, language)

        # Performance analysis
        if analysis_type in ['performance', 'comprehensive']:
            analysis_result['performance_tips'] = self._check_performance(code, language)

        # Security analysis
        if analysis_type in ['security', 'comprehensive']:
            analysis_result['security_concerns'] = self._check_security(code, language)

        # Style analysis
        if analysis_type in ['style', 'comprehensive']:
            analysis_result['style_improvements'] = self._check_style(code, language)

        # Calculate overall score
        analysis_result['overall_score'] = self._calculate_code_score(analysis_result)

        # Generate comprehensive feedback
        analysis_result['feedback'] = self._generate_code_feedback(analysis_result, language)

        return analysis_result

    def _check_syntax(self, code: str, language: str) -> List[Dict[str, str]]:
        """Check for basic syntax issues"""
        issues = []
        lines = code.split('\n')

        if language in self.syntax_patterns:
            patterns = self.syntax_patterns[language]

            for i, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith('#') or line.startswith('//'):
                    continue

                # Check for missing colons (Python)
                if language == 'python' and 'missing_colon' in patterns:
                    if re.search(patterns['missing_colon'], line):
                        issues.append({
                            'line': i,
                            'type': 'syntax',
                            'message': f"Missing colon at end of line {i}",
                            'suggestion': "Add ':' at the end of control statements"
                        })

                # Check for missing semicolons (JavaScript, Java)
                if language in ['javascript', 'java'] and 'missing_semicolon' in patterns:
                    if re.search(patterns['missing_semicolon'], line) and not line.endswith(('{', '}', ';')):
                        issues.append({
                            'line': i,
                            'type': 'syntax',
                            'message': f"Missing semicolon at line {i}",
                            'suggestion': "Add ';' at the end of statements"
                        })

        return issues

    def _check_best_practices(self, code: str, language: str) -> List[Dict[str, str]]:
        """Check for best practice violations"""
        suggestions = []

        if language in self.best_practices:
            practices = self.best_practices[language]

            # Check for variable naming
            if re.search(r'\b[a-z]\b', code):
                suggestions.append({
                    'type': 'naming',
                    'message': "Consider using more descriptive variable names",
                    'suggestion': practices[0]
                })

            # Check for function documentation
            if 'def ' in code and '"""' not in code and "'''" not in code:
                suggestions.append({
                    'type': 'documentation',
                    'message': "Add docstrings to your functions",
                    'suggestion': practices[2] if len(practices) > 2 else "Document your functions"
                })

        return suggestions

    def _check_performance(self, code: str, language: str) -> List[Dict[str, str]]:
        """Check for performance issues"""
        tips = []

        # Check for inefficient loops
        if re.search(r'for\s+\w+\s+in\s+range\(len\(', code):
            tips.append({
                'type': 'loop_optimization',
                'message': "Consider using direct iteration instead of range(len())",
                'suggestion': "Use 'for item in list:' instead of 'for i in range(len(list)):'"
            })

        # Check for string concatenation in loops
        if re.search(r'for.*:\s*\w+\s*\+=\s*["\']', code):
            tips.append({
                'type': 'string_optimization',
                'message': "String concatenation in loops can be inefficient",
                'suggestion': "Consider using join() or list comprehension for better performance"
            })

        return tips

    def _check_security(self, code: str, language: str) -> List[Dict[str, str]]:
        """Check for security concerns"""
        concerns = []

        # Check for eval usage
        if 'eval(' in code:
            concerns.append({
                'type': 'code_injection',
                'severity': 'high',
                'message': "Using eval() can be dangerous",
                'suggestion': "Avoid eval() and use safer alternatives like ast.literal_eval()"
            })

        # Check for hardcoded passwords/secrets
        if re.search(r'(password|secret|key)\s*=\s*["\'][^"\']+["\']', code, re.IGNORECASE):
            concerns.append({
                'type': 'hardcoded_secrets',
                'severity': 'medium',
                'message': "Avoid hardcoding sensitive information",
                'suggestion': "Use environment variables or secure configuration files"
            })

        return concerns

    def _check_style(self, code: str, language: str) -> List[Dict[str, str]]:
        """Check for style improvements"""
        improvements = []

        # Check line length
        lines = code.split('\n')
        for i, line in enumerate(lines, 1):
            if len(line) > 100:
                improvements.append({
                    'line': i,
                    'type': 'line_length',
                    'message': f"Line {i} is too long ({len(line)} characters)",
                    'suggestion': "Keep lines under 80-100 characters for better readability"
                })

        # Check for consistent indentation
        if language == 'python':
            indent_sizes = []
            for line in lines:
                if line.strip() and line.startswith(' '):
                    indent_sizes.append(len(line) - len(line.lstrip()))

            if indent_sizes and len(set(indent_sizes)) > 2:
                improvements.append({
                    'type': 'indentation',
                    'message': "Inconsistent indentation detected",
                    'suggestion': "Use consistent indentation (4 spaces recommended for Python)"
                })

        return improvements

    def _calculate_code_score(self, analysis: Dict[str, Any]) -> int:
        """Calculate overall code quality score (0-100)"""
        base_score = 100

        # Deduct points for issues
        base_score -= len(analysis['syntax_issues']) * 10
        base_score -= len(analysis['security_concerns']) * 15
        base_score -= len(analysis['best_practice_suggestions']) * 5
        base_score -= len(analysis['performance_tips']) * 3
        base_score -= len(analysis['style_improvements']) * 2

        return max(0, min(100, base_score))

    def _generate_code_feedback(self, analysis: Dict[str, Any], language: str) -> str:
        """Generate comprehensive feedback message"""
        score = analysis['overall_score']

        if score >= 90:
            feedback = "🎉 Excellent code! Your implementation follows best practices and shows strong programming skills."
        elif score >= 75:
            feedback = "👍 Good work! Your code is solid with just a few areas for improvement."
        elif score >= 60:
            feedback = "📈 You're on the right track! Focus on the suggestions below to enhance your code quality."
        else:
            feedback = "🔧 There's room for improvement. Don't worry - every expert was once a beginner!"

        # Add specific guidance
        total_issues = (len(analysis['syntax_issues']) + len(analysis['security_concerns']) +
                       len(analysis['best_practice_suggestions']) + len(analysis['performance_tips']) +
                       len(analysis['style_improvements']))

        if total_issues > 0:
            feedback += f"\n\nI found {total_issues} areas to improve. Focus on the most critical issues first:"

            if analysis['syntax_issues']:
                feedback += f"\n• Fix {len(analysis['syntax_issues'])} syntax issues"
            if analysis['security_concerns']:
                feedback += f"\n• Address {len(analysis['security_concerns'])} security concerns"
            if analysis['best_practice_suggestions']:
                feedback += f"\n• Apply {len(analysis['best_practice_suggestions'])} best practices"

        feedback += f"\n\nKeep coding and improving! 🚀"

        return feedback
