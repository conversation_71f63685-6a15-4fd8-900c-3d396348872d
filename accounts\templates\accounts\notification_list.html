{% extends 'base.html' %}
{% load static %}
{% block title %}Notifications{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="account-page">
    <div class="account-card fade-in expanded-profile">
        <h2>Notifications</h2>

        <div class="notification-tabs">
            <div class="tab-header">
                <button class="tab-btn active" data-tab="project">🏆 Project Notifications</button>
                <button class="tab-btn" data-tab="system">🔔 System Notifications</button>
            </div>

            <!-- Project Notifications Tab -->
            <div id="project-tab" class="tab-content active">
                <div class="tab-actions">
                    <button class="action-btn mark-all" onclick="markAllProjectAsRead()">
                        <i class="fas fa-check-double"></i> Mark All as Read
                    </button>
                </div>

                <h3>Unread Notifications</h3>
                <div class="notification-list">
                    {% for notification in project_unread_notifications %}
                        <div class="notification-item unread" id="notification-{{ notification.id }}">
                            <div class="notification-content">
                                <strong>{{ notification.project.title }}</strong>
                                <p>{{ notification.message }}</p>

                                {% if notification.is_accepted is None and notification.project %}
                                    <!-- Project Details Section -->
                                    <div class="project-details">
                                        <div class="project-info">
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-info-circle"></i> Description:</span>
                                                <span class="info-value">{{ notification.project.description|truncatechars:150 }}</span>
                                            </div>

                                            {% if notification.project.required_skills.all %}
                                                <div class="info-item">
                                                    <span class="info-label"><i class="fas fa-code"></i> Required Skills:</span>
                                                    <div class="skill-tags">
                                                        {% for skill in notification.project.required_skills.all %}
                                                            <span class="skill-tag">{{ skill.name }}</span>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            {% endif %}

                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-user"></i> Owner:</span>
                                                <span class="info-value">{{ notification.project.owner.username }}</span>
                                            </div>

                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-calendar-alt"></i> Created:</span>
                                                <span class="info-value">{{ notification.project.created_at|date:"M d, Y" }}</span>
                                            </div>

                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-users"></i> Team Size:</span>
                                                <span class="info-value">{{ notification.project.team_members.count }} member(s)</span>
                                            </div>

                                            <button type="button" class="view-more-btn" onclick="window.open('{% url 'collaborate:project_detail' notification.project.id %}', '_blank')">
                                                <i class="fas fa-external-link-alt"></i> View Full Project Details
                                            </button>
                                        </div>
                                    </div>

                                    <div class="invite-actions">
                                        <form id="accept-form-{{ notification.id }}" action="{% url 'accounts:respond_invite' notification.id %}" method="post" style="display: inline;">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="accept">
                                            <button type="button" class="action-btn accept" onclick="submitForm('accept-form-{{ notification.id }}')">
                                                <i class="fas fa-check"></i> Accept
                                            </button>
                                        </form>
                                        <form id="decline-form-{{ notification.id }}" action="{% url 'accounts:respond_invite' notification.id %}" method="post" style="display: inline;">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="reject">
                                            <button type="button" class="action-btn decline" onclick="submitForm('decline-form-{{ notification.id }}')">
                                                <i class="fas fa-times"></i> Decline
                                            </button>
                                        </form>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn mark-read" onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check"></i> Mark as Read
                                </button>
                            </div>
                        </div>
                    {% empty %}
                        <div class="notification-item empty">
                            No unread project notifications.
                        </div>
                    {% endfor %}
                </div>

                <h3>Read Notifications</h3>
                <div class="notification-list" id="read-project-notifications">
                    {% for notification in project_read_notifications %}
                        <div class="notification-item">
                            <div class="notification-content">
                                <strong>{{ notification.project.title }}</strong>
                                <p>{{ notification.message }}</p>

                                <!-- Project Details Section -->
                                <div class="project-details">
                                    <div class="project-info">
                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-info-circle"></i> Description:</span>
                                            <span class="info-value">{{ notification.project.description|truncatechars:150 }}</span>
                                        </div>

                                        {% if notification.project.required_skills.all %}
                                            <div class="info-item">
                                                <span class="info-label"><i class="fas fa-code"></i> Required Skills:</span>
                                                <div class="skill-tags">
                                                    {% for skill in notification.project.required_skills.all %}
                                                        <span class="skill-tag">{{ skill.name }}</span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endif %}

                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-user"></i> Owner:</span>
                                            <span class="info-value">{{ notification.project.owner.username }}</span>
                                        </div>

                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-calendar-alt"></i> Created:</span>
                                            <span class="info-value">{{ notification.project.created_at|date:"M d, Y" }}</span>
                                        </div>

                                        <div class="info-item">
                                            <span class="info-label"><i class="fas fa-users"></i> Team Size:</span>
                                            <span class="info-value">{{ notification.project.team_members.count }} member(s)</span>
                                        </div>
                                    </div>
                                </div>

                                {% if notification.is_accepted is not None %}
                                    <div class="response-status">
                                        {% if notification.is_accepted %}
                                            <span class="status-badge accepted">✅ Invitation Accepted</span>
                                            <a href="{% url 'collaborate:project_detail' notification.project.id %}" class="view-details-btn">
                                                <i class="fas fa-external-link-alt"></i> View Project
                                            </a>
                                        {% else %}
                                            <span class="status-badge declined">❌ Invitation Declined</span>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <div class="invite-actions">
                                        <form id="accept-form-{{ notification.id }}" action="{% url 'accounts:respond_invite' notification.id %}" method="post" style="display: inline;">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="accept">
                                            <button type="button" class="action-btn accept" onclick="submitForm('accept-form-{{ notification.id }}')">
                                                <i class="fas fa-check"></i> Accept
                                            </button>
                                        </form>
                                        <form id="decline-form-{{ notification.id }}" action="{% url 'accounts:respond_invite' notification.id %}" method="post" style="display: inline;">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="reject">
                                            <button type="button" class="action-btn decline" onclick="submitForm('decline-form-{{ notification.id }}')">
                                                <i class="fas fa-times"></i> Decline
                                            </button>
                                        </form>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% empty %}
                        <div class="notification-item empty">
                            No read project notifications.
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- System Notifications Tab -->
            <div id="system-tab" class="tab-content">
                <div class="tab-actions">
                    <button class="action-btn mark-all" onclick="markAllSystemAsRead()">
                        <i class="fas fa-check-double"></i> Mark All as Read
                    </button>
                </div>

                <h3>Unread Notifications</h3>
                <div class="notification-list">
                    {% for notification in system_unread_notifications %}
                        <div class="notification-item unread" id="notification-{{ notification.id }}">
                            <div class="notification-content">
                                <strong>{{ notification.title }}</strong>
                                <p>{{ notification.message }}</p>
                            </div>
                            <div class="notification-actions">
                                <button class="action-btn mark-read" onclick="markAsRead({{ notification.id }})">
                                    <i class="fas fa-check"></i> Mark as Read
                                </button>
                            </div>
                        </div>
                    {% empty %}
                        <div class="notification-item empty">
                            No unread system notifications.
                        </div>
                    {% endfor %}
                </div>

                <h3>Read Notifications</h3>
                <div class="notification-list" id="read-system-notifications">
                    {% for notification in system_read_notifications %}
                        <div class="notification-item">
                            <div class="notification-content">
                                <strong>{{ notification.title }}</strong>
                                <p>{{ notification.message }}</p>
                            </div>
                        </div>
                    {% empty %}
                        <div class="notification-item empty">
                            No read system notifications.
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
function markAsRead(id) {
    // Show loading state
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    button.disabled = true;

    fetch(`/accounts/notification/read/${id}/`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if(data.status === 'success'){
            // Show success animation
            button.innerHTML = '<i class="fas fa-check"></i> Done!';
            button.style.backgroundColor = 'rgba(76, 175, 80, 0.2)';
            button.style.borderColor = '#4CAF50';
            button.style.color = '#4CAF50';

            // Get the notification element
            const notificationItem = document.getElementById(`notification-${id}`);

            if (notificationItem) {
                // Clone the notification to move it to the read section
                const clonedItem = notificationItem.cloneNode(true);

                // Remove the unread class and mark-read button
                clonedItem.classList.remove('unread');
                const actionDiv = clonedItem.querySelector('.notification-actions');
                if (actionDiv) {
                    actionDiv.remove();
                }

                // Keep the invite actions (accept/decline buttons)
                const inviteActions = clonedItem.querySelector('.invite-actions');
                if (inviteActions) {
                    // Make sure the invite actions are still visible and functional
                    inviteActions.style.display = 'flex';
                }

                // Determine if it's a project or system notification
                const isProjectTab = document.getElementById('project-tab').classList.contains('active');
                const targetList = isProjectTab ?
                    document.getElementById('read-project-notifications') :
                    document.getElementById('read-system-notifications');

                // Add to the read notifications section
                if (targetList) {
                    // Remove any "No read notifications" message if it exists
                    const emptyItem = targetList.querySelector('.notification-item.empty');
                    if (emptyItem) {
                        emptyItem.remove();
                    }

                    // Add the notification to the read list
                    targetList.prepend(clonedItem);

                    // Remove the original notification with animation
                    notificationItem.style.transition = 'all 0.5s ease';
                    notificationItem.style.opacity = '0';
                    notificationItem.style.height = '0';
                    notificationItem.style.padding = '0';
                    notificationItem.style.margin = '0';
                    notificationItem.style.overflow = 'hidden';

                    setTimeout(() => {
                        notificationItem.remove();

                        // Check if there are no more unread notifications
                        const unreadList = notificationItem.parentElement;
                        if (unreadList && unreadList.children.length === 0) {
                            // Add empty message
                            const emptyDiv = document.createElement('div');
                            emptyDiv.className = 'notification-item empty';
                            emptyDiv.textContent = isProjectTab ?
                                'No unread project notifications.' :
                                'No unread system notifications.';
                            unreadList.appendChild(emptyDiv);
                        }
                    }, 500);
                }
            } else {
                // If we can't find the notification, reload the page
                setTimeout(() => {
                    location.reload();
                }, 800);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        alert('There was an error processing your request. Please try again.');
    });
}

function markAllSystemAsRead() {
    // Show loading state
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    button.disabled = true;

    fetch('/accounts/notifications/mark_all_read/?type=system', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if(data.status === 'success'){
            // Show success animation
            button.innerHTML = '<i class="fas fa-check"></i> Done!';
            button.style.backgroundColor = 'rgba(76, 175, 80, 0.2)';
            button.style.borderColor = '#4CAF50';
            button.style.color = '#4CAF50';

            // Get all unread system notifications
            const unreadItems = document.querySelectorAll('#system-tab .notification-item.unread');
            const targetList = document.getElementById('read-system-notifications');

            if (unreadItems.length > 0 && targetList) {
                // Remove any "No read notifications" message if it exists
                const emptyItem = targetList.querySelector('.notification-item.empty');
                if (emptyItem) {
                    emptyItem.remove();
                }

                // Process each unread notification
                unreadItems.forEach((item, index) => {
                    // Clone the notification to move it to the read section
                    const clonedItem = item.cloneNode(true);

                    // Remove the unread class and mark-read button
                    clonedItem.classList.remove('unread');
                    const actionDiv = clonedItem.querySelector('.notification-actions');
                    if (actionDiv) {
                        actionDiv.remove();
                    }

                    // Add to the read notifications section with a slight delay for animation
                    setTimeout(() => {
                        targetList.prepend(clonedItem);
                    }, index * 100);

                    // Remove the original notification with animation
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '0';
                    item.style.height = '0';
                    item.style.padding = '0';
                    item.style.margin = '0';
                    item.style.overflow = 'hidden';

                    setTimeout(() => {
                        item.remove();

                        // If this was the last item, add the empty message
                        if (index === unreadItems.length - 1) {
                            const unreadList = document.querySelector('#system-tab .notification-list:first-of-type');
                            if (unreadList && unreadList.children.length === 0) {
                                const emptyDiv = document.createElement('div');
                                emptyDiv.className = 'notification-item empty';
                                emptyDiv.textContent = 'No unread system notifications.';
                                unreadList.appendChild(emptyDiv);
                            }

                            // Reset the button after all animations are done
                            setTimeout(() => {
                                button.innerHTML = originalHTML;
                                button.style.backgroundColor = '';
                                button.style.borderColor = '';
                                button.style.color = '';
                                button.disabled = false;
                            }, 500);
                        }
                    }, 500 + (index * 100));
                });
            } else {
                // If no unread notifications, just reset the button
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.style.backgroundColor = '';
                    button.style.borderColor = '';
                    button.style.color = '';
                    button.disabled = false;
                }, 800);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        alert('There was an error processing your request. Please try again.');
    });
}

function markAllProjectAsRead() {
    // Show loading state
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    button.disabled = true;

    fetch('/accounts/notifications/mark_all_read/?type=project', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if(data.status === 'success'){
            // Show success animation
            button.innerHTML = '<i class="fas fa-check"></i> Done!';
            button.style.backgroundColor = 'rgba(76, 175, 80, 0.2)';
            button.style.borderColor = '#4CAF50';
            button.style.color = '#4CAF50';

            // Get all unread project notifications
            const unreadItems = document.querySelectorAll('#project-tab .notification-item.unread');
            const targetList = document.getElementById('read-project-notifications');

            if (unreadItems.length > 0 && targetList) {
                // Remove any "No read notifications" message if it exists
                const emptyItem = targetList.querySelector('.notification-item.empty');
                if (emptyItem) {
                    emptyItem.remove();
                }

                // Process each unread notification
                unreadItems.forEach((item, index) => {
                    // Clone the notification to move it to the read section
                    const clonedItem = item.cloneNode(true);

                    // Remove the unread class and mark-read button
                    clonedItem.classList.remove('unread');
                    const actionDiv = clonedItem.querySelector('.notification-actions');
                    if (actionDiv) {
                        actionDiv.remove();
                    }

                    // Keep the invite actions (accept/decline buttons)
                    const inviteActions = clonedItem.querySelector('.invite-actions');
                    if (inviteActions) {
                        // Make sure the invite actions are still visible and functional
                        inviteActions.style.display = 'flex';
                    }

                    // Add to the read notifications section with a slight delay for animation
                    setTimeout(() => {
                        targetList.prepend(clonedItem);
                    }, index * 100);

                    // Remove the original notification with animation
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '0';
                    item.style.height = '0';
                    item.style.padding = '0';
                    item.style.margin = '0';
                    item.style.overflow = 'hidden';

                    setTimeout(() => {
                        item.remove();

                        // If this was the last item, add the empty message
                        if (index === unreadItems.length - 1) {
                            const unreadList = document.querySelector('#project-tab .notification-list:first-of-type');
                            if (unreadList && unreadList.children.length === 0) {
                                const emptyDiv = document.createElement('div');
                                emptyDiv.className = 'notification-item empty';
                                emptyDiv.textContent = 'No unread project notifications.';
                                unreadList.appendChild(emptyDiv);
                            }

                            // Reset the button after all animations are done
                            setTimeout(() => {
                                button.innerHTML = originalHTML;
                                button.style.backgroundColor = '';
                                button.style.borderColor = '';
                                button.style.color = '';
                                button.disabled = false;
                            }, 500);
                        }
                    }, 500 + (index * 100));
                });
            } else {
                // If no unread notifications, just reset the button
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.style.backgroundColor = '';
                    button.style.borderColor = '';
                    button.style.color = '';
                    button.disabled = false;
                }, 800);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        alert('There was an error processing your request. Please try again.');
    });
}

function submitForm(formId) {
    const form = document.getElementById(formId);
    if (form) {
        try {
            // Show a temporary status message
            const button = form.querySelector('button');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            button.disabled = true;

            // Submit the form
            form.submit();

            // Set a fallback to reset button if page doesn't reload
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.disabled = false;
            }, 5000);
        } catch (error) {
            console.error('Error submitting form:', error);
            alert('There was an error processing your response. Please try again.');
        }
    } else {
        console.error('Form not found:', formId);
    }
}

// Get CSRF token from cookies
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
        },
        "shape": {
          "type": ["circle", "triangle"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Tab functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons and contents
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked button and corresponding content
            btn.classList.add('active');
            const tabId = btn.getAttribute('data-tab');
            document.getElementById(`${tabId}-tab`).classList.add('active');
        });
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
});
</script>

<style>
/* Override base styles to match the screenshot */
body {
    background-color: #1a1a1a;
    color: #ffffff;
}

.account-page {
    background-color: transparent;
}

.account-card {
    background-color: rgba(28, 28, 28, 0.8);
    border: 1px solid rgba(192, 255, 107, 0.3);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* Notification page specific styles */
.notification-tabs {
    width: 100%;
    margin-top: 20px;
}

.tab-header {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(192, 255, 107, 0.3);
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 10px 20px;
    color: #ffffff;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: var(--color-border);
}

.tab-btn.active {
    color: var(--color-border);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--color-border);
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { width: 0; }
    to { width: 100%; }
}

.tab-content {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.tab-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 15px;
}

.notification-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.notification-item {
    background-color: rgba(40, 40, 40, 0.5);
    border-radius: 8px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    transition: all 0.3s ease;
    border-left: 3px solid rgba(192, 255, 107, 0.3);
}

.notification-item:hover {
    background-color: rgba(50, 50, 50, 0.7);
    transform: translateX(5px);
}

.notification-item.unread {
    border-left-color: var(--color-border);
    background-color: rgba(50, 50, 50, 0.7);
}

.notification-item.empty {
    justify-content: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
}

.notification-content {
    flex: 1;
    background-color: rgba(40, 40, 40, 0.5);
}

.notification-content strong {
    display: block;
    margin-bottom: 5px;
    color: var(--color-border);
}

.notification-content p {
    margin-bottom: 10px;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-left: 15px;
}

.invite-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.action-btn {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    padding: 8px 12px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.action-btn.mark-all {
    border-color: var(--color-border);
    color: var(--color-border);
    border-radius: 20px;
    padding: 5px 15px;
}

.action-btn.mark-all:hover {
    background-color: rgba(192, 255, 107, 0.1);
}

.action-btn.mark-read:hover {
    border-color: #4CAF50;
    color: #4CAF50;
}

.action-btn.accept {
    border-color: #4CAF50;
    color: #4CAF50;
}

.action-btn.accept:hover {
    background-color: rgba(76, 175, 80, 0.2);
}

.action-btn.decline {
    border-color: #f44336;
    color: #f44336;
}

.action-btn.decline:hover {
    background-color: rgba(244, 67, 54, 0.2);
}

.response-status {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-badge {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.accepted {
    background-color: rgba(76, 175, 80, 0.2);
    color: #4CAF50;
    border: 1px solid #4CAF50;
}

.status-badge.declined {
    background-color: rgba(244, 67, 54, 0.2);
    color: #f44336;
    border: 1px solid #f44336;
}

.view-details-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background-color: transparent;
    color: var(--color-border);
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid var(--color-border);
    transition: all 0.3s ease;
}

.view-details-btn:hover {
    background-color: var(--color-border);
    color: #000000;
    text-decoration: none;
}

/* Match the screenshot styling */
.account-card h2 {
    color: #ffffff;
    font-size: 28px;
    margin-bottom: 30px;
    border-bottom: 2px solid var(--color-border);
    padding-bottom: 10px;
    display: inline-block;
}

.tab-header {
    border-bottom: none;
}

.tab-btn {
    padding: 8px 15px;
    margin-right: 10px;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    border-bottom: 2px solid var(--color-border);
}

.tab-btn.active::after {
    display: none;
}

h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #ffffff;
}

/* Project Details Styling */
.project-details {
    background-color: rgba(30, 30, 30, 0.6);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border: 1px solid rgba(192, 255, 107, 0.2);
}

.project-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    font-weight: 600;
    color: var(--color-border);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.info-value {
    color: #ffffff;
    font-size: 14px;
    line-height: 1.5;
    padding-left: 20px;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-left: 20px;
    margin-top: 5px;
}

.skill-tag {
    background-color: rgba(192, 255, 107, 0.15);
    color: var(--color-border);
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    transition: all 0.3s ease;
}

.skill-tag:hover {
    background-color: rgba(192, 255, 107, 0.25);
    transform: translateY(-2px);
}

.view-more-btn {
    background-color: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-border);
    border-radius: 20px;
    padding: 6px 15px;
    font-size: 13px;
    margin-top: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    align-self: flex-start;
}

.view-more-btn:hover {
    background-color: rgba(192, 255, 107, 0.15);
    transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 768px) {
    .notification-item {
        flex-direction: column;
    }

    .notification-actions {
        margin-left: 0;
        margin-top: 15px;
        flex-direction: row;
        justify-content: flex-end;
    }

    .invite-actions {
        flex-direction: column;
    }

    .tab-header {
        flex-direction: column;
    }

    .tab-btn {
        width: 100%;
        text-align: center;
    }

    .project-details {
        padding: 10px;
    }

    .info-item {
        gap: 3px;
    }

    .info-label {
        font-size: 13px;
    }

    .info-value {
        font-size: 13px;
        padding-left: 10px;
    }

    .skill-tags {
        padding-left: 10px;
    }

    .skill-tag {
        font-size: 11px;
        padding: 3px 8px;
    }
}
</style>
{% endblock %}