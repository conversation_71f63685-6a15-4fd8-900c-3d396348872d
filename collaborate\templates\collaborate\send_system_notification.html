{% extends 'base.html' %}

{% block title %}Send System Notification{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h1 class="card-title mb-0">Send System Notification</h1>
                </div>
                
                <div class="card-body">
                    {% if messages %}
                        <div class="alert alert-info">
                            {% for message in messages %}
                                {{ message }}
                            {% endfor %}
                        </div>
                    {% endif %}
                    
                    <p class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> 
                        This will send a notification to <strong>all active users</strong> in the system. 
                        Use this feature responsibly.
                    </p>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Notification Title</label>
                            <input type="text" class="form-control" id="title" name="title" required 
                                   placeholder="e.g., New Feature Announcement">
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Notification Message</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required
                                      placeholder="Enter the notification message here..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">Notification Type</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                {% for type_code, type_name in notification_types %}
                                    <option value="{{ type_code }}" {% if type_code == 'system' %}selected{% endif %}>
                                        {{ type_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'collaborate:notifications' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Notifications
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Send Notification
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Preview Card -->
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Notification Preview</h5>
                </div>
                <div class="card-body">
                    <div id="notification-preview" class="notification-toast show">
                        <div class="notification-content notification-system">
                            <div class="notification-header">
                                <div class="notification-icon"><i class="fas fa-bullhorn"></i></div>
                                <div class="notification-title" id="preview-title">Notification Title</div>
                                <button type="button" class="notification-close" aria-label="Close">&times;</button>
                            </div>
                            <div class="notification-body" id="preview-message">
                                Notification message will appear here.
                            </div>
                            <div class="notification-progress"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // Live preview functionality
    document.addEventListener('DOMContentLoaded', function() {
        const titleInput = document.getElementById('title');
        const messageInput = document.getElementById('message');
        const typeSelect = document.getElementById('notification_type');
        const previewTitle = document.getElementById('preview-title');
        const previewMessage = document.getElementById('preview-message');
        const previewContainer = document.querySelector('.notification-content');
        
        // Update preview when inputs change
        function updatePreview() {
            previewTitle.textContent = titleInput.value || 'Notification Title';
            previewMessage.textContent = messageInput.value || 'Notification message will appear here.';
            
            // Update notification type/color
            previewContainer.className = 'notification-content';
            previewContainer.classList.add('notification-' + typeSelect.value);
            
            // Update icon
            const iconElement = document.querySelector('.notification-icon');
            const iconMap = {
                'info': '<i class="fas fa-info-circle"></i>',
                'success': '<i class="fas fa-check-circle"></i>',
                'warning': '<i class="fas fa-exclamation-triangle"></i>',
                'error': '<i class="fas fa-times-circle"></i>',
                'system': '<i class="fas fa-bullhorn"></i>',
                'invitation': '<i class="fas fa-user-plus"></i>'
            };
            iconElement.innerHTML = iconMap[typeSelect.value] || iconMap['system'];
        }
        
        titleInput.addEventListener('input', updatePreview);
        messageInput.addEventListener('input', updatePreview);
        typeSelect.addEventListener('change', updatePreview);
    });
</script>
{% endblock %}
