from django.urls import path
from . import views, api_views
from . import views_team_analysis

app_name = 'collaborate'


urlpatterns = [
    path('', views.project_list, name='project_list'),
    path('create/', views.create_project, name='create_project'),
    path('project/<str:project_hash>/', views.project_detail, name='project_detail'),
    path('project/<str:project_hash>/apply/', views.apply_project, name='apply_project'),
    path('editor/<str:project_hash>/', views.editor_view, name='editor'),
    path('project/<str:project_hash>/delete/', views.delete_project, name='delete_project'),
    path('project/<str:project_hash>/update-status/', views.update_project_status, name='update_project_status'),
    path('project/<str:project_hash>/match-users/', views.match_users_view, name='match_users'),
    path('project/<str:project_hash>/auto-team/', views.auto_pairing_view, name='auto_team'),
    path('preview/<str:project_hash>/', views.preview_project, name='preview_project'),
    path('project/<str:project_hash>/team-review/', views.finalize_ai_team, name='team_review'),
    path('notifications/', views.notifications, name='notifications'),
    path('notification/read/<int:notification_id>/', views.mark_notification_as_read, name='mark_notification_as_read'),
    path('handle-invite/<int:notification_id>/', views.handle_invite, name='handle_invite'),
    path('send-system-notification/', views.send_system_notification_view, name='send_system_notification'),
    path('test-notification/', views.test_notification_view, name='test_notification'),
    path('project/<str:project_hash>/search-users/', views.search_users, name='search_users'),
    path('project/<str:project_hash>/invite/<int:user_id>/', views.invite_user, name='invite_user'),
    path('project/<str:project_hash>/invite-all-matched/', views.invite_all_matched_users, name='invite_all_matched'),
    path('project/<str:project_hash>/remove-member/<int:user_id>/', views.remove_team_member, name='remove_team_member'),
    path('project/<str:project_hash>/re-pair-team/', views.re_pair_team, name='re_pair_team'),
    path('project/<str:project_hash>/pairing-debug/', views.pairing_debug_view, name='pairing_debug'),
    path('project/<str:project_hash>/chat-history/', views.get_project_chat_history, name='project_chat_history'),
    path('user_profile/<str:username>/', views.user_profile_modal, name='user_profile_modal'),

    # Team analysis views
    path('project/<str:project_hash>/team-balance/', views_team_analysis.team_balance_view, name='team_balance'),
    path('project/<str:project_hash>/timezone-overlap/', views_team_analysis.timezone_overlap_view, name='timezone_overlap'),
    path('project/<str:project_hash>/match-feedback/', views_team_analysis.match_feedback_view, name='match_feedback'),
    path('project/<str:project_hash>/submit-feedback/', views_team_analysis.submit_match_feedback_view, name='submit_match_feedback'),
    path('api/project/<str:project_hash>/feedback-summary/', views_team_analysis.feedback_summary_api, name='feedback_summary_api'),

    # Test views
    path('test-template-tag/', views_team_analysis.test_template_tag_view, name='test_template_tag'),

    # Chatbot URLs
    path('chatbot/<str:project_hash>/message/', views.chatbot_message, name='chatbot_message'),
    path('chatbot/<str:project_hash>/analyze/', views.chatbot_analyze_code, name='chatbot_analyze_code'),
    path('chatbot/<str:project_hash>/clear/', views.chatbot_clear_history, name='chatbot_clear_history'),

    # Marketplace URLs
    path('marketplace/', views.marketplace_list, name='marketplace_list'),
    path('marketplace/<int:post_id>/', views.marketplace_detail, name='marketplace_detail'),
    path('marketplace/publish/<str:project_hash>/', views.publish_to_marketplace, name='publish_to_marketplace'),
    path('marketplace/react/<int:post_id>/', views.marketplace_react, name='marketplace_react'),
    path('marketplace/manage/<str:project_hash>/', views.manage_marketplace_applications, name='manage_marketplace_applications'),

    # Docker Connection Test
    path('test-docker/', views.test_docker_connection, name='test_docker_connection'),

    # Local Storage API URLs (fallback when Docker is not available)
    path('api/', api_views.api_root, name='api_root'),
    path('api/health/', api_views.api_health_check, name='api_health'),
    path('api/files/<str:project_identifier>/', api_views.api_get_files, name='api_get_files'),
    path('api/file/<str:project_identifier>/', api_views.api_get_file, name='api_get_file'),
    path('api/file/<str:project_identifier>/', api_views.api_create_file, name='api_create_file'),
    path('api/file/<str:project_identifier>/', api_views.api_update_file, name='api_update_file'),
    path('api/file/<str:project_identifier>/', api_views.api_delete_file, name='api_delete_file'),
    path('api/file/<str:project_identifier>/rename/', api_views.api_rename_file, name='api_rename_file'),
    path('api/file/<str:project_identifier>/move/', api_views.api_move_file, name='api_move_file'),
]
