# Generated by Django 5.2.1 on 2025-05-31 14:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("learn", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="chapter",
            options={"ordering": ["order", "id"]},
        ),
        migrations.AlterModelOptions(
            name="course",
            options={"ordering": ["name"]},
        ),
        migrations.AlterModelOptions(
            name="lesson",
            options={"ordering": ["order", "id"]},
        ),
        migrations.AlterModelOptions(
            name="lessoncontent",
            options={"ordering": ["order", "id"]},
        ),
        migrations.AddField(
            model_name="chapter",
            name="description",
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name="chapter",
            name="order",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="course",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="course",
            name="difficulty",
            field=models.CharField(
                choices=[
                    ("beginner", "Beginner"),
                    ("intermediate", "Intermediate"),
                    ("advanced", "Advanced"),
                ],
                default="beginner",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="estimated_duration",
            field=models.CharField(default="Self-paced", max_length=50),
        ),
        migrations.AddField(
            model_name="course",
            name="learning_objectives",
            field=models.TextField(
                blank=True, help_text="What students will learn from this course"
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="prerequisites",
            field=models.TextField(
                blank=True,
                help_text="What students should know before taking this course",
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name="lesson",
            name="estimated_time",
            field=models.CharField(default="15 min", max_length=20),
        ),
        migrations.AddField(
            model_name="lesson",
            name="lesson_type",
            field=models.CharField(
                choices=[
                    ("theory", "Theory"),
                    ("practical", "Practical"),
                    ("exercise", "Exercise"),
                    ("project", "Project"),
                ],
                default="theory",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="lesson",
            name="order",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="lessoncontent",
            name="code_language",
            field=models.CharField(
                blank=True,
                help_text="For code examples (e.g., python, javascript)",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="lessoncontent",
            name="content_type",
            field=models.CharField(
                choices=[
                    ("text", "Text Content"),
                    ("code_example", "Code Example"),
                    ("tip", "Pro Tip"),
                    ("warning", "Warning"),
                    ("exercise", "Exercise"),
                    ("summary", "Summary"),
                ],
                default="text",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="lessoncontent",
            name="order",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name="lessoncontent",
            name="title",
            field=models.CharField(blank=True, max_length=200),
        ),
    ]
