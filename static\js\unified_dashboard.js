// Unified Dashboard JavaScript

// Global variables
let currentTab = 'learning';
let toolStates = {
    'ai-assistant': false,
    'code-analyzer': false
};

function initializeUnifiedDashboard() {
    console.log('Unified Dashboard initialized');
    initializeParticles();
    animateCards();
    
    // Set initial tab
    switchTab('learning');
}

function switchTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabName + '-tab');
    if (selectedTab) {
        selectedTab.classList.add('active');
    }
    
    // Add active class to selected tab button
    const selectedButton = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
    
    currentTab = tabName;
    
    // Initialize tab-specific features
    if (tabName === 'ai-tools') {
        initializeAITools();
    }
}

function toggleTool(toolId) {
    const toolContent = document.getElementById(toolId);
    const toggleButton = document.querySelector(`[onclick="toggleTool('${toolId}')"] span`);
    
    if (toolStates[toolId]) {
        // Hide tool
        toolContent.style.display = 'none';
        toolStates[toolId] = false;
        
        if (toolId === 'ai-assistant') {
            toggleButton.textContent = 'Show Assistant';
        } else if (toolId === 'code-analyzer') {
            toggleButton.textContent = 'Show Analyzer';
        }
    } else {
        // Show tool
        toolContent.style.display = 'block';
        toolStates[toolId] = true;
        
        if (toolId === 'ai-assistant') {
            toggleButton.textContent = 'Hide Assistant';
            // Focus on chat input
            setTimeout(() => {
                const chatInput = document.getElementById('chat-input');
                if (chatInput) chatInput.focus();
            }, 100);
        } else if (toolId === 'code-analyzer') {
            toggleButton.textContent = 'Hide Analyzer';
            // Focus on code input
            setTimeout(() => {
                const codeInput = document.getElementById('code-input');
                if (codeInput) codeInput.focus();
            }, 100);
        }
    }
}

function initializeAITools() {
    // Set up keyboard shortcuts for AI tools
    document.addEventListener('keydown', function(event) {
        // Ctrl/Cmd + Shift + A for AI Assistant
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
            event.preventDefault();
            if (currentTab === 'ai-tools') {
                toggleTool('ai-assistant');
            } else {
                switchTab('ai-tools');
                setTimeout(() => toggleTool('ai-assistant'), 100);
            }
        }
        
        // Ctrl/Cmd + Shift + C for Code Analyzer
        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
            event.preventDefault();
            if (currentTab === 'ai-tools') {
                toggleTool('code-analyzer');
            } else {
                switchTab('ai-tools');
                setTimeout(() => toggleTool('code-analyzer'), 100);
            }
        }
    });
}

function animateCards() {
    // Animate analytics cards
    const analyticsCards = document.querySelectorAll('.analytics-card');
    analyticsCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
    
    // Animate recommendation cards
    const recCards = document.querySelectorAll('.recommendation-card');
    recCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateX(-20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateX(0)';
            }, 100);
        }, (index + analyticsCards.length) * 100);
    });
}

// Enhanced AI Chat Functions (reuse from ai_learning.js but with dashboard context)
function sendMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    
    // Clear input
    chatInput.value = '';
    
    // Show typing indicator
    addTypingIndicator();
    
    // Send to AI
    fetch('/learn/ai/chat/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            message: message,
            type: 'question'
        })
    })
    .then(response => response.json())
    .then(data => {
        removeTypingIndicator();
        if (data.success) {
            addMessageToChat(data.response, 'ai');
        } else {
            addMessageToChat('Sorry, I encountered an error. Please try again.', 'ai');
        }
    })
    .catch(error => {
        removeTypingIndicator();
        addMessageToChat('Sorry, I\'m having trouble connecting. Please try again.', 'ai');
        console.error('AI Chat error:', error);
    });
}

function addMessageToChat(message, sender) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = message;
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = 'Just now';
    
    messageDiv.appendChild(contentDiv);
    if (sender === 'user') {
        messageDiv.appendChild(timeDiv);
    }
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addTypingIndicator() {
    const chatMessages = document.getElementById('chat-messages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = '🤖 Thinking...';
    
    typingDiv.appendChild(contentDiv);
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Enhanced Code Analysis Functions
function analyzeCode() {
    const codeInput = document.getElementById('code-input');
    const languageSelect = document.getElementById('code-language');
    const analysisTypeSelect = document.getElementById('analysis-type');
    const resultsDiv = document.getElementById('analysis-results');
    
    const code = codeInput.value.trim();
    if (!code) {
        alert('Please enter some code to analyze');
        return;
    }
    
    // Show loading
    resultsDiv.style.display = 'block';
    document.getElementById('ai-feedback').innerHTML = '🔍 Analyzing your code...';
    
    fetch('/learn/ai/analyze-code/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            code: code,
            language: languageSelect.value,
            analysis_type: analysisTypeSelect.value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResults(data.analysis);
        } else {
            document.getElementById('ai-feedback').innerHTML = 'Error analyzing code: ' + data.error;
        }
    })
    .catch(error => {
        document.getElementById('ai-feedback').innerHTML = 'Error connecting to analysis service.';
        console.error('Code analysis error:', error);
    });
}

function displayAnalysisResults(analysis) {
    // Update score
    const scoreElement = document.getElementById('code-score');
    const score = analysis.overall_score;
    scoreElement.textContent = score;
    scoreElement.style.setProperty('--score-angle', (score * 3.6) + 'deg');
    
    // Update feedback
    document.getElementById('ai-feedback').innerHTML = analysis.feedback;
    
    // Update suggestions
    const suggestionsList = document.getElementById('suggestions-list');
    suggestionsList.innerHTML = '';
    
    const allSuggestions = [
        ...analysis.syntax_issues,
        ...analysis.best_practice_suggestions,
        ...analysis.performance_tips,
        ...analysis.security_concerns,
        ...analysis.style_improvements
    ];
    
    if (allSuggestions.length > 0) {
        const suggestionsTitle = document.createElement('h4');
        suggestionsTitle.textContent = 'Suggestions for Improvement:';
        suggestionsTitle.style.color = '#C0ff6b';
        suggestionsList.appendChild(suggestionsTitle);
        
        allSuggestions.forEach(suggestion => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'suggestion-item';
            suggestionDiv.innerHTML = `
                <div class="suggestion-type">${suggestion.type || 'General'}</div>
                <div class="suggestion-message">${suggestion.message || suggestion}</div>
                ${suggestion.suggestion ? `<div class="suggestion-fix">${suggestion.suggestion}</div>` : ''}
            `;
            suggestionsList.appendChild(suggestionDiv);
        });
    }
}

// Utility Functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize particles for dashboard
function initializeParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 80,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": ["#C0ff6b", "#a0e066", "#80c055"]
                },
                "shape": {
                    "type": ["circle", "triangle"],
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 0.5,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 5,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 2,
                        "size_min": 0.5,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#C0ff6b",
                    "opacity": 0.4,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 2,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": true,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "bubble"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "bubble": {
                        "distance": 200,
                        "size": 8,
                        "duration": 2,
                        "opacity": 0.8,
                        "speed": 3
                    },
                    "push": {
                        "particles_nb": 4
                    }
                }
            },
            "retina_detect": true
        });
    }
}
