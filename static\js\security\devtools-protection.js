/**
 * DevTools Protection System for ForgeX
 * Blocks developer tools access for non-admin users
 */

(function() {
    'use strict';
    
    // Check if user is admin (from security config)
    const isAdmin = window.FORGEX_CONFIG && window.FORGEX_CONFIG.debug_mode;
    
    if (isAdmin) {
        console.log('🔓 DevTools protection disabled for admin user');
        return;
    }
    
    console.log('🛡️ DevTools protection active');
    
    // DevTools detection variables
    let devtools = {
        open: false,
        orientation: null
    };
    
    // Console protection
    const disableConsole = () => {
        // Override console methods
        const noop = () => {};
        const consoleProps = ['log', 'debug', 'info', 'warn', 'error', 'assert', 'dir', 'dirxml', 
                             'group', 'groupEnd', 'time', 'timeEnd', 'count', 'trace', 'profile', 'profileEnd'];
        
        consoleProps.forEach(prop => {
            try {
                window.console[prop] = noop;
            } catch(e) {}
        });
        
        // Clear console
        try {
            console.clear();
        } catch(e) {}
    };
    
    // DevTools detection method 1: Console timing
    const detectDevToolsByTiming = () => {
        let start = performance.now();
        debugger;
        let end = performance.now();
        
        if (end - start > 100) {
            return true;
        }
        return false;
    };
    
    // DevTools detection method 2: Window size
    const detectDevToolsBySize = () => {
        const threshold = 160;
        
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
            return true;
        }
        return false;
    };
    
    // DevTools detection method 3: Console object
    const detectDevToolsByConsole = () => {
        let devtools = false;
        const element = new Image();
        
        Object.defineProperty(element, 'id', {
            get: function() {
                devtools = true;
                return 'devtools-detected';
            }
        });
        
        console.log(element);
        return devtools;
    };
    
    // Block right-click context menu
    const blockContextMenu = () => {
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            showWarning('Right-click is disabled for security reasons');
            return false;
        });
    };
    
    // Block common DevTools shortcuts
    const blockKeyboardShortcuts = () => {
        document.addEventListener('keydown', (e) => {
            // F12
            if (e.keyCode === 123) {
                e.preventDefault();
                showWarning('Developer tools are disabled');
                return false;
            }
            
            // Ctrl+Shift+I (DevTools)
            if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
                e.preventDefault();
                showWarning('Developer tools are disabled');
                return false;
            }
            
            // Ctrl+Shift+J (Console)
            if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
                e.preventDefault();
                showWarning('Console access is disabled');
                return false;
            }
            
            // Ctrl+U (View Source)
            if (e.ctrlKey && e.keyCode === 85) {
                e.preventDefault();
                showWarning('View source is disabled');
                return false;
            }
            
            // Ctrl+Shift+C (Element Inspector)
            if (e.ctrlKey && e.shiftKey && e.keyCode === 67) {
                e.preventDefault();
                showWarning('Element inspector is disabled');
                return false;
            }
            
            // Ctrl+Shift+K (Console in Firefox)
            if (e.ctrlKey && e.shiftKey && e.keyCode === 75) {
                e.preventDefault();
                showWarning('Console access is disabled');
                return false;
            }
        });
    };
    
    // Show warning message
    const showWarning = (message) => {
        // Create warning overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: #ff4444;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999999;
            font-family: Arial, sans-serif;
            font-size: 24px;
            text-align: center;
        `;
        
        overlay.innerHTML = `
            <div style="background: #000; padding: 40px; border-radius: 10px; border: 2px solid #ff4444;">
                <h2 style="color: #ff4444; margin-bottom: 20px;">🚫 Access Denied</h2>
                <p style="margin-bottom: 20px;">${message}</p>
                <p style="font-size: 16px; color: #888;">This action has been logged for security purposes.</p>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: #ff4444;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin-top: 20px;
                ">Close</button>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (overlay.parentElement) {
                overlay.remove();
            }
        }, 3000);
    };
    
    // Redirect if DevTools detected
    const handleDevToolsDetection = () => {
        if (!devtools.open) {
            devtools.open = true;
            
            // Log security event
            if (window.fetch) {
                fetch('/accounts/api/security/log/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrf-token]')?.content || ''
                    },
                    body: JSON.stringify({
                        event_type: 'devtools_access_attempt',
                        details: 'User attempted to access developer tools',
                        user_agent: navigator.userAgent,
                        timestamp: new Date().toISOString()
                    })
                }).catch(() => {});
            }
            
            // Show warning and blur page
            document.body.style.filter = 'blur(10px)';
            showWarning('Developer tools detected! Access denied for security reasons.');
            
            // Optional: Redirect to warning page
            setTimeout(() => {
                window.location.href = '/security/access-denied/';
            }, 5000);
        }
    };
    
    // Continuous DevTools monitoring
    const monitorDevTools = () => {
        // Method 1: Timing-based detection
        if (detectDevToolsByTiming()) {
            handleDevToolsDetection();
            return;
        }
        
        // Method 2: Size-based detection
        if (detectDevToolsBySize()) {
            handleDevToolsDetection();
            return;
        }
        
        // Method 3: Console-based detection
        if (detectDevToolsByConsole()) {
            handleDevToolsDetection();
            return;
        }
    };
    
    // Initialize protection
    const initProtection = () => {
        // Disable console
        disableConsole();
        
        // Block context menu
        blockContextMenu();
        
        // Block keyboard shortcuts
        blockKeyboardShortcuts();
        
        // Start monitoring
        setInterval(monitorDevTools, 1000);
        
        // Monitor window resize (DevTools opening/closing)
        window.addEventListener('resize', () => {
            setTimeout(monitorDevTools, 100);
        });
        
        console.log('🛡️ Security protection initialized');
    };
    
    // Start protection when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initProtection);
    } else {
        initProtection();
    }
    
    // Prevent script tampering
    Object.freeze(window.console);
    
})();
