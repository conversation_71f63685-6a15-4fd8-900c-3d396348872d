{% extends 'base.html' %}
{% load static %}

{% block title %}Ticket #{{ ticket.ticket_id }} - Support{% endblock %}

{% block content %}
<style>
/* Support Ticket Detail Styles */
.support-container {
  padding: 20px 0;
  min-height: 80vh;
}

.ticket-header {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.ticket-title {
  color: #C0ff6b;
  font-size: 1.8rem;
  margin-bottom: 15px;
}

.ticket-meta {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.meta-label {
  color: #C0ff6b;
  font-weight: bold;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.meta-value {
  color: #ffffff;
  font-size: 1rem;
}

.status-badge {
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  display: inline-block;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #FFC107; border: 1px solid #FFC107; }
.status-in_progress { background: rgba(33, 150, 243, 0.2); color: #2196F3; border: 1px solid #2196F3; }
.status-waiting_user { background: rgba(156, 39, 176, 0.2); color: #9C27B0; border: 1px solid #9C27B0; }
.status-resolved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; border: 1px solid #4CAF50; }
.status-closed { background: rgba(158, 158, 158, 0.2); color: #9E9E9E; border: 1px solid #9E9E9E; }

.priority-low { color: #4CAF50; }
.priority-medium { color: #FF9800; }
.priority-high { color: #F44336; }
.priority-urgent { color: #E91E63; font-weight: bold; }

/* Chat Container */
.chat-container {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 0;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
  height: 600px;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 20px 30px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
  background: rgba(192, 255, 107, 0.1);
  border-radius: 20px 20px 0 0;
}

.chat-header h3 {
  color: #C0ff6b;
  margin: 0;
  font-size: 1.3rem;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 400px;
}

.message {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 12px;
  position: relative;
}

.message-user {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  margin-left: 50px;
}

.message-staff {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  margin-right: 50px;
}

.message-internal {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  margin-right: 50px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-sender {
  font-weight: bold;
  color: #C0ff6b;
}

.message-time {
  font-size: 0.8rem;
  color: #999;
}

.message-content {
  color: #ffffff;
  line-height: 1.5;
  white-space: pre-wrap;
}

.internal-badge {
  background: rgba(255, 193, 7, 0.3);
  color: #FFC107;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  margin-left: 10px;
}

/* Chat Input */
.chat-input {
  padding: 20px;
  border-top: 1px solid rgba(192, 255, 107, 0.2);
  background: rgba(192, 255, 107, 0.05);
  border-radius: 0 0 20px 20px;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.message-input {
  flex: 1;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 12px;
  color: #ffffff;
  resize: vertical;
  min-height: 40px;
  max-height: 120px;
}

.message-input:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
}

.send-btn {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Typing Indicator */
.typing-indicator {
  padding: 10px 20px;
  font-style: italic;
  color: #999;
  font-size: 0.9rem;
}

.typing-dots {
  display: inline-block;
  animation: typing 1.5s infinite;
}

@keyframes typing {
  0%, 60%, 100% { opacity: 0; }
  30% { opacity: 1; }
}

/* Connection Status */
.connection-status {
  padding: 10px 20px;
  text-align: center;
  font-size: 0.9rem;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
}

.status-connected {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.status-disconnected {
  color: #F44336;
  background: rgba(244, 67, 54, 0.1);
}

.status-connecting {
  color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
}

/* Admin Controls */
.admin-controls {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
}

.admin-controls h4 {
  color: #FFC107;
  margin: 0 0 10px 0;
  font-size: 1rem;
}

.control-group {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.control-group select {
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 6px;
  padding: 8px;
  color: #ffffff;
}

.internal-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #FFC107;
  font-size: 0.9rem;
}

/* Chat Input Styling */
.input-group {
  display: flex;
  width: 100%;
}

.message-input {
  width: 100%;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  padding: 15px 20px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
}

.message-input:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 0 3px rgba(192, 255, 107, 0.2);
  background: rgba(28, 28, 28, 0.95);
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .support-container {
    padding: 10px;
  }

  .ticket-header, .chat-container {
    margin: 0 10px 20px 10px;
    padding: 20px;
  }

  .ticket-meta {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .chat-container {
    height: 500px;
  }

  .message-user, .message-staff, .message-internal {
    margin-left: 0;
    margin-right: 0;
  }

  .input-group {
    flex-direction: column;
    align-items: stretch;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>

<div class="support-container">
  <div class="tile-wrap">
    <!-- Ticket Header -->
    <div class="ticket-header">
      <h1 class="ticket-title">Ticket #{{ ticket.ticket_id }}: {{ ticket.subject }}</h1>

      <div class="ticket-meta">
        <div class="meta-item">
          <div class="meta-label">Status</div>
          <div class="meta-value">
            <span class="status-badge status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
          </div>
        </div>

        <div class="meta-item">
          <div class="meta-label">Priority</div>
          <div class="meta-value priority-{{ ticket.priority }}">{{ ticket.get_priority_display }}</div>
        </div>

        <div class="meta-item">
          <div class="meta-label">Category</div>
          <div class="meta-value">{{ ticket.category.name|default:"General" }}</div>
        </div>

        <div class="meta-item">
          <div class="meta-label">Created</div>
          <div class="meta-value">{{ ticket.created_at|date:"M d, Y H:i" }}</div>
        </div>

        {% if ticket.assigned_to %}
        <div class="meta-item">
          <div class="meta-label">Assigned to</div>
          <div class="meta-value">{{ ticket.assigned_to.username }}</div>
        </div>
        {% endif %}

        {% if ticket.resolved_at %}
        <div class="meta-item">
          <div class="meta-label">Resolved</div>
          <div class="meta-value">{{ ticket.resolved_at|date:"M d, Y H:i" }}</div>
        </div>
        {% endif %}
      </div>

      <div class="meta-item">
        <div class="meta-label">Description</div>
        <div class="meta-value">{{ ticket.description|linebreaks }}</div>
      </div>
    </div>

    <!-- Real-time Chat -->
    <div class="chat-container">
      <div class="chat-header">
        <h3>💬 Support Chat</h3>
        <div id="connection-status" class="connection-status status-connecting">
          Connecting to chat...
        </div>
      </div>

      <div id="chat-messages" class="chat-messages">
        <!-- Load existing messages -->
        {% for message in messages %}
        <div class="message {% if message.sender.is_staff or message.sender.is_superuser %}{% if message.is_internal %}message-internal{% else %}message-staff{% endif %}{% else %}message-user{% endif %}">
          <div class="message-header">
            <span class="message-sender">
              {{ message.sender.username }}
              {% if message.is_internal and can_see_internal %}
                <span class="internal-badge">Internal</span>
              {% endif %}
            </span>
            <span class="message-time">{{ message.created_at|date:"M d, H:i" }}</span>
          </div>
          <div class="message-content">{{ message.message }}</div>
        </div>
        {% endfor %}
      </div>

      <div id="typing-indicator" class="typing-indicator" style="display: none;">
        <span id="typing-user"></span> is typing<span class="typing-dots">...</span>
      </div>

      <div class="chat-input">
        {% csrf_token %}
        {% if is_staff %}
        <div class="admin-controls">
          <h4>Staff Controls</h4>
          <div class="control-group">
            <label>
              <select id="status-update">
                <option value="">Update Status</option>
                <option value="open">Open</option>
                <option value="in_progress">In Progress</option>
                <option value="waiting_user">Waiting for User</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </label>
            <label class="internal-checkbox">
              <input type="checkbox" id="internal-message"> Internal Note
            </label>
          </div>
        </div>
        {% endif %}

        <div class="input-group">
          <input
            type="text"
            id="message-input"
            class="message-input"
            placeholder="Type your message here and press Enter to send..."
            autocomplete="off"
          />
        </div>
      </div>
    </div>

    <!-- Back to Tickets -->
    <div style="text-align: center; margin-top: 30px;">
      <a href="{% if is_staff %}{% url 'accounts:support_admin_dashboard' %}{% else %}{% url 'accounts:my_support_tickets' %}{% endif %}"
         class="back-btn" style="text-decoration: none; display: inline-block; background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%); color: #1a1a1a; border: none; border-radius: 8px; padding: 12px 24px; font-weight: bold; transition: all 0.3s ease;">
        ← Back to {% if is_staff %}Admin Dashboard{% else %}My Tickets{% endif %}
      </a>
    </div>
  </div>
</div>

<!-- WebSocket Chat JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ticketId = '{{ ticket.ticket_id }}';
    const currentUser = '{{ user.username }}';
    const isStaff = {{ is_staff|yesno:"true,false" }};

    // WebSocket connection
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = window.location.hostname;
    const wsPort = '8001'; // ASGI server port

    // Try multiple WebSocket URLs
    const wsUrls = [
        `${protocol}//${wsHost}:${wsPort}/ws/support/chat/${ticketId}/`,
        `${protocol}//localhost:${wsPort}/ws/support/chat/${ticketId}/`,
        `${protocol}//127.0.0.1:${wsPort}/ws/support/chat/${ticketId}/`
    ];

    let wsUrl = wsUrls[0]; // Start with first URL
    console.log('WebSocket URLs to try:', wsUrls);
    let socket = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    // DOM elements
    const messagesContainer = document.getElementById('chat-messages');
    const messageInput = document.getElementById('message-input');
    const connectionStatus = document.getElementById('connection-status');
    const typingIndicator = document.getElementById('typing-indicator');
    const typingUser = document.getElementById('typing-user');
    const statusUpdate = document.getElementById('status-update');
    const internalCheckbox = document.getElementById('internal-message');

    let typingTimer = null;
    let isTyping = false;

    function connectWebSocket() {
        console.log('Attempting to connect to WebSocket:', wsUrl);
        socket = new WebSocket(wsUrl);

        socket.onopen = function(e) {
            console.log('Connected to support chat');
            console.log('WebSocket readyState after open:', socket.readyState);
            connectionStatus.textContent = 'Connected to live chat';
            connectionStatus.className = 'connection-status status-connected';
            reconnectAttempts = 0;
        };

        socket.onmessage = function(e) {
            console.log('WebSocket message received:', e.data);
            try {
                const data = JSON.parse(e.data);
                handleWebSocketMessage(data);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        socket.onclose = function(e) {
            console.log('Disconnected from support chat. Code:', e.code, 'Reason:', e.reason);
            connectionStatus.textContent = 'Disconnected from chat';
            connectionStatus.className = 'connection-status status-disconnected';

            // Attempt to reconnect
            if (reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;
                setTimeout(() => {
                    connectionStatus.textContent = `Reconnecting... (${reconnectAttempts}/${maxReconnectAttempts})`;
                    connectionStatus.className = 'connection-status status-connecting';
                    connectWebSocket();
                }, 2000 * reconnectAttempts);
            } else {
                connectionStatus.textContent = 'WebSocket unavailable. Using fallback mode.';
                connectionStatus.className = 'connection-status status-disconnected';
                // Enable fallback mode
                enableFallbackMode();
            }
        };

        socket.onerror = function(e) {
            console.error('WebSocket error:', e);
            connectionStatus.textContent = 'Connection error';
            connectionStatus.className = 'connection-status status-disconnected';
        };
    }

    function handleWebSocketMessage(data) {
        console.log('Handling WebSocket message:', data);
        switch(data.type) {
            case 'connection_established':
                console.log('Connection established:', data.message);
                connectionStatus.textContent = 'Connected to live chat';
                connectionStatus.className = 'connection-status status-connected';
                break;

            case 'chat_message':
                console.log('Received chat_message:', data);
                addMessageToChat(data);
                break;

            case 'typing_indicator':
                handleTypingIndicator(data);
                break;

            case 'status_update':
                handleStatusUpdate(data);
                break;

            case 'error':
                console.error('Chat error:', data.message);
                connectionStatus.textContent = 'Error: ' + data.message;
                connectionStatus.className = 'connection-status status-disconnected';
                alert('Chat Error: ' + data.message);
                break;

            default:
                console.log('Unknown message type:', data.type);
        }
    }

    function addMessageToChat(data) {
        console.log('Adding message to chat:', data);

        const messageDiv = document.createElement('div');
        const messageClass = data.is_staff ?
            (data.is_internal ? 'message-internal' : 'message-staff') :
            'message-user';

        messageDiv.className = `message ${messageClass}`;

        const internalBadge = data.is_internal && isStaff ?
            '<span class="internal-badge">Internal</span>' : '';

        // Format timestamp
        const timestamp = new Date(data.timestamp);
        const timeString = timestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

        messageDiv.innerHTML = `
            <div class="message-header">
                <span class="message-sender">
                    ${data.username}${internalBadge}
                </span>
                <span class="message-time">${timeString}</span>
            </div>
            <div class="message-content">${data.message}</div>
        `;

        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        console.log('Message added to chat successfully');
    }

    function handleTypingIndicator(data) {
        if (data.username === currentUser) return;

        if (data.is_typing) {
            typingUser.textContent = data.username;
            typingIndicator.style.display = 'block';
        } else {
            typingIndicator.style.display = 'none';
        }
    }

    function handleStatusUpdate(data) {
        // Update status badge in the header
        const statusBadge = document.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.className = `status-badge status-${data.status}`;
            statusBadge.textContent = data.status.replace('_', ' ').toUpperCase();
        }

        // Add system message
        const systemMessage = document.createElement('div');
        systemMessage.className = 'message message-staff';
        systemMessage.innerHTML = `
            <div class="message-header">
                <span class="message-sender">System</span>
                <span class="message-time">${new Date().toLocaleString()}</span>
            </div>
            <div class="message-content">Status updated to: ${data.status.replace('_', ' ')} by ${data.updated_by}</div>
        `;
        messagesContainer.appendChild(systemMessage);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message) return;

        console.log('sendMessage called with:', message);
        console.log('WebSocket state:', socket ? socket.readyState : 'null');
        console.log('WebSocket.OPEN constant:', WebSocket.OPEN);

        // Check if WebSocket is available and connected
        if (socket && socket.readyState === WebSocket.OPEN) {
            const isInternal = internalCheckbox && internalCheckbox.checked;

            const messageData = {
                type: 'chat_message',
                message: message,
                is_internal: isInternal
            };

            console.log('Sending message via WebSocket:', messageData);
            console.log('WebSocket send result:', socket.send(JSON.stringify(messageData)));

            // Clear input immediately for better UX
            messageInput.value = '';
            if (internalCheckbox) internalCheckbox.checked = false;
            sendTypingIndicator(false);
        } else {
            // Fallback: Use traditional form submission
            console.log('WebSocket not available, using form fallback');
            console.log('Socket state details:', {
                socket: !!socket,
                readyState: socket ? socket.readyState : 'N/A',
                expectedState: WebSocket.OPEN
            });
            sendMessageViaForm(message);
        }
    }

    function sendMessageViaForm(message) {
        // Create a hidden form to submit the message
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        // CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrfmiddlewaretoken';
        csrfInput.value = csrfToken;
        form.appendChild(csrfInput);

        // Message content
        const messageInput = document.createElement('input');
        messageInput.type = 'hidden';
        messageInput.name = 'message';
        messageInput.value = message;
        form.appendChild(messageInput);

        // Send message flag
        const sendFlag = document.createElement('input');
        sendFlag.type = 'hidden';
        sendFlag.name = 'send_message';
        sendFlag.value = '1';
        form.appendChild(sendFlag);

        // Internal flag if checked
        if (internalCheckbox && internalCheckbox.checked) {
            const internalInput = document.createElement('input');
            internalInput.type = 'hidden';
            internalInput.name = 'is_internal';
            internalInput.value = 'on';
            form.appendChild(internalInput);
        }

        document.body.appendChild(form);
        form.submit();
    }

    function enableFallbackMode() {
        // Show a message that we're in fallback mode
        const fallbackMessage = document.createElement('div');
        fallbackMessage.style.cssText = `
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            color: #FFC107;
            font-size: 0.9rem;
            text-align: center;
        `;
        fallbackMessage.innerHTML = `
            <i class="fas fa-info-circle"></i>
            Real-time chat unavailable. Messages will be sent via form submission.
        `;

        const chatInput = document.querySelector('.chat-input');
        chatInput.insertBefore(fallbackMessage, chatInput.firstChild);

        // Disable typing indicators and real-time features
        messageInput.removeEventListener('input', handleTypingInput);
    }

    function sendTypingIndicator(typing) {
        if (!socket || socket.readyState !== WebSocket.OPEN) return;

        socket.send(JSON.stringify({
            type: 'typing_indicator',
            is_typing: typing
        }));
    }

    // Event listeners
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            sendMessage();
        }
    });

    function handleTypingInput() {
        if (!isTyping) {
            isTyping = true;
            sendTypingIndicator(true);
        }

        clearTimeout(typingTimer);
        typingTimer = setTimeout(() => {
            isTyping = false;
            sendTypingIndicator(false);
        }, 1000);
    }

    messageInput.addEventListener('input', handleTypingInput);

    if (statusUpdate) {
        statusUpdate.addEventListener('change', function() {
            const newStatus = this.value;
            if (!newStatus || !socket || socket.readyState !== WebSocket.OPEN) return;

            socket.send(JSON.stringify({
                type: 'status_update',
                status: newStatus
            }));

            this.value = '';
        });
    }

    // Initialize connection
    connectWebSocket();

    // Scroll to bottom on load
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
});
</script>
{% endblock %}
