{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Project Marketplace - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Welcome Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🚀 Project Marketplace</h1>
      <p class="welcome-subtitle">Discover amazing projects and find your next collaboration opportunity</p>
    </div>

    <!-- Search Section -->
    <div class="dashboard-section">
      <h2>🔍 Find Projects</h2>
      <form method="GET" style="display: flex; gap: 1rem; margin-bottom: 1rem;">
        <input type="text" name="search" class="form-control"
               placeholder="Search projects by title, description, or tags..."
               value="{{ search_query }}" style="flex: 1;">
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-search"></i> Search
        </button>
      </form>
      {% if search_query or tag_filter %}
        <div style="margin-top: 1rem;">
          <small style="color: rgba(255, 255, 255, 0.7);">
            {% if search_query %}Searching for: "{{ search_query }}"{% endif %}
            {% if tag_filter %}Tag: "{{ tag_filter }}"{% endif %}
            <a href="{% url 'collaborate:marketplace_list' %}" class="btn btn-outline btn-sm" style="margin-left: 1rem;">Clear filters</a>
          </small>
        </div>
      {% endif %}
    </div>

    <!-- Marketplace Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-content">
          <h3>{{ posts|length }}</h3>
          <p>Projects Available</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">👥</div>
        <div class="stat-content">
          <h3>{{ posts|length }}</h3>
          <p>Active Collaborations</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🏷️</div>
        <div class="stat-content">
          <h3>{{ popular_tags|length }}</h3>
          <p>Popular Tags</p>
        </div>
      </div>
    </div>

    <!-- Projects Grid -->
    {% if posts %}
      <div class="dashboard-section">
        <h2>🌟 Featured Projects</h2>
        <div class="recent-projects-grid">
          {% for post in posts %}
            <div class="project-card-mini {% if post.is_featured %}featured-project{% endif %}">
              {% if post.is_featured %}
                <div class="featured-badge">⭐ Featured</div>
              {% endif %}

              {% if post.featured_image %}
                <div class="project-image-container">
                  <img src="{{ post.featured_image.url }}" alt="{{ post.project.title }}" class="project-image">
                </div>
              {% endif %}

              <div class="project-header">
                <h4>{{ post.project.title }}</h4>
                <span class="project-status">
                  <span class="badge badge-success">Open</span>
                </span>
              </div>

              <p class="project-description">{{ post.showcase_description|truncatewords:20 }}</p>

              <div class="project-owner">
                <i class="fas fa-user"></i> by {{ post.project.owner.username }}
              </div>

              {% if post.project.required_skills.all %}
                <div class="project-skills">
                  {% for skill in post.project.required_skills.all|slice:":3" %}
                    <span class="skill-tag">{{ skill.name }}</span>
                  {% endfor %}
                  {% if post.project.required_skills.count > 3 %}
                    <span class="skill-tag">+{{ post.project.required_skills.count|add:"-3" }}</span>
                  {% endif %}
                </div>
              {% endif %}

              {% if post.tags %}
                <div class="project-tags">
                  {% for tag in post.tags|split:","|slice:":3" %}
                    <span class="tag-badge">{{ tag|trim }}</span>
                  {% endfor %}
                </div>
              {% endif %}

              <div class="project-meta">
                <div class="project-stats-mini">
                  <span><i class="fas fa-eye"></i> {{ post.view_count }}</span>
                  <span><i class="fas fa-heart"></i> {{ post.reaction_count }}</span>
                  <span><i class="fas fa-comments"></i> {{ post.comment_count }}</span>
                </div>
                <span class="project-date">{{ post.published_at|date:"M d, Y" }}</span>
              </div>

              <div class="project-actions">
                <a href="{% url 'collaborate:marketplace_detail' post.id %}" class="btn btn-primary btn-sm">View Project</a>
              </div>
            </div>
          {% endfor %}
        </div>
      </div>
    {% else %}
      <div class="dashboard-section">
        <div class="no-projects-message">
          <div class="no-projects-icon">🚀</div>
          <h3>No Projects Found</h3>
          <p>{% if search_query or tag_filter %}Try adjusting your search criteria{% else %}Be the first to publish a project to the marketplace!{% endif %}</p>
          {% if not search_query and not tag_filter %}
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary">Create Your First Project</a>
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- Popular Tags Section -->
    {% if popular_tags %}
      <div class="dashboard-section">
        <h2>🏷️ Popular Tags</h2>
        <div class="category-leaders">
          <div class="tag-cloud">
            {% for tag, count in popular_tags %}
              <a href="?tag={{ tag }}" class="tag-item" style="font-size: {% widthratio count 10 1.5 %}rem;">
                {{ tag }} <span class="tag-count">({{ count }})</span>
              </a>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Getting Started -->
    <div class="dashboard-section">
      <h2>Ready to Showcase Your Project?</h2>
      <div class="getting-started">
        <div class="step-item">
          <div class="step-number">1</div>
          <div class="step-content">
            <h4>Create Your Project</h4>
            <p>Start by creating a project with clear goals and requirements</p>
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary btn-sm">Create Project</a>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">2</div>
          <div class="step-content">
            <h4>Publish to Marketplace</h4>
            <p>Showcase your project to attract talented collaborators</p>
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary btn-sm">Publish Project</a>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">3</div>
          <div class="step-content">
            <h4>Build Your Team</h4>
            <p>Review applications and build an amazing team</p>
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary btn-sm">Manage Team</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .step-item, .project-card-mini, .stat-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds (similar to dashboard)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
  });
</script>

<style>
/* Override collaborate.css with dashboard styling */
:root {
  --color-border: #C0ff6b;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent !important;
  padding: 50px 0 !important;
  min-height: 100vh !important;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Dashboard welcome section */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
  background: linear-gradient(135deg, var(--color-border), #a0e066) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

.welcome-subtitle {
  font-size: 1.2rem !important;
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard stats */
.dashboard-stats {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 40px !important;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 25px !important;
  text-align: center !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
}

.stat-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.stat-icon {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
}

.stat-content h3 {
  font-size: 2rem !important;
  color: var(--color-border) !important;
  margin-bottom: 5px !important;
}

.stat-content p {
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard sections */
.dashboard-section {
  margin-bottom: 40px !important;
}

.dashboard-section h2 {
  color: #ffffff !important;
  margin-bottom: 25px !important;
  text-align: center !important;
  font-size: 1.8rem !important;
}

/* Quick actions */
.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 15px !important;
  padding: 30px !important;
  text-align: center !important;
  text-decoration: none !important;
  color: inherit !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.4s ease !important;
  display: block !important;
}

.action-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
  border-color: rgba(192, 255, 107, 0.5) !important;
  text-decoration: none !important;
  color: inherit !important;
}

.action-icon {
  font-size: 3rem !important;
  margin-bottom: 20px !important;
}

.action-card h3 {
  color: var(--color-border) !important;
  margin-bottom: 15px !important;
  font-size: 1.3rem !important;
}

.action-card p {
  color: #e0e0e0 !important;
  line-height: 1.5 !important;
  margin: 0 !important;
}

/* Recent projects grid */
.recent-projects-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 20px !important;
  margin-bottom: 25px !important;
}

.project-card-mini {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
}

.project-card-mini:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.project-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
  margin-bottom: 15px !important;
}

.project-header h4 {
  color: var(--color-border) !important;
  margin: 0 !important;
  font-size: 1.1rem !important;
  flex: 1 !important;
  margin-right: 10px !important;
}

.project-description {
  color: #e0e0e0 !important;
  line-height: 1.5 !important;
  margin-bottom: 15px !important;
  font-size: 0.9rem !important;
}

.project-meta {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 15px !important;
  font-size: 0.8rem !important;
  color: #b0b0b0 !important;
}

.project-actions {
  text-align: center !important;
}

/* Getting started */
.getting-started {
  display: flex !important;
  flex-direction: column !important;
  gap: 20px !important;
}

.step-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: 20px !important;
  padding: 20px !important;
  background-color: rgba(40, 40, 40, 0.3) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
  transition: all 0.5s ease !important;
}

.step-number {
  background-color: var(--color-border) !important;
  color: #000 !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  font-size: 1.2rem !important;
  flex-shrink: 0 !important;
}

.step-content {
  flex: 1 !important;
}

.step-content h4 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

.step-content p {
  color: #e0e0e0 !important;
  margin-bottom: 15px !important;
  line-height: 1.5 !important;
}

/* No projects message */
.no-projects-message {
  text-align: center !important;
  padding: 40px 20px !important;
  background-color: rgba(40, 40, 40, 0.3) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
}

.no-projects-icon {
  font-size: 3rem !important;
  margin-bottom: 20px !important;
  opacity: 0.8 !important;
}

.no-projects-message h3 {
  color: var(--color-border) !important;
  margin-bottom: 15px !important;
  font-size: 1.3rem !important;
}

.no-projects-message p {
  color: #e0e0e0 !important;
  margin-bottom: 25px !important;
  line-height: 1.6 !important;
  max-width: 400px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Marketplace specific styles */
.featured-project {
  border: 2px solid #C0ff6b !important;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(0, 212, 255, 0.1)) !important;
}

.featured-badge {
  background: linear-gradient(45deg, #C0ff6b, #00d4ff);
  color: #1a1a2e;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 1rem;
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 2;
}

.project-image-container {
  position: relative;
  margin-bottom: 1rem;
}

.project-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 10px;
}

.project-owner {
  color: #00d4ff;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.project-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.skill-tag {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-badge {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.project-stats-mini {
  display: flex;
  gap: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem;
}

.project-stats-mini i {
  color: #C0ff6b;
  margin-right: 0.3rem;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.tag-item {
  color: #C0ff6b;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 20px;
  transition: all 0.3s ease;
  background: rgba(192, 255, 107, 0.1);
}

.tag-item:hover {
  background: rgba(192, 255, 107, 0.2);
  transform: translateY(-2px);
  text-decoration: none;
  color: #C0ff6b;
}

.tag-count {
  opacity: 0.7;
  font-size: 0.8em;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  background-color: rgba(40, 40, 40, 0.8);
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 15px;
  border: none;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-info {
  border-left: 4px solid #17a2b8;
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-warning {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-danger {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.alert-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard-style animations */
.project-card-mini {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.project-card-mini.visible {
  opacity: 1;
  transform: translateY(0);
}

.stat-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.stat-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.action-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.4s ease;
}

.action-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.step-item {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.5s ease;
}

.step-item.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Button styling consistency */
.btn {
  background-color: transparent !important;
  color: #ffffff !important;
  border: 2px solid var(--color-border) !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  min-width: 120px !important;
}

.btn:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3) !important;
  transform: translateY(-2px) !important;
  text-decoration: none !important;
}

.btn-primary {
  background-color: rgba(192, 255, 107, 0.2) !important;
  border-color: var(--color-border) !important;
  color: var(--color-border) !important;
}

.btn-primary:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: #ffffff !important;
}

.btn-sm {
  padding: 8px 16px !important;
  font-size: 0.9rem !important;
  min-width: 100px !important;
}

.btn-outline {
  background-color: transparent !important;
  border: 2px solid rgba(255, 255, 255, 0.3) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.btn-outline:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: #ffffff !important;
}

/* Badge styling */
.badge {
  padding: 4px 8px !important;
  border-radius: 12px !important;
  font-size: 0.75rem !important;
  font-weight: bold !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

.badge-success {
  background-color: rgba(40, 167, 69, 0.2) !important;
  color: #28a745 !important;
  border: 1px solid rgba(40, 167, 69, 0.3) !important;
}

.badge-owner {
  background-color: rgba(192, 255, 107, 0.2) !important;
  color: var(--color-border) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
}

.badge-member {
  background-color: rgba(33, 150, 243, 0.2) !important;
  color: #2196F3 !important;
  border: 1px solid rgba(33, 150, 243, 0.3) !important;
}

/* Form styling consistency */
.form-control {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.8rem;
  color: white;
  font-size: 1rem;
}

.form-control:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--color-border);
  box-shadow: 0 0 0 0.2rem rgba(192, 255, 107, 0.25);
  outline: none;
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .recent-projects-grid {
    grid-template-columns: 1fr;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .project-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .project-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
{% endblock %}
