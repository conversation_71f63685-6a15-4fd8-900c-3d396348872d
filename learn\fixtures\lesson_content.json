[{"model": "learn.lessoncontent", "pk": 1, "fields": {"lesson": 1, "content_type": "text", "title": "Introduction to Python", "content": "<h3>What is Python?</h3><p>Python is a high-level, interpreted programming language known for its simplicity and readability. Created by <PERSON> in 1991, Python has become one of the most popular programming languages in the world.</p><h4>Key Features of Python:</h4><ul><li><strong>Easy to Learn:</strong> Python's syntax is clean and intuitive</li><li><strong>Versatile:</strong> Used in web development, data science, AI, automation, and more</li><li><strong>Large Community:</strong> Extensive libraries and community support</li><li><strong>Cross-platform:</strong> Runs on Windows, macOS, and Linux</li></ul>", "order": 1}}, {"model": "learn.lessoncontent", "pk": 2, "fields": {"lesson": 1, "content_type": "tip", "title": "Why Choose Python?", "content": "<p>Python is an excellent first programming language because:</p><ul><li>Its syntax closely resembles English</li><li>It handles many complex tasks automatically</li><li>It has a gentle learning curve</li><li>It's used by major companies like Google, Netflix, and Instagram</li></ul>", "order": 2}}, {"model": "learn.lessoncontent", "pk": 3, "fields": {"lesson": 1, "content_type": "code_example", "title": "Your First Look at Python Code", "content": "# This is a simple Python program\nprint(\"Hello, World!\")\n\n# Variables are easy to create\nname = \"Python\"\nversion = 3.9\n\nprint(f\"Welcome to {name} {version}!\")", "code_language": "python", "order": 3}}, {"model": "learn.lessoncontent", "pk": 4, "fields": {"lesson": 2, "content_type": "text", "title": "Installing Python on Your System", "content": "<h3>Step-by-Step Installation Guide</h3><p>Follow these steps to install Python on your computer:</p><h4>For Windows:</h4><ol><li>Visit <a href='https://python.org' target='_blank'>python.org</a></li><li>Download the latest Python 3.x version</li><li>Run the installer and check 'Add Python to PATH'</li><li>Click 'Install Now'</li></ol><h4>For macOS:</h4><ol><li>Python comes pre-installed, but install the latest version from python.org</li><li>Or use Homebrew: <code>brew install python</code></li></ol><h4>For Linux:</h4><ol><li>Most distributions include Python</li><li>Update with: <code>sudo apt update && sudo apt install python3</code></li></ol>", "order": 1}}, {"model": "learn.lessoncontent", "pk": 5, "fields": {"lesson": 2, "content_type": "exercise", "title": "Verify Your Installation", "content": "<p>After installation, verify Python is working correctly:</p><ol><li>Open your terminal or command prompt</li><li>Type <code>python --version</code> or <code>python3 --version</code></li><li>You should see the Python version number</li><li>Try running the Python interpreter by typing <code>python</code> or <code>python3</code></li></ol>", "order": 2}}, {"model": "learn.lessoncontent", "pk": 6, "fields": {"lesson": 3, "content_type": "text", "title": "Writing Your First Python Program", "content": "<h3>Creating a Python File</h3><p>Let's create your first Python program step by step:</p><ol><li>Create a new file called <code>hello.py</code></li><li>Open it in your favorite text editor</li><li>Write your first Python code</li><li>Run the program from the terminal</li></ol>", "order": 1}}, {"model": "learn.lessoncontent", "pk": 7, "fields": {"lesson": 3, "content_type": "code_example", "title": "Hello World Program", "content": "# hello.py - Your first Python program\n\n# Print a greeting message\nprint(\"Hello, World!\")\nprint(\"Welcome to Python programming!\")\n\n# You can also use variables\nmessage = \"Python is awesome!\"\nprint(message)\n\n# Ask for user input\nname = input(\"What's your name? \")\nprint(f\"Nice to meet you, {name}!\")", "code_language": "python", "order": 2}}, {"model": "learn.lessoncontent", "pk": 8, "fields": {"lesson": 3, "content_type": "exercise", "title": "Practice Exercise", "content": "<h4>Try This:</h4><ol><li>Create a new Python file called <code>about_me.py</code></li><li>Write a program that asks for your name, age, and favorite hobby</li><li>Display a personalized message using this information</li><li>Run your program and test it with different inputs</li></ol><p><strong>Challenge:</strong> Add some calculations, like calculating what year you were born based on your age!</p>", "order": 3}}, {"model": "learn.lessoncontent", "pk": 9, "fields": {"lesson": 4, "content_type": "text", "title": "What are Variables?", "content": "<h3>Understanding Variables in Python</h3><p>Variables are containers that store data values. In Python, you don't need to declare the type of variable explicitly - Python figures it out automatically!</p><h4>Key Concepts:</h4><ul><li><strong>Assignment:</strong> Use the = operator to assign values</li><li><strong>Dynamic Typing:</strong> Variables can change type during execution</li><li><strong>Naming Rules:</strong> Must start with letter or underscore, can contain letters, numbers, and underscores</li></ul>", "order": 1}}, {"model": "learn.lessoncontent", "pk": 10, "fields": {"lesson": 4, "content_type": "code_example", "title": "Variable Examples", "content": "# Different types of variables\nname = \"<PERSON>\"           # String\nage = 25                 # Integer\nheight = 5.6            # Float\nis_student = True       # Boolean\n\n# Variables can change\nage = 26                # Now age is 26\nage = \"twenty-six\"      # Now age is a string!\n\n# Multiple assignment\nx, y, z = 1, 2, 3\n\n# Same value to multiple variables\na = b = c = 0\n\nprint(f\"Name: {name}, Age: {age}, Height: {height}\")", "code_language": "python", "order": 2}}, {"model": "learn.lessoncontent", "pk": 11, "fields": {"lesson": 4, "content_type": "warning", "title": "Variable Naming Best Practices", "content": "<h4>Do:</h4><ul><li>Use descriptive names: <code>user_age</code> instead of <code>a</code></li><li>Use snake_case for variable names: <code>first_name</code></li><li>Start with lowercase letters</li></ul><h4>Don't:</h4><ul><li>Use Python keywords: <code>class</code>, <code>def</code>, <code>if</code>, etc.</li><li>Start with numbers: <code>2name</code> is invalid</li><li>Use special characters except underscore</li></ul>", "order": 3}}, {"model": "learn.lessoncontent", "pk": 12, "fields": {"lesson": 5, "content_type": "text", "title": "JavaScript Variables and Data Types", "content": "<h3>Variables in JavaScript</h3><p>JavaScript has three ways to declare variables: <code>var</code>, <code>let</code>, and <code>const</code>. Each has different scoping rules and use cases.</p><h4>Variable Declaration Keywords:</h4><ul><li><strong>let:</strong> Block-scoped, can be reassigned</li><li><strong>const:</strong> Block-scoped, cannot be reassigned</li><li><strong>var:</strong> Function-scoped, can be reassigned (legacy)</li></ul>", "order": 1}}, {"model": "learn.lessoncontent", "pk": 13, "fields": {"lesson": 5, "content_type": "code_example", "title": "JavaScript Variable Examples", "content": "// Modern variable declarations\nlet userName = \"<PERSON>\";\nconst PI = 3.14159;\nlet age = 30;\n\n// Data types\nlet message = \"Hello World\";     // String\nlet count = 42;                  // Number\nlet isActive = true;             // Boolean\nlet data = null;                 // Null\nlet undefined_var;               // Undefined\n\n// Objects and Arrays\nlet person = {\n    name: \"<PERSON>\",\n    age: 25,\n    city: \"New York\"\n};\n\nlet colors = [\"red\", \"green\", \"blue\"];\n\nconsole.log(`Hello, ${userName}!`);\nconsole.log(person.name);\nconsole.log(colors[0]);", "code_language": "javascript", "order": 2}}]