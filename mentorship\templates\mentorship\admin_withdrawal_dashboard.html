{% extends 'base.html' %}
{% load static %}

{% block title %}Admin Withdrawal Management - ForgeX{% endblock %}

{% block content %}
<div class="admin-withdrawal-dashboard">
    {% csrf_token %}
    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="header-content">
                <div class="admin-welcome">
                    <h1><i class="fas fa-shield-alt"></i> Withdrawal Management</h1>
                    <p>Manage mentor withdrawal requests</p>
                </div>

                <div class="admin-stats">
                    <div class="stat-card pending">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">{{ pending_count }}</span>
                            <span class="stat-label">Pending</span>
                            <span class="stat-amount">${{ total_pending_amount }}</span>
                        </div>
                    </div>

                    <div class="stat-card processing">
                        <div class="stat-icon">
                            <i class="fas fa-cog fa-spin"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">{{ processing_count }}</span>
                            <span class="stat-label">Processing</span>
                            <span class="stat-amount">${{ total_processing_amount }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="dashboard-content">
        <div class="container">
            <div class="dashboard-grid">
                <!-- Pending Withdrawals -->
                <div class="dashboard-section pending-section">
                    <div class="section-header">
                        <h2><i class="fas fa-exclamation-circle"></i> Pending Withdrawals</h2>
                        <span class="badge badge-warning">{{ pending_count }} requests</span>
                    </div>

                    {% if pending_withdrawals %}
                        <div class="withdrawals-table-container">
                            <table class="withdrawals-table">
                                <thead>
                                    <tr>
                                        <th>Mentor</th>
                                        <th>Amount</th>
                                        <th>Net Amount</th>
                                        <th>Requested</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for withdrawal in pending_withdrawals %}
                                        <tr class="withdrawal-row" data-withdrawal-id="{{ withdrawal.id }}">
                                            <td>
                                                <div class="mentor-info">
                                                    <strong>{{ withdrawal.mentor.get_full_name|default:withdrawal.mentor.username }}</strong>
                                                    <small>{{ withdrawal.mentor.email }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="amount">${{ withdrawal.amount }}</span>
                                                <small class="fee">Fee: ${{ withdrawal.processing_fee }}</small>
                                            </td>
                                            <td>
                                                <span class="net-amount">${{ withdrawal.net_amount }}</span>
                                            </td>
                                            <td>
                                                <div class="date-info">
                                                    <span>{{ withdrawal.requested_at|date:"M d, Y" }}</span>
                                                    <small>{{ withdrawal.requested_at|time:"g:i A" }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    <button class="btn btn-sm btn-success"
                                                            onclick="approveWithdrawal({{ withdrawal.id }})">
                                                        <i class="fas fa-check"></i>
                                                        Approve
                                                    </button>
                                                    <button class="btn btn-sm btn-danger"
                                                            onclick="openRejectModal({{ withdrawal.id }})">
                                                        <i class="fas fa-times"></i>
                                                        Reject
                                                    </button>
                                                    <a href="{% url 'mentorship:admin_withdrawal_details' withdrawal.id %}"
                                                       class="btn btn-sm btn-outline">
                                                        <i class="fas fa-eye"></i>
                                                        Details
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-check-circle"></i>
                            <h3>No Pending Withdrawals</h3>
                            <p>All withdrawal requests have been processed.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Processing Withdrawals -->
                <div class="dashboard-section processing-section">
                    <div class="section-header">
                        <h2><i class="fas fa-cog"></i> Processing Withdrawals</h2>
                        <span class="badge badge-info">{{ processing_count }} requests</span>
                    </div>

                    {% if processing_withdrawals %}
                        <div class="withdrawals-list">
                            {% for withdrawal in processing_withdrawals %}
                                <div class="withdrawal-item processing">
                                    <div class="withdrawal-info">
                                        <div class="mentor-name">{{ withdrawal.mentor.get_full_name|default:withdrawal.mentor.username }}</div>
                                        <div class="withdrawal-amount">${{ withdrawal.amount }}</div>
                                        <div class="withdrawal-date">
                                            Processing since {{ withdrawal.processed_at|date:"M d, Y g:i A" }}
                                        </div>
                                    </div>
                                    <div class="withdrawal-actions">
                                        <button class="btn btn-sm btn-success"
                                                onclick="approveWithdrawal({{ withdrawal.id }})">
                                            <i class="fas fa-check"></i>
                                            Complete
                                        </button>
                                        <a href="{% url 'mentorship:admin_withdrawal_details' withdrawal.id %}"
                                           class="btn btn-sm btn-outline">
                                            <i class="fas fa-eye"></i>
                                            Details
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-clock"></i>
                            <h3>No Processing Withdrawals</h3>
                            <p>No withdrawals are currently being processed.</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section recent-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> Recent Activity</h2>
                    </div>

                    {% if recent_completed %}
                        <div class="activity-list">
                            {% for withdrawal in recent_completed %}
                                <div class="activity-item status-{{ withdrawal.status }}">
                                    <div class="activity-icon">
                                        {% if withdrawal.status == 'completed' %}
                                            <i class="fas fa-check-circle"></i>
                                        {% elif withdrawal.status == 'failed' %}
                                            <i class="fas fa-times-circle"></i>
                                        {% elif withdrawal.status == 'cancelled' %}
                                            <i class="fas fa-ban"></i>
                                        {% endif %}
                                    </div>
                                    <div class="activity-info">
                                        <div class="activity-title">
                                            {{ withdrawal.mentor.get_full_name|default:withdrawal.mentor.username }}
                                        </div>
                                        <div class="activity-details">
                                            ${{ withdrawal.amount }} - {{ withdrawal.get_status_display }}
                                        </div>
                                        <div class="activity-date">
                                            {{ withdrawal.completed_at|date:"M d, Y g:i A" }}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <h3>No Recent Activity</h3>
                            <p>No recent withdrawal activity to display.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Rejection Modal -->
<div id="rejectModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Reject Withdrawal Request</h3>
            <span class="close" onclick="closeRejectModal()">&times;</span>
        </div>
        <div class="modal-body">
            <p>Please provide a reason for rejecting this withdrawal request:</p>

            <div class="form-group">
                <label for="rejectionReason">Rejection Reason *</label>
                <textarea id="rejectionReason" rows="4"
                          placeholder="Enter the reason for rejection..." required></textarea>
            </div>

            <div class="rejection-info">
                <i class="fas fa-info-circle"></i>
                <p>The mentor will be notified of the rejection and the amount will be restored to their available balance.</p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeRejectModal()">Cancel</button>
            <button class="btn btn-danger" onclick="confirmRejectWithdrawal()">
                <i class="fas fa-times"></i>
                Reject Withdrawal
            </button>
        </div>
    </div>
</div>

<style>
.admin-withdrawal-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.dashboard-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: #ffffff;
    padding: 3rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.admin-welcome h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-stats {
    display: flex;
    gap: 2rem;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 150px;
}

.stat-card.pending {
    border: 2px solid #ffc107;
}

.stat-card.processing {
    border: 2px solid #007bff;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-card.pending .stat-icon {
    background: rgba(255,193,7,0.2);
    color: #ffc107;
}

.stat-card.processing .stat-icon {
    background: rgba(0,123,255,0.2);
    color: #007bff;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stat-amount {
    font-size: 0.8rem;
    font-weight: 600;
    color: #C0ff6b;
}

.dashboard-content {
    padding: 4rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.pending-section {
    grid-column: 1 / 2;
    grid-row: 1 / 3;
}

.processing-section {
    grid-column: 2 / 3;
    grid-row: 1 / 2;
}

.recent-section {
    grid-column: 2 / 3;
    grid-row: 2 / 3;
}

.dashboard-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.section-header h2 {
    color: #C0ff6b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.3rem;
}

.badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.badge-warning {
    background: rgba(255,193,7,0.2);
    color: #ffc107;
}

.badge-info {
    background: rgba(0,123,255,0.2);
    color: #007bff;
}

.withdrawals-table-container {
    overflow-x: auto;
}

.withdrawals-table {
    width: 100%;
    border-collapse: collapse;
}

.withdrawals-table th,
.withdrawals-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(192,255,107,0.1);
}

.withdrawals-table th {
    background: rgba(192,255,107,0.1);
    color: #C0ff6b;
    font-weight: 600;
    font-size: 0.9rem;
}

.mentor-info {
    display: flex;
    flex-direction: column;
}

.mentor-info small {
    opacity: 0.7;
    font-size: 0.8rem;
}

.amount {
    font-weight: 600;
    color: #C0ff6b;
    font-size: 1.1rem;
}

.fee {
    display: block;
    font-size: 0.8rem;
    color: #ffc107;
}

.net-amount {
    font-weight: 600;
    color: #28a745;
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date-info small {
    opacity: 0.7;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    box-shadow: 0 8px 25px rgba(40,167,69,0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    box-shadow: 0 8px 25px rgba(220,53,69,0.4);
}

.btn-outline {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-outline:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn:hover {
    transform: translateY(-2px);
}

.withdrawals-list,
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.withdrawal-item,
.activity-item {
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(192,255,107,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item {
    align-items: flex-start;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.status-completed .activity-icon {
    background: rgba(40,167,69,0.2);
    color: #28a745;
}

.status-failed .activity-icon {
    background: rgba(220,53,69,0.2);
    color: #dc3545;
}

.status-cancelled .activity-icon {
    background: rgba(108,117,125,0.2);
    color: #6c757d;
}

.activity-info {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.activity-details {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.25rem;
}

.activity-date {
    font-size: 0.8rem;
    opacity: 0.6;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    opacity: 0.6;
}

.empty-state i {
    font-size: 3rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #C0ff6b;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
}

.modal-content {
    background: #2d2d2d;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    color: #ffffff;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(192,255,107,0.2);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    resize: vertical;
    transition: all 0.3s ease;
}

.form-group textarea:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 15px rgba(192,255,107,0.4);
    background: rgba(255,255,255,0.15);
}

.rejection-info {
    background: rgba(255,193,7,0.1);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 1rem;
}

.rejection-info i {
    color: #ffc107;
    margin-top: 0.25rem;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #C0ff6b;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .admin-stats {
        flex-direction: column;
        width: 100%;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .pending-section,
    .processing-section,
    .recent-section {
        grid-column: 1;
        grid-row: auto;
    }

    .action-buttons {
        flex-direction: column;
    }
}
</style>

<script>
let currentWithdrawalId = null;

// Approve withdrawal
async function approveWithdrawal(withdrawalId) {
    if (!confirm('Are you sure you want to approve this withdrawal request?')) {
        return;
    }

    try {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (!csrfToken) {
            alert('CSRF token not found. Please refresh the page and try again.');
            return;
        }

        const url = `{% url 'mentorship:admin_approve_withdrawal' 0 %}`.replace('0', withdrawalId);
        console.log('Requesting URL:', url);

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken.value
            }
        });

        console.log('Response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Response error:', errorText);
            alert(`Server error (${response.status}): ${errorText}`);
            return;
        }

        const result = await response.json();
        console.log('Response result:', result);

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        console.error('Fetch error:', error);
        alert('An error occurred. Please try again. Check console for details.');
    }
}

// Open reject modal
function openRejectModal(withdrawalId) {
    currentWithdrawalId = withdrawalId;
    document.getElementById('rejectionReason').value = '';
    document.getElementById('rejectModal').style.display = 'block';
}

// Close reject modal
function closeRejectModal() {
    currentWithdrawalId = null;
    document.getElementById('rejectModal').style.display = 'none';
}

// Confirm rejection
async function confirmRejectWithdrawal() {
    const reason = document.getElementById('rejectionReason').value.trim();

    if (!reason) {
        alert('Please provide a rejection reason.');
        return;
    }

    if (!currentWithdrawalId) {
        alert('No withdrawal selected.');
        return;
    }

    try {
        const response = await fetch(`{% url 'mentorship:admin_reject_withdrawal' 0 %}`.replace('0', currentWithdrawalId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ reason: reason })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            closeRejectModal();
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('rejectModal');
    if (event.target == modal) {
        closeRejectModal();
    }
}
</script>
{% endblock %}
