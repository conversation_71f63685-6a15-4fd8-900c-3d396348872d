"""
API Views for Collaborate App - Local File Storage
"""

import json
import logging
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from .models import Project
from .local_storage import (
    get_project_files, 
    get_file_content, 
    save_file_content, 
    delete_file_or_directory,
    rename_file_or_directory
)

logger = logging.getLogger(__name__)

def get_project_from_identifier(identifier):
    """Get project from hash or ID"""
    try:
        # Try to get by hash first
        return Project.objects.get(hash=identifier)
    except Project.DoesNotExist:
        try:
            # Try to get by ID
            return Project.objects.get(id=int(identifier))
        except (Project.DoesNotExist, ValueError):
            return None

@csrf_exempt
@require_http_methods(["GET"])
def api_get_files(request, project_identifier):
    """Get file tree for a project"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        file_tree = get_project_files(project.id)
        logger.info(f"API: Retrieved file tree for project {project.id}: {len(file_tree)} items")
        
        return JsonResponse(file_tree, safe=False)
        
    except Exception as e:
        logger.error(f"API: Error getting files for project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def api_get_file(request, project_identifier):
    """Get content of a specific file"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        file_path = request.GET.get('path')
        if not file_path:
            return JsonResponse({'error': 'File path is required'}, status=400)
        
        content = get_file_content(project.id, file_path)
        logger.info(f"API: Retrieved file {file_path} from project {project.id}")
        
        return HttpResponse(content, content_type='text/plain')
        
    except FileNotFoundError:
        return JsonResponse({'error': 'File not found'}, status=404)
    except Exception as e:
        logger.error(f"API: Error getting file {file_path} from project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def api_create_file(request, project_identifier):
    """Create a new file or directory"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        data = json.loads(request.body)
        file_path = data.get('path')
        content = data.get('content', '')
        is_directory = data.get('isDirectory', False)
        
        if not file_path:
            return JsonResponse({'error': 'File path is required'}, status=400)
        
        save_file_content(project.id, file_path, content, is_directory)
        logger.info(f"API: Created {'directory' if is_directory else 'file'} {file_path} in project {project.id}")
        
        return JsonResponse({'status': 'success', 'path': file_path})
        
    except Exception as e:
        logger.error(f"API: Error creating file in project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["PUT"])
def api_update_file(request, project_identifier):
    """Update content of an existing file"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        data = json.loads(request.body)
        file_path = data.get('path')
        content = data.get('content', '')
        
        if not file_path:
            return JsonResponse({'error': 'File path is required'}, status=400)
        
        save_file_content(project.id, file_path, content, is_directory=False)
        logger.info(f"API: Updated file {file_path} in project {project.id}")
        
        return JsonResponse({'status': 'success', 'path': file_path})
        
    except Exception as e:
        logger.error(f"API: Error updating file in project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["DELETE"])
def api_delete_file(request, project_identifier):
    """Delete a file or directory"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        file_path = request.GET.get('path')
        if not file_path:
            return JsonResponse({'error': 'File path is required'}, status=400)
        
        delete_file_or_directory(project.id, file_path)
        logger.info(f"API: Deleted {file_path} from project {project.id}")
        
        return JsonResponse({'status': 'success'})
        
    except FileNotFoundError:
        return JsonResponse({'error': 'File not found'}, status=404)
    except Exception as e:
        logger.error(f"API: Error deleting file from project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def api_rename_file(request, project_identifier):
    """Rename a file or directory"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        data = json.loads(request.body)
        old_path = data.get('oldPath')
        new_path = data.get('newPath')
        
        if not old_path or not new_path:
            return JsonResponse({'error': 'Both old and new paths are required'}, status=400)
        
        rename_file_or_directory(project.id, old_path, new_path)
        logger.info(f"API: Renamed {old_path} to {new_path} in project {project.id}")
        
        return JsonResponse({'status': 'success'})
        
    except FileNotFoundError:
        return JsonResponse({'error': 'File not found'}, status=404)
    except Exception as e:
        logger.error(f"API: Error renaming file in project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def api_move_file(request, project_identifier):
    """Move a file or directory (same as rename but different endpoint)"""
    try:
        project = get_project_from_identifier(project_identifier)
        if not project:
            return JsonResponse({'error': 'Project not found'}, status=404)
        
        # Check if user has access to this project
        if not project.can_access(request.user):
            return JsonResponse({'error': 'Access denied'}, status=403)
        
        data = json.loads(request.body)
        source = data.get('source')
        target = data.get('target')
        
        if not source or not target:
            return JsonResponse({'error': 'Both source and target paths are required'}, status=400)
        
        # For move operation, we need to construct the new path
        import os
        if target.endswith('/'):
            # Moving into a directory
            filename = os.path.basename(source)
            new_path = os.path.join(target, filename).replace('\\', '/')
        else:
            # Renaming/moving to specific path
            new_path = target
        
        rename_file_or_directory(project.id, source, new_path)
        logger.info(f"API: Moved {source} to {new_path} in project {project.id}")
        
        return JsonResponse({'status': 'success'})
        
    except FileNotFoundError:
        return JsonResponse({'error': 'File not found'}, status=404)
    except Exception as e:
        logger.error(f"API: Error moving file in project {project_identifier}: {e}")
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@require_http_methods(["GET"])
def api_health_check(request):
    """Health check endpoint"""
    return JsonResponse({
        'status': 'healthy',
        'service': 'ForgeX Local Storage API',
        'version': '1.0.0'
    })

# Compatibility endpoint for the editor
@csrf_exempt
def api_root(request):
    """Root API endpoint for compatibility"""
    return JsonResponse({
        'message': 'ForgeX Local Storage API',
        'version': '1.0.0',
        'endpoints': {
            'files': '/api/files/{project_id}',
            'file': '/api/file/{project_id}',
            'health': '/api/health'
        }
    })
