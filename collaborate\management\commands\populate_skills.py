from django.core.management.base import BaseCommand
from collaborate.models import Skill
from collaborate.utils.skill_standardization import SKILL_ALIASES, SKILL_HIERARCHY

class Command(BaseCommand):
    help = 'Populate the database with predefined skills from skill_standardization.py'

    def handle(self, *args, **kwargs):
        # Get all skills from aliases and hierarchies
        all_skills = set()
        
        # Add all standard skill names from aliases
        for standard_name in SKILL_ALIASES.keys():
            all_skills.add(standard_name)
        
        # Add all skills from hierarchies
        for parent, children in SKILL_HIERARCHY.items():
            all_skills.add(parent)
            all_skills.update(children)
        
        # Sort skills alphabetically
        all_skills = sorted(all_skills)
        
        # Create skills in the database
        created_count = 0
        existing_count = 0
        
        for skill_name in all_skills:
            skill, created = Skill.objects.get_or_create(name=skill_name)
            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f'Created skill: {skill_name}'))
            else:
                existing_count += 1
                self.stdout.write(self.style.WARNING(f'Skill already exists: {skill_name}'))
        
        self.stdout.write(self.style.SUCCESS(
            f'Skill population complete. Created {created_count} new skills. '
            f'{existing_count} skills already existed.'
        ))
