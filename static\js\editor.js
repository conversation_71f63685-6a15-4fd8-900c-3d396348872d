// editor.js (ES Module)
import * as Y from '/node_modules/yjs/dist/yjs.mjs';
import { WebsocketProvider } from '/node_modules/y-websocket/dist/y-websocket.mjs';
import { MonacoBinding } from '/node_modules/y-monaco/dist/y-monaco.mjs';

// Monaco uses AMD, load it via require.js (AMD loader)
import '/node_modules/monaco-editor/min/vs/loader.js';

require.config({ paths: { vs: '/node_modules/monaco-editor/min/vs' } });

require(['vs/editor/editor.main'], function () {
  const editor = monaco.editor.create(document.getElementById('monaco-editor'), {
    value: '',
    language: 'javascript',
    theme: 'vs-dark',
    automaticLayout: true
  });

  // Create Yjs doc
  const ydoc = new Y.Doc();

  // Connect to WebSocket provider
  const provider = new WebsocketProvider('ws://localhost:8001', 'room-id', ydoc);

  const yText = ydoc.getText('monaco');

  // Bind Yjs doc to Monaco
  const binding = new MonacoBinding(
    yText,
    editor.getModel(),
    new Set([editor]),
    provider.awareness
  );

  console.log('✅ Yjs + Monaco binding is active');
});