import json
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)




class YjsConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs'].get('room_name', 'default')
        self.room_group_name = f"yjs_{self.room_name}"
        self.client_id = f"client_{id(self)}"

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Send a welcome message
        await self.send(text_data=json.dumps({
            'type': 'welcome',
            'message': f'Connected to room: {self.room_name}',
            'client_id': self.client_id
        }))

        print(f"Client {self.client_id} connected to room {self.room_name}")

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        print(f"Client {self.client_id} disconnected from room {self.room_name}")

    async def receive(self, text_data=None, bytes_data=None):
        # Handle both text and binary messages
        if text_data:
            try:
                data = json.loads(text_data)

                # Add sender information
                data['sender'] = self.client_id

                # Echo message to everyone in the group
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        "type": "yjs_text_message",
                        "message": data,
                    },
                )

                print(f"Received text message from {self.client_id} in room {self.room_name}: {data.get('type', 'unknown')}")

                # If this is a join message, send a confirmation
                if data.get('type') == 'join':
                    await self.send(text_data=json.dumps({
                        'type': 'joined',
                        'room': data.get('room', self.room_name),
                        'client_id': self.client_id
                    }))
            except json.JSONDecodeError:
                print(f"Received invalid JSON from {self.client_id}")

        if bytes_data:
            # Echo binary data to everyone in the group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    "type": "yjs_binary_message",
                    "bytes_data": bytes_data,
                    "sender": self.client_id,
                },
            )
            print(f"Received binary data from {self.client_id} in room {self.room_name}: {len(bytes_data)} bytes")

    async def yjs_text_message(self, event):
        message = event["message"]

        # Don't send the message back to the sender
        if message.get('sender') != self.client_id:
            try:
                await self.send(text_data=json.dumps(message))
                print(f"Sent text message to {self.client_id} in room {self.room_name}")
            except Exception as e:
                print(f"Error sending text message to {self.client_id}: {str(e)}")

    async def yjs_binary_message(self, event):
        # Don't send the message back to the sender
        if event.get('sender') != self.client_id:
            try:
                await self.send(bytes_data=event["bytes_data"])
                print(f"Sent binary data to {self.client_id} in room {self.room_name}")
            except Exception as e:
                print(f"Error sending binary data to {self.client_id}: {str(e)}")





class EditorConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.project_hash = self.scope['url_route']['kwargs']['project_hash']
        # Get project ID from hash for internal use
        self.project_id = await self.get_project_id_from_hash()
        if not self.project_id:
            await self.close()
            return

        self.room_group_name = f'collaborate_{self.project_id}'

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    @database_sync_to_async
    def get_project_id_from_hash(self):
        """Get project ID from hash"""
        try:
            from .models import Project
            project = Project.objects.get(hash=self.project_hash)
            return project.id
        except Project.DoesNotExist:
            return None

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        data = json.loads(text_data)

        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'editor_message',
                'message': data
            }
        )

    async def editor_message(self, event):
        message = event['message']

        # Send message to WebSocket
        await self.send(text_data=json.dumps(message))

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        if self.scope['user'].is_authenticated:
            # Add user to their personal notification group
            self.user_group = f"user_{self.scope['user'].id}"
            await self.channel_layer.group_add(self.user_group, self.channel_name)

            # Also add to system-wide notification group
            self.system_group = "system_notifications"
            await self.channel_layer.group_add(self.system_group, self.channel_name)

            await self.accept()

            # Send connection confirmation
            await self.send(text_data=json.dumps({
                'type': 'connection_established',
                'message': 'Connected to notification service'
            }))
        else:
            await self.close()

    async def disconnect(self, close_code):
        # Remove from user-specific group
        if hasattr(self, 'user_group'):
            await self.channel_layer.group_discard(self.user_group, self.channel_name)

        # Remove from system-wide group
        if hasattr(self, 'system_group'):
            await self.channel_layer.group_discard(self.system_group, self.channel_name)

    async def receive(self, text_data):
        """Handle messages from the client"""
        if not self.scope['user'].is_authenticated:
            return

        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            # Handle message acknowledgment
            if message_type == 'mark_read':
                notification_id = data.get('notification_id')
                if notification_id:
                    # This would be handled asynchronously in a real implementation
                    # For now, we just acknowledge receipt
                    await self.send(text_data=json.dumps({
                        'type': 'ack',
                        'notification_id': notification_id
                    }))
        except json.JSONDecodeError:
            pass

    async def send_notification(self, event):
        """Send a notification to the connected client"""
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'title': event.get('title', ''),
            'message': event.get('message', ''),
            'notification_type': event.get('notification_type', 'info'),
            'notification_id': event.get('notification_id')
        }))

    async def system_notification(self, event):
        """Send a system notification to all connected clients"""
        await self.send(text_data=json.dumps({
            'type': 'system_notification',
            'title': event.get('title', ''),
            'message': event.get('message', ''),
            'notification_type': event.get('notification_type', 'system')
        }))

class CollabConsumer(AsyncWebsocketConsumer):
    chat_users = set()
    voice_users = set()

    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f"project_{self.room_id}"
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Get the project object
        self.project = await self.get_project()
        if not self.project:
            await self.close()
            return

        # Add user to chat presence
        CollabConsumer.chat_users.add(self.user.username)
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Send chat history to the user
        print(f"DEBUG: About to send chat history to user {self.user.username}")
        await self.send_chat_history()
        print(f"DEBUG: Chat history sent to user {self.user.username}")

        # Send welcome message to user
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': 'Connected to project chat',
            'timestamp': timezone.now().timestamp()
        }))

        # Save and notify others about new participant
        await self.save_system_message(f'{self.user.username} joined the project', 'user_joined')

        # Broadcast updated user list
        await self.broadcast_user_list()

    async def disconnect(self, close_code):
        # Save and notify others about participant leaving
        await self.save_system_message(f'{self.user.username} left the project', 'user_left')

        # Remove user from all presences
        CollabConsumer.chat_users.discard(self.user.username)
        CollabConsumer.voice_users.discard(self.user.username)
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

        # Broadcast updated user list
        await self.broadcast_user_list()

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            data['sender_id'] = self.user.id
            data['sender_username'] = self.user.username
            data['timestamp'] = timezone.now().timestamp()

            message_type = data.get('type')

            if message_type == "chat":
                # Save chat message to database
                saved_message = await self.save_chat_message(data['message'])
                print(f"DEBUG: Saved chat message: {saved_message}")

                # Send chat message to room
                broadcast_data = {
                    "type": "chat_message_broadcast",
                    "message": data['message'],
                    "username": self.user.username,
                    "user_id": self.user.id,
                    "timestamp": data['timestamp']
                }
                print(f"DEBUG: Broadcasting chat message: {broadcast_data}")

                await self.channel_layer.group_send(
                    self.room_group_name,
                    broadcast_data
                )
            elif message_type == "voice":
                action = data.get("action")
                if action == "join":
                    CollabConsumer.voice_users.add(self.user.username)
                elif action == "leave":
                    CollabConsumer.voice_users.discard(self.user.username)

                # Broadcast updated user list
                await self.broadcast_user_list()
        except json.JSONDecodeError:
            pass

    async def chat_message_broadcast(self, event):
        """Send chat message to WebSocket (including sender)"""
        await self.send(text_data=json.dumps({
            'type': 'chat',
            'message': event['message'],
            'username': event['username'],
            'user_id': event['user_id'],
            'timestamp': event['timestamp']
        }))

    async def broadcast_user_list(self):
        user_list = {
            "chat_users": list(CollabConsumer.chat_users),
            "voice_users": list(CollabConsumer.voice_users),
        }
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                "type": "user_list_update",
                "chat_users": user_list["chat_users"],
                "voice_users": user_list["voice_users"],
            }
        )

    async def user_list_update(self, event):
        await self.send(text_data=json.dumps({
            "type": "user_list_update",
            "chat_users": event["chat_users"],
            "voice_users": event["voice_users"],
        }))

    @database_sync_to_async
    def get_project(self):
        """Get the project by room_id"""
        try:
            from .models import Project
            return Project.objects.get(id=self.room_id)
        except Project.DoesNotExist:
            return None

    @database_sync_to_async
    def save_chat_message(self, content):
        """Save a chat message to the database"""
        from django.db import transaction
        from .models import ProjectChatMessage
        try:
            with transaction.atomic():
                return ProjectChatMessage.objects.create(
                    project=self.project,
                    sender=self.user,
                    message=content,
                    message_type='chat'
                )
        except Exception as e:
            print(f"Error saving chat message: {e}")
            return None

    @database_sync_to_async
    def save_system_message(self, content, message_type):
        """Save a system message to the database"""
        from django.db import transaction
        from .models import ProjectChatMessage
        try:
            with transaction.atomic():
                return ProjectChatMessage.objects.create(
                    project=self.project,
                    sender=None,  # System messages have no sender
                    message=content,
                    message_type=message_type
                )
        except Exception as e:
            print(f"Error saving system message: {e}")
            return None

    @database_sync_to_async
    def get_chat_history(self):
        """Get chat history for the project"""
        from .models import ProjectChatMessage
        messages = list(ProjectChatMessage.objects.filter(project=self.project).select_related('sender').order_by('created_at'))

        # Convert to common format
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                'content': msg.message,
                'sender': msg.sender,
                'message_type': msg.message_type,
                'timestamp': msg.created_at,
            })

        return formatted_messages

    async def send_chat_history(self):
        """Send chat history to the user"""
        messages = await self.get_chat_history()
        print(f"DEBUG: Sending {len(messages)} chat history messages to user {self.user.username}")

        for message in messages:
            history_data = {
                'type': 'chat_history',
                'message': message['content'],
                'username': message['sender'].username if message['sender'] else 'System',
                'user_id': message['sender'].id if message['sender'] else None,
                'message_type': message['message_type'],
                'timestamp': message['timestamp'].timestamp() if hasattr(message['timestamp'], 'timestamp') else message['timestamp']
            }
            print(f"DEBUG: Sending history message: {history_data}")
            await self.send(text_data=json.dumps(history_data))

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_name = self.scope['url_route']['kwargs']['room_name']
        self.room_group_name = f'chat_{self.room_name}'

        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message = text_data_json['message']

        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': message
            }
        )

    async def chat_message(self, event):
        message = event['message']

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'message': message
        }))

class VoiceConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.project_hash = self.scope["url_route"]["kwargs"]["project_hash"]
        # Get project ID from hash for internal use
        self.project_id = await self.get_project_id_from_hash()
        if not self.project_id:
            await self.close()
            return

        self.group_name = f"voice_{self.project_id}"
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        await self.channel_layer.group_add(self.group_name, self.channel_name)
        await self.accept()

    @database_sync_to_async
    def get_project_id_from_hash(self):
        """Get project ID from hash"""
        try:
            from .models import Project
            project = Project.objects.get(hash=self.project_hash)
            return project.id
        except Project.DoesNotExist:
            return None

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(self.group_name, self.channel_name)

    async def receive(self, bytes_data=None, text_data=None):
        if bytes_data:
            # Forward binary audio data to all other clients in the group except sender
            await self.channel_layer.group_send(
                self.group_name,
                {
                    "type": "voice_message",
                    "bytes": bytes_data,
                    "sender_channel": self.channel_name  # Add sender channel to exclude
                },
            )

    async def voice_message(self, event):
        # Only send the audio data if this is not the sender
        if event.get("sender_channel") != self.channel_name:
            await self.send(bytes_data=event["bytes"])

class FileChangeConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.project_hash = self.scope['url_route']['kwargs']['project_hash']
        # Get project ID from hash for internal use
        self.project_id = await self.get_project_id_from_hash()
        if not self.project_id:
            await self.close()
            return

        self.room_group_name = f'project_{self.project_id}_file_changes'
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        await self.accept()

    @database_sync_to_async
    def get_project_id_from_hash(self):
        """Get project ID from hash"""
        try:
            from .models import Project
            project = Project.objects.get(hash=self.project_hash)
            return project.id
        except Project.DoesNotExist:
            return None

    async def disconnect(self, close_code):
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )

    async def receive(self, text_data=None, bytes_data=None):
        # Relay both text and binary messages as-is
        if bytes_data is not None:
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'file_change_message',
                    'bytes_data': bytes_data
                }
            )
        elif text_data is not None:
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'file_change_message',
                    'text_data': text_data
                }
            )

    async def file_change_message(self, event):
        if 'bytes_data' in event:
            await self.send(bytes_data=event['bytes_data'])
        elif 'text_data' in event:
            await self.send(text_data=event['text_data'])


class TerminalConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for terminal functionality.
    Provides real-time terminal access with multiple terminal support.
    """

    # Class-level storage for terminal sessions
    terminal_sessions = {}

    async def connect(self):
        self.terminal_id = self.scope['url_route']['kwargs']['terminal_id']
        self.project_hash = self.scope['url_route']['kwargs'].get('project_hash', 'default')
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Get project ID from hash
        self.project_id = await self.get_project_id_from_hash()
        if not self.project_id:
            await self.close()
            return

        # Create unique session key
        self.session_key = f"{self.project_id}_{self.terminal_id}"

        await self.accept()

        # Initialize terminal session
        await self.initialize_terminal()

    @database_sync_to_async
    def get_project_id_from_hash(self):
        """Get project ID from hash"""
        try:
            from .models import Project
            project = Project.objects.get(hash=self.project_hash)
            return project.id
        except Project.DoesNotExist:
            return None

    async def disconnect(self, close_code):
        """Clean up terminal session on disconnect"""
        await self.cleanup_terminal()

    async def receive(self, text_data):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')

            if message_type == 'input':
                # Handle terminal input
                await self.handle_terminal_input(data.get('data', ''))
            elif message_type == 'resize':
                # Handle terminal resize
                await self.handle_terminal_resize(data.get('data', {}))
            elif message_type == 'command':
                # Handle direct command execution
                await self.execute_command(data.get('command', ''))

        except json.JSONDecodeError:
            logger.error(f"Invalid JSON received from terminal {self.terminal_id}")

    async def initialize_terminal(self):
        """Initialize a new terminal session - DISABLED for Windows compatibility"""
        try:
            # Send message that terminal is disabled
            await self.send_terminal_output("Terminal WebSocket functionality is disabled for Windows compatibility.\r\n")
            await self.send_terminal_output("Please use the HTTP-based terminal in the editor instead.\r\n")

        except Exception as e:
            logger.error(f"Failed to initialize terminal {self.terminal_id}: {str(e)}")
            await self.send_terminal_output(f"Error: Terminal functionality disabled - {str(e)}\r\n")

    @database_sync_to_async
    def get_project_directory(self):
        """Get the project directory path"""
        try:
            from .models import Project
            project = Project.objects.get(id=self.project_id)
            # Return project directory or default
            return f"/app/projects/{project.hash}" if hasattr(project, 'hash') else "/tmp"
        except:
            return "/tmp"

    async def read_terminal_output(self):
        """Read output from terminal process and send to WebSocket"""
        session = TerminalConsumer.terminal_sessions.get(self.session_key)
        if not session or not session['active']:
            return

        process = session['process']

        try:
            while session['active'] and process.returncode is None:
                # Read output in chunks
                data = await process.stdout.read(1024)
                if data:
                    # Send output to WebSocket
                    await self.send_terminal_output(data.decode('utf-8', errors='ignore'))
                else:
                    # Process ended
                    break

        except Exception as e:
            logger.error(f"Error reading terminal output for {self.terminal_id}: {str(e)}")
        finally:
            # Mark session as inactive
            if session:
                session['active'] = False

    async def handle_terminal_input(self, data):
        """Handle input from WebSocket and send to terminal process"""
        session = TerminalConsumer.terminal_sessions.get(self.session_key)
        if not session or not session['active']:
            return

        process = session['process']

        try:
            if process.stdin and not process.stdin.is_closing():
                process.stdin.write(data.encode('utf-8'))
                await process.stdin.drain()
        except Exception as e:
            logger.error(f"Error sending input to terminal {self.terminal_id}: {str(e)}")

    async def handle_terminal_resize(self, resize_data):
        """Handle terminal resize (placeholder for future implementation)"""
        cols = resize_data.get('cols', 80)
        rows = resize_data.get('rows', 24)
        logger.info(f"Terminal {self.terminal_id} resized to {cols}x{rows}")
        # Note: Actual PTY resize would require additional implementation

    async def execute_command(self, command):
        """Execute a command directly in the terminal"""
        await self.handle_terminal_input(command + '\n')

    async def send_terminal_output(self, output):
        """Send terminal output to WebSocket"""
        try:
            await self.send(text_data=json.dumps({
                'type': 'output',
                'data': output
            }))
        except Exception as e:
            logger.error(f"Error sending terminal output for {self.terminal_id}: {str(e)}")

    async def cleanup_terminal(self):
        """Clean up terminal session"""
        session = TerminalConsumer.terminal_sessions.get(self.session_key)
        if session:
            session['active'] = False
            process = session['process']

            try:
                # Close stdin
                if process.stdin and not process.stdin.is_closing():
                    process.stdin.close()

                # Terminate process
                if process.returncode is None:
                    process.terminate()
                    try:
                        await asyncio.wait_for(process.wait(), timeout=5.0)
                    except asyncio.TimeoutError:
                        process.kill()
                        await process.wait()

            except Exception as e:
                logger.error(f"Error cleaning up terminal {self.terminal_id}: {str(e)}")
            finally:
                # Remove from sessions
                del TerminalConsumer.terminal_sessions[self.session_key]

