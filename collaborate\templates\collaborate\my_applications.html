{% extends "base.html" %}

{% block content %}
<div class="my-applications-container">
    <h1>My Applications</h1>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Pending Applications -->
    <section class="applications-section">
        <h2>Pending Applications</h2>
        {% if pending_applications %}
            <div class="applications-list">
                {% for application in pending_applications %}
                    <div class="application-card status-pending">
                        <div class="application-header">
                            <h3>{{ application.project.title }}</h3>
                            <span class="status-badge status-pending">Pending Review</span>
                        </div>
                        
                        <div class="application-details">
                            <p><strong>Project Owner:</strong> {{ application.project.owner.username }}</p>
                            <p><strong>Applied on:</strong> {{ application.created_at|date:"F j, Y" }}</p>
                        </div>

                        {% if application.message %}
                            <div class="application-message">
                                <h4>Your Application Message:</h4>
                                <p>{{ application.message }}</p>
                            </div>
                        {% endif %}

                        <div class="project-link">
                            <a href="{% url 'collaborate:project_detail' application.project.id %}" 
                               class="btn btn-secondary">View Project</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="no-applications">No pending applications</p>
        {% endif %}
    </section>

    <!-- Accepted Applications -->
    <section class="applications-section">
        <h2>Accepted Applications</h2>
        {% if accepted_applications %}
            <div class="applications-list">
                {% for application in accepted_applications %}
                    <div class="application-card status-accepted">
                        <div class="application-header">
                            <h3>{{ application.project.title }}</h3>
                            <span class="status-badge status-accepted">Accepted</span>
                        </div>
                        
                        <div class="application-details">
                            <p><strong>Project Owner:</strong> {{ application.project.owner.username }}</p>
                            <p><strong>Applied on:</strong> {{ application.created_at|date:"F j, Y" }}</p>
                            <p><strong>Accepted on:</strong> {{ application.updated_at|date:"F j, Y" }}</p>
                        </div>

                        <div class="project-actions">
                            <a href="{% url 'collaborate:editor' application.project.id %}" 
                               class="btn btn-primary">Open Editor</a>
                            <a href="{% url 'collaborate:project_detail' application.project.id %}" 
                               class="btn btn-secondary">View Project</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="no-applications">No accepted applications</p>
        {% endif %}
    </section>

    <!-- Rejected Applications -->
    <section class="applications-section">
        <h2>Rejected Applications</h2>
        {% if rejected_applications %}
            <div class="applications-list">
                {% for application in rejected_applications %}
                    <div class="application-card status-rejected">
                        <div class="application-header">
                            <h3>{{ application.project.title }}</h3>
                            <span class="status-badge status-rejected">Not Accepted</span>
                        </div>
                        
                        <div class="application-details">
                            <p><strong>Project Owner:</strong> {{ application.project.owner.username }}</p>
                            <p><strong>Applied on:</strong> {{ application.created_at|date:"F j, Y" }}</p>
                            <p><strong>Decision made:</strong> {{ application.updated_at|date:"F j, Y" }}</p>
                        </div>

                        {% if application.message %}
                            <div class="application-message">
                                <h4>Your Application Message:</h4>
                                <p>{{ application.message }}</p>
                            </div>
                        {% endif %}

                        <div class="project-link">
                            <a href="{% url 'collaborate:project_detail' application.project.id %}" 
                               class="btn btn-secondary">View Project</a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="no-applications">No rejected applications</p>
        {% endif %}
    </section>

    {% if not pending_applications and not accepted_applications and not rejected_applications %}
        <div class="no-applications-container">
            <p>You haven't applied to any projects yet.</p>
            <a href="{% url 'collaborate:project_list' %}" class="btn btn-primary">Browse Projects</a>
        </div>
    {% endif %}
</div>
{% endblock %}
