{% extends "base.html" %}
{% block title %}{{ project.title }}{% endblock %}

{% block head_extra %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <h1>{{ project.title }}</h1>

    {% if messages %}
    <div class="messages">
      {% for message in messages %}
      <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
        {{ message }}
      </div>
      {% endfor %}
    </div>
    {% endif %}

    <div class="project-info">
      <div class="description">
        <h3>Project Description</h3>
        <p>{{ project.description }}</p>
      </div>

      <div class="requirements">
        <h3>Team Requirements</h3>
        <div class="requirements-grid">
          <div class="requirement-section">
            <h4>Team Size</h4>
            <p>
              Looking for {{ project.team_size|add:"-1" }} more team member{% if project.team_size|add:"-1" != 1 %}s{% endif %}
            </p>
          </div>
        </div>
      </div>

      <div class="project-skills">
        <h3>Required Skills</h3>
        {% if project.required_skills.exists %}
        <ul>
          {% for skill in project.required_skills.all %}
          <li>{{ skill.name }}</li>
          {% endfor %}
        </ul>
        {% else %}
        <p>No specific skills required.</p>
        {% endif %}

        <h3>Critical Skills</h3>
        {% if project.critical_skills.exists %}
        <ul>
          {% for skill in project.critical_skills.all %}
          <li>{{ skill.name }}</li>
          {% endfor %}
        </ul>
        {% else %}
        <p>No critical skills specified.</p>
        {% endif %}
      </div>

      <div class="meta">
        <p><strong>Owner:</strong> {{ project.owner.username }}</p>
        <p><strong>Type:</strong> {{ project.get_type_display }}</p>
        <p><strong>Created:</strong> {{ project.created_at|date:"F j, Y" }}</p>
      </div>
    </div>

    <!-- AI Developer Pairing Buttons for Project Owner -->
    {% if project.owner == user or is_member %}
    <div class="ai-pairing-buttons">
      <h3 class="section-title">Team Management & Analysis Tools</h3>
      <div class="enhanced-btn-grid">
        <!-- Team Formation - Only for project owner -->
        {% if project.owner == user %}
        <a href="{% url 'collaborate:match_users' project.hash %}" class="btn btn-primary enhanced-btn">
          <i class="fas fa-robot"></i>
          <span>Find Matching Users</span>
        </a>
        {% endif %}

        <!-- Team Analysis -->
        <a href="{% url 'collaborate:team_balance' project.hash %}" class="btn btn-success enhanced-btn">
          <i class="fas fa-balance-scale"></i>
          <span>Team Balance Analysis</span>
        </a>

        <!-- Feedback -->
        <a href="{% url 'collaborate:match_feedback' project.hash %}" class="btn btn-secondary enhanced-btn">
          <i class="fas fa-comment"></i>
          <span>Match Feedback</span>
        </a>

        {% if project.owner == user %}
        <!-- Marketplace Publish Button -->
        {% if not project.is_published_to_marketplace %}
        <a href="{% url 'collaborate:publish_to_marketplace' project.hash %}" class="btn btn-success enhanced-btn">
          <i class="fas fa-rocket"></i>
          <span>Publish to Marketplace</span>
        </a>
        {% else %}
        <a href="{% url 'collaborate:marketplace_detail' project.marketplace_post.id %}" class="btn btn-info enhanced-btn">
          <i class="fas fa-store"></i>
          <span>View in Marketplace</span>
        </a>
        {% endif %}

        <!-- Invite Friends Button -->
        <button type="button" class="btn btn-warning enhanced-btn" data-bs-toggle="modal" data-bs-target="#inviteFriendsModal">
          <i class="fas fa-user-plus"></i>
          <span>Invite Friends</span>
        </button>

        <!-- Team Management Button -->
        <button type="button" class="btn btn-info enhanced-btn" data-bs-toggle="modal" data-bs-target="#teamManagementModal">
          <i class="fas fa-users-cog"></i>
          <span>Team Management</span>
        </button>
        {% endif %}
      </div>
    </div>
    {% endif %}

    <!-- Storage Usage Section -->
    <div class="storage-usage-container">
      <label>Project Storage</label>
      <div class="storage-bar">
        <div class="used-bar" style="width: {{ storage_data.used_percent }}%;"></div>
      </div>
      <p class="storage-label">Used {{ storage_data.used_mb|floatformat:1 }}MB of {{ storage_data.limit_mb|floatformat:1 }}MB</p>
      <p>Need more space?<a href="/upgrade-storage" class="upgrade-link"> Upgrade your plan</a></p>
    </div>

  </div>
</section>




<!-- Delete Project Modal -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1" aria-labelledby="deleteProjectModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteProjectModalLabel">Delete Project</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the project "<strong>{{ project.title }}</strong>"?</p>
        <p class="text-danger">This action cannot be undone.</p>
      </div>
      <div class="modal-footer">
        <form method="post" action="{% url 'collaborate:delete_project' project.hash %}">
          {% csrf_token %}
          <button type="submit" class="btn btn-dange">Yes, Delete</button>
        </form>
        <button type="button" class="btn btn-second" data-bs-dismiss="modal">Cancel</button>
      </div>
    </div>
  </div>
</div>

<!-- Invite Friends Modal -->
<div class="modal fade" id="inviteFriendsModal" style="overflow=scroll;" tabindex="-1" aria-labelledby="inviteFriendsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="inviteFriendsModalLabel">Invite Friends to Project</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Hidden CSRF token for invite functionality -->
        <form style="display: none;">
          {% csrf_token %}
        </form>

        <form id="searchForm" class="search-container mb-4" onsubmit="return false;">
          <div class="input-group">
            <span class="input-group-text"><i class="fas fa-search"></i></span>
            <input type="text" id="userSearchInput" class="form-control" placeholder="Search by username, name, or email...">
            <button type="submit" class="btn btn-primary" id="searchUsersBtn">Search</button>
          </div>
          <small class="form-text text-muted">Enter at least 3 characters to search</small>
        </form>

        <div id="searchResults" class="mt-3">
          <!-- Search results will be displayed here -->
          <div class="initial-message text-center text-muted">
            <i class="fas fa-users fa-3x mb-3"></i>
            <p>Search for users to invite to your project</p>
          </div>
        </div>

        <div id="loadingIndicator" style="display: none;" class="text-center my-4 d-none">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Searching for users...</p>
        </div>

        <div id="noResultsMessage" style='display: none;' class="alert alert-info d-none">
          <i class="fas fa-info-circle me-2"></i>
          No users found matching your search criteria.
        </div>

        <div id="errorMessage" style='display: none;' class="alert alert-danger d-none">
          <i class="fas fa-exclamation-circle me-2"></i>
          <span id="errorText">An error occurred while searching for users.</span>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<!-- Team Management Modal -->
<div class="modal fade" id="teamManagementModal" tabindex="-1" aria-labelledby="teamManagementModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="teamManagementModalLabel">Team Management</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="team-members-container">
          <h6 class="mb-3">Current Team Members</h6>

          {% if project.memberships.count > 0 %}
            <div class="list-group" id="teamMembersList">
              {% for membership in project.memberships.all %}
                {% if membership.user != project.owner %}
                  <div class="list-group-item d-flex justify-content-between align-items-center" data-member-id="{{ membership.user.id }}">
                    <div>
                      <h6 class="mb-1">{{ membership.user.username }}</h6>
                      <p class="mb-1">{{ membership.user.first_name }} {{ membership.user.last_name }}</p>
                      <small class="text-muted">{{ membership.user.email }}</small>
                    </div>
                    <div>
                      <button class="btn btn-danger btn-sm remove-member-btn" data-user-id="{{ membership.user.id }}" data-username="{{ membership.user.username }}">
                        <i class="fas fa-user-minus me-1"></i> Remove
                      </button>
                    </div>
                  </div>
                {% endif %}
              {% endfor %}
            </div>

            <div class="alert alert-warning mt-3">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <strong>Warning:</strong> Removing a team member is permanent and cannot be undone. The user will need to reapply or be invited again to rejoin the project.
            </div>
          {% else %}
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              This project doesn't have any team members yet (besides you as the owner).
            </div>
          {% endif %}
        </div>

        <div id="removeConfirmation" class="mt-3 d-none">
          <div class="alert alert-danger">
            <p><strong>Are you sure you want to remove <span id="removeUsername"></span> from the team?</strong></p>
            <p>This action cannot be undone.</p>
            <div class="d-flex justify-content-end mt-2" style="display: flex; align-items: center; justify-content: center; flex-direction: column-reverse; gap: 1vh;">
              <button class="btn btn-secondary me-2" id="cancelRemoveBtn">Cancel</button>
              <button class="btn btn-danger" id="confirmRemoveBtn" data-user-id="">Confirm Remove</button>
            </div>
          </div>
        </div>
      </div>
      <br>
      <br>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
</div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);
});
</script>

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Set up modal functionality for AI pairing features
    const modalButtons = document.querySelectorAll('.open-modal');

    // Reset search form when invite friends modal is closed
    const inviteFriendsModal = document.getElementById('inviteFriendsModal');
    if (inviteFriendsModal) {
      // Focus the search input when the modal is shown
      inviteFriendsModal.addEventListener('shown.bs.modal', function() {
        const userSearchInput = document.getElementById('userSearchInput');
        if (userSearchInput) {
          userSearchInput.focus();
        }
      });

      // Reset the form when the modal is closed
      inviteFriendsModal.addEventListener('hidden.bs.modal', function() {
        // Reset search input
        const userSearchInput = document.getElementById('userSearchInput');
        if (userSearchInput) {
          userSearchInput.value = '';
        }

        // Reset search results
        const searchResults = document.getElementById('searchResults');
        if (searchResults) {
          searchResults.innerHTML = `
            <div class="initial-message text-center text-muted">
              <i class="fas fa-users fa-3x mb-3"></i>
              <p>Search for users to invite to your project</p>
            </div>
          `;
        }

        // Hide all messages
        const loadingIndicator = document.getElementById('loadingIndicator');
        const noResultsMessage = document.getElementById('noResultsMessage');
        const errorMessage = document.getElementById('errorMessage');

        if (loadingIndicator) loadingIndicator.classList.add('d-none');
        if (noResultsMessage) noResultsMessage.classList.add('d-none');
        if (errorMessage) errorMessage.classList.add('d-none');

        console.log('Modal closed, form reset');
      });
    }

    modalButtons.forEach(button => {
      button.addEventListener('click', function() {
        const url = this.getAttribute('data-url');
        const targetModalId = this.getAttribute('data-target');
        const modal = document.getElementById(targetModalId);

        // Initialize Bootstrap modal
        const modalInstance = new bootstrap.Modal(modal);

        // Show modal with loading spinner
        modalInstance.show();

        // Load content via fetch
        fetch(url)
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok: ' + response.status);
            }
            return response.text();
          })
          .then(html => {
            // Hide loading spinner
            modal.querySelector('.loading-spinner').style.display = 'none';

            // Fill content
            modal.querySelector('.modal-content-container').innerHTML = html;

            // Initialize any form handlers within the modal
            initializeModalForms(modal);
          })
          .catch(error => {
            modal.querySelector('.modal-content-container').innerHTML =
              `<div class="alert alert-danger">Error loading content: ${error.message}</div>`;
            modal.querySelector('.loading-spinner').style.display = 'none';
          });
      });
    });

    function initializeModalForms(modal) {
      const forms = modal.querySelectorAll('form');

      forms.forEach(form => {
        form.addEventListener('submit', function(e) {
          e.preventDefault();

          const formData = new FormData(form);

          fetch(form.action, {
            method: form.method,
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => {
            if (!response.ok) {
              throw new Error('Form submission failed: ' + response.status);
            }
            return response.text();
          })
          .then(html => {
            modal.querySelector('.modal-content-container').innerHTML = html;
          })
          .catch(error => {
            modal.querySelector('.modal-content-container').innerHTML =
              `<div class="alert alert-danger">Error submitting form: ${error.message}</div>`;
          });
        });
      });
    }

    // User search and invite functionality
    const userSearchInput = document.getElementById('userSearchInput');
    const searchUsersBtn = document.getElementById('searchUsersBtn');
    const searchResults = document.getElementById('searchResults');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const noResultsMessage = document.getElementById('noResultsMessage');
    const projectId = {{ project.id }};
    const projectHash = "{{ project.hash }}";

    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
      searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        try {
          // Clear any previous error messages
          const errorMessage = document.getElementById('errorMessage');
          if (errorMessage) {
            errorMessage.classList.add('d-none');
          }

          const query = userSearchInput.value.trim();
          console.log('Search button clicked with query:', query);

          if (query.length < 3) {
            alert('Please enter at least 3 characters to search');
            return;
          }

        // Clear previous results
        searchResults.innerHTML = `
          <div class="initial-message text-center text-muted d-none">
            <i class="fas fa-users fa-3x mb-3"></i>
            <p>Search for users to invite to your project</p>
          </div>
        `;

        // Hide all messages and show loading indicator
        document.getElementById('errorMessage').classList.add('d-none');
        noResultsMessage.classList.add('d-none');
        loadingIndicator.classList.remove('d-none');

        // Fetch search results
        console.log('Sending search request for:', query);

        // Create a URL with the search query
        const searchUrl = new URL(`${window.location.origin}/collaborate/project/${projectHash}/search-users/`);
        searchUrl.searchParams.append('q', query);

        console.log('Search URL:', searchUrl.toString());

        fetch(searchUrl.toString(), {
          method: 'GET',
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          },
          credentials: 'same-origin'
        })
          .then(response => {
            console.log('Search response status:', response.status);
            if (!response.ok) {
              throw new Error('Search failed: ' + response.status);
            }
            return response.json();
          })
          .then(data => {
            console.log('Search results:', data); // Debug log

            // Hide error message if it was shown
            document.getElementById('errorMessage').classList.add('d-none');

            // Hide initial message
            const initialMessage = searchResults.querySelector('.initial-message');
            if (initialMessage) {
              initialMessage.classList.add('d-none');
            }

            // Make sure to hide loading indicator and error message
            loadingIndicator.classList.add('d-none');
            document.getElementById('errorMessage').classList.add('d-none');

            // Display results
            if (data && data.users && data.users.length > 0) {
              // Hide no results message
              noResultsMessage.classList.add('d-none');

              const resultsHtml = `
                <div class="list-group">
                  ${data.users.map(user => `
                    <div class="list-group-item">
                      <div class="d-flex justify-content-between align-items-center">
                        <div>
                          <h5 class="mb-1">${user.username}</h5>
                          <p class="mb-1">${user.full_name || ''}</p>
                          <small>${user.email}</small>
                        </div>
                        <div class="text-end" style="display: flex; gap:2vw; justify-content: center; align-items: flex-end; ">
                          <div class="mb-2">
                            <span class="badge ${user.matching_skills_count > 0 ?
                              (user.matching_skills_count === user.required_skills_count ? 'bg-success' : 'bg-warning') :
                              'bg-secondary'}">
                              ${user.matching_skills_count}/${user.required_skills_count} matching skills
                            </span>
                          </div>
                          <button class="btn btn-primary btn-sm invite-user-btn"
                                  data-user-id="${user.id}"
                                  data-project-id="${projectId}">
                            <i class="fas fa-user-plus me-1"></i> Invite
                          </button>
                        </div>
                      </div>
                      <div class="mt-2 skills-container">
                        ${user.matching_skills.length > 0 ? `
                          <div>
                            <small><strong>Matching skills:</strong>
                              ${user.matching_skills.map(skill =>
                                `<span class="badge bg-success me-1">${skill}</span>`
                              ).join('')}
                            </small>
                          </div>
                        ` : ''}
                        ${user.missing_skills && user.missing_skills.length > 0 ? `
                          <div class="mt-1">
                            <small><strong>Missing skills:</strong>
                              ${user.missing_skills.map(skill =>
                                `<span class="badge bg-secondary me-1">${skill}</span>`
                              ).join('')}
                            </small>
                          </div>
                        ` : ''}
                      </div>
                    </div>
                  `).join('')}
                </div>
              `;
              searchResults.innerHTML = resultsHtml;

              // Add event listeners to invite buttons
              document.querySelectorAll('.invite-user-btn').forEach(button => {
                button.addEventListener('click', function() {
                  const userId = this.getAttribute('data-user-id');
                  inviteUser(userId, this);
                });
              });
            } else if (data && data.status === 'error') {
              // Show error message
              const errorMessage = document.getElementById('errorMessage');
              const errorText = document.getElementById('errorText');
              errorText.textContent = data.message || 'An error occurred while searching for users.';
              errorMessage.classList.remove('d-none');

              // Hide no results message
              noResultsMessage.classList.add('d-none');

              // Clear previous results
              searchResults.innerHTML = '';

              // Make sure loading indicator is hidden
              loadingIndicator.classList.add('d-none');
            } else {
              // Clear previous results
              searchResults.innerHTML = '';

              // Show no results message
              noResultsMessage.classList.remove('d-none');

              // Hide error message
              document.getElementById('errorMessage').classList.add('d-none');

              // Make sure loading indicator is hidden
              loadingIndicator.classList.add('d-none');
            }
          })
          .catch(error => {
            console.error('Search error:', error); // Debug log

            // Make sure loading indicator is hidden
            loadingIndicator.classList.add('d-none');

            // Show error message
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = `Error: ${error.message}`;
            errorMessage.classList.remove('d-none');

            // Hide other messages
            noResultsMessage.classList.add('d-none');
            const initialMessage = searchResults.querySelector('.initial-message');
            if (initialMessage) {
              initialMessage.classList.add('d-none');
            }

            // Clear previous results
            searchResults.innerHTML = '';
          });
        } catch (error) {
          console.error('Unexpected error in search function:', error);

          // Hide loading indicator
          loadingIndicator.classList.add('d-none');

          // Show error message
          const errorMessage = document.getElementById('errorMessage');
          const errorText = document.getElementById('errorText');
          errorText.textContent = `Unexpected error: ${error.message}`;
          errorMessage.classList.remove('d-none');

          // Hide other messages
          noResultsMessage.classList.add('d-none');
          const initialMessage = searchResults.querySelector('.initial-message');
          if (initialMessage) {
            initialMessage.classList.add('d-none');
          }
        }
      });

      // The form's submit event will handle Enter key presses automatically
    }

    // Function to get CSRF token with multiple fallback methods
    function getCSRFToken() {
      // Method 1: From meta tag
      let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

      // Method 2: From cookie (Django's default cookie name)
      if (!token) {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'csrftoken') {
            token = decodeURIComponent(value);
            break;
          }
        }
      }

      // Method 3: From hidden input (if exists)
      if (!token) {
        const hiddenInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
        if (hiddenInput) {
          token = hiddenInput.value;
        }
      }

      // Method 4: Try to get from Django's CSRF cookie function if available
      if (!token && typeof getCookie === 'function') {
        token = getCookie('csrftoken');
      }

      return token;
    }

    // Django's standard getCookie function
    function getCookie(name) {
      let cookieValue = null;
      if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
          const cookie = cookies[i].trim();
          if (cookie.substring(0, name.length + 1) === (name + '=')) {
            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
            break;
          }
        }
      }
      return cookieValue;
    }

    // Function to invite a user
    function inviteUser(userId, button) {
      // Get the CSRF token using multiple methods
      const csrfToken = getCSRFToken();

      if (!csrfToken) {
        console.error('CSRF token not found');
        alert('Error: CSRF token not found. Please refresh the page and try again.');
        return;
      }

      console.log('CSRF Token found:', csrfToken ? 'Yes' : 'No');
      console.log('CSRF Token value:', csrfToken);

      // Disable the button and show loading state
      button.disabled = true;
      button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Inviting...';

      console.log(`Sending invitation to user ${userId} for project ${projectId}`);

      // Send the invitation
      fetch(`/collaborate/project/${projectHash}/invite/${userId}/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
      })
      .then(response => {
        console.log('Invitation response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
          // Try to get error details from response
          return response.text().then(text => {
            console.error('Error response body:', text);
            throw new Error(`Invitation failed: ${response.status} - ${text}`);
          });
        }
        return response.json();
      })
      .then(data => {
        console.log('Invitation response data:', data);
        // Update the button to show success
        button.classList.remove('btn-primary');
        button.classList.add('btn-success');
        button.innerHTML = '<i class="fas fa-check me-1"></i> Invited';
        button.disabled = true;
      })
      .catch(error => {
        console.error('Invitation error:', error);
        // Reset the button and show error
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-user-plus me-1"></i> Invite';
        alert('Error sending invitation: ' + error.message);
      });
    }

    // Team Management functionality
    const removeMemberBtns = document.querySelectorAll('.remove-member-btn');
    const removeConfirmation = document.getElementById('removeConfirmation');
    const removeUsername = document.getElementById('removeUsername');
    const cancelRemoveBtn = document.getElementById('cancelRemoveBtn');
    const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');

    if (removeMemberBtns.length > 0) {
      // Add event listeners to remove buttons
      removeMemberBtns.forEach(button => {
        button.addEventListener('click', function() {
          const userId = this.getAttribute('data-user-id');
          const username = this.getAttribute('data-username');

          // Show confirmation dialog
          removeUsername.textContent = username;
          confirmRemoveBtn.setAttribute('data-user-id', userId);
          removeConfirmation.classList.remove('d-none');

          // Scroll to the confirmation dialog
          removeConfirmation.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        });
      });

      // Cancel button event listener
      cancelRemoveBtn.addEventListener('click', function() {
        removeConfirmation.classList.add('d-none');
      });

      // Confirm button event listener
      confirmRemoveBtn.addEventListener('click', function() {
        const userId = this.getAttribute('data-user-id');
        removeMember(userId);
      });
    }

    // Function to remove a team member
    function removeMember(userId) {
      // Get the CSRF token using the robust method
      const csrfToken = getCSRFToken();

      // Disable the confirm button and show loading state
      confirmRemoveBtn.disabled = true;
      confirmRemoveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Removing...';

      // Send the remove request
      fetch(`/collaborate/project/${projectHash}/remove-member/${userId}/`, {
        method: 'POST',
        headers: {
          'X-CSRFToken': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Removal failed: ' + response.status);
        }
        return response.json();
      })
      .then(data => {
        // Remove the member from the list
        const memberItem = document.querySelector(`.list-group-item[data-member-id="${userId}"]`);
        if (memberItem) {
          memberItem.remove();
        }

        // Hide the confirmation dialog
        removeConfirmation.classList.add('d-none');

        // Show success message
        alert(data.message || 'Team member removed successfully.');

        // If no more members, show the empty message
        if (document.querySelectorAll('.list-group-item').length === 0) {
          document.getElementById('teamMembersList').innerHTML = `
            <div class="alert alert-info">
              <i class="fas fa-info-circle me-2"></i>
              This project doesn't have any team members yet (besides you as the owner).
            </div>
          `;
        }
      })
      .catch(error => {
        // Reset the button and show error
        confirmRemoveBtn.disabled = false;
        confirmRemoveBtn.innerHTML = 'Confirm Remove';
        alert('Error removing team member: ' + error.message);
      });
    }
  });
</script>
<style>
.storage-usage-container {
  margin: 20px 0;
}
.storage-bar {
  width: 100%;
  height: 20px;
  background: #eee;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}
.used-bar {
  height: 100%;
  background: linear-gradient(to right, #4ade80, #22c55e);
  transition: width 0.4s ease;
}
.storage-label {
  font-size: 14px;
  margin-top: 5px;
  color: white;
}
.upgrade-link {
  font-size: 13px;
  color: #2563eb;
  text-decoration: underline;
  cursor: pointer;
}

.project-detail {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.project-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.requirements-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.requirement-section {
  flex: 1 1 100%;
  padding: 10px;
  background-color: #222222;
  color: black;
  border-radius: 8px;
}

.requirement-section h4 {
  margin-bottom: 5px;
}

.project-skills ul {
  list-style: none;
  padding-left: 0;
}

.project-skills li {
  background: #e0f2fe;
  color: black;
  width: fit-content;
  padding: 6px 10px;
  margin: 4px 0;
  border-radius: 5px;
}

.meta {
  margin-top: 30px;
  font-size: 14px;
  color: white;
}

.ai-pairing-buttons {
  margin-top: 40px;
}

.ai-pairing-buttons .btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.application-status {
  margin-top: 40px;
}

.application-form textarea {
  width: 100%;
  border: 1px solid #d1d5db;
  border-radius: 5px;
  padding: 10px;
}

.status-card {
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  font-size: 15px;
}

.status-icon {
  font-size: 1.4rem;
  margin-right: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  color: white;
  background-color: #3b82f6;
  border: none;
  cursor: pointer;
}

.btn:hover {
  background-color: #2563eb;
}

.btn-danger,
.btn-dange{
  background-color: #dc2626;
}

.btn-danger:hover,
.btn-dange:hover{
  background-color: #b91c1c;
}

.upgrade-link {
  display: inline-block;
  margin-top: 10px;
  font-size: 14px;
  color: green;
  text-decoration: underline;
}

.btn-prime,
.btn-danger,
.btn-dange,
.btn-second{
  margin: 0px 30px;
}

.css12,
.modal-footer{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  align-self: center;
  text-align: center;
}

/* Enhanced Button Styling Overrides */
.ai-pairing-buttons {
  margin: 40px 0 !important;
  padding: 25px !important;
  background: rgba(40, 40, 40, 0.3) !important;
  border-radius: 15px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
}

.section-title {
  color: var(--color-border) !important;
  margin-bottom: 20px !important;
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  text-align: center !important;
}

.enhanced-btn-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 15px !important;
  margin-top: 20px !important;
}

.enhanced-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 10px !important;
  padding: 12px 20px !important;
  min-height: 50px !important;
  font-weight: 600 !important;
  text-transform: none !important;
  letter-spacing: 0.5px !important;
}

.enhanced-btn i {
  font-size: 1.1rem !important;
}

.enhanced-btn span {
  font-size: 0.95rem !important;
}

.enhanced-action-buttons {
  display: flex !important;
  gap: 20px !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
  margin: 30px 0 !important;
}

.primary-action {
  min-width: 180px !important;
  font-size: 1.1rem !important;
  padding: 15px 25px !important;
}

.danger-action {
  min-width: 160px !important;
}

.apply-btn {
  margin-top: 20px !important;
  min-width: 200px !important;
  padding: 15px 25px !important;
  font-size: 1.1rem !important;
}

.application-form {
  background: rgba(40, 40, 40, 0.4) !important;
  padding: 25px !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
}

.application-form textarea {
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 8px !important;
  padding: 12px !important;
  background: rgba(40, 40, 40, 0.6) !important;
  color: #ffffff !important;
}

.application-form textarea:focus {
  outline: none !important;
  border-color: var(--color-border) !important;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3) !important;
}

.requirement-section {
  background: rgba(40, 40, 40, 0.6) !important;
  color: #ffffff !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  padding: 15px !important;
}

.requirement-section h4 {
  color: var(--color-border) !important;
  margin-bottom: 8px !important;
}

.project-skills li {
  background: rgba(192, 255, 107, 0.2) !important;
  color: var(--color-border) !important;
  padding: 8px 12px !important;
  margin: 6px 4px 6px 0 !important;
  border-radius: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  font-weight: 500 !important;
  display: inline-block !important;
}

.meta {
  background: rgba(40, 40, 40, 0.4) !important;
  padding: 15px !important;
  border-radius: 10px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
}

/* Enhanced Invite Friends Modal Styling */
#inviteFriendsModal .modal-dialog {
  max-width: 900px !important;
}

#inviteFriendsModal .modal-content {
  background: linear-gradient(135deg, rgba(40, 40, 40, 0.95), rgba(30, 30, 30, 0.98)) !important;
  border: 2px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 20px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(192, 255, 107, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  width: 550px;
}

#inviteFriendsModal .modal-header {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(192, 255, 107, 0.05)) !important;
  border-bottom: 2px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 18px 18px 0 0 !important;
  padding: 20px 25px !important;
}

#inviteFriendsModal .modal-title {
  color: var(--color-border) !important;
  font-size: 1.4rem !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

#inviteFriendsModal .modal-title::before {
  content: "👥" !important;
  font-size: 1.2em !important;
}

#inviteFriendsModal .btn-close {
  background: rgba(192, 255, 107, 0.2) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 50% !important;
  width: 35px !important;
  height: 35px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
}

#inviteFriendsModal .btn-close:hover {
  background: rgba(192, 255, 107, 0.4) !important;
  transform: rotate(90deg) !important;
}

#inviteFriendsModal .modal-body {
  padding: 25px !important;
  color: #ffffff !important;
}

/* Enhanced Search Form */
#inviteFriendsModal .search-container {
  background: rgba(40, 40, 40, 0.6) !important;
  padding: 20px !important;
  border-radius: 15px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  margin-bottom: 25px !important;
}

#inviteFriendsModal .input-group {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

#inviteFriendsModal .input-group-text {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.2), rgba(192, 255, 107, 0.1)) !important;
  border: none !important;
  color: var(--color-border) !important;
  font-size: 1.1rem !important;
  padding: 12px 15px !important;
}

#inviteFriendsModal #userSearchInput {
  background: rgba(40, 40, 40, 0.8) !important;
  border: none !important;
  color: #ffffff !important;
  font-size: 1rem !important;
  padding: 12px 15px !important;
  transition: all 0.3s ease !important;
}

#inviteFriendsModal #userSearchInput:focus {
  background: rgba(40, 40, 40, 0.9) !important;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.3) !important;
  outline: none !important;
}

#inviteFriendsModal #userSearchInput::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

#inviteFriendsModal #searchUsersBtn {
  background: linear-gradient(135deg, var(--color-border), rgba(192, 255, 107, 0.8)) !important;
  border: none !important;
  color: #000000 !important;
  font-weight: 600 !important;
  padding: 12px 20px !important;
  transition: all 0.3s ease !important;
}

#inviteFriendsModal #searchUsersBtn:hover {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.9), var(--color-border)) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.4) !important;
}

#inviteFriendsModal .form-text {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.9rem !important;
  margin-top: 10px !important;
}

/* Enhanced Search Results */
#inviteFriendsModal #searchResults {
  max-height: 400px !important;
  overflow-y: auto !important;
  padding-right: 5px !important;
}

#inviteFriendsModal #searchResults::-webkit-scrollbar {
  width: 8px !important;
}

#inviteFriendsModal #searchResults::-webkit-scrollbar-track {
  background: rgba(40, 40, 40, 0.3) !important;
  border-radius: 4px !important;
}

#inviteFriendsModal #searchResults::-webkit-scrollbar-thumb {
  background: rgba(192, 255, 107, 0.5) !important;
  border-radius: 4px !important;
}

#inviteFriendsModal #searchResults::-webkit-scrollbar-thumb:hover {
  background: rgba(192, 255, 107, 0.7) !important;
}

#inviteFriendsModal .list-group-item {
  background: rgba(40, 40, 40, 0.6) !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 12px !important;
  margin-bottom: 15px !important;
  padding: 20px !important;
  transition: all 0.3s ease !important;
  color: #ffffff !important;
}

#inviteFriendsModal .list-group-item:hover {
  background: rgba(40, 40, 40, 0.8) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

#inviteFriendsModal .list-group-item h5 {
  color: var(--color-border) !important;
  font-weight: 600 !important;
  margin-bottom: 5px !important;
}

#inviteFriendsModal .list-group-item p {
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 3px !important;
}

#inviteFriendsModal .list-group-item small {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Enhanced Skills Container */
#inviteFriendsModal .skills-container {
  background: rgba(30, 30, 30, 0.4) !important;
  border-radius: 10px !important;
  padding: 12px !important;
  margin-top: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
}

#inviteFriendsModal .skills-container small {
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
}

/* Enhanced Badges */
#inviteFriendsModal .badge {
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-weight: 500 !important;
  font-size: 0.85rem !important;
}

#inviteFriendsModal .badge.bg-success {
  background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  color: #ffffff !important;
}

#inviteFriendsModal .badge.bg-warning {
  background: linear-gradient(135deg, #FFC107, #e6ac00) !important;
  color: #000000 !important;
}

#inviteFriendsModal .badge.bg-secondary {
  background: linear-gradient(135deg, #6c757d, #5a6268) !important;
  color: #ffffff !important;
}

/* Enhanced Invite Button */
#inviteFriendsModal .invite-user-btn {
  background: linear-gradient(135deg, var(--color-border), rgba(192, 255, 107, 0.8)) !important;
  border: none !important;
  color: #000000 !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
  min-width: 100px !important;
}

#inviteFriendsModal .invite-user-btn:hover {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.9), var(--color-border)) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.4) !important;
}

#inviteFriendsModal .invite-user-btn:disabled {
  opacity: 0.8 !important;
  transform: none !important;
}

/* Enhanced Initial Message */
#inviteFriendsModal .initial-message {
  background: rgba(40, 40, 40, 0.4) !important;
  border: 2px dashed rgba(192, 255, 107, 0.3) !important;
  border-radius: 15px !important;
  padding: 40px 20px !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

#inviteFriendsModal .initial-message i {
  color: rgba(192, 255, 107, 0.6) !important;
  margin-bottom: 15px !important;
}

/* Enhanced Loading Indicator */
#inviteFriendsModal .spinner-border {
  color: var(--color-border) !important;
  width: 3rem !important;
  height: 3rem !important;
}

/* Enhanced Alerts */
#inviteFriendsModal .alert {
  border-radius: 12px !important;
  border: none !important;
  padding: 15px 20px !important;
  margin: 15px 0 !important;
}

#inviteFriendsModal .alert-info {
  background: rgba(33, 150, 243, 0.2) !important;
  color: #ffffff !important;
  border-left: 4px solid #2196F3 !important;
}

#inviteFriendsModal .alert-danger {
  background: rgba(244, 67, 54, 0.2) !important;
  color: #ffffff !important;
  border-left: 4px solid #f44336 !important;
}

/* Enhanced Modal Footer */
#inviteFriendsModal .modal-footer {
  background: rgba(40, 40, 40, 0.3) !important;
  border-top: 1px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 0 0 18px 18px !important;
  padding: 20px 25px !important;
}

#inviteFriendsModal .modal-footer .btn-secondary {
  background: rgba(108, 117, 125, 0.2) !important;
  border: 1px solid rgba(108, 117, 125, 0.3) !important;
  color: #ffffff !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
}

#inviteFriendsModal .modal-footer .btn-secondary:hover {
  background: rgba(108, 117, 125, 0.4) !important;
  transform: translateY(-2px) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-btn-grid {
    grid-template-columns: 1fr !important;
  }

  .enhanced-action-buttons {
    flex-direction: column !important;
    align-items: center !important;
  }

  .enhanced-btn {
    min-width: 100% !important;
  }

  #inviteFriendsModal .modal-dialog {
    max-width: 95% !important;
    margin: 10px !important;
  }

  #inviteFriendsModal .modal-body {
    padding: 15px !important;
  }

  #inviteFriendsModal .search-container {
    padding: 15px !important;
  }
}
</style>
<!-- Enhanced Team Management Modal Styling -->
<style>
/* Team Management Modal - Enhanced Design */
#teamManagementModal .modal-dialog {
  max-width: 900px !important;
}

#teamManagementModal .modal-content {
  background: linear-gradient(135deg, rgba(40, 40, 40, 0.95), rgba(30, 30, 30, 0.98)) !important;
  border: 2px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 20px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(192, 255, 107, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  color: #ffffff !important;
  width: 650px;
}

#teamManagementModal .modal-header {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(192, 255, 107, 0.05)) !important;
  border-bottom: 2px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 18px 18px 0 0 !important;
  padding: 20px 25px !important;
}

#teamManagementModal .modal-title {
  color: var(--color-border) !important;
  font-size: 1.4rem !important;
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

#teamManagementModal .modal-title::before {
  content: "👥" !important;
  font-size: 1.2em !important;
}

#teamManagementModal .btn-close {
  background: rgba(192, 255, 107, 0.2) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 50% !important;
  width: 35px !important;
  height: 35px !important;
  opacity: 1 !important;
  transition: all 0.3s ease !important;
}

#teamManagementModal .btn-close:hover {
  background: rgba(192, 255, 107, 0.4) !important;
  transform: rotate(90deg) !important;
}

#teamManagementModal .modal-body {
  padding: 25px !important;
  color: #ffffff !important;
}

/* Enhanced Team Members Container */
#teamManagementModal .team-members-container {
  background: rgba(40, 40, 40, 0.6) !important;
  padding: 20px !important;
  border-radius: 15px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  margin-bottom: 20px !important;
}

#teamManagementModal .team-members-container h6 {
  color: var(--color-border) !important;
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  margin-bottom: 20px !important;
  text-align: center !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Enhanced Team Members List */
#teamMembersList .list-group-item {
  background: rgba(40, 40, 40, 0.6) !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 12px !important;
  margin-bottom: 15px !important;
  padding: 20px !important;
  transition: all 0.3s ease !important;
  color: #ffffff !important;
  position: relative !important;
  overflow: hidden !important;
}

#teamMembersList .list-group-item::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(45deg, transparent, rgba(192, 255, 107, 0.05), transparent) !important;
  transform: translateX(-100%) !important;
  transition: transform 0.6s ease !important;
}

#teamMembersList .list-group-item:hover::before {
  transform: translateX(100%) !important;
}

#teamMembersList .list-group-item:hover {
  background: rgba(40, 40, 40, 0.8) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
  transform: translateY(-3px) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(192, 255, 107, 0.2) !important;
}

#teamMembersList h6 {
  color: var(--color-border) !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  font-size: 1.1rem !important;
}

#teamMembersList p {
  color: rgba(255, 255, 255, 0.9) !important;
  margin-bottom: 5px !important;
  font-size: 0.95rem !important;
}

#teamMembersList small {
  color: rgba(255, 255, 255, 0.7) !important;
  font-size: 0.85rem !important;
}

/* Enhanced Remove Member Button */
.remove-member-btn {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  border: none !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
  min-width: 120px !important;
  font-size: 0.875rem !important;
  position: relative !important;
  overflow: hidden !important;
}

.remove-member-btn::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.5s ease !important;
}

.remove-member-btn:hover::before {
  left: 100% !important;
}

.remove-member-btn:hover {
  background: linear-gradient(135deg, #bb2d3b, #a02834) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4) !important;
}

.remove-member-btn i {
  margin-right: 6px !important;
  font-size: 0.9rem !important;
}

/* Enhanced Alerts */
#teamManagementModal .alert {
  border-radius: 12px !important;
  border: none !important;
  padding: 15px 20px !important;
  margin: 15px 0 !important;
  position: relative !important;
  overflow: hidden !important;
}

#teamManagementModal .alert::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 4px !important;
  height: 100% !important;
  background: currentColor !important;
}

#teamManagementModal .alert-warning {
  background: rgba(255, 193, 7, 0.2) !important;
  color: #ffc107 !important;
  border-left: 4px solid #ffc107 !important;
  gap: 20px;
}

#teamManagementModal .alert-info {
  background: rgba(33, 150, 243, 0.2) !important;
  color: #2196F3 !important;
  border-left: 4px solid #2196F3 !important;
}

#teamManagementModal .alert-danger {
  background: rgba(244, 67, 54, 0.2) !important;
  color: #f44336 !important;
  border-left: 4px solid #f44336 !important;
}

/* Enhanced Confirmation Section */
#removeConfirmation {
  background: rgba(30, 30, 30, 0.8) !important;
  border-radius: 15px !important;
  padding: 20px !important;
  border: 1px solid rgba(244, 67, 54, 0.3) !important;
  animation: slideInUp 0.3s ease-out !important;
}

#removeConfirmation .alert-danger {
  background: rgba(244, 67, 54, 0.3) !important;
  border: 1px solid rgba(244, 67, 54, 0.5) !important;
  margin-bottom: 0 !important;
}

#removeConfirmation button {
  min-width: 120px !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;
}

#cancelRemoveBtn {
  background: rgba(108, 117, 125, 0.2) !important;
  border: 1px solid rgba(108, 117, 125, 0.3) !important;
  color: #ffffff !important;
}

#cancelRemoveBtn:hover {
  background: rgba(108, 117, 125, 0.4) !important;
  transform: translateY(-2px) !important;
}

#confirmRemoveBtn {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  border: none !important;
  color: #ffffff !important;
}

#confirmRemoveBtn:hover {
  background: linear-gradient(135deg, #bb2d3b, #a02834) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4) !important;
}

/* Enhanced Modal Footer */
#teamManagementModal .modal-footer {
  background: rgba(40, 40, 40, 0.3) !important;
  border-top: 1px solid rgba(192, 255, 107, 0.2) !important;
  border-radius: 0 0 18px 18px !important;
  padding: 20px 25px !important;
}

#teamManagementModal .modal-footer .btn-secondary {
  background: rgba(108, 117, 125, 0.2) !important;
  border: 1px solid rgba(108, 117, 125, 0.3) !important;
  color: #ffffff !important;
  padding: 10px 20px !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
}

#teamManagementModal .modal-footer .btn-secondary:hover {
  background: rgba(108, 117, 125, 0.4) !important;
  transform: translateY(-2px) !important;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design for Team Management Modal */
@media (max-width: 768px) {
  #teamManagementModal .modal-dialog {
    max-width: 95% !important;
    margin: 10px !important;
  }

  #teamManagementModal .modal-body {
    padding: 15px !important;
  }

  #teamManagementModal .team-members-container {
    padding: 15px !important;
  }

  #teamMembersList .list-group-item {
    padding: 15px !important;
  }

  .remove-member-btn {
    min-width: 100px !important;
    font-size: 0.8rem !important;
  }
}
</style>
{% endblock %}
{% endblock %}