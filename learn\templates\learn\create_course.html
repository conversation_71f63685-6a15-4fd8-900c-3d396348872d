{% extends "base.html" %}
{% load static %}
{% block title %}Create Course - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="course-form-container fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">📚 Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active" aria-current="page">
        Create Course
      </li>
    </ol>
  </nav>

  <!-- Form Header -->
  <div class="admin-form-header">
    <h1>🎓 Create New Course</h1>
    <p>Build an amazing learning experience for developers</p>
  </div>

  <!-- Course Creation Form -->
  <form method="POST" enctype="multipart/form-data" class="course-creation-form">
    {% csrf_token %}

    <!-- Display form errors if any -->
    {% if form.errors %}
    <div class="form-errors">
      <h4>Please correct the following errors:</h4>
      <ul>
        {% for field, errors in form.errors.items %}
          {% for error in errors %}
            <li>{{ field|title }}: {{ error }}</li>
          {% endfor %}
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- Basic Information Section -->
    <div class="form-section">
      <h3 class="form-section-title">Basic Information</h3>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.name.id_for_label }}" class="required">Course Title</label>
          {{ form.name }}
          {% if form.name.help_text %}
            <div class="form-help-text">{{ form.name.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.description.id_for_label }}" class="required">Course Description</label>
          {{ form.description }}
          {% if form.description.help_text %}
            <div class="form-help-text">{{ form.description.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.image.id_for_label }}">Course Image</label>
          {{ form.image }}
          {% if form.image.help_text %}
            <div class="form-help-text">{{ form.image.help_text }}</div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Course Details Section -->
    <div class="form-section">
      <h3 class="form-section-title">Course Details</h3>

      <div class="form-row">
        <div class="form-group">
          <label for="{{ form.difficulty.id_for_label }}" class="required">Difficulty Level</label>
          {{ form.difficulty }}
          {% if form.difficulty.help_text %}
            <div class="form-help-text">{{ form.difficulty.help_text }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.estimated_duration.id_for_label }}" class="required">Estimated Duration</label>
          {{ form.estimated_duration }}
          {% if form.estimated_duration.help_text %}
            <div class="form-help-text">{{ form.estimated_duration.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.status.id_for_label }}" class="required">Course Status</label>
          {{ form.status }}
          {% if form.status.help_text %}
            <div class="form-help-text">{{ form.status.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.prerequisites.id_for_label }}">Prerequisites</label>
          {{ form.prerequisites }}
          {% if form.prerequisites.help_text %}
            <div class="form-help-text">{{ form.prerequisites.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.learning_objectives.id_for_label }}">Learning Objectives</label>
          {{ form.learning_objectives }}
          {% if form.learning_objectives.help_text %}
            <div class="form-help-text">{{ form.learning_objectives.help_text }}</div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <a href="{% url 'learn:course_list' %}" class="btn btn-secondary">
        <span>❌</span> Cancel
      </a>
      <button type="submit" class="btn btn-primary">
        <span>✅</span> Create Course
      </button>
    </div>
  </form>
</div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 30,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.15,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.05,
            "sync": false
          }
        },
        "size": {
          "value": 2,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.08,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 0.8,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for form
    setTimeout(() => {
      document.querySelector('.course-form-container').classList.add('visible');
    }, 200);
  });
</script>
{% endblock %}
