{% extends "base.html" %}
{% load static %}
{% block title %}Learn - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Welcome Header -->
    <div class="welcome-header">
      {% if user.is_authenticated %}
        <h1>Welcome back, {{ user.username }}! 👋</h1>
        <p class="welcome-subtitle">Continue your learning journey with our courses</p>
      {% else %}
        <h1>Discover Amazing Courses</h1>
        <p class="welcome-subtitle">Start your learning journey today</p>
      {% endif %}
    </div>

    <!-- Course Stats -->
    <div class="course-stats">
      <div class="stat-item">
        <span class="stat-number">{{ courses|length }}</span>
        <span class="stat-label">Available Courses</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">∞</span>
        <span class="stat-label">Learning Opportunities</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">24/7</span>
        <span class="stat-label">Access</span>
      </div>
    </div>

    <!-- Course Creation Controls -->
    {% if can_create_courses %}
    <div class="admin-controls">
      {% if is_superuser %}
        <a href="{% url 'learn:create_course' %}" class="btn btn-success">
          <i class="icon">➕</i> Create New Course
        </a>
        <a href="{% url 'learn:manage_course_approvals' %}" class="btn btn-primary">
          <i class="icon">📋</i> Manage Approvals
        </a>
      {% elif is_verified_mentor %}
        <a href="{% url 'learn:create_course' %}" class="btn btn-success">
          <i class="icon">🎓</i> Create Course as Mentor
        </a>
        <div class="mentor-info">
          <small>✅ You're a verified mentor! Create courses to share your expertise.</small>
        </div>
      {% endif %}
    </div>
    {% endif %}

    <!-- AI Learning Dashboard Widget -->
    {% if user.is_authenticated and ai_enabled %}
    <div class="ai-dashboard-widget">
      <div class="dashboard-header">
        <h3>🤖 Your AI Learning Progress</h3>
        <a href="{% url 'learn:unified_dashboard' %}" class="dashboard-btn">
          📊 Full Dashboard
        </a>
      </div>
      <div class="ai-stats">
        <div class="ai-stat">
          <span class="stat-value">{{ progress_analytics.completion_rate }}%</span>
          <span class="stat-label">Completion Rate</span>
        </div>
        <div class="ai-stat">
          <span class="stat-value">{{ progress_analytics.total_study_time }}m</span>
          <span class="stat-label">Study Time</span>
        </div>
        <div class="ai-stat">
          <span class="stat-value">{{ progress_analytics.average_score }}</span>
          <span class="stat-label">Avg Score</span>
        </div>
      </div>
      {% if recommendations %}
      <div class="ai-quick-recommendations">
        <h4>🎯 Recommended for You</h4>
        {% for rec in recommendations %}
        <div class="quick-rec">
          <span class="rec-title">{{ rec.title }}</span>
          {% if rec.lesson %}
          <a href="{% url 'learn:lesson_details' rec.lesson.id %}" class="rec-link">Start →</a>
          {% endif %}
        </div>
        {% endfor %}
      </div>
      {% endif %}
    </div>
    {% elif user.is_authenticated and setup_required %}
    <div class="ai-setup-widget">
      <h3>🤖 AI Learning Available</h3>
      <p>Enable AI-powered learning features by running migrations.</p>
      <a href="{% url 'learn:unified_dashboard' %}" class="dashboard-btn">
        📊 View Dashboard
      </a>
    </div>
    {% elif user.is_authenticated %}
    <div class="dashboard-access-widget">
      <h3>📊 Learning Dashboard</h3>
      <p>Access your personalized learning dashboard with progress tracking and course management.</p>
      <a href="{% url 'learn:unified_dashboard' %}" class="dashboard-btn primary">
        📊 Open Dashboard
      </a>
    </div>
    {% endif %}

    <!-- Course Grid -->
    {% if courses %}
    <div class="courses-grid">
      {% for course in courses %}
      <div class="course-card slide-up" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;">
        <div class="course-image-container">
          {% if course.image %}
            <img src="{{ course.image.url }}" alt="{{ course.name }}" class="course-image" />
          {% else %}
            <div class="course-image-placeholder">
              <span class="placeholder-icon">📚</span>
            </div>
          {% endif %}
          <div class="course-overlay">
            <span class="course-category">Course</span>
          </div>
        </div>

        <div class="course-content">
          <h3 class="course-title">{{ course.name }}</h3>
          <p class="course-description">{{ course.description|truncatewords:20 }}</p>

          <!-- Course Creator Info -->
          <div class="course-creator">
            {% if course.created_by %}
              {% if course.is_created_by_mentor %}
                <span class="creator-badge mentor-badge">
                  <i class="icon">🎓</i> By {{ course.get_creator_name }}
                </span>
              {% else %}
                <span class="creator-badge admin-badge">
                  <i class="icon">👨‍💼</i> By {{ course.get_creator_name }}
                </span>
              {% endif %}
            {% else %}
              <span class="creator-badge admin-badge">
                <i class="icon">👨‍💼</i> By Admin
              </span>
            {% endif %}

            <!-- Course Status -->
            {% if course.status == 'draft' %}
              <span class="status-badge draft-status">📝 Draft</span>
            {% elif not course.is_approved and course.created_by %}
              <span class="status-badge pending-status">⏳ Pending Review</span>
            {% endif %}
          </div>

          <div class="course-meta">
            <span class="course-level">
              <i class="icon">🎯</i> {{ course.get_difficulty_display }}
            </span>
            <span class="course-duration">
              <i class="icon">⏱️</i> {{ course.estimated_duration }}
            </span>
          </div>

          <div class="course-actions">
            <a href="{% url 'learn:course_details' course.id %}" class="btn btn-primary course-enroll-btn">
              <i class="icon">🚀</i> Start Learning
            </a>

            <!-- Edit permissions for course creators and admins -->
            {% if user.is_authenticated %}
              {% if user.is_superuser or course.created_by == user %}
            <div class="admin-actions">
              <a href="{% url 'learn:edit_course' course.id %}" class="btn btn-secondary btn-sm">
                <i class="icon">✏️</i> Edit
              </a>
              {% if is_superuser %}
              <a href="{% url 'learn:delete_course' course.id %}"
                 onclick="return confirm('Are you sure you want to delete this course?')"
                 class="btn btn-danger btn-sm">
                <i class="icon">🗑️</i> Delete
              </a>
              {% endif %}
            </div>
              {% endif %}
            {% endif %}
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="empty-state">
      <div class="empty-icon">📚</div>
      <h3>No Courses Available Yet</h3>
      <p>Check back soon for exciting new learning opportunities!</p>
      {% if can_create_courses %}
        {% if is_superuser %}
        <a href="{% url 'learn:create_course' %}" class="btn btn-primary">
          Create the First Course
        </a>
        {% elif is_verified_mentor %}
        <a href="{% url 'learn:create_course' %}" class="btn btn-primary">
          🎓 Create Your First Course
        </a>
        <p><small>Share your expertise with the community!</small></p>
        {% endif %}
      {% endif %}
    </div>
    {% endif %}
  </div>
</section>

<!-- Include AI Learning CSS -->
{% if ai_enabled or setup_required %}
<link rel="stylesheet" href="{% static 'css/ai_learning.css' %}">
{% endif %}

<!-- Particles.js and animations script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 60,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055"]
        },
        "shape": {
          "type": ["circle", "triangle"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-up');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    // Run animations
    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });

    // Add smooth scroll to course grid if there are courses
    const coursesGrid = document.querySelector('.courses-grid');
    if (coursesGrid && window.location.search.includes('welcome=')) {
      setTimeout(() => {
        coursesGrid.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }, 1000);
    }
  });
</script>
{% endblock %}
