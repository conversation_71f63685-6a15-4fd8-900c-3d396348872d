{% extends 'base.html' %}
{% load static %}

{% block title %}My Support Tickets - Forge X{% endblock %}

{% block content %}
<style>
/* My Tickets Styles */
.tickets-container {
  padding: 40px 0;
  min-height: 80vh;
}

.tickets-header {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.tickets-title {
  color: #C0ff6b;
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tickets-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
}

.ticket-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ticket-item {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.ticket-item:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.3);
  transform: translateY(-2px);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.ticket-id {
  color: #C0ff6b;
  font-weight: bold;
  font-size: 1.1rem;
  text-decoration: none;
}

.ticket-id:hover {
  color: #ffffff;
  text-decoration: none;
}

.ticket-status {
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #FFC107; border: 1px solid #FFC107; }
.status-in_progress { background: rgba(33, 150, 243, 0.2); color: #2196F3; border: 1px solid #2196F3; }
.status-waiting_user { background: rgba(156, 39, 176, 0.2); color: #9C27B0; border: 1px solid #9C27B0; }
.status-resolved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; border: 1px solid #4CAF50; }
.status-closed { background: rgba(158, 158, 158, 0.2); color: #9E9E9E; border: 1px solid #9E9E9E; }

.ticket-subject {
  color: #ffffff;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.ticket-description {
  color: #d5d5d5;
  margin-bottom: 10px;
  line-height: 1.5;
}

.ticket-meta {
  display: flex;
  gap: 20px;
  font-size: 0.9rem;
  color: #999;
  flex-wrap: wrap;
}

.priority-high { color: #F44336; }
.priority-urgent { color: #E91E63; font-weight: bold; }
.priority-medium { color: #FF9800; }
.priority-low { color: #4CAF50; }

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.btn {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 10px 20px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:hover {
  background: rgba(192, 255, 107, 0.2);
  color: #ffffff;
  text-decoration: none;
  transform: translateY(-2px);
}

.btn-primary {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #1a1a1a;
  border-color: #C0ff6b;
}

.btn-primary:hover {
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.ticket-item:hover .btn {
  background: rgba(192, 255, 107, 0.3);
  border-color: rgba(192, 255, 107, 0.5);
}

.ticket-item:hover .btn-primary {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 60px 20px;
}

.empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  display: block;
  color: #666;
}

.empty-state h3 {
  color: #C0ff6b;
  margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tickets-container {
    padding: 20px 10px;
  }

  .tickets-header, .tickets-section {
    margin: 0 10px 20px 10px;
    padding: 20px;
  }

  .tickets-title {
    font-size: 2rem;
  }

  .ticket-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .ticket-meta {
    flex-direction: column;
    gap: 5px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>

<div class="tickets-container">
  <div class="tile-wrap">
    <!-- Header -->
    <div class="tickets-header">
      <h1 class="tickets-title">My Support Tickets</h1>
      <p style="color: #d5d5d5; margin: 0;">Track and manage your support requests</p>

      <div class="action-buttons">
        <a href="{% url 'accounts:create_support_ticket' %}" class="btn btn-primary">
          <i class="fas fa-plus"></i> Create New Ticket
        </a>
        <a href="{% url 'accounts:help_center' %}" class="btn">
          <i class="fas fa-home"></i> Help Center
        </a>
      </div>
    </div>

    <!-- Tickets List -->
    <div class="tickets-section">
      {% if tickets %}
        <ul class="ticket-list">
          {% for ticket in tickets %}
          <li class="ticket-item" onclick="window.location.href='{% url 'accounts:support_ticket_detail' ticket.ticket_id %}'" style="cursor: pointer;">
            <div class="ticket-header">
              <span class="ticket-id">
                #{{ ticket.ticket_id }}
              </span>
              <span class="ticket-status status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
            </div>

            <div class="ticket-subject">{{ ticket.subject }}</div>

            <div class="ticket-description">
              {{ ticket.description|truncatewords:20 }}
            </div>

            <div class="ticket-meta">
              <span><i class="fas fa-calendar"></i> Created: {{ ticket.created_at|date:"M d, Y H:i" }}</span>
              <span><i class="fas fa-flag"></i> Priority: <span class="priority-{{ ticket.priority }}">{{ ticket.get_priority_display }}</span></span>
              {% if ticket.category %}
                <span><i class="fas fa-tag"></i> Category: {{ ticket.category.name }}</span>
              {% endif %}
              {% if ticket.assigned_to %}
                <span><i class="fas fa-user"></i> Assigned to: {{ ticket.assigned_to.username }}</span>
              {% endif %}
              {% if ticket.resolved_at %}
                <span><i class="fas fa-check"></i> Resolved: {{ ticket.resolved_at|date:"M d, Y H:i" }}</span>
              {% endif %}
            </div>

            <div style="margin-top: 15px; text-align: right;">
              <a href="{% url 'accounts:support_ticket_detail' ticket.ticket_id %}" class="btn btn-primary" onclick="event.stopPropagation();">
                <i class="fas fa-comments"></i> Open Chat
              </a>
            </div>
          </li>
          {% endfor %}
        </ul>
      {% else %}
        <div class="empty-state">
          <i class="fas fa-ticket-alt"></i>
          <h3>No Support Tickets Yet</h3>
          <p>You haven't created any support tickets. If you need help, feel free to create one!</p>
          <div style="margin-top: 30px;">
            <a href="{% url 'accounts:create_support_ticket' %}" class="btn btn-primary">
              <i class="fas fa-plus"></i> Create Your First Ticket
            </a>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
