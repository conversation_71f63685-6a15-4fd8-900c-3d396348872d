from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json
# Create your models here.

class Course(models.Model):
    DIFFICULTY_CHOICES = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
    ]

    COURSE_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('published', 'Published'),
        ('archived', 'Archived'),
    ]

    name = models.CharField(max_length=100)
    image = models.ImageField(upload_to='learn/images', blank=True, null=True)
    description = models.TextField()
    difficulty = models.CharField(max_length=20, choices=DIFFICULTY_CHOICES, default='beginner')
    estimated_duration = models.CharField(max_length=50, default='Self-paced')
    prerequisites = models.TextField(blank=True, help_text="What students should know before taking this course")
    learning_objectives = models.TextField(blank=True, help_text="What students will learn from this course")

    # Course creator and status
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_courses', null=True, blank=True)
    status = models.CharField(max_length=20, choices=COURSE_STATUS_CHOICES, default='draft')
    is_approved = models.BooleanField(default=False, help_text="Admin approval for mentor-created courses")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True, null=True, blank=True)

    def __str__(self):
        return self.name

    def is_created_by_mentor(self):
        """Check if course was created by a verified mentor"""
        if not self.created_by:
            return False
        return hasattr(self.created_by, 'mentor_profile') and self.created_by.mentor_profile.verified

    def get_creator_name(self):
        """Get the name of the course creator"""
        if self.created_by:
            return self.created_by.get_full_name() or self.created_by.username
        return "Admin"

    def can_be_edited_by(self, user):
        """Check if user can edit this course"""
        if user.is_superuser:
            return True
        if self.created_by == user and hasattr(user, 'mentor_profile') and user.mentor_profile.verified:
            return True
        return False

    class Meta:
        ordering = ['-created_at', 'name']

class Chapter(models.Model):
    name = models.CharField(max_length=100)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.course.name} - {self.name}"

    class Meta:
        ordering = ['order', 'id']

class Lesson(models.Model):
    LESSON_TYPES = [
        ('theory', 'Theory'),
        ('practical', 'Practical'),
        ('exercise', 'Exercise'),
        ('project', 'Project'),
    ]

    name = models.CharField(max_length=100)
    chapter = models.ForeignKey(Chapter, on_delete=models.CASCADE)
    lesson_type = models.CharField(max_length=20, choices=LESSON_TYPES, default='theory')
    estimated_time = models.CharField(max_length=20, default='15 min')
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.chapter.name} - {self.name}"

    class Meta:
        ordering = ['order', 'id']

class LessonContent(models.Model):
    CONTENT_TYPES = [
        ('text', 'Text Content'),
        ('code_example', 'Code Example'),
        ('tip', 'Pro Tip'),
        ('warning', 'Warning'),
        ('exercise', 'Exercise'),
        ('summary', 'Summary'),
    ]

    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE)
    content_type = models.CharField(max_length=20, choices=CONTENT_TYPES, default='text')
    title = models.CharField(max_length=200, blank=True)
    content = models.TextField()
    code_language = models.CharField(max_length=20, blank=True, help_text="For code examples (e.g., python, javascript)")
    order = models.PositiveIntegerField(default=0)

    def __str__(self):
        return f"{self.lesson.name} - {self.title or self.content_type}"

    class Meta:
        ordering = ['order', 'id']


# AI-Powered Learning Models
class UserLearningProfile(models.Model):
    """Track user's learning progress and preferences for AI personalization"""
    LEARNING_STYLES = [
        ('visual', 'Visual Learner'),
        ('auditory', 'Auditory Learner'),
        ('kinesthetic', 'Kinesthetic Learner'),
        ('reading', 'Reading/Writing Learner'),
    ]

    SKILL_LEVELS = [
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='learning_profile')
    learning_style = models.CharField(max_length=20, choices=LEARNING_STYLES, default='visual')
    current_skill_level = models.CharField(max_length=20, choices=SKILL_LEVELS, default='beginner')
    preferred_difficulty = models.CharField(max_length=20, choices=SKILL_LEVELS, default='beginner')
    learning_goals = models.TextField(blank=True, help_text="User's learning objectives")
    strengths = models.JSONField(default=list, help_text="Areas where user excels")
    weaknesses = models.JSONField(default=list, help_text="Areas needing improvement")
    total_study_time = models.PositiveIntegerField(default=0, help_text="Total minutes spent learning")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Learning Profile"


class LessonProgress(models.Model):
    """Track user progress through lessons with AI analytics"""
    COMPLETION_STATUS = [
        ('not_started', 'Not Started'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('mastered', 'Mastered'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=COMPLETION_STATUS, default='not_started')
    time_spent = models.PositiveIntegerField(default=0, help_text="Minutes spent on this lesson")
    attempts = models.PositiveIntegerField(default=0)
    score = models.FloatField(null=True, blank=True, help_text="AI-calculated comprehension score")
    difficulty_rating = models.FloatField(null=True, blank=True, help_text="User's difficulty rating 1-5")
    ai_insights = models.JSONField(default=dict, help_text="AI-generated learning insights")
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    last_accessed = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['user', 'lesson']

    def __str__(self):
        return f"{self.user.username} - {self.lesson.name} ({self.status})"


class AILearningAssistant(models.Model):
    """AI Assistant conversations and interactions"""
    INTERACTION_TYPES = [
        ('question', 'Question'),
        ('explanation', 'Explanation Request'),
        ('code_help', 'Code Help'),
        ('concept_clarification', 'Concept Clarification'),
        ('study_plan', 'Study Plan Request'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, null=True, blank=True)
    interaction_type = models.CharField(max_length=30, choices=INTERACTION_TYPES, default='question')
    user_message = models.TextField()
    ai_response = models.TextField()
    context_data = models.JSONField(default=dict, help_text="Additional context for AI response")
    helpful_rating = models.IntegerField(null=True, blank=True, help_text="User rating 1-5")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"AI Chat - {self.user.username} ({self.interaction_type})"

    class Meta:
        ordering = ['-created_at']


class SmartRecommendation(models.Model):
    """AI-generated learning recommendations"""
    RECOMMENDATION_TYPES = [
        ('next_lesson', 'Next Lesson'),
        ('review_content', 'Review Content'),
        ('practice_exercise', 'Practice Exercise'),
        ('skill_improvement', 'Skill Improvement'),
        ('course_suggestion', 'Course Suggestion'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    recommendation_type = models.CharField(max_length=30, choices=RECOMMENDATION_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    target_lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, null=True, blank=True)
    target_course = models.ForeignKey(Course, on_delete=models.CASCADE, null=True, blank=True)
    confidence_score = models.FloatField(help_text="AI confidence in recommendation (0-1)")
    priority = models.IntegerField(default=1, help_text="Priority level 1-5")
    is_active = models.BooleanField(default=True)
    clicked = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Recommendation for {self.user.username}: {self.title}"

    class Meta:
        ordering = ['-priority', '-created_at']


class CodeAnalysis(models.Model):
    """AI-powered code analysis and feedback"""
    ANALYSIS_TYPES = [
        ('syntax_check', 'Syntax Check'),
        ('best_practices', 'Best Practices'),
        ('performance', 'Performance Analysis'),
        ('security', 'Security Review'),
        ('style', 'Code Style'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    lesson = models.ForeignKey(Lesson, on_delete=models.CASCADE, null=True, blank=True)
    code_content = models.TextField()
    language = models.CharField(max_length=50)
    analysis_type = models.CharField(max_length=30, choices=ANALYSIS_TYPES, default='syntax_check')
    ai_feedback = models.TextField()
    suggestions = models.JSONField(default=list, help_text="List of improvement suggestions")
    score = models.FloatField(help_text="Code quality score 0-100")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Code Analysis - {self.user.username} ({self.language})"

    class Meta:
        ordering = ['-created_at']