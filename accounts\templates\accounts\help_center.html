{% extends 'base.html' %}
{% load static %}

{% block title %}Help Center - Forge X{% endblock %}

{% block content %}
<style>
/* Help Center Specific Styles */
.help-center-container {
  padding: 40px 0;
  min-height: 80vh;
}

.help-hero {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.help-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.help-hero p {
  font-size: 1.2rem;
  color: #d5d5d5;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Quick Actions */
.quick-actions {
  background: linear-gradient(135deg, rgba(28,28,28,0.9) 0%, rgba(45,45,45,0.9) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.quick-actions h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.3s ease;
  display: block;
  text-align: center;
}

.action-card:hover {
  background: rgba(192, 255, 107, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(192, 255, 107, 0.1);
  color: #ffffff;
  text-decoration: none;
}

.action-card i {
  font-size: 2rem;
  color: #C0ff6b;
  margin-bottom: 10px;
  display: block;
}

.action-card h3 {
  color: #C0ff6b;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.action-card p {
  margin: 0;
  font-size: 0.9rem;
  color: #d5d5d5;
}

/* Categories Section */
.categories-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.categories-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.category-card {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.category-card:hover {
  border-color: rgba(192, 255, 107, 0.4);
  transform: translateY(-2px);
}

.category-card h3 {
  color: #C0ff6b;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.category-card p {
  color: #d5d5d5;
  margin: 0;
  font-size: 0.9rem;
}

/* Recent Tickets */
.recent-tickets {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.recent-tickets h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.8rem;
}

.ticket-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ticket-item {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.ticket-item:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.3);
}

.ticket-item a {
  text-decoration: none;
  color: #ffffff;
  display: block;
}

.ticket-item a:hover {
  color: #C0ff6b;
  text-decoration: none;
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.ticket-id {
  color: #C0ff6b;
  font-weight: bold;
}

.ticket-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
.status-in_progress { background: rgba(33, 150, 243, 0.2); color: #2196F3; }
.status-resolved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-closed { background: rgba(158, 158, 158, 0.2); color: #9E9E9E; }

.ticket-subject {
  font-weight: 600;
  margin-bottom: 3px;
}

.ticket-date {
  font-size: 0.8rem;
  color: #999;
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Particles Container */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .help-hero h1 {
    font-size: 2.5rem;
  }

  .help-hero p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions, .categories-section, .recent-tickets {
    padding: 20px;
    margin: 0 10px 20px 10px;
  }

  .ticket-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

@media (max-width: 480px) {
  .help-hero h1 {
    font-size: 2rem;
  }

  .quick-actions, .categories-section, .recent-tickets {
    padding: 15px;
  }
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<div class="help-center-container">
  <div class="tile-wrap">
    <!-- Hero Section -->
    <div class="help-hero fade-in">
      <h1>Help Center</h1>
      <p>Get the support you need. Browse our knowledge base, submit tickets, or chat with our support team in real-time.</p>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions fade-in" data-delay="100">
      <h2>🚀 Quick Actions</h2>
      <div class="action-grid">
        {% if user.is_authenticated %}
          <a href="{% url 'accounts:create_support_ticket' %}" class="action-card">
            <i class="fas fa-plus-circle"></i>
            <h3>Create Ticket</h3>
            <p>Submit a new support request</p>
          </a>
          <a href="{% url 'accounts:my_support_tickets' %}" class="action-card">
            <i class="fas fa-ticket-alt"></i>
            <h3>My Tickets</h3>
            <p>View your support tickets</p>
          </a>
          {% if user.is_staff or user.is_superuser %}
          <a href="{% url 'accounts:support_admin_dashboard' %}" class="action-card">
            <i class="fas fa-cogs"></i>
            <h3>Admin Dashboard</h3>
            <p>Manage support tickets</p>
          </a>
          {% if not categories %}
          <a href="{% url 'accounts:create_default_categories' %}" class="action-card" style="border-color: #FFC107;">
            <i class="fas fa-plus-circle" style="color: #FFC107;"></i>
            <h3 style="color: #FFC107;">Setup Categories</h3>
            <p>Create default support categories</p>
          </a>
          {% endif %}
          {% endif %}
        {% else %}
          <a href="{% url 'accounts:login' %}" class="action-card">
            <i class="fas fa-sign-in-alt"></i>
            <h3>Login</h3>
            <p>Sign in to create tickets</p>
          </a>
        {% endif %}
        <a href="{% url 'accounts:contact' %}" class="action-card">
          <i class="fas fa-envelope"></i>
          <h3>Contact Us</h3>
          <p>Send us a direct message</p>
        </a>
      </div>
    </div>

    <!-- Support Categories -->
    {% if categories %}
    <div class="categories-section fade-in" data-delay="200">
      <h2>📋 Support Categories</h2>
      <div class="categories-grid">
        {% for category in categories %}
        <div class="category-card">
          <h3>{{ category.name }}</h3>
          <p>{{ category.description|default:"Get help with this topic" }}</p>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Recent Tickets (for authenticated users) -->
    {% if user.is_authenticated and user_tickets %}
    <div class="recent-tickets fade-in" data-delay="300">
      <h2>🎫 Your Recent Tickets</h2>
      <ul class="ticket-list">
        {% for ticket in user_tickets %}
        <li class="ticket-item">
          <a href="{% url 'accounts:support_ticket_detail' ticket.ticket_id %}">
            <div class="ticket-header">
              <span class="ticket-id">#{{ ticket.ticket_id }}</span>
              <span class="ticket-status status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
            </div>
            <div class="ticket-subject">{{ ticket.subject }}</div>
            <div class="ticket-date">{{ ticket.created_at|date:"M d, Y" }}</div>
          </a>
        </li>
        {% endfor %}
      </ul>
      <div style="text-align: center; margin-top: 20px;">
        <a href="{% url 'accounts:my_support_tickets' %}" class="action-card" style="display: inline-block; width: auto; min-width: 200px;">
          <i class="fas fa-list"></i>
          <h3>View All Tickets</h3>
          <p>See your complete ticket history</p>
        </a>
      </div>
    </div>
    {% endif %}

    <!-- Knowledge Base / FAQ Section -->
    <div class="categories-section fade-in" data-delay="400">
      <h2>💡 Frequently Asked Questions</h2>
      <div class="categories-grid">
        <div class="category-card">
          <h3>Getting Started</h3>
          <p>Learn how to set up your profile, join projects, and navigate the platform</p>
        </div>
        <div class="category-card">
          <h3>Collaboration</h3>
          <p>Find answers about project creation, team management, and real-time coding</p>
        </div>
        <div class="category-card">
          <h3>Mentorship</h3>
          <p>Get help with booking sessions, becoming a mentor, and payment issues</p>
        </div>
        <div class="category-card">
          <h3>Learning</h3>
          <p>Course access, progress tracking, and skill assessment questions</p>
        </div>
        <div class="category-card">
          <h3>Account & Billing</h3>
          <p>Profile settings, password reset, subscription, and payment support</p>
        </div>
        <div class="category-card">
          <h3>Technical Issues</h3>
          <p>Bug reports, performance issues, and browser compatibility problems</p>
        </div>
      </div>
    </div>

    <!-- Contact Information -->
    <div class="quick-actions fade-in" data-delay="500">
      <h2>📞 Other Ways to Get Help</h2>
      <div class="action-grid">
        <div class="action-card" style="cursor: default;">
          <i class="fas fa-clock"></i>
          <h3>Support Hours</h3>
          <p>Monday - Friday: 9 AM - 6 PM PST<br>Weekend: Limited support</p>
        </div>
        <div class="action-card" style="cursor: default;">
          <i class="fas fa-reply"></i>
          <h3>Response Time</h3>
          <p>Urgent: 2-4 hours<br>Normal: 24-48 hours</p>
        </div>
        <a href="{% url 'accounts:documentation' %}" class="action-card">
          <i class="fas fa-book"></i>
          <h3>Documentation</h3>
          <p>Comprehensive guides and API reference</p>
        </a>
        <a href="{% url 'accounts:status' %}" class="action-card">
          <i class="fas fa-heartbeat"></i>
          <h3>System Status</h3>
          <p>Check platform health and uptime</p>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for animations and particles -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": "#C0ff6b"
        },
        "shape": {
          "type": "circle",
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.3,
          "random": false,
          "anim": {
            "enable": false,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": false,
            "speed": 40,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 3,
          "direction": "none",
          "random": false,
          "straight": false,
          "out_mode": "out",
          "bounce": false,
          "attract": {
            "enable": false,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "repulse"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 400,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 400,
            "size": 40,
            "duration": 2,
            "opacity": 8,
            "speed": 3
          },
          "repulse": {
            "distance": 100,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
    });

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        const delay = parseInt(element.getAttribute('data-delay')) || 0;

        if (elementTop < windowHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('visible');
          }, delay);
        }
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up scroll event listener
    window.addEventListener('scroll', revealOnScroll);
  });
</script>
{% endblock %}
