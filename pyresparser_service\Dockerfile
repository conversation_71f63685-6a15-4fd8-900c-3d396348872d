# Use Python 3.10 as the base image
FROM python:3.10-slim

# Set the working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpoppler-cpp-dev \
    tesseract-ocr \
    tesseract-ocr-eng \
    && rm -rf /var/lib/apt/lists/*

# Set the NLTK data directory
ENV NLTK_DATA=/usr/local/nltk_data

# Copy requirements and install Python dependencies
COPY ../extrasetip.txt ./
RUN pip install --no-cache-dir numpy pandas nltk spacy pdfminer.six pyresparser

# Install FastAPI for the application
RUN pip install --no-cache-dir fastapi

# Install uvicorn for running the FastAPI server
RUN pip install --no-cache-dir uvicorn

# Install python-multipart for handling form data
RUN pip install --no-cache-dir python-multipart

# Download NLTK and spaCy models
RUN python -m nltk.downloader punkt && python -m spacy download en_core_web_sm

# Download NLTK stopwords explicitly
RUN python -m nltk.downloader stopwords

# Copy application code
COPY . .

# Expose the port for the FastAPI service
EXPOSE 8000

# Start the FastAPI server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]