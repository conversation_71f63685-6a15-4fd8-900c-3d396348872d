{% extends 'base.html' %}
{% load static %}

{% block title %}Mentorship Marketplace - ForgeX{% endblock %}

{% block content %}
<div class="mentorship-marketplace">
    <!-- Hero Section -->
    <section class="marketplace-hero">
        <div class="tile-wrap">
            <h1>Find Your Perfect Mentor</h1>
            <p>Connect with experienced developers for personalized 1-on-1 mentorship sessions</p>

            <!-- Search and Filters -->
            <div class="search-filters">
                <form method="GET" class="filter-form">
                    <div class="search-bar">
                        <input type="text" name="search" placeholder="Search mentors by name or expertise..."
                               value="{{ current_filters.search }}" class="search-input">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <div class="filters-row">
                        <select name="skill" class="filter-select">
                            <option value="">All Skills</option>
                            {% for skill in all_skills %}
                                <option value="{{ skill.name }}"
                                        {% if current_filters.skill == skill.name %}selected{% endif %}>
                                    {{ skill.name }}
                                </option>
                            {% endfor %}
                        </select>

                        <div class="price-range">
                            <input type="number" name="min_rate" placeholder="Min $/hr"
                                   value="{{ current_filters.min_rate }}" class="price-input">
                            <input type="number" name="max_rate" placeholder="Max $/hr"
                                   value="{{ current_filters.max_rate }}" class="price-input">
                        </div>

                        <select name="min_rating" class="filter-select">
                            <option value="">Any Rating</option>
                            <option value="4" {% if current_filters.min_rating == "4" %}selected{% endif %}>4+ Stars</option>
                            <option value="4.5" {% if current_filters.min_rating == "4.5" %}selected{% endif %}>4.5+ Stars</option>
                        </select>

                        <select name="sort" class="filter-select">
                            <option value="rating" {% if current_filters.sort == "rating" %}selected{% endif %}>Best Rated</option>
                            <option value="price_low" {% if current_filters.sort == "price_low" %}selected{% endif %}>Price: Low to High</option>
                            <option value="price_high" {% if current_filters.sort == "price_high" %}selected{% endif %}>Price: High to Low</option>
                            <option value="experience" {% if current_filters.sort == "experience" %}selected{% endif %}>Most Experienced</option>
                        </select>

                        <button type="submit" class="apply-filters-btn">Apply Filters</button>
                    </div>
                </form>
            </div>

            <!-- Action Buttons Section -->
            <div class="mentor-cta-section">
                <div class="mentor-cta-content">
                    <div class="cta-text">
                        <h3>Ready to get started?</h3>
                        <p>Join our community of mentors or manage your existing sessions</p>
                    </div>
                    <div class="cta-buttons">
                        <a href="{% url 'mentorship:my_sessions' %}" class="btn btn-cta-secondary">
                            <i class="fas fa-calendar-alt"></i>
                            My Sessions
                        </a>
                        {% if user.is_superuser %}
                            <a href="{% url 'mentorship:admin_withdrawal_dashboard' %}" class="btn btn-cta-admin">
                                <i class="fas fa-shield-alt"></i>
                                Check Withdrawal Requests
                            </a>
                        {% endif %}
                        <a href="{% url 'mentorship:become_mentor' %}" class="btn btn-cta">
                            <i class="fas fa-user-graduate"></i>
                            Become a Mentor
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Mentors Grid -->
    <section class="mentors-section">
        <div class="tile-wrap">
            {% if page_obj %}
                <div class="mentors-grid">
                    {% for mentor in page_obj %}
                        <div class="mentor-card">
                            <div class="mentor-header">
                                <div class="mentor-avatar {% if not mentor.user.profile or not mentor.user.profile.profile_picture %}default{% endif %}">
                                    {% if mentor.user.profile and mentor.user.profile.profile_picture %}
                                        <img src="{{ mentor.user.profile.profile_picture.url }}"
                                             alt="{{ mentor.user.get_full_name }}"
                                             class="profile-image">
                                    {% else %}
                                        {{ mentor.user.first_name|first|upper }}{{ mentor.user.last_name|first|upper }}
                                    {% endif %}
                                </div>

                                <div class="mentor-info">
                                    <h3>{{ mentor.user.get_full_name|default:mentor.user.username }}</h3>
                                    <div class="mentor-rating">
                                        {% for i in "12345" %}
                                            <span class="star {% if mentor.average_rating >= i|add:0 %}filled{% endif %}">★</span>
                                        {% endfor %}
                                        <span class="rating-text">({{ mentor.average_rating }}/5.0)</span>
                                    </div>
                                    <div class="mentor-stats">
                                        <span class="sessions-count">{{ mentor.total_sessions }} sessions</span>
                                        {% if mentor.verified %}
                                            <span class="verified-badge">✓ Verified</span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="mentor-price">
                                    <span class="price">${{ mentor.hourly_rate }}/hr</span>
                                </div>
                            </div>

                            <div class="mentor-bio">
                                <p>{{ mentor.bio|truncatewords:20 }}</p>
                            </div>

                            <div class="mentor-skills">
                                {% for skill in mentor.specializations.all|slice:":3" %}
                                    <span class="skill-tag">{{ skill.name }}</span>
                                {% endfor %}
                                {% if mentor.specializations.count > 3 %}
                                    <span class="skill-tag more">+{{ mentor.specializations.count|add:"-3" }} more</span>
                                {% endif %}
                            </div>

                            <div class="mentor-actions">
                                <a href="{% url 'mentorship:mentor_detail' mentor.id %}" class="btn btn-secondary">
                                    View Profile
                                </a>
                                <a href="{% url 'mentorship:mentor_detail' mentor.id %}#book" class="btn btn-primary book-btn">
                                    Book Session
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-mentors">
                    <h3>No mentors found</h3>
                    <p>Ready to start your mentorship journey? Create some mentor profiles to get started!</p>
                    <a href="{% url 'mentorship:become_mentor' %}" class="btn btn-primary">Become a Mentor</a>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="tile-wrap">
            <div class="cta-content">
                <h2>Want to become a mentor?</h2>
                <p>Share your expertise and earn money by helping other developers grow</p>
                <a href="{% url 'mentorship:become_mentor' %}" class="btn btn-primary btn-large">
                    Become a Mentor
                </a>
            </div>
        </div>
    </section>
</div>

<style>
.mentorship-marketplace {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.marketplace-hero {
    background: linear-gradient(135deg, #1c1c1c 0%, #656565 100%);
    color: #ffffff;
    padding: 4rem 0;
    text-align: center;
    border-bottom: 2px solid #C0ff6b;
}

.marketplace-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
    color: #C0ff6b;
    text-shadow: 0 0 10px rgba(192,255,107,0.3);
}

.marketplace-hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: #d5d5d5;
}

.search-filters {
    max-width: 800px;
    margin: 0 auto;
}

.search-bar {
    display: flex;
    margin-bottom: 1rem;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    font-size: 1rem;
    background: #656565;
    color: #ffffff;
}

.search-input::placeholder {
    color: #d5d5d5;
}

.search-btn {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #000000;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.search-btn:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.filters-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.filter-select, .price-input {
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: #656565;
    color: #ffffff;
}

.filter-select option, .price-input::placeholder {
    color: #d5d5d5;
}

.price-range {
    display: flex;
    gap: 0.5rem;
}

.apply-filters-btn {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #000000;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.apply-filters-btn:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.mentor-cta-section {
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(101,101,101,0.2);
    border-radius: 15px;
    border: 2px solid rgba(192,255,107,0.3);
}

.mentor-cta-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 900px;
    margin: 0 auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.cta-text h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #C0ff6b;
}

.cta-text p {
    margin: 0;
    opacity: 0.9;
    color: #d5d5d5;
}

.btn-cta {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #000000;
    border: 2px solid #C0ff6b;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-cta:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    color: #000000;
    border-color: #a0e066;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-cta i {
    font-size: 1.2rem;
}

.btn-cta-secondary {
    padding: 1rem 2rem;
    background: transparent;
    color: #d5d5d5;
    border: 2px solid #656565;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-cta-secondary:hover {
    background: #656565;
    color: #ffffff;
    border-color: #C0ff6b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(192,255,107,0.2);
}

.btn-cta-secondary i {
    font-size: 1.2rem;
}

.btn-cta-admin {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: #ffffff;
    border: 2px solid #dc3545;
    border-radius: 10px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.btn-cta-admin:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220,53,69,0.4);
}

.btn-cta-admin i {
    font-size: 1.2rem;
}

.mentors-section {
    padding: 4rem 0;
}

.mentors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2.5rem;
    margin-bottom: 3rem;
}

.mentor-card {
    background: linear-gradient(145deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03));
    border-radius: 20px;
    padding: 2rem;
    border: 2px solid rgba(192,255,107,0.15);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.mentor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #C0ff6b, #a0e066);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mentor-card:hover::before {
    opacity: 1;
}

.mentor-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(192,255,107,0.15), 0 0 0 1px rgba(192,255,107,0.3);
    border-color: rgba(192,255,107,0.4);
    background: linear-gradient(145deg, rgba(255,255,255,0.12), rgba(255,255,255,0.06));
}

.mentor-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.mentor-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(192,255,107,0.3);
    transition: all 0.3s ease;
}

.mentor-avatar.default {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.4rem;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.mentor-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mentor-card:hover .mentor-avatar {
    border-color: rgba(192,255,107,0.6);
    transform: scale(1.05);
}

.mentor-info {
    flex: 1;
    min-width: 0;
}

.mentor-info h3 {
    margin: 0 0 0.75rem 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
}

.mentor-rating {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    gap: 0.25rem;
}

.star {
    color: rgba(255,255,255,0.3);
    font-size: 1.1rem;
    transition: color 0.2s ease;
}

.star.filled {
    color: #ffd700;
    text-shadow: 0 0 8px rgba(255,215,0,0.4);
}

.rating-text {
    margin-left: 0.75rem;
    font-size: 0.95rem;
    color: rgba(255,255,255,0.8);
    font-weight: 500;
}

.mentor-stats {
    display: flex;
    gap: 1.5rem;
    font-size: 0.95rem;
    color: rgba(255,255,255,0.7);
    align-items: center;
}

.sessions-count {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sessions-count::before {
    content: '📚';
    font-size: 0.9rem;
}

.verified-badge {
    color: #C0ff6b;
    font-weight: 700;
    background: rgba(192,255,107,0.15);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.85rem;
    border: 1px solid rgba(192,255,107,0.3);
}

.mentor-price {
    font-size: 1.6rem;
    font-weight: 800;
    color: #C0ff6b;
    text-shadow: 0 0 10px rgba(192,255,107,0.3);
    white-space: nowrap;
}

.mentor-bio {
    margin-bottom: 1.5rem;
    color: rgba(255,255,255,0.85);
    line-height: 1.6;
    font-size: 0.95rem;
    background: rgba(255,255,255,0.03);
    padding: 1rem;
    border-radius: 12px;
    border-left: 3px solid rgba(192,255,107,0.3);
}

.mentor-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.skill-tag {
    background: linear-gradient(135deg, rgba(192,255,107,0.2), rgba(192,255,107,0.1));
    color: #C0ff6b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(192,255,107,0.3);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.skill-tag:hover {
    background: linear-gradient(135deg, rgba(192,255,107,0.3), rgba(192,255,107,0.2));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(192,255,107,0.2);
}

.skill-tag.more {
    background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05));
    color: rgba(255,255,255,0.7);
    border-color: rgba(255,255,255,0.2);
}

.mentor-actions {
    display: flex;
    gap: 1rem;
    margin-top: auto;
}

.btn {
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 700;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #000000;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
    backdrop-filter: blur(5px);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.2);
}

.no-mentors {
    text-align: center;
    padding: 4rem 0;
}

.no-mentors h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #C0ff6b;
}

.cta-section {
    background: rgba(192,255,107,0.1);
    padding: 4rem 0;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #C0ff6b;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    color: #ffffff !important;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .marketplace-hero h1 {
        font-size: 2rem;
    }

    .filters-row {
        flex-direction: column;
    }

    .mentor-cta-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        width: 100%;
        gap: 1rem;
    }

    .btn-cta,
    .btn-cta-secondary {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .mentors-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mentor-card {
        padding: 1.5rem;
    }

    .mentor-header {
        margin-bottom: 1rem;
    }

    .mentor-avatar {
        width: 60px;
        height: 60px;
    }

    .mentor-avatar.default {
        font-size: 1.2rem;
    }

    .mentor-info h3 {
        font-size: 1.2rem;
    }

    .mentor-price {
        font-size: 1.4rem;
    }

    .mentor-bio {
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .mentor-skills {
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .skill-tag {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .mentor-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .btn {
        padding: 0.875rem 1.25rem;
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}
