[{"model": "learn.course", "pk": 1, "fields": {"name": "Python Fundamentals for Developers", "description": "Master the fundamentals of Python programming with hands-on examples and real-world projects. Perfect for beginners and those looking to strengthen their Python foundation.", "difficulty": "beginner", "estimated_duration": "4-6 weeks", "prerequisites": "Basic computer literacy and willingness to learn programming concepts", "learning_objectives": "Understand Python syntax, data types, control structures, functions, and object-oriented programming basics"}}, {"model": "learn.course", "pk": 2, "fields": {"name": "JavaScript & Modern Web Development", "description": "Learn modern JavaScript, ES6+ features, DOM manipulation, and build interactive web applications. Includes practical projects and best practices.", "difficulty": "intermediate", "estimated_duration": "6-8 weeks", "prerequisites": "Basic HTML/CSS knowledge and programming fundamentals", "learning_objectives": "Master JavaScript fundamentals, ES6+ features, asynchronous programming, and modern web development practices"}}, {"model": "learn.course", "pk": 3, "fields": {"name": "Git & Version Control Mastery", "description": "Comprehensive guide to Git version control system. Learn branching, merging, collaboration workflows, and advanced Git techniques used in professional development.", "difficulty": "beginner", "estimated_duration": "2-3 weeks", "prerequisites": "Basic command line knowledge", "learning_objectives": "Master Git commands, branching strategies, collaboration workflows, and version control best practices"}}, {"model": "learn.course", "pk": 4, "fields": {"name": "Database Design & SQL", "description": "Learn database design principles, SQL queries, and database optimization. Covers relational databases, normalization, and practical database management.", "difficulty": "intermediate", "estimated_duration": "5-7 weeks", "prerequisites": "Basic programming knowledge and logical thinking", "learning_objectives": "Design efficient databases, write complex SQL queries, understand normalization, and optimize database performance"}}, {"model": "learn.course", "pk": 5, "fields": {"name": "API Development & RESTful Services", "description": "Build robust APIs and RESTful web services. Learn API design principles, authentication, documentation, and testing strategies for modern applications.", "difficulty": "advanced", "estimated_duration": "6-8 weeks", "prerequisites": "Solid programming background, understanding of HTTP protocol, and web development experience", "learning_objectives": "Design and implement RESTful APIs, handle authentication and authorization, implement proper error handling, and create comprehensive API documentation"}}, {"model": "learn.chapter", "pk": 1, "fields": {"name": "Getting Started with Python", "course": 1, "description": "Introduction to Python programming language, installation, and basic concepts", "order": 1}}, {"model": "learn.chapter", "pk": 2, "fields": {"name": "Data Types and Variables", "course": 1, "description": "Understanding Python data types, variables, and basic operations", "order": 2}}, {"model": "learn.chapter", "pk": 3, "fields": {"name": "JavaScript Fundamentals", "course": 2, "description": "Core JavaScript concepts and syntax", "order": 1}}, {"model": "learn.chapter", "pk": 4, "fields": {"name": "DOM Manipulation", "course": 2, "description": "Working with the Document Object Model", "order": 2}}, {"model": "learn.chapter", "pk": 5, "fields": {"name": "Git Basics", "course": 3, "description": "Introduction to version control and Git fundamentals", "order": 1}}, {"model": "learn.lesson", "pk": 1, "fields": {"name": "What is <PERSON>?", "chapter": 1, "lesson_type": "theory", "estimated_time": "15 min", "order": 1}}, {"model": "learn.lesson", "pk": 2, "fields": {"name": "Installing Python", "chapter": 1, "lesson_type": "practical", "estimated_time": "20 min", "order": 2}}, {"model": "learn.lesson", "pk": 3, "fields": {"name": "Your First Python Program", "chapter": 1, "lesson_type": "practical", "estimated_time": "25 min", "order": 3}}, {"model": "learn.lesson", "pk": 4, "fields": {"name": "Understanding Variables", "chapter": 2, "lesson_type": "theory", "estimated_time": "20 min", "order": 1}}, {"model": "learn.lesson", "pk": 5, "fields": {"name": "JavaScript Variables and Data Types", "chapter": 3, "lesson_type": "theory", "estimated_time": "30 min", "order": 1}}]