{% extends "base.html" %}
{% block title %}Projects{% endblock %}
{% block content %}

<style>
/* Find Projects Section Styles */
.find-projects-section {
  margin-bottom: 3rem;
}

.find-projects-card {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(160, 224, 102, 0.05));
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.find-projects-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #C0ff6b, #00d4ff, #C0ff6b);
  border-radius: 20px 20px 0 0;
}

.find-projects-content {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 3rem;
  align-items: center;
}

.find-projects-text h2 {
  color: #C0ff6b;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.find-projects-text p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.find-projects-features {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.95rem;
}

.feature-item i {
  color: #C0ff6b;
  font-size: 1.1rem;
}

.find-projects-action {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.btn-find-projects {
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
  color: #1a1a2e;
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 700;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.4s ease;
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-find-projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn-find-projects:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 35px rgba(192, 255, 107, 0.4);
  color: #1a1a2e;
  text-decoration: none;
}

.btn-find-projects:hover::before {
  left: 100%;
}

.btn-find-projects i {
  font-size: 1.3rem;
  animation: rocketPulse 2s ease-in-out infinite;
}

@keyframes rocketPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.find-projects-subtitle {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .find-projects-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .find-projects-features {
    justify-content: center;
  }

  .find-projects-card {
    padding: 2rem 1.5rem;
  }

  .btn-find-projects {
    padding: 0.875rem 2rem;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .find-projects-features {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .find-projects-text h2 {
    font-size: 1.5rem;
  }

  .find-projects-text p {
    font-size: 1rem;
  }
}

/* Empty Projects Actions */
.empty-projects-actions {
  margin-top: 1.5rem;
  text-align: center;
}

.empty-projects-actions .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.95rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.empty-projects-actions .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
  <div class="projects-header">
    <h1>{% if is_superuser %}All Projects{% else %}Projects{% endif %}</h1>
    <a href="{% url 'collaborate:create_project' %}" class="btn btn-primary">Create New Project</a>
  </div>

  {% if is_superuser %}
  <div class="admin-notice">
    <p>
      <i class="admin-icon">👑</i> You are viewing this page as an administrator. You can see and manage all projects.
    </p>
  </div>
  {% endif %}

  {% if messages %}
  <div class="messages">
    {% for message in messages %}
    <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">{{ message }}</div>
    {% endfor %}
  </div>
  {% endif %}

  <!-- Find Projects Section -->
  <section class="find-projects-section">
    <div class="find-projects-card">
      <div class="find-projects-content">
        <div class="find-projects-text">
          <h2><i class="fas fa-search"></i> Looking for Projects to Join?</h2>
          <p>Discover exciting projects from other developers and contribute your skills to collaborative efforts. Browse the marketplace to find projects that match your interests and expertise.</p>
          <div class="find-projects-features">
            <div class="feature-item">
              <i class="fas fa-users"></i>
              <span>Join collaborative teams</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-code"></i>
              <span>Work on diverse projects</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-star"></i>
              <span>Build your portfolio</span>
            </div>
          </div>
        </div>
        <div class="find-projects-action">
          <a href="{% url 'collaborate:marketplace_list' %}" class="btn btn-find-projects">
            <i class="fas fa-rocket"></i>
            Find Projects
          </a>
          <p class="find-projects-subtitle">Browse available projects in the marketplace</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Projects You Own Section -->
  <section class="projects-section">
    <h2>{% if is_superuser %}All Projects{% else %}Projects You Own{% endif %}</h2>
    {% if owned_projects %}
      <div class="project-grid">
        {% for project in owned_projects %}
          <div class="project-card owned">
            <div class="project-header">
              <h3>{{ project.title }}</h3>
              <div class="owner-badge">
                <span class="badge">Owner</span>
              </div>
            </div>
            <div class="project-body">
              <p><strong>Description:</strong> {{ project.description }}</p>
              <p><strong>Team Members:</strong>
                {% for member in project.memberships.all %}
                  {% if member.user != project.owner %}
                    {{ member.user.username }}{% if not forloop.last %}, {% endif %}
                  {% endif %}
                {% empty %}
                  No team members yet.
                {% endfor %}
              </p>
            </div>
            <div class="project-actions">
              <a href="{% url 'collaborate:editor' project.hash %}" class="btn btn-secondary">Open Editor</a>
              <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-primary">View Details</a>
            </div>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <div class="empty-projects">
        <p>You haven't created any projects yet.</p>
        <a href="{% url 'collaborate:create_project' %}" class="btn btn-primary">Create Your First Project</a>
      </div>
    {% endif %}
  </section>

  <!-- Projects You've Joined Section -->
  {% if not is_superuser %}
    <section class="projects-section">
      <h2>Projects You've Joined</h2>
      {% if joined_projects %}
        <div class="project-grid">
          {% for project in joined_projects %}
            <div class="project-card joined">
              <div class="project-header">
                <h3>{{ project.title }}</h3>
                <p><strong>Owner:</strong> {{ project.owner.username }}</p>
              </div>
              <div class="project-body">
                <p><strong>Description:</strong> {{ project.description }}</p>
                <p><strong>Team Members:</strong>
                  {% for member in project.memberships.all %}
                    {% if member.user != project.owner %}
                      {{ member.user.username }}{% if not forloop.last %}, {% endif %}
                    {% endif %}
                  {% empty %}
                    No other team members.
                  {% endfor %}
                </p>
              </div>
              <div class="project-actions">
                <a href="{% url 'collaborate:editor' project.hash %}" class="btn btn-secondary">Open Editor</a>
                <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-primary">View Details</a>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="empty-projects">
          <p>You haven't joined any projects yet.</p>
          <p>Browse available projects or wait for invitations.</p>
          <div class="empty-projects-actions">
            <a href="{% url 'collaborate:marketplace_list' %}" class="btn btn-secondary">
              <i class="fas fa-search"></i> Find Projects to Join
            </a>
          </div>
        </div>
      {% endif %}
    </section>
  {% endif %}
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);
});
</script>
{% endblock %}
