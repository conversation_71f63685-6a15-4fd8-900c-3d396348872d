{% extends 'base.html' %}
{% load static %}

{% block title %}Support Admin Dashboard - Forge X{% endblock %}

{% block content %}
<style>
/* Admin Dashboard Styles */
.admin-container {
  padding: 20px 0;
  min-height: 80vh;
}

.dashboard-header {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.dashboard-title {
  color: #C0ff6b;
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.stat-card {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(192, 255, 107, 0.1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.stat-label {
  color: #ffffff;
  font-size: 1rem;
}

.section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.section-title {
  color: #C0ff6b;
  font-size: 1.8rem;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.ticket-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.ticket-item {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.1);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.ticket-item:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.3);
}

.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ticket-id {
  color: #C0ff6b;
  font-weight: bold;
  text-decoration: none;
}

.ticket-id:hover {
  color: #ffffff;
  text-decoration: none;
}

.ticket-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.status-open { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
.status-in_progress { background: rgba(33, 150, 243, 0.2); color: #2196F3; }
.status-waiting_user { background: rgba(156, 39, 176, 0.2); color: #9C27B0; }
.status-resolved { background: rgba(76, 175, 80, 0.2); color: #4CAF50; }
.status-closed { background: rgba(158, 158, 158, 0.2); color: #9E9E9E; }

.ticket-subject {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 5px;
}

.ticket-meta {
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
  color: #999;
}

.priority-high { color: #F44336; }
.priority-urgent { color: #E91E63; font-weight: bold; }
.priority-medium { color: #FF9800; }
.priority-low { color: #4CAF50; }

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  flex-wrap: wrap;
}

.btn {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 10px 20px;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:hover {
  background: rgba(192, 255, 107, 0.2);
  color: #ffffff;
  text-decoration: none;
  transform: translateY(-2px);
}

.btn-primary {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #1a1a1a;
  border-color: #C0ff6b;
}

.btn-primary:hover {
  color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 10px;
  }

  .dashboard-header, .section {
    margin: 0 10px 20px 10px;
    padding: 20px;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .ticket-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }

  .ticket-meta {
    flex-direction: column;
    gap: 5px;
  }

  .action-buttons {
    flex-direction: column;
  }
}
</style>

<div class="admin-container">
  <div class="tile-wrap">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">Support Admin Dashboard</h1>
      <p style="color: #d5d5d5; margin: 0;">Manage support tickets and help users resolve their issues</p>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ total_tickets }}</div>
          <div class="stat-label">Total Tickets</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ open_tickets }}</div>
          <div class="stat-label">Open</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ in_progress_tickets }}</div>
          <div class="stat-label">In Progress</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ resolved_tickets }}</div>
          <div class="stat-label">Resolved</div>
        </div>
      </div>

      <div class="action-buttons">
        <a href="{% url 'accounts:support_categories_manage' %}" class="btn">
          <i class="fas fa-tags"></i> Manage Categories
        </a>
        <a href="{% url 'accounts:create_default_categories' %}" class="btn btn-primary">
          <i class="fas fa-plus-circle"></i> Setup Default Categories
        </a>
        <a href="{% url 'accounts:help_center' %}" class="btn">
          <i class="fas fa-home"></i> Help Center
        </a>
      </div>
    </div>

    <!-- Unassigned Tickets -->
    {% if unassigned_tickets %}
    <div class="section">
      <h2 class="section-title">
        <i class="fas fa-exclamation-triangle"></i>
        Unassigned Tickets
      </h2>
      <ul class="ticket-list">
        {% for ticket in unassigned_tickets %}
        <li class="ticket-item">
          <div class="ticket-header">
            <span class="ticket-id">
              #{{ ticket.ticket_id }}
            </span>
            <span class="ticket-status status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
          </div>
          <div class="ticket-subject">{{ ticket.subject }}</div>
          <div class="ticket-meta">
            <span>By: {{ ticket.user.username }}</span>
            <span class="priority-{{ ticket.priority }}">{{ ticket.get_priority_display }} Priority</span>
            <span>{{ ticket.created_at|timesince }} ago</span>
          </div>
          <div style="margin-top: 10px; text-align: right;">
            <a href="{% url 'accounts:support_ticket_detail' ticket.ticket_id %}" class="btn" style="font-size: 0.9rem; padding: 8px 16px;">
              <i class="fas fa-comments"></i> Open Chat
            </a>
          </div>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- My Assigned Tickets -->
    {% if my_tickets %}
    <div class="section">
      <h2 class="section-title">
        <i class="fas fa-user-check"></i>
        My Assigned Tickets
      </h2>
      <ul class="ticket-list">
        {% for ticket in my_tickets %}
        <li class="ticket-item">
          <div class="ticket-header">
            <span class="ticket-id">
              #{{ ticket.ticket_id }}
            </span>
            <span class="ticket-status status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
          </div>
          <div class="ticket-subject">{{ ticket.subject }}</div>
          <div class="ticket-meta">
            <span>By: {{ ticket.user.username }}</span>
            <span class="priority-{{ ticket.priority }}">{{ ticket.get_priority_display }} Priority</span>
            <span>{{ ticket.created_at|timesince }} ago</span>
          </div>
          <div style="margin-top: 10px; text-align: right;">
            <a href="{% url 'accounts:support_ticket_detail' ticket.ticket_id %}" class="btn" style="font-size: 0.9rem; padding: 8px 16px;">
              <i class="fas fa-comments"></i> Open Chat
            </a>
          </div>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- Recent Tickets -->
    <div class="section">
      <h2 class="section-title">
        <i class="fas fa-clock"></i>
        Recent Tickets
      </h2>
      {% if recent_tickets %}
      <ul class="ticket-list">
        {% for ticket in recent_tickets %}
        <li class="ticket-item">
          <div class="ticket-header">
            <span class="ticket-id">
              #{{ ticket.ticket_id }}
            </span>
            <span class="ticket-status status-{{ ticket.status }}">{{ ticket.get_status_display }}</span>
          </div>
          <div class="ticket-subject">{{ ticket.subject }}</div>
          <div class="ticket-meta">
            <span>By: {{ ticket.user.username }}</span>
            <span class="priority-{{ ticket.priority }}">{{ ticket.get_priority_display }} Priority</span>
            <span>{{ ticket.created_at|timesince }} ago</span>
            {% if ticket.assigned_to %}
              <span>Assigned to: {{ ticket.assigned_to.username }}</span>
            {% endif %}
          </div>
          <div style="margin-top: 10px; text-align: right;">
            <a href="{% url 'accounts:support_ticket_detail' ticket.ticket_id %}" class="btn" style="font-size: 0.9rem; padding: 8px 16px;">
              <i class="fas fa-comments"></i> Open Chat
            </a>
          </div>
        </li>
        {% endfor %}
      </ul>
      {% else %}
      <div class="empty-state">
        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 20px; display: block;"></i>
        No tickets found. All caught up!
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
