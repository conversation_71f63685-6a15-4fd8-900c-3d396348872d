# Generated by Django 5.2 on 2025-04-20 11:30

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0002_skill_alter_project_required_skills_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="userpairing",
            name="team_members",
            field=models.ManyToManyField(
                blank=True,
                help_text="All team members in this pairing (including user1 and user2 for backward compatibility)",
                related_name="team_pairings",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
