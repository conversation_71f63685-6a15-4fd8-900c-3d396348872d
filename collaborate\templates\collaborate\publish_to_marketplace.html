{% extends 'base.html' %}
{% load static %}

{% block title %}Publish to Marketplace - {{ project.title }}{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🚀 Publish to Marketplace</h1>
      <p class="welcome-subtitle">Showcase your project and find amazing collaborators</p>
    </div>
    
    <!-- Project Preview -->
    <div class="dashboard-section">
      <h2><i class="fas fa-project-diagram"></i> Current Project: {{ project.title }}</h2>
      <div class="mentorship-content">
        <div class="session-card">
          <div class="session-info">
            <h4>Project Overview</h4>
            <div class="session-details">
              <span class="session-date">
                <i class="fas fa-user"></i>
                Owner: {{ project.owner.username }}
              </span>
              <span class="session-date">
                <i class="fas fa-users"></i>
                Team Size: {{ project.team_size }} members
              </span>
            </div>
            <p style="color: rgba(255, 255, 255, 0.9); margin-top: 1rem;">{{ project.description|truncatewords:30 }}</p>
            {% if project.required_skills.all %}
              <div style="margin-top: 1rem;">
                <strong style="color: #C0ff6b;">Required Skills:</strong>
                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-top: 0.5rem;">
                  {% for skill in project.required_skills.all|slice:":5" %}
                    <span style="background: rgba(0, 212, 255, 0.2); color: #00d4ff; padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;">{{ skill.name }}</span>
                  {% endfor %}
                  {% if project.required_skills.count > 5 %}
                    <span style="background: rgba(0, 212, 255, 0.2); color: #00d4ff; padding: 0.3rem 0.6rem; border-radius: 12px; font-size: 0.8rem;">+{{ project.required_skills.count|add:"-5" }} more</span>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tips Section -->
    <div class="dashboard-section" style="background: rgba(0, 212, 255, 0.1); border-left: 3px solid #00d4ff;">
      <h2 style="color: #00d4ff;"><i class="fas fa-lightbulb"></i> Tips for a Great Marketplace Post</h2>
      <div class="getting-started">
        <div class="step-item">
          <div class="step-number">📸</div>
          <div class="step-content">
            <h4>Add an eye-catching image</h4>
            <p>Projects with images get 3x more views</p>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">📝</div>
          <div class="step-content">
            <h4>Write a compelling description</h4>
            <p>Explain what makes your project unique</p>
          </div>
        </div>
        <div class="step-item">
          <div class="step-number">🏷️</div>
          <div class="step-content">
            <h4>Use relevant tags</h4>
            <p>Help people discover your project</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Form -->
    <div class="dashboard-section">
      <h2><i class="fas fa-rocket"></i> Publish Your Project</h2>
      
      <form method="post" enctype="multipart/form-data" id="publishForm">
        {% csrf_token %}
        
        <!-- Display form errors -->
        {% if form.errors %}
          <div class="alert alert-danger" style="margin-bottom: 2rem;">
            <h5><i class="fas fa-exclamation-triangle"></i> Please fix the following errors:</h5>
            {% for field, errors in form.errors.items %}
              <ul style="list-style: none; padding: 0; margin: 0;">
                {% for error in errors %}
                  <li style="margin-bottom: 0.5rem;">{{ field|title }}: {{ error }}</li>
                {% endfor %}
              </ul>
            {% endfor %}
          </div>
        {% endif %}
        
        <!-- Featured Image -->
        <div class="form-group" style="margin-bottom: 2rem;">
          <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.8rem; display: block; font-size: 1.1rem;">
            <i class="fas fa-image"></i> Project Showcase Image
          </label>
          <div style="position: relative; display: inline-block; width: 100%;">
            {{ form.featured_image }}
            <div id="fileDisplay" style="background: rgba(255, 255, 255, 0.1); border: 2px dashed rgba(255, 255, 255, 0.3); border-radius: 10px; padding: 2rem; text-align: center; cursor: pointer; transition: all 0.3s ease;">
              <div style="font-size: 3rem; color: #C0ff6b; margin-bottom: 1rem;">
                <i class="fas fa-cloud-upload-alt"></i>
              </div>
              <div style="color: rgba(255, 255, 255, 0.8); font-size: 1.1rem;">
                Click to upload an image or drag and drop
              </div>
              <div id="fileName" style="color: #C0ff6b; font-weight: 600; margin-top: 0.5rem; display: none;"></div>
            </div>
          </div>
          <div class="form-help">
            Recommended: 1200x600px, max 5MB. Supported formats: JPG, PNG, GIF
          </div>
        </div>
        
        <!-- Showcase Description -->
        <div class="form-group" style="margin-bottom: 2rem;">
          <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.8rem; display: block; font-size: 1.1rem;">
            <i class="fas fa-edit"></i> Marketplace Description
          </label>
          {{ form.showcase_description }}
          <div id="descCounter" style="text-align: right; color: rgba(255, 255, 255, 0.6); font-size: 0.8rem; margin-top: 0.5rem;">
            <span id="descCount">0</span> characters
          </div>
          <div class="form-help">
            Write a compelling description that highlights what makes your project special and what kind of collaborators you're looking for.
          </div>
        </div>
        
        <!-- Tags -->
        <div class="form-group" style="margin-bottom: 2rem;">
          <label style="color: #C0ff6b; font-weight: 600; margin-bottom: 0.8rem; display: block; font-size: 1.1rem;">
            <i class="fas fa-tags"></i> Tags
          </label>
          {{ form.tags }}
          <div class="form-help">
            Add relevant tags separated by commas (e.g., "web development, react, startup, open source"). Maximum 10 tags.
          </div>
        </div>
        
        <!-- Submit Buttons -->
        <div class="mentorship-actions">
          <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Cancel
          </a>
          <button type="submit" class="btn btn-primary" id="submitBtn">
            <i class="fas fa-rocket"></i> Publish to Marketplace
          </button>
        </div>
      </form>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .session-card, .step-item, .form-group');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds (similar to dashboard)
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
    
    // File input handling
    const fileInput = document.getElementById('id_featured_image');
    const fileDisplay = document.getElementById('fileDisplay');
    const fileName = document.getElementById('fileName');
    
    fileInput.addEventListener('change', function(e) {
      const file = e.target.files[0];
      if (file) {
        fileName.textContent = file.name;
        fileName.style.display = 'block';
        fileDisplay.style.borderColor = '#C0ff6b';
        fileDisplay.style.background = 'rgba(192, 255, 107, 0.1)';
        
        // Show image preview if it's an image
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.style.cssText = 'max-width: 200px; max-height: 150px; border-radius: 5px; margin-top: 1rem;';
            
            // Remove existing preview
            const existingPreview = fileDisplay.querySelector('img');
            if (existingPreview) {
              existingPreview.remove();
            }
            
            fileDisplay.appendChild(img);
          };
          reader.readAsDataURL(file);
        }
      } else {
        fileName.style.display = 'none';
        fileDisplay.style.borderColor = 'rgba(255, 255, 255, 0.3)';
        fileDisplay.style.background = 'rgba(255, 255, 255, 0.1)';
        const existingPreview = fileDisplay.querySelector('img');
        if (existingPreview) {
          existingPreview.remove();
        }
      }
    });
    
    // Character counter for description
    const descTextarea = document.getElementById('id_showcase_description');
    const descCounter = document.getElementById('descCounter');
    const descCount = document.getElementById('descCount');
    
    function updateDescCounter() {
      const length = descTextarea.value.length;
      descCount.textContent = length;
      
      if (length > 1000) {
        descCounter.style.color = '#dc3545';
      } else if (length > 800) {
        descCounter.style.color = '#ffc107';
      } else {
        descCounter.style.color = 'rgba(255, 255, 255, 0.6)';
      }
    }
    
    descTextarea.addEventListener('input', updateDescCounter);
    updateDescCounter(); // Initial count
    
    // Form validation
    const form = document.getElementById('publishForm');
    const submitBtn = document.getElementById('submitBtn');
    
    form.addEventListener('submit', function(e) {
      submitBtn.disabled = true;
      submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Publishing...';
      
      // Re-enable button after 5 seconds in case of error
      setTimeout(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-rocket"></i> Publish to Marketplace';
      }, 5000);
    });
  });
</script>

<style>
/* Override collaborate.css with dashboard styling */
:root {
  --color-border: #C0ff6b;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent !important;
  padding: 50px 0 !important;
  min-height: 100vh !important;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Dashboard welcome section */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem !important;
  margin-bottom: 15px !important;
  background: linear-gradient(135deg, var(--color-border), #a0e066) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  color: transparent !important;
}

.welcome-subtitle {
  font-size: 1.2rem !important;
  color: #b0b0b0 !important;
  margin: 0 !important;
}

/* Dashboard sections */
.dashboard-section {
  margin-bottom: 40px !important;
}

.dashboard-section h2 {
  color: #ffffff !important;
  margin-bottom: 25px !important;
  text-align: center !important;
  font-size: 1.8rem !important;
}

/* Mentorship content */
.mentorship-content {
  margin-bottom: 30px;
}

.session-card {
  background-color: rgba(40, 40, 40, 0.5) !important;
  border-radius: 12px !important;
  padding: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 20px !important;
}

.session-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3) !important;
  border-color: rgba(192, 255, 107, 0.4) !important;
}

.session-info h4 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

.session-details {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
  margin-bottom: 10px !important;
  font-size: 0.9rem !important;
  color: #b0b0b0 !important;
}

.session-date {
  display: flex !important;
  align-items: center !important;
  gap: 5px !important;
}

/* Getting started */
.getting-started {
  display: flex !important;
  flex-direction: column !important;
  gap: 20px !important;
}

.step-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: 20px !important;
  padding: 20px !important;
  background-color: rgba(40, 40, 40, 0.3) !important;
  border-radius: 12px !important;
  border: 1px solid rgba(192, 255, 107, 0.1) !important;
  transition: all 0.5s ease !important;
}

.step-number {
  background-color: var(--color-border) !important;
  color: #000 !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: bold !important;
  font-size: 1.2rem !important;
  flex-shrink: 0 !important;
}

.step-content {
  flex: 1 !important;
}

.step-content h4 {
  color: var(--color-border) !important;
  margin-bottom: 10px !important;
  font-size: 1.1rem !important;
}

.step-content p {
  color: #e0e0e0 !important;
  margin-bottom: 15px !important;
  line-height: 1.5 !important;
}

/* Mentorship actions */
.mentorship-actions {
  display: flex !important;
  gap: 15px !important;
  justify-content: center !important;
  flex-wrap: wrap !important;
}

/* Publish to marketplace specific styles */

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  background-color: rgba(40, 40, 40, 0.8);
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 15px;
  border: none;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-info {
  border-left: 4px solid #17a2b8;
  background: linear-gradient(135deg, rgba(23, 162, 184, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-warning {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-danger {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(40, 40, 40, 0.8));
}

.alert-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.alert-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dashboard-style animations */
.session-card {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s ease;
}

.session-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.step-item {
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.5s ease;
}

.step-item.visible {
  opacity: 1;
  transform: translateX(0);
}

.form-group {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease;
}

.form-group.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Button styling consistency */
.btn {
  background-color: transparent !important;
  color: #ffffff !important;
  border: 2px solid var(--color-border) !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
  min-width: 120px !important;
}

.btn:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3) !important;
  transform: translateY(-2px) !important;
  text-decoration: none !important;
}

.btn-primary {
  background-color: rgba(192, 255, 107, 0.2) !important;
  border-color: var(--color-border) !important;
  color: var(--color-border) !important;
}

.btn-primary:hover {
  background-color: var(--color-border) !important;
  color: #000000 !important;
}

.btn-secondary {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  color: #ffffff !important;
}

.btn-sm {
  padding: 8px 16px !important;
  font-size: 0.9rem !important;
  min-width: 100px !important;
}

/* Enhanced form styling */
.form-control, textarea, input[type="file"] {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  padding: 0.8rem !important;
  color: white !important;
  font-size: 1rem !important;
  width: 100% !important;
  resize: vertical !important;
  transition: all 0.3s ease !important;
}

.form-control:focus, textarea:focus, input[type="file"]:focus {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: var(--color-border) !important;
  box-shadow: 0 0 0 0.2rem rgba(192, 255, 107, 0.25) !important;
  outline: none !important;
}

.form-control::placeholder, textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* File upload styling */
#fileDisplay {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

#fileDisplay:hover {
  border-color: rgba(192, 255, 107, 0.5);
  background: rgba(192, 255, 107, 0.05);
}

#fileDisplay.active {
  border-color: #C0ff6b;
  background: rgba(192, 255, 107, 0.1);
}

/* File input hidden styling */
input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* Character counter styling */
#descCounter {
  text-align: right;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  margin-top: 0.5rem;
  transition: color 0.3s ease;
}

/* Alert styling */
.alert {
  border-radius: 10px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  border: none;
}

.alert-danger {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1));
  color: #dc3545;
  border-left: 4px solid #dc3545;
}

/* Button enhancements */
.btn {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Skill tags styling */
.skill-tag {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 0.3rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  border: 1px solid rgba(0, 212, 255, 0.3);
  display: inline-block;
  margin: 0.2rem;
  transition: all 0.3s ease;
}

.skill-tag:hover {
  background: rgba(0, 212, 255, 0.3);
  transform: translateY(-1px);
}

/* Tips section styling */
.dashboard-section[style*="background: rgba(0, 212, 255, 0.1)"] {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(0, 212, 255, 0.05)) !important;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
}

/* Form labels */
label {
  color: #C0ff6b;
  font-weight: 600;
  margin-bottom: 0.8rem;
  display: block;
  font-size: 1.1rem;
}

/* Help text */
.form-help {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.9rem;
  margin-top: 0.5rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .mentorship-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .mentorship-actions .btn {
    width: 100%;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    margin-bottom: 1rem;
  }

  #fileDisplay {
    padding: 1.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }
}
</style>
{% endblock %}
