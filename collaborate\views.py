from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse, HttpResponseForbidden, HttpResponseNotFound, HttpResponseBadRequest
from .models import Project, ProjectMembership, ProjectApplication, ProjectAsset, UserPairing, Notification, TeamRejectionLog, TeamMatchLog, TeamMatchAnalysisLog, ProjectChatMessage
from django.contrib import messages
from django.db.models import Case, When, IntegerField, Q
from django.utils.timezone import now
import jwt
from django.conf import settings
from datetime import datetime, timedelta
import os
from .forms import ProjectForm
from django.contrib.auth import get_user_model
from .matching_engine import score_user_for_project, is_user_available_now
from .notification_utils import send_notification, send_project_invitation, send_bulk_notification, mark_notifications_as_read, send_system_notification
import requests
import logging

# Define the FastAPI base URL in one place for easy configuration
# You can set this to your public IP address when needed
FASTAPI_BASE_URL = os.environ.get("FASTAPI_SERVICE_URL", "http://localhost:8002")


@login_required
def editor_view(request, project_hash):
    """
    View for the code editor
    """
    try:
        project = Project.objects.get(hash=project_hash)

        # Check if user has permission to access this project
        if not project.can_access(request.user):
            return HttpResponseForbidden("You don't have permission to access this project")

        # Test FastAPI connection
        fastapi_status = "unknown"
        try:
            response = requests.get(f"{FASTAPI_BASE_URL}/", timeout=3)
            if response.status_code == 200:
                fastapi_status = "connected"
            else:
                fastapi_status = f"error_{response.status_code}"
        except requests.ConnectionError:
            fastapi_status = "connection_refused"
        except requests.Timeout:
            fastapi_status = "timeout"
        except Exception as e:
            fastapi_status = f"error_{str(e)}"

        return render(request, 'collaborate/editor.html', {
            'project': project,
            'api_url': FASTAPI_BASE_URL,
            'fastapi_status': fastapi_status
        })
    except Project.DoesNotExist:
        return HttpResponseNotFound("Project not found")

def test_docker_connection(request):
    """
    Test Docker connection and provide debugging information
    """
    results = {
        'fastapi_url': FASTAPI_BASE_URL,
        'tests': []
    }

    # Test 1: Basic FastAPI connection
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/", timeout=5)
        results['tests'].append({
            'name': 'FastAPI Root',
            'status': 'success' if response.status_code == 200 else 'error',
            'details': f"Status: {response.status_code}, Response: {response.text[:100]}..."
        })
    except requests.ConnectionError:
        results['tests'].append({
            'name': 'FastAPI Root',
            'status': 'error',
            'details': 'Connection refused - FastAPI service is not running'
        })
    except Exception as e:
        results['tests'].append({
            'name': 'FastAPI Root',
            'status': 'error',
            'details': f'Error: {str(e)}'
        })

    # Test 2: Files endpoint
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/api/files/test", timeout=5)
        results['tests'].append({
            'name': 'Files Endpoint',
            'status': 'success' if response.status_code in [200, 404] else 'error',
            'details': f"Status: {response.status_code}"
        })
    except Exception as e:
        results['tests'].append({
            'name': 'Files Endpoint',
            'status': 'error',
            'details': f'Error: {str(e)}'
        })

    # Test 3: Health endpoint
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/health", timeout=5)
        results['tests'].append({
            'name': 'Health Endpoint',
            'status': 'success' if response.status_code == 200 else 'error',
            'details': f"Status: {response.status_code}"
        })
    except Exception as e:
        results['tests'].append({
            'name': 'Health Endpoint',
            'status': 'error',
            'details': f'Error: {str(e)}'
        })

    return JsonResponse(results)

@login_required
def project_list(request):
    """View to list all projects with different sections"""
    if request.user.is_superuser:
        # Superusers can see all projects
        owned_projects = Project.objects.all()
        joined_projects = []
    else:
        # Regular users
        # Get projects owned by the user
        owned_projects = Project.objects.filter(owner=request.user)

        # Get projects where the user is a member but not the owner
        joined_projects = Project.objects.filter(
            memberships__user=request.user
        ).exclude(
            owner=request.user
        ).distinct()

    context = {
        'owned_projects': owned_projects,
        'joined_projects': joined_projects,
        'is_superuser': request.user.is_superuser
    }
    return render(request, 'collaborate/project_list.html', context)

@login_required
def create_project(request):
    """View to create a new project"""
    if request.method == 'POST':
        form = ProjectForm(request.POST)
        if form.is_valid():
            project = form.save(commit=False)
            project.owner = request.user
            project.ai_matching_enabled = True  # Enforce AI matching enabled
            project.matching_approach = 'ai_based'  # Default to AI-based matching
            project.save()
            form.save_m2m()  # Save many-to-many relationships

            # Handle required skills
            from .models import Skill
            if 'required_skills_input' in request.POST and request.POST['required_skills_input']:
                skill_names = [name.strip() for name in request.POST['required_skills_input'].split(',') if name.strip()]
                project.required_skills.clear()

                for skill_name in skill_names:
                    skill, created = Skill.objects.get_or_create(name=skill_name)
                    project.required_skills.add(skill)

            # Handle critical skills - must be a subset of required skills
            if 'critical_skills_input' in request.POST and request.POST['critical_skills_input']:
                critical_skill_names = [name.strip() for name in request.POST['critical_skills_input'].split(',') if name.strip()]
                project.critical_skills.clear()

                for skill_name in critical_skill_names:
                    try:
                        skill = Skill.objects.get(name=skill_name)
                        # Only add as critical if it's already in required skills
                        if project.required_skills.filter(id=skill.id).exists():
                            project.critical_skills.add(skill)
                    except Skill.DoesNotExist:
                        pass  # Skip skills that don't exist

            # Create project membership for the owner
            ProjectMembership.objects.create(
                user=request.user,
                project=project,
                role='owner'
            )

            # Trigger AI matching logic
            from .matching_engine import find_best_team_with_availability_priority
            from django.contrib.auth import get_user_model
            User = get_user_model()
            available_users = User.objects.exclude(id=request.user.id).filter(is_active=True)
            find_best_team_with_availability_priority(project, available_users)

            return redirect('collaborate:project_detail', project_hash=project.hash)  # Redirect to project details
        else:
            # Log form errors for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error("Form validation failed: %s", form.errors)

    else:
        form = ProjectForm()

    return render(request, 'collaborate/create_project.html', {'form': form})

@login_required
def project_detail(request, project_hash):
    """View to show project details and handle applications or AI-based matching"""
    project = get_object_or_404(Project, hash=project_hash)
    is_member = ProjectMembership.objects.filter(
        user=request.user,
        project=project
    ).exists()

    # Check for any existing application (pending or rejected)
    application = ProjectApplication.objects.filter(
        user=request.user,
        project=project
    ).first()

    has_pending_application = application and application.status == 'pending'
    was_rejected = application and application.status == 'rejected'

    suggested_teammates = []
    if hasattr(project, 'match_users'):
        suggested_teammates = project.match_users()

    # Fetch storage usage from FastAPI
    storage_data = {"used_mb": 0, "limit_mb": 3072, "used_percent": 0}
    try:
        response = requests.get(f"{FASTAPI_BASE_URL}/usage/{project.id}")
        if response.status_code == 200:
            storage_data = response.json()
            # Calculate percentage and round to 2 decimal places
            storage_data["used_percent"] = round((storage_data["used_mb"] / storage_data["limit_mb"]) * 100, 2)
    except requests.RequestException as e:
        logging.error(f"Failed to fetch storage data: {e}")

    context = {
        'project': project,
        'is_member': is_member,
        'has_pending_application': has_pending_application,
        'was_rejected': was_rejected,
        'application': application,
        'suggested_teammates': suggested_teammates,
        'storage_data': storage_data
    }
    return render(request, 'collaborate/project_detail.html', context)



@login_required
def apply_project(request, project_hash):
    """Handle project application submissions"""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user is already a member
    if ProjectMembership.objects.filter(user=request.user, project=project).exists():
        messages.error(request, 'You are already a member of this project.')
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Check if user was previously rejected
    rejected_application = ProjectApplication.objects.filter(
        user=request.user,
        project=project,
        status='rejected'
    ).exists()

    if rejected_application:
        messages.error(request, 'Your previous application was rejected. You cannot reapply to this project.')
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Check if user already has a pending application
    existing_application = ProjectApplication.objects.filter(
        user=request.user,
        project=project,
        status='pending'
    ).first()

    if existing_application:
        messages.info(request, 'You already have a pending application for this project.')
    else:
        # Create new application
        application = ProjectApplication.objects.create(
            user=request.user,
            project=project,
            status='pending',
            message=request.POST.get('message', '')
        )

        # Send notification to project owner
        send_notification(
            user=project.owner,
            message=f"{request.user.username} has applied to join your project '{project.title}'",
            project=project
        )

        messages.success(request, 'Your application has been submitted successfully.')

    return redirect('collaborate:project_detail', project_hash=project.hash)

@login_required
def manage_applications(request):
    """View to manage project applications"""
    # Get all projects owned by the user
    owned_projects = Project.objects.filter(owner=request.user)

    # Get all applications for these projects, ordered by status (pending first) and creation date
    applications = ProjectApplication.objects.filter(
        project__owner=request.user
    ).select_related('user', 'project').order_by(
        # Order by status (putting 'pending' first)
        Case(
            When(status='pending', then=0),
            When(status='accepted', then= 1),
            When(status='rejected', then =2),
            default=3,
            output_field=IntegerField()
        ),
        '-created_at'  # Then by creation date, newest first
    )

    # Separate applications by status
    pending_applications = applications.filter(status='pending')
    processed_applications = applications.exclude(status='pending')

    context = {
        'pending_applications': pending_applications,
        'processed_applications': processed_applications,
        'owned_projects': owned_projects
    }
    return render(request, 'collaborate/manage_applications.html', context)

@login_required
def handle_application(request, application_id, action):
    """Handle accepting or rejecting applications"""
    application = get_object_or_404(ProjectApplication, id=application_id)

    # Check if the user is the project owner
    if application.project.owner != request.user:
        messages.error(request, 'You do not have permission to manage this application.')
        return redirect('collaborate:manage_applications')

    if application.status != 'pending':
        messages.error(request, 'This application has already been processed.')
        return redirect('collaborate:manage_applications')

    if action == 'accept':
        # Create project membership
        ProjectMembership.objects.create(
            user=application.user,
            project=application.project,
            role='member'
        )
        application.status = 'accepted'

        # Send notification to the applicant
        send_notification(
            user=application.user,
            message=f"Your application to join project '{application.project.title}' has been accepted!",
            project=application.project
        )

        messages.success(request, f'Application from {application.user.username} has been accepted.')

    elif action == 'reject':
        application.status = 'rejected'

        # Send notification to the applicant
        send_notification(
            user=application.user,
            message=f"Your application to join project '{application.project.title}' has been rejected.",
            project=application.project
        )

        messages.success(request, f'Application from {application.user.username} has been rejected.')

    application.save()
    return redirect('collaborate:manage_applications')

@login_required
def delete_project(request, project_hash):
    """View to delete a project"""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if the user is the project owner
    if project.owner != request.user:
        messages.error(request, 'You do not have permission to delete this project.')
        return redirect('collaborate:project_list')

    if request.method == 'POST':
        project_title = project.title
        project.delete()
        messages.success(request, f'Project "{project_title}" has been deleted successfully.')
        return redirect('collaborate:project_list')

    return render(request, 'collaborate/delete_project.html', {'project': project})

@login_required
def update_project_status(request, project_hash):
    """View to update project status"""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if the user is the project owner
    if project.owner != request.user:
        messages.error(request, 'You do not have permission to update this project.')
        return redirect('collaborate:project_detail', project_hash=project.hash)

    if request.method == 'POST':
        new_status = request.POST.get('status')
        if new_status in dict(Project.STATUS_CHOICES):
            project.status = new_status
            project.save()
            messages.success(request, f'Project status updated to {project.get_status_display()}')
        else:
            messages.error(request, 'Invalid status selected.')

        return redirect('collaborate:project_detail', project_hash=project.hash)

    return render(request, 'collaborate/update_project_status.html', {
        'project': project,
        'status_choices': Project.STATUS_CHOICES
    })

def generate_editor_token(user):
    payload = {
        'user_id': user.id,
        'username': user.username
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

from collaborate.utils.ai_helpers import get_missing_skills

@login_required
def match_users_view(request, project_hash):
    """View to show matched users using the advanced matching engine"""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if the user is the project owner
    if project.owner != request.user:
        messages.error(request, "Only the project owner can access the matching users feature.")
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Import the advanced matching engine function
    from .matching_engine import find_best_team_with_availability_priority

    # Get all available users except the project owner
    from django.contrib.auth import get_user_model
    User = get_user_model()
    available_users = User.objects.exclude(id=project.owner.id).filter(is_active=True)

    # Use the advanced matching algorithm
    match_result = find_best_team_with_availability_priority(project, available_users)

    # Format the results for display
    matched_users = []
    selected_users = []  # Track actual user objects for missing skills check

    for user in match_result["team_members"]:
        # Add user to selected_users for missing skills check
        selected_users.append(user)

        # Check if this user is already a member of the project
        is_member = ProjectMembership.objects.filter(user=user, project=project).exists()

        # Calculate user score using the same method as in matching_engine.py
        score, matching_skills, _ = score_user_for_project(user, project, project.owner)

        # Add availability bonus if applicable
        if is_user_available_now(user):
            score += 10

        # Get invitation status for this user
        invitation = Notification.objects.filter(
            user=user,
            project=project
        ).order_by('-created_at').first()

        invitation_status = "not_invited"  # Default status
        invitation_date = None

        if invitation:
            if invitation.is_accepted is None:
                invitation_status = "pending"
            elif invitation.is_accepted:
                invitation_status = "accepted"
            else:
                invitation_status = "declined"
            invitation_date = invitation.created_at

        matched_users.append({
            "user": user,
            "score": score,
            "matching_skills": matching_skills,
            "is_member": is_member,
            "is_available_now": is_user_available_now(user),
            "invitation_status": invitation_status,
            "invitation_date": invitation_date
        })

    # Get missing skills
    missing_skills = get_missing_skills(project, selected_users)

    # Track accepted and declined users using Notification model
    accepted_users = User.objects.filter(
        notifications__project=project,
        notifications__is_accepted=True
    ).distinct()

    declined_users = User.objects.filter(
        notifications__project=project,
        notifications__is_accepted=False
    ).distinct()

    return render(request, 'collaborate/match_users.html', {
        'project': project,
        'matched_users': matched_users,
        'skill_coverage_percent': match_result.get("skill_coverage_percent", 0),
        'is_optimal_match': match_result.get("is_optimal_match", False),
        'missing_skills': missing_skills,
        'accepted_users': accepted_users,
        'declined_users': declined_users,
    })

def preview_project(request, project_hash):
    """
    Redirect to the FastAPI service for file preview
    """
    from django.shortcuts import redirect
    import requests

    # Get project by hash to get the ID for FastAPI
    try:
        project = Project.objects.get(hash=project_hash)
        project_id = project.id
    except Project.DoesNotExist:
        return HttpResponseNotFound("Project not found")

    # Build the preview URL using project ID for FastAPI compatibility
    preview_url = f"{FASTAPI_BASE_URL}/api/preview/{project_id}/"

    # For debugging - log the URL being accessed
    print(f"Redirecting to preview URL: {preview_url}")

    # Try to check if the endpoint exists before redirecting
    try:
        response = requests.head(preview_url, timeout=2)
        if response.status_code >= 400:
            # If the endpoint doesn't exist, try without the /api prefix
            # Some FastAPI deployments might not use the /api prefix
            alt_preview_url = f"{FASTAPI_BASE_URL}/preview/{project_id}/"
            print(f"First URL failed, trying alternative URL: {alt_preview_url}")
            return redirect(alt_preview_url)
    except:
        # If we can't connect, just proceed with original redirect
        pass

    # Redirect to the FastAPI preview endpoint
    return redirect(preview_url)

@login_required
def notifications(request):
    """View to show user's notifications"""
    notifications = request.user.notifications.order_by('-created_at')

    # Check if there are any unread notifications
    has_unread_notifications = notifications.filter(is_read=False).exists()

    # Mark notifications as read if requested
    if request.method == 'POST' and 'mark_read' in request.POST:
        notification_id = request.POST.get('notification_id')
        if notification_id:
            try:
                notification = Notification.objects.get(id=notification_id, user=request.user)
                notification.is_read = True
                notification.save()
                messages.success(request, "Notification marked as read.")
            except Notification.DoesNotExist:
                pass
        elif 'mark_all_read' in request.POST:
            Notification.objects.filter(user=request.user, is_read=False).update(is_read=True)
            messages.success(request, "All notifications marked as read.")

    return render(request, 'collaborate/notifications.html', {
        'notifications': notifications,
        'has_unread_notifications': has_unread_notifications
    })

@login_required
@user_passes_test(lambda u: u.is_staff)
def send_system_notification_view(request):
    """Admin view to send system-wide notifications to all users"""
    if request.method == 'POST':
        title = request.POST.get('title')
        message = request.POST.get('message')
        notification_type = request.POST.get('notification_type', 'system')

        if title and message:
            # Send the system notification
            notifications = send_system_notification(title, message, notification_type)

            messages.success(
                request,
                f"System notification sent to {len(notifications)} users: '{title}'"
            )
            return redirect('collaborate:notifications')
        else:
            messages.error(request, "Title and message are required.")

    # For GET requests, show the form
    return render(request, 'collaborate/send_system_notification.html', {
        'notification_types': Notification.NOTIFICATION_TYPES
    })

@login_required
def test_notification_view(request):
    """View to test the notification system"""
    return render(request, 'collaborate/test_notification.html')

@login_required
def handle_invite(request, notification_id):
    """Handle accepting or declining project invitations"""
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)

    if request.method == 'POST':
        action = request.POST.get('action')
        project = notification.project
        user = request.user
        if notification.is_accepted is not None:
            messages.warning(request, "You've already responded to this invitation.")
            return redirect('collaborate:notifications')
        if action == 'accept':
            notification.is_accepted = True
            if project:
                membership, created = ProjectMembership.objects.get_or_create(
                    user=user,
                    project=project,
                    defaults={'role': 'member'}
                )
                # Update accepted/declined teammates
                project.accepted_teammates.add(user)
                project.declined_teammates.remove(user)
                messages.success(request, f"You've accepted the invitation to join '{project.title}'.")
        elif action == 'decline':
            notification.is_accepted = False
            if project:
                ProjectMembership.objects.filter(user=user, project=project).delete()
                # Update accepted/declined teammates
                project.declined_teammates.add(user)
                project.accepted_teammates.remove(user)
                # --- TeamRejectionLog logic ---
                # Calculate overlap & timezone diff
                overlap = {skill.name: skill in user.user_skills.all().values_list('skill', flat=True) for skill in project.required_skills.all()}
                tz_diff = abs(getattr(user, 'timezone_offset', 0) - getattr(project.owner, 'timezone_offset', 0))
                # Placeholder for availability check
                def check_availability_match(u1, u2):
                    return True  # TODO: Implement real logic
                availability_mismatch = not check_availability_match(user, project.owner)
                TeamRejectionLog.objects.create(
                    user=user,
                    project=project,
                    rejected_skills_overlap=overlap,
                    timezone_difference=tz_diff,
                    availability_mismatch=availability_mismatch
                )
        # Mark notification as read
        notification.is_read = True
        notification.save()

    return redirect('collaborate:notifications')

@login_required
def search_users(request, project_hash):
    """Search for users to invite to a project"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        logger.info(f"Search users request received for project {project_hash}")
        logger.info(f"Request method: {request.method}")
        logger.info(f"Request GET params: {request.GET}")
        logger.info(f"Request headers: {dict(request.headers)}")

        project = get_object_or_404(Project, hash=project_hash)
        logger.info(f"Project found: {project.title}")

        # Check if the user has permission to invite (must be project owner)
        if project.owner != request.user:
            logger.warning(f"Permission denied: {request.user.username} is not the owner of project {project_hash}")
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'message': "Only the project owner can search for users to invite."}, status=403)
            messages.error(request, "Only the project owner can search for users to invite.")
            return redirect('collaborate:project_detail', project_hash=project.hash)

        # Get the search query
        query = request.GET.get('q', '').strip()
        logger.info(f"Search query: '{query}'")

        if not query:
            logger.info("Empty query, returning empty results")
            return JsonResponse({'users': []})

        # Get the User model
        from django.contrib.auth import get_user_model
        User = get_user_model()

        # Search for users by username, first_name, last_name, or email
        # Exclude the project owner and users who are already members or have pending invitations
        member_ids = ProjectMembership.objects.filter(project=project).values_list('user_id', flat=True)
        invited_ids = Notification.objects.filter(project=project, is_accepted__isnull=True).values_list('user_id', flat=True)

        logger.info(f"Excluding project owner ({project.owner.id}), members ({list(member_ids)}), and invited users ({list(invited_ids)})")

        # First try an exact match on username
        exact_match_users = User.objects.filter(
            username__iexact=query
        ).exclude(
            Q(id=project.owner.id) |
            Q(id__in=member_ids) |
            Q(id__in=invited_ids)
        ).distinct()

        # Then try a contains match
        contains_match_users = User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(email__icontains=query)
        ).exclude(
            Q(id=project.owner.id) |
            Q(id__in=member_ids) |
            Q(id__in=invited_ids) |
            Q(id__in=exact_match_users.values_list('id', flat=True))  # Exclude exact matches
        ).distinct()[:10]  # Limit to 10 results

        # Combine the results, with exact matches first
        users = list(exact_match_users) + list(contains_match_users)
        users = users[:10]  # Limit to 10 results

        logger.info(f"Found {len(users)} users matching the query")

        # Format the results
        results = []
        for user in users:
            # Get user skills from UserSkill model
            from collaborate.models import UserSkill
            user_skill_objects = UserSkill.objects.filter(user=user).select_related('skill')
            user_skills = [us.skill.name for us in user_skill_objects]

            # Also try to get skills from the profile if available
            try:
                if hasattr(user, 'profile') and user.profile:
                    profile_skills = [skill.name for skill in user.profile.skills.all()]
                    # Combine both skill sets
                    user_skills = list(set(user_skills + profile_skills))
            except Exception as e:
                logger.error(f"Error getting profile skills: {e}")

            # Check if the user has any matching skills with the project
            matching_skills = []
            missing_skills = []
            required_skill_names = []

            # Get all required skill names
            for skill in project.required_skills.all():
                required_skill_names.append(skill.name)
                if skill.name in user_skills:
                    matching_skills.append(skill.name)
                else:
                    missing_skills.append(skill.name)

            results.append({
                'id': user.id,
                'username': user.username,
                'full_name': f"{user.first_name} {user.last_name}".strip(),
                'email': user.email,
                'matching_skills': matching_skills,
                'missing_skills': missing_skills,
                'matching_skills_count': len(matching_skills),
                'required_skills_count': project.required_skills.count()
            })

        logger.info(f"Returning {len(results)} formatted user results")
        response_data = {'users': results}
        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"Error in search_users: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
def invite_user(request, project_hash, user_id):
    """Send an invitation to a user for a project"""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if the user has permission to invite (must be project owner)
    if project.owner != request.user:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "Only the project owner can send invitations."}, status=403)
        messages.error(request, "Only the project owner can send invitations.")
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Get the user to invite
    User = get_user_model()
    try:
        user_to_invite = User.objects.get(id=user_id)
    except User.DoesNotExist:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "The selected user does not exist."}, status=404)
        messages.error(request, "The selected user does not exist.")
        return redirect('collaborate:match_users', project_hash=project.hash)

    # Check if user is already a member
    if ProjectMembership.objects.filter(user=user_to_invite, project=project).exists():
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'warning',
                'message': f"{user_to_invite.username} is already a member of this project."
            })
        messages.warning(request, f"{user_to_invite.username} is already a member of this project.")
        return redirect('collaborate:match_users', project_hash=project.hash)

    # Check if notification already exists for this user and project
    existing_notification = Notification.objects.filter(
        user=user_to_invite,
        project=project,
        is_accepted__isnull=True  # No response yet
    ).exists()

    if existing_notification:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'status': 'warning',
                'message': f"{user_to_invite.username} has already been invited to this project."
            })
        messages.warning(request, f"{user_to_invite.username} has already been invited to this project.")
        return redirect('collaborate:match_users', project_hash=project.hash)

    # Send project invitation using utility function
    send_project_invitation(
        user=user_to_invite,
        project=project,
        message=f"You have been invited to join the project '{project.title}' by {request.user.username}!"
    )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': 'success',
            'message': f"Invitation sent to {user_to_invite.username}.",
            'user_id': user_id
        })

    messages.success(request, f"Invitation sent to {user_to_invite.username}.")
    return redirect('collaborate:match_users', project_hash=project.hash)

@login_required
def remove_team_member(request, project_id, user_id):
    """Remove a team member from a project"""
    project = get_object_or_404(Project, id=project_id)

    # Check if the user has permission to remove team members (must be project owner)
    if project.owner != request.user:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "Only the project owner can remove team members."}, status=403)
        messages.error(request, "Only the project owner can remove team members.")
        return redirect('collaborate:project_detail', project_id=project.id)

    # Get the user to remove
    User = get_user_model()
    try:
        user_to_remove = User.objects.get(id=user_id)
    except User.DoesNotExist:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "The selected user does not exist."}, status=404)
        messages.error(request, "The selected user does not exist.")
        return redirect('collaborate:project_detail', project_id=project.id)

    # Check if the user is a member of the project
    membership = ProjectMembership.objects.filter(user=user_to_remove, project=project).first()
    if not membership:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "This user is not a member of the project."}, status=400)
        messages.error(request, "This user is not a member of the project.")
        return redirect('collaborate:project_detail', project_id=project.id)

    # Remove the user from the project
    membership.delete()

    # Remove from accepted_teammates if present
    project.accepted_teammates.remove(user_to_remove)

    # Send notification to the removed user
    from .notification_utils import send_notification
    send_notification(
        user=user_to_remove,
        title="Removed from Project",
        message=f"You have been removed from the project '{project.title}' by the project owner.",
        project=None
    )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': 'success',
            'message': f"{user_to_remove.username} has been removed from the project."
        })

    messages.success(request, f"{user_to_remove.username} has been removed from the project.")
    return redirect('collaborate:project_detail', project_id=project.id)

@login_required
def invite_all_matched_users(request, project_id):
    """Send invitations to all matched users for a project with score ≥ 0"""
    project = get_object_or_404(Project, id=project_id)

    # Check if the user has permission to invite (must be project owner)
    if project.owner != request.user:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "Only the project owner can send invitations."}, status=403)
        messages.error(request, "Only the project owner can send invitations.")
        return redirect('collaborate:project_detail', project_id=project.id)

    # Check if it's a POST request for security
    if request.method != 'POST':
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': "Invalid request method."}, status=400)
        messages.error(request, "Invalid request method.")
        return redirect('collaborate:match_users', project_id=project.id)

    # Import the matching engine function
    from .matching_engine import find_best_team_with_availability_priority, score_user_for_project

    # Get all available users except the project owner
    User = get_user_model()
    available_users = User.objects.exclude(id=project.owner.id).filter(is_active=True)

    # Use the advanced matching algorithm
    match_result = find_best_team_with_availability_priority(project, available_users)

    # Keep track of how many invitations were sent
    invitations_sent = 0
    already_members = 0
    already_invited = 0
    negative_score = 0
    invited_users = []

    # Process each potential team member
    for user in match_result["team_members"]:
        # Calculate user score to filter out negative scores
        score, _, _ = score_user_for_project(user, project, project.owner)

        # Add availability bonus if applicable
        if is_user_available_now(user):
            score += 10

        # Skip users with negative scores
        if score < 0:
            negative_score += 1
            continue

        # Skip users who are already members
        if ProjectMembership.objects.filter(user=user, project=project).exists():
            already_members += 1
            continue

        # Skip users who already have a pending invitation
        if Notification.objects.filter(
            user=user,
            project=project,
            is_accepted__isnull=True  # No response yet
        ).exists():
            already_invited += 1
            continue

        # Send project invitation using utility function
        send_project_invitation(
            user=user,
            project=project,
            message=f"You have been invited to join the project '{project.title}' by {request.user.username}!"
        )
        invitations_sent += 1
        invited_users.append(user.id)

        # Debug log
        print(f"Invited user: {user.username} (Score: {score})")

    # Return JSON response for AJAX requests
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': 'success',
            'invited_count': invitations_sent,
            'already_members': already_members,
            'already_invited': already_invited,
            'negative_score': negative_score,
            'invited_users': invited_users,
            'message': f"Invites sent to {invitations_sent} users!"
        })

    # Provide appropriate feedback
    if invitations_sent > 0:
        messages.success(request, f"✅ Invites sent to {invitations_sent} users!")
    else:
        messages.info(request, "No new invitations were sent.")

    if already_members > 0:
        messages.info(request, f"{already_members} matched users are already project members.")

    if already_invited > 0:
        messages.info(request, f"{already_invited} matched users already have pending invitations.")

    if negative_score > 0:
        messages.info(request, f"{negative_score} users were skipped due to low match scores.")

    return redirect('collaborate:match_users', project_id=project.id)

@login_required
def mark_notification_as_read(request, notification_id):
    try:
        notification = Notification.objects.get(id=notification_id, user=request.user)
        notification.is_read = True
        notification.save()
        return JsonResponse({'status': 'success'})
    except Notification.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'Notification not found'})

@login_required
def auto_pairing_view(request, project_id):
    """
    View to display auto-selected teammates for a given project using existing matched users template.
    """
    project = get_object_or_404(Project, id=project_id)

    # Check if the user has permission to view the project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Use accepted/declined teammates logic
    already_accepted = set(project.accepted_teammates.all())
    already_declined = set(project.declined_teammates.all())
    remaining_slots = project.team_size - len(already_accepted)

    # We'll use the auto_select_teammates_for_project function to get candidates

    # Only suggest for remaining slots
    from collaborate.utils.auto_pairing import auto_select_teammates_for_project, get_team_coverage_stats
    # Patch: auto_select_teammates_for_project should accept a user queryset for available_users
    # For now, filter after selection
    selected_teammates = list(already_accepted)
    if remaining_slots > 0:
        # Get best candidates for remaining slots
        candidates = auto_select_teammates_for_project(project)
        # Only add from available_users and not already accepted
        for user in candidates:
            if user in already_accepted or user in already_declined:
                continue
            if len(selected_teammates) < project.team_size:
                selected_teammates.append(user)
            if len(selected_teammates) >= project.team_size:
                break

    # Get coverage statistics
    coverage_stats = get_team_coverage_stats(project, selected_teammates)

    # Format the data for display, similar to match_users_view
    matched_users = []
    for user in selected_teammates:
        is_member = ProjectMembership.objects.filter(user=user, project=project).exists()
        score, matching_skills, _ = score_user_for_project(user, project, project.owner)
        if is_user_available_now(user):
            score += 10
        matched_users.append({
            "user": user,
            "score": score,
            "matching_skills": matching_skills,
            "is_member": is_member,
            "is_available_now": is_user_available_now(user)
        })

    context = {
        'project': project,
        'matched_users': matched_users,
        'skill_coverage_percent': coverage_stats.get('coverage_percent', 0),
        'is_optimal_match': coverage_stats.get('is_complete_coverage', False),
        'missing_skills': coverage_stats.get('missing_skills', []),
        'critical_coverage_percent': coverage_stats.get('critical_coverage_percent', 0),
        'ai_selected': True,  # Flag to show special message
    }

    return render(request, 'collaborate/match_users.html', context)


@login_required
def get_project_chat_history(request, project_hash):
    """Get chat history for a collaborative project"""
    try:
        # Get the project
        project = get_object_or_404(Project, hash=project_hash)

        # Check if user has access to this project
        if not (project.owner == request.user or
                ProjectMembership.objects.filter(user=request.user, project=project).exists()):
            return JsonResponse({'error': 'Access denied'}, status=403)

        # Get chat messages for this project
        messages = ProjectChatMessage.objects.filter(
            project=project
        ).select_related('sender').order_by('created_at')

        print(f"DEBUG: Found {messages.count()} messages for project {project.hash}")

        # Format messages for JSON response
        chat_history = []
        for message in messages:
            chat_history.append({
                'id': message.id,
                'content': message.message,
                'username': message.sender.username if message.sender else 'System',
                'user_id': message.sender.id if message.sender else None,
                'message_type': message.message_type,
                'timestamp': message.created_at.timestamp()
            })

        return JsonResponse({
            'success': True,
            'messages': chat_history
        })

    except Exception as e:
        print(f"DEBUG: Error in get_project_chat_history: {str(e)}")
        return JsonResponse({'error': str(e)}, status=500)

from collaborate.utils.ai_helpers import get_missing_skills
# from .code_chatbot import get_chatbot_response, analyze_current_code, clear_chatbot_history

@login_required
def finalize_ai_team(request, project_id):
    """
    View to finalize and review an AI-selected team for a project.
    Shows team composition and highlights any missing skills.
    """
    project = get_object_or_404(Project, id=project_id)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Get the User model
    User = get_user_model()

    # Get selected team members (from accepted_teammates or memberships)
    selected_users = list(project.accepted_teammates.all())

    # If no accepted teammates yet, get from memberships
    if not selected_users:
        selected_users = list(User.objects.filter(
            project_memberships__project=project
        ).exclude(id=project.owner.id))

    # Add owner to the list
    if project.owner not in selected_users:
        selected_users.append(project.owner)

    # Detect missing skills
    missing_skills = get_missing_skills(project, selected_users)

    # Get team coverage statistics
    from collaborate.utils.ranking import get_team_skill_coverage
    coverage_stats = get_team_skill_coverage(project, selected_users)

    context = {
        "project": project,
        "missing_skills": missing_skills,
        "selected_users": selected_users,
        "coverage_stats": coverage_stats,
    }

    return render(request, "collaborate/match_users.html", context)

from django.views.decorators.http import require_POST

@require_POST
@login_required
def re_pair_team(request, project_id):
    """
    Re-pair a team when some users have declined invitations,
    finding replacements while excluding both accepted and declined users.
    """
    project = get_object_or_404(Project, id=project_id)

    # Check if user has permission (must be project owner)
    if project.owner != request.user:
        messages.error(request, "Only the project owner can re-pair the team.")
        return redirect('collaborate:project_detail', project_id=project.id)

    # Get accepted and declined users
    User = get_user_model()
    accepted_users = list(project.accepted_teammates.all())
    declined_users = list(project.declined_teammates.all())

    # Calculate remaining slots
    remaining_slots = project.team_size - len(accepted_users) - 1  # -1 for owner

    if remaining_slots <= 0:
        messages.info(request, "Your team is already full! No need to re-pair.")
        return redirect('collaborate:match_users', project_id=project.id)

    # Get all available users excluding accepted and declined
    exclude_ids = [user.id for user in accepted_users + declined_users + [project.owner]]
    available_users = User.objects.exclude(id__in=exclude_ids).filter(is_active=True)

    # Use the matching engine to find best teammates for remaining slots
    from .matching_engine import find_best_team_with_availability_priority

    # Create a copy of the project for simulation
    from copy import copy
    sim_project = copy(project)
    # Adjust team size to only what's needed for remaining slots
    sim_project.team_size = remaining_slots

    # Find best teammates for remaining slots
    match_result = find_best_team_with_availability_priority(sim_project, available_users)
    new_teammates = match_result.get("team_members", [])

    if not new_teammates:
        messages.warning(request, "No suitable replacements found for declined teammates.")
        return redirect('collaborate:match_users', project_id=project.id)

    # Send invitations to new teammates
    invites_sent = 0
    for user in new_teammates:
        # Skip if already invited
        if Notification.objects.filter(user=user, project=project, is_accepted__isnull=True).exists():
            continue

        # Create invitation
        send_project_invitation(
            user=user,
            project=project,
            message=f"You have been selected as a replacement teammate for project '{project.title}'!"
        )
        invites_sent += 1

    if invites_sent > 0:
        messages.success(request, f"Invitations sent to {invites_sent} new potential teammates to replace those who declined.")
    else:
        messages.info(request, "No new invitations were sent. Potential replacements may already have pending invites.")

    return redirect('collaborate:match_users', project_id=project.id)

@login_required
def pairing_debug_view(request, project_id):
    """View to debug AI pairing decisions with detailed match logs"""
    project = get_object_or_404(Project, id=project_id)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Get the match logs in reverse chronological order
    logs = TeamMatchLog.objects.filter(project_id=project_id).order_by("-timestamp")

    # Get analysis logs
    analysis_logs = TeamMatchAnalysisLog.objects.filter(project_id=project_id).order_by("-timestamp")

    return render(request, "collaborate/pairing_debug.html", {
        "project": project,
        "logs": logs,
        "analysis_logs": analysis_logs
    })

@login_required
def user_profile_modal(request, username):
    """
    View to return user profile data in JSON format for modal display
    """
    try:
        User = get_user_model()
        user = get_object_or_404(User, username=username)

        # Get user skills with proficiency levels
        user_skills = []
        try:
            from collaborate.models import UserSkill
            user_skills_objects = UserSkill.objects.filter(user=user).select_related('skill')
            user_skills = [
                {
                    'name': us.skill.name,
                    'level': us.get_proficiency_display(),
                    'level_value': us.proficiency,
                    'is_critical': False  # Default value
                }
                for us in user_skills_objects
            ]
        except:
            # Fallback to basic skills if UserSkill is not available
            user_skills = [{'name': skill.name, 'level': 'Unknown', 'level_value': 0} for skill in user.profile.skills.all()]

        # Check if user is available now
        is_available = is_user_available_now(user)

        # Get number of projects joined
        from .models import ProjectMembership
        projects_count = ProjectMembership.objects.filter(user=user).count()

        # Get match score with the project if project_id is provided
        match_score = None
        matching_skills = []
        if 'project_id' in request.GET:
            from .models import Project
            try:
                project = Project.objects.get(id=request.GET.get('project_id'))
                match_score, matching_skills, _ = score_user_for_project(user, project, project.owner)

                # Add availability bonus if applicable
                if is_available:
                    match_score += 10

                # Mark skills that are critical for the project
                critical_skills = list(project.critical_skills.values_list('name', flat=True))
                for skill in user_skills:
                    skill['is_critical'] = skill['name'] in critical_skills
            except Project.DoesNotExist:
                pass

        # Build the response data
        response_data = {
            'username': user.username,
            'user_id': user.id,
            'full_name': f"{user.first_name} {user.last_name}".strip() or user.username,
            'bio': getattr(user.profile, 'bio', ''),
            'description': getattr(user.profile, 'description', ''),
            'skills': user_skills,
            'timezone': getattr(user.profile, 'timezone', 'UTC'),
            'availability_type': getattr(user.profile, 'availability_type', 'Flexible'),
            'availability_start': getattr(user.profile, 'availability_start', None),
            'availability_end': getattr(user.profile, 'availability_end', None),
            'country': getattr(user.profile, 'country', ''),
            'is_available_now': is_available,
            'projects_count': projects_count,
            'match_score': match_score,
            'matching_skills': matching_skills,
            'is_member': False  # Default value, will be updated below if project_id is provided
        }

        # Add profile picture URL if available
        if hasattr(user, 'profile') and hasattr(user.profile, 'profile_picture') and user.profile.profile_picture:
            response_data['profile_picture'] = user.profile.profile_picture.url

        # Add CV information if available
        if hasattr(user, 'profile') and hasattr(user.profile, 'cv') and user.profile.cv:
            response_data['cv_url'] = user.profile.cv.url
            response_data['has_cv'] = True
        else:
            response_data['has_cv'] = False

        # Check if user is a member of the project
        if 'project_id' in request.GET:
            try:
                project = Project.objects.get(id=request.GET.get('project_id'))
                response_data['is_member'] = ProjectMembership.objects.filter(user=user, project=project).exists()
            except Project.DoesNotExist:
                pass

        return JsonResponse(response_data)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=400)


# === CHATBOT VIEWS ===

@login_required
def chatbot_message(request, project_hash):
    """
    Handle chatbot messages for code assistance
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        # Get the project
        project = get_object_or_404(Project, hash=project_hash)

        # Check if user has access to this project
        is_owner = project.owner == request.user
        is_member = ProjectMembership.objects.filter(user=request.user, project=project).exists()

        print(f"DEBUG: Chatbot access check - User: {request.user}, Project: {project.title}")
        print(f"DEBUG: Is owner: {is_owner}, Is member: {is_member}")

        if not (is_owner or is_member):
            return JsonResponse({'error': 'Access denied'}, status=403)

        # Parse request data
        import json
        data = json.loads(request.body)
        user_message = data.get('message', '').strip()
        code_context = data.get('code_context', '')
        file_name = data.get('file_name', '')

        if not user_message:
            return JsonResponse({'error': 'Message is required'}, status=400)

        # Get chatbot response
        # result = get_chatbot_response(
        #     user_message=user_message,
        #     code_context=code_context if code_context else None,
        #     file_name=file_name if file_name else None
        # )
        result = {'response': 'Chatbot temporarily disabled', 'success': False}

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def chatbot_analyze_code(request, project_hash):
    """
    Analyze current code for errors and suggestions
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        # Get the project
        project = get_object_or_404(Project, hash=project_hash)

        # Check if user has access to this project
        is_owner = project.owner == request.user
        is_member = ProjectMembership.objects.filter(user=request.user, project=project).exists()

        if not (is_owner or is_member):
            return JsonResponse({'error': 'Access denied'}, status=403)

        # Parse request data
        import json
        data = json.loads(request.body)
        code = data.get('code', '').strip()
        file_name = data.get('file_name', '')

        if not code:
            return JsonResponse({'error': 'Code is required'}, status=400)

        # Analyze code
        # result = analyze_current_code(code, file_name if file_name else None)
        result = {'analysis': 'Code analysis temporarily disabled', 'success': False}

        return JsonResponse(result)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def chatbot_clear_history(request, project_hash):
    """
    Clear chatbot conversation history
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        # Get the project
        project = get_object_or_404(Project, hash=project_hash)

        # Check if user has access to this project
        is_owner = project.owner == request.user
        is_member = ProjectMembership.objects.filter(user=request.user, project=project).exists()

        if not (is_owner or is_member):
            return JsonResponse({'error': 'Access denied'}, status=403)

        # Clear history
        # result = clear_chatbot_history()
        result = {'message': 'History cleared', 'success': True}

        return JsonResponse(result)

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# === MARKETPLACE VIEWS ===

@login_required
def marketplace_list(request):
    """Display all published projects in the marketplace"""
    from .models import ProjectMarketplacePost, ProjectReaction
    from django.db.models import Count, Q

    # Check if the marketplace tables exist
    try:
        posts = ProjectMarketplacePost.objects.select_related(
            'project', 'project__owner'
        ).prefetch_related(
            'reactions', 'comments', 'project__required_skills'
        ).annotate(
            reaction_count=Count('reactions'),
            comment_count=Count('comments')
        ).order_by('-is_featured', '-published_at')
    except Exception as e:
        # Handle case where tables don't exist yet (before migrations)
        messages.error(request, "Marketplace feature is not yet available. Please run migrations first.")
        return redirect('collaborate:project_list')


    # Filter by search query if provided
    search_query = request.GET.get('search', '').strip()
    if search_query:
        posts = posts.filter(
            Q(project__title__icontains=search_query) |
            Q(showcase_description__icontains=search_query) |
            Q(tags__icontains=search_query)
        )

    # Filter by tags if provided
    tag_filter = request.GET.get('tag', '').strip()
    if tag_filter:
        posts = posts.filter(tags__icontains=tag_filter)

    # Get popular tags for the sidebar
    all_tags = ProjectMarketplacePost.objects.exclude(tags='').values_list('tags', flat=True)
    tag_counts = {}
    for tag_string in all_tags:
        for tag in tag_string.split(','):
            tag = tag.strip()
            if tag:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1

    popular_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]

    context = {
        'posts': posts,
        'search_query': search_query,
        'tag_filter': tag_filter,
        'popular_tags': popular_tags,
    }

    return render(request, 'collaborate/marketplace.html', context)


@login_required
def marketplace_detail(request, post_id):
    """Display detailed view of a marketplace post"""
    from .models import ProjectMarketplacePost, ProjectReaction, ProjectComment, MarketplaceApplication
    from .forms import MarketplaceApplicationForm, ProjectCommentForm

    post = get_object_or_404(
        ProjectMarketplacePost.objects.select_related('project', 'project__owner'),
        id=post_id
    )

    # Increment view count
    post.increment_view_count()

    # Check if user has already applied
    existing_application = None
    if request.user != post.project.owner:
        existing_application = MarketplaceApplication.objects.filter(
            post=post, applicant=request.user
        ).first()

    # Check if user has reacted
    user_reaction = None
    if request.user.is_authenticated:
        user_reaction = ProjectReaction.objects.filter(
            post=post, user=request.user
        ).first()

    # Get reactions grouped by type
    from django.db.models import Count
    reactions = ProjectReaction.objects.filter(post=post).values(
        'reaction_type'
    ).annotate(count=Count('id')).order_by('-count')

    # Get comments with replies
    comments = ProjectComment.objects.filter(
        post=post, parent=None
    ).select_related('author').prefetch_related(
        'replies__author'
    ).order_by('-created_at')

    # Handle form submissions
    if request.method == 'POST':
        if 'apply' in request.POST:
            if existing_application:
                messages.warning(request, 'You have already applied to this project.')
            else:
                application_form = MarketplaceApplicationForm(request.POST)
                if application_form.is_valid():
                    try:
                        # Create the application
                        application = application_form.save(commit=False)
                        application.post = post
                        application.applicant = request.user
                        application.save()

                        # Log success for debugging
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.info(f"Application created: ID={application.id}, User={request.user.username}, Project={post.project.title}")

                        # Send notification to project owner
                        try:
                            send_notification(
                                user=post.project.owner,
                                title="New Marketplace Application",
                                message=f"{request.user.username} applied to join your project '{post.project.title}' from the marketplace",
                                project=post.project,
                                notification_type='info'
                            )
                        except Exception as notification_error:
                            # Don't fail the application if notification fails
                            pass

                        messages.success(request, 'Your application has been submitted successfully!')
                        return redirect('collaborate:marketplace_detail', post_id=post.id)

                    except Exception as e:
                        messages.error(request, f'Error submitting application. Please try again.')
                else:
                    # Show form validation errors
                    for field, errors in application_form.errors.items():
                        field_name = field.replace('_', ' ').title()
                        for error in errors:
                            messages.error(request, f'{field_name}: {error}')
                    # Keep the form data for re-display
                    pass  # application_form will be passed to template with errors

        elif 'comment' in request.POST:
            comment_form = ProjectCommentForm(request.POST)
            if comment_form.is_valid():
                comment = comment_form.save(commit=False)
                comment.post = post
                comment.author = request.user

                # Handle reply to another comment
                parent_id = request.POST.get('parent_id')
                if parent_id:
                    try:
                        parent_comment = ProjectComment.objects.get(id=parent_id, post=post)
                        comment.parent = parent_comment
                    except ProjectComment.DoesNotExist:
                        pass

                comment.save()

                # Send notification to project owner (if not commenting on own post)
                if request.user != post.project.owner:
                    send_notification(
                        user=post.project.owner,
                        title="New Comment on Your Project",
                        message=f"{request.user.username} commented on your marketplace post for '{post.project.title}'",
                        project=post.project,
                        notification_type='info'
                    )

                messages.success(request, 'Comment added!')
                return redirect('collaborate:marketplace_detail', post_id=post.id)

    # Initialize forms (if not already set from POST processing)
    if 'application_form' not in locals():
        application_form = MarketplaceApplicationForm()
    if 'comment_form' not in locals():
        comment_form = ProjectCommentForm()

    context = {
        'post': post,
        'existing_application': existing_application,
        'user_reaction': user_reaction,
        'reactions': reactions,
        'comments': comments,
        'application_form': application_form,
        'comment_form': comment_form,
        'is_owner': request.user == post.project.owner,
    }

    return render(request, 'collaborate/marketplace_detail.html', context)


@login_required
def publish_to_marketplace(request, project_hash):
    """Publish a project to the marketplace"""
    from .models import ProjectMarketplacePost
    from .forms import PublishToMarketplaceForm
    from django.utils import timezone

    project = get_object_or_404(Project, hash=project_hash)

    # Check if user is the project owner
    if project.owner != request.user:
        messages.error(request, "Only the project owner can publish to marketplace.")
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Check if already published
    existing_post = ProjectMarketplacePost.objects.filter(project=project).first()
    if existing_post:
        messages.info(request, "This project is already published to the marketplace.")
        return redirect('collaborate:marketplace_detail', post_id=existing_post.id)

    if request.method == 'POST':
        form = PublishToMarketplaceForm(request.POST, request.FILES)
        if form.is_valid():
            post = form.save(commit=False)
            post.project = project
            post.save()

            # Update project marketplace fields
            project.is_published_to_marketplace = True
            project.marketplace_published_at = timezone.now()
            if form.cleaned_data.get('featured_image'):
                project.marketplace_image = form.cleaned_data['featured_image']
            if form.cleaned_data.get('showcase_description'):
                project.marketplace_description = form.cleaned_data['showcase_description']
            project.save()

            messages.success(request, "Your project has been published to the marketplace!")
            return redirect('collaborate:marketplace_detail', post_id=post.id)
    else:
        # Pre-populate form with project data
        initial_data = {
            'showcase_description': project.description,
        }
        form = PublishToMarketplaceForm(initial=initial_data)

    context = {
        'form': form,
        'project': project,
    }

    return render(request, 'collaborate/publish_to_marketplace.html', context)


@login_required
def marketplace_react(request, post_id):
    """Add or update reaction to a marketplace post"""
    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ProjectMarketplacePost, ProjectReaction
    import json

    try:
        post = get_object_or_404(ProjectMarketplacePost, id=post_id)
        data = json.loads(request.body)
        reaction_type = data.get('reaction_type', 'like')

        # Validate reaction type
        valid_reactions = [choice[0] for choice in ProjectReaction.REACTION_CHOICES]
        if reaction_type not in valid_reactions:
            return JsonResponse({'error': 'Invalid reaction type'}, status=400)

        # Get or create reaction
        reaction, created = ProjectReaction.objects.get_or_create(
            post=post,
            user=request.user,
            defaults={'reaction_type': reaction_type}
        )

        if not created:
            if reaction.reaction_type == reaction_type:
                # Remove reaction if same type clicked again
                reaction.delete()
                return JsonResponse({
                    'success': True,
                    'action': 'removed',
                    'reaction_type': reaction_type
                })
            else:
                # Update reaction type
                reaction.reaction_type = reaction_type
                reaction.save()
                return JsonResponse({
                    'success': True,
                    'action': 'updated',
                    'reaction_type': reaction_type
                })

        # Send notification to project owner (if not reacting to own post)
        if request.user != post.project.owner:
            send_notification(
                user=post.project.owner,
                title="New Reaction on Your Project",
                message=f"{request.user.username} reacted to your marketplace post for '{post.project.title}'",
                project=post.project,
                notification_type='info'
            )

        return JsonResponse({
            'success': True,
            'action': 'created',
            'reaction_type': reaction_type
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def manage_marketplace_applications(request, project_hash):
    """Manage applications received from marketplace"""
    from .models import ProjectMarketplacePost, MarketplaceApplication
    from django.utils import timezone

    project = get_object_or_404(Project, hash=project_hash)

    # Check if user is the project owner
    if project.owner != request.user:
        messages.error(request, "Only the project owner can manage applications.")
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Get marketplace post
    try:
        marketplace_post = get_object_or_404(ProjectMarketplacePost, project=project)
    except:
        messages.error(request, "This project is not published to the marketplace.")
        return redirect('collaborate:project_detail', project_hash=project.hash)

    # Get all applications
    applications = MarketplaceApplication.objects.filter(
        post=marketplace_post
    ).select_related('applicant').order_by('-created_at')

    # Handle application actions
    if request.method == 'POST':
        application_id = request.POST.get('application_id')
        action = request.POST.get('action')

        if application_id and action in ['accept', 'reject']:
            try:
                application = MarketplaceApplication.objects.get(
                    id=application_id, post=marketplace_post
                )

                if action == 'accept':
                    application.status = 'accepted'
                    application.reviewed_at = timezone.now()
                    application.save()

                    # Create project membership
                    ProjectMembership.objects.get_or_create(
                        user=application.applicant,
                        project=project,
                        defaults={'role': 'member'}
                    )

                    # Send notification to applicant
                    send_notification(
                        user=application.applicant,
                        title="Application Accepted!",
                        message=f"Your application to join '{project.title}' has been accepted!",
                        project=project,
                        notification_type='success'
                    )

                    messages.success(request, f"Accepted {application.applicant.username}'s application.")

                elif action == 'reject':
                    application.status = 'rejected'
                    application.reviewed_at = timezone.now()
                    application.reviewer_notes = request.POST.get('notes', '')
                    application.save()

                    # Send notification to applicant
                    send_notification(
                        user=application.applicant,
                        title="Application Update",
                        message=f"Your application to join '{project.title}' has been reviewed.",
                        project=project,
                        notification_type='info'
                    )

                    messages.info(request, f"Rejected {application.applicant.username}'s application.")

            except MarketplaceApplication.DoesNotExist:
                messages.error(request, "Application not found.")

        return redirect('collaborate:manage_marketplace_applications', project_hash=project.hash)

    context = {
        'project': project,
        'marketplace_post': marketplace_post,
        'applications': applications,
    }

    return render(request, 'collaborate/manage_marketplace_applications.html', context)
