{% extends "base.html" %}
{% block content %}
<div class="update-status-container">
    <h1>Update Project Status</h1>
    <h2>{{ project.title }}</h2>

    <form method="post" class="status-form">
        {% csrf_token %}
        <div class="form-group">
            <label for="status">New Status:</label>
            <select name="status" id="status" class="form-control">
                {% for value, label in status_choices %}
                    <option value="{{ value }}" {% if project.status == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="form-actions">
            <button type="submit" class="btn btn-primary">Update Status</button>
            <a href="{% url 'collaborate:project_detail' project.id %}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>
{% endblock %} 