import os
import sys
import django
import random
from datetime import time
import pytz
from faker import Faker
import string

# Set up Django environment
# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'web_django.settings')
django.setup()

from django.contrib.auth.models import User
from collaborate.models import Project, Skill, UserSkill, TeamMatchLog
from accounts.models import UserProfile
from collaborate.matching_engine import find_best_team, find_best_team_with_availability_priority

# Initialize Faker
fake = Faker()

def create_test_data():
    """Create sample users, skills, and a project for testing."""
    # Create Skills
    skills = []
    skill_names = ["Python", "JavaScript", "Django", "React", "Data Science",
                   "UX Design", "DevOps", "Machine Learning", "SQL", "Cloud Computing"]

    print("Creating skills...")
    for name in skill_names:
        skill, created = Skill.objects.get_or_create(name=name)
        skills.append(skill)
        if created:
            print(f"Created skill: {name}")

    # Create Users
    users = []
    user_data = [
        {"username": "project_owner", "email": "<EMAIL>",
         "skills": ["Python", "Django"], "timezone": "UTC",
         "availability_start": time(9, 0), "availability_end": time(17, 0)},

        {"username": "user1", "email": "<EMAIL>",
         "skills": ["Python", "React", "SQL"], "timezone": "America/New_York",
         "availability_start": time(10, 0), "availability_end": time(18, 0)},

        {"username": "user2", "email": "<EMAIL>",
         "skills": ["JavaScript", "React", "UX Design"], "timezone": "Europe/London",
         "availability_start": time(8, 0), "availability_end": time(16, 0)},

        {"username": "user3", "email": "<EMAIL>",
         "skills": ["Data Science", "Machine Learning", "Python"], "timezone": "Asia/Tokyo",
         "availability_start": time(22, 0), "availability_end": time(6, 0)},

        {"username": "user4", "email": "<EMAIL>",
         "skills": ["DevOps", "Cloud Computing", "SQL"], "timezone": "Australia/Sydney",
         "availability_start": time(7, 0), "availability_end": time(15, 0)},

        {"username": "user5", "email": "<EMAIL>",
         "skills": ["Django", "JavaScript", "React"], "timezone": "Europe/Berlin",
         "availability_start": time(9, 0), "availability_end": time(17, 0)},
    ]

    print("\nCreating users...")
    for data in user_data:
        user, created = User.objects.get_or_create(
            username=data["username"],
            defaults={"email": data["email"]}
        )

        if created:
            user.set_password("testpassword123")
            user.save()
            print(f"Created user: {data['username']}")

        # Create or update profile
        profile, _ = UserProfile.objects.get_or_create(user=user)
        profile.timezone = data["timezone"]
        profile.availability_start = data["availability_start"]
        profile.availability_end = data["availability_end"]
        profile.save()

        # Assign skills to user
        for skill_name in data["skills"]:
            skill = Skill.objects.get(name=skill_name)
            UserSkill.objects.get_or_create(
                user=user,
                skill=skill,
                defaults={"proficiency": random.randint(1, 5)}
            )

        users.append(user)

    # Create a Project
    owner = User.objects.get(username="project_owner")
    print("\nCreating project...")
    project, created = Project.objects.get_or_create(
        title="Test Project",
        defaults={
            "description": "A test project for the matching engine",
            "owner": owner,
            "team_size": 3,
            "ai_matching_enabled": True  # Using the correct field name
        }
    )

    if created:
        print(f"Created project: {project.title}")

    # Assign required skills to project
    required_skill_names = ["Python", "JavaScript", "React", "Django", "SQL"]
    for skill_name in required_skill_names:
        skill = Skill.objects.get(name=skill_name)
        project.required_skills.add(skill)

    print(f"Added required skills to project: {', '.join(required_skill_names)}")

    return project, users

def test_matching_engine():
    """Test the matching engine with the sample data."""
    project, all_users = create_test_data()

    # Get all users except project owner
    available_users = [user for user in all_users if user != project.owner]

    print("\n=== Testing Basic Matching Algorithm ===")
    result = find_best_team(project, available_users)

    print(f"\nSelected team (size: {len(result['team_members'])}):")
    for i, user in enumerate(result['team_members'], 1):
        profile = UserProfile.objects.get(user=user)
        skills = ", ".join([us.skill.name for us in UserSkill.objects.filter(user=user)])
        print(f"{i}. {user.username} - Skills: {skills} - Timezone: {profile.timezone}")

    if result['missing_skills']:
        print(f"\nMissing skills: {', '.join(result['missing_skills'])}")
    else:
        print("\nAll required skills covered by the team!")

    print("\n=== Testing Advanced Matching Algorithm (with availability priority) ===")
    result_advanced = find_best_team_with_availability_priority(project, available_users)

    print(f"\nSelected team (size: {len(result_advanced['team_members'])}):")
    for i, user in enumerate(result_advanced['team_members'], 1):
        profile = UserProfile.objects.get(user=user)
        skills = ", ".join([us.skill.name for us in UserSkill.objects.filter(user=user)])
        print(f"{i}. {user.username} - Skills: {skills} - Timezone: {profile.timezone}")

    if result_advanced['missing_skills']:
        print(f"\nMissing skills: {', '.join(result_advanced['missing_skills'])}")
    else:
        print("\nAll required skills covered by the team!")

    print(f"\nSkill coverage: {result_advanced['skill_coverage_percent']:.1f}%")
    print(f"Is optimal match: {result_advanced['is_optimal_match']}")

def create_fake_users(num_users=100):
    """
    Create a specified number of fake users with random skills and profiles.

    Args:
        num_users: Number of fake users to create (default: 100)

    Returns:
        list: List of created User objects
    """
    # Define a comprehensive list of skills for various domains
    all_skill_names = [
        # Programming Languages
        "Python", "JavaScript", "Java", "C++", "C#", "Ruby", "Go", "Rust", "PHP", "Swift",
        "TypeScript", "Kotlin", "Scala", "R", "Perl", "Haskell", "Lua", "Dart", "COBOL", "Fortran",

        # Web Development
        "HTML", "CSS", "React", "Angular", "Vue.js", "Node.js", "Express", "Django", "Flask",
        "Laravel", "Spring Boot", "ASP.NET", "Ruby on Rails", "jQuery", "Bootstrap", "Tailwind CSS",
        "GraphQL", "REST API", "WebSockets", "PWA",

        # Mobile Development
        "Android", "iOS", "React Native", "Flutter", "Xamarin", "Ionic", "SwiftUI", "Kotlin Multiplatform",
        "Mobile UI Design", "App Store Optimization",

        # Database
        "SQL", "MySQL", "PostgreSQL", "MongoDB", "SQLite", "Oracle", "Redis", "Cassandra",
        "DynamoDB", "Firebase", "Neo4j", "Elasticsearch", "MariaDB", "Database Design",

        # DevOps & Cloud
        "Docker", "Kubernetes", "AWS", "Azure", "Google Cloud", "CI/CD", "Jenkins", "GitHub Actions",
        "Terraform", "Ansible", "Prometheus", "Grafana", "ELK Stack", "Nginx", "Apache",

        # Data Science & AI
        "Machine Learning", "Deep Learning", "TensorFlow", "PyTorch", "Scikit-learn", "NLP",
        "Computer Vision", "Data Analysis", "Data Visualization", "Big Data", "Pandas", "NumPy",
        "Jupyter", "Tableau", "Power BI", "Statistics", "A/B Testing",

        # Game Development
        "Unity", "Unreal Engine", "Game Design", "3D Modeling", "Animation", "Shader Programming",
        "Game Physics", "Level Design", "Character Design", "Game AI",

        # Soft Skills
        "Project Management", "Agile", "Scrum", "Team Leadership", "Communication", "Problem Solving",
        "Critical Thinking", "Time Management", "Creativity", "Collaboration",

        # Design
        "UI Design", "UX Design", "Graphic Design", "Adobe Photoshop", "Adobe Illustrator",
        "Figma", "Sketch", "InVision", "Wireframing", "Prototyping",

        # Security
        "Cybersecurity", "Ethical Hacking", "Penetration Testing", "Security Auditing",
        "Encryption", "Network Security", "OWASP", "Security Compliance",

        # Blockchain
        "Blockchain", "Smart Contracts", "Ethereum", "Solidity", "Web3", "DeFi", "NFT",
        "Cryptocurrency", "Consensus Algorithms",

        # IoT & Embedded
        "IoT", "Embedded Systems", "Arduino", "Raspberry Pi", "MQTT", "Sensor Networks",
        "Firmware Development", "Hardware Prototyping"
    ]

    # Ensure all skills exist in the database
    print("Creating skills...")
    skills = []
    for name in all_skill_names:
        skill, created = Skill.objects.get_or_create(name=name)
        skills.append(skill)
        if created:
            print(f"Created skill: {name}")

    # Define timezone options
    timezones = [
        "UTC", "America/New_York", "America/Los_Angeles", "America/Chicago",
        "Europe/London", "Europe/Berlin", "Europe/Paris", "Asia/Tokyo",
        "Asia/Singapore", "Asia/Dubai", "Australia/Sydney", "Pacific/Auckland"
    ]

    # Create fake users
    print(f"\nCreating {num_users} fake users...")
    created_users = []

    for i in range(1, num_users + 1):
        # Generate a unique username
        username = f"test_user_{i}"

        # Check if user already exists
        if User.objects.filter(username=username).exists():
            user = User.objects.get(username=username)
            print(f"User {username} already exists, skipping creation.")
        else:
            # Create user with fake data
            first_name = fake.first_name()
            last_name = fake.last_name()
            email = f"{username}@example.com"

            user = User.objects.create_user(
                username=username,
                email=email,
                password="testpassword123",
                first_name=first_name,
                last_name=last_name
            )
            print(f"Created user: {username}")

            # Create or update profile
            profile, _ = UserProfile.objects.get_or_create(user=user)

            # Set random timezone and availability
            profile.timezone = random.choice(timezones)

            # Generate random availability times (with 80% chance of having availability set)
            if random.random() < 0.8:
                start_hour = random.randint(6, 12)
                duration = random.randint(6, 10)
                end_hour = (start_hour + duration) % 24

                profile.availability_start = time(start_hour, 0)
                profile.availability_end = time(end_hour, 0)

                # Set availability type
                availability_types = ['Full Time', 'Part Time', 'Weekends Only', 'Flexible']
                profile.availability_type = random.choice(availability_types)

            # Set random country
            profile.country = fake.country()
            profile.bio = fake.paragraph(nb_sentences=3)
            profile.save()

            # Assign random skills to user (between 3 and 10 skills)
            num_skills = random.randint(3, 10)
            user_skills = random.sample(skills, num_skills)

            for skill in user_skills:
                # Assign random proficiency level (1-5)
                proficiency = random.randint(1, 5)
                UserSkill.objects.get_or_create(
                    user=user,
                    skill=skill,
                    defaults={"proficiency": proficiency}
                )

            print(f"Added {num_skills} skills to {username}")

        created_users.append(user)

    print(f"\nCreated {len(created_users)} users with random skills and profiles.")
    return created_users

def create_test_projects(num_projects=5):
    """
    Create a specified number of test projects with random required skills.

    Args:
        num_projects: Number of test projects to create (default: 5)

    Returns:
        list: List of created Project objects
    """
    # Get all available skills
    all_skills = list(Skill.objects.all())

    # Get a random user to be the project owner
    users = list(User.objects.all())
    if not users:
        print("No users found. Please create users first.")
        return []

    print(f"\nCreating {num_projects} test projects...")
    created_projects = []

    for i in range(1, num_projects + 1):
        # Generate project data
        title = f"Test Project {i}: {fake.catch_phrase()}"
        description = fake.paragraph(nb_sentences=5)
        owner = random.choice(users)
        team_size = random.randint(2, 6)

        # Create project
        project, created = Project.objects.get_or_create(
            title=title,
            defaults={
                "description": description,
                "owner": owner,
                "team_size": team_size,
                "ai_matching_enabled": True  # Using the correct field name
            }
        )

        if created:
            print(f"Created project: {title}")

            # Assign random required skills (between 3 and 8 skills)
            num_required_skills = random.randint(3, 8)
            required_skills = random.sample(all_skills, num_required_skills)

            for skill in required_skills:
                project.required_skills.add(skill)

            # Assign a subset of required skills as critical (1-3 skills)
            num_critical = min(random.randint(1, 3), num_required_skills)
            critical_skills = random.sample(required_skills, num_critical)

            for skill in critical_skills:
                project.critical_skills.add(skill)

            print(f"Added {num_required_skills} required skills and {num_critical} critical skills to {title}")

            created_projects.append(project)
        else:
            print(f"Project '{title}' already exists, skipping creation.")

    print(f"\nCreated {len(created_projects)} test projects.")
    return created_projects

if __name__ == "__main__":
    # Choose which function to run
    import argparse

    parser = argparse.ArgumentParser(description='Test the matching engine or create fake data.')
    parser.add_argument('--test', action='store_true', help='Run the matching engine test')
    parser.add_argument('--create-users', type=int, default=0, help='Create specified number of fake users')
    parser.add_argument('--create-projects', type=int, default=0, help='Create specified number of test projects')

    args = parser.parse_args()

    if args.test:
        test_matching_engine()
    elif args.create_users > 0:
        create_fake_users(args.create_users)
    elif args.create_projects > 0:
        create_test_projects(args.create_projects)
    else:
        # Default behavior if no arguments provided
        print("Creating 100 fake users...")
        create_fake_users(100)
        print("\nCreating 5 test projects...")
        create_test_projects(5)
        print("\nRunning matching engine test...")
        test_matching_engine()