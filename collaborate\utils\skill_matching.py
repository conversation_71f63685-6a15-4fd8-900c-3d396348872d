"""
Skill Matching Utility - ForgeX

This module provides functions for calculating skill matching scores between users and projects,
with special emphasis on critical skills for team formation.
"""

def calculate_skill_overlap(user_skills, project_skills):
    """
    Simple skill overlap percentage.
    
    Args:
        user_skills (list): List of skill IDs or names that the user has
        project_skills (list): List of skill IDs or names required for the project
        
    Returns:
        float: Percentage of project skills matched by user (0.0 to 1.0)
    """
    if not project_skills:
        return 0
    matches = set(user_skills).intersection(set(project_skills))
    return len(matches) / len(project_skills)

def calculate_critical_skill_score(user_skills, critical_skills):
    """
    Bonus if user has critical project skills.
    
    Args:
        user_skills (list): List of skill IDs or names that the user has
        critical_skills (list): List of critical skill IDs or names for the project
        
    Returns:
        float: Percentage of critical skills matched by user (0.0 to 1.0)
    """
    if not critical_skills:
        return 0
    matches = set(user_skills).intersection(set(critical_skills))
    return len(matches) / len(critical_skills)

def calculate_missing_skill_penalty(user_skills, project_skills):
    """
    Penalty for missing skills.
    
    Args:
        user_skills (list): List of skill IDs or names that the user has
        project_skills (list): List of skill IDs or names required for the project
        
    Returns:
        int: Number of project skills not matched by the user
    """
    missing = set(project_skills) - set(user_skills)
    return len(missing)

def calculate_total_matching_score(user_skills, project_skills, critical_skills):
    """
    Final combined scoring system.
    
    Args:
        user_skills (list): List of skill IDs or names that the user has
        project_skills (list): List of skill IDs or names required for the project
        critical_skills (list): List of critical skill IDs or names for the project
        
    Returns:
        float: Final matching score (rounded to 3 decimal places)
        
    Formula:
        - 50% weight for basic skill overlap
        - 40% weight for critical skill matches
        - 10% penalty for each missing skill
    """
    overlap_score = calculate_skill_overlap(user_skills, project_skills)
    critical_score = calculate_critical_skill_score(user_skills, critical_skills)
    missing_penalty = calculate_missing_skill_penalty(user_skills, project_skills)
    
    # Formula: basic overlap + bonus for critical skills - penalty for missing
    final_score = (overlap_score * 0.5) + (critical_score * 0.4) - (missing_penalty * 0.1)
    
    # Ensure score doesn't go below 0
    final_score = max(0, final_score)
    
    return round(final_score, 3)