/* Account Pages CSS with Parallax Effects and Animations */

/* Base styles for account pages */
.account-page {
  position: relative;
  z-index: 1;
  padding: 50px 0;
}

/* Particles background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
}

/* Card styling for account pages */
.account-card {
  background-color: rgba(28, 28, 28, 0.8);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(192, 255, 107, 0.3);
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Expanded profile card for profile page */
.expanded-profile {
  max-width: 1000px;
  padding: 40px;
}

.account-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.2);
}

/* Heading styles */
.account-card h2 {
  color: #ffffff;
  font-size: 32px;
  margin-bottom: 20px;
  position: relative;
}

.account-card h2:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.account-card:hover h2:after {
  width: 100px;
}

/* Form styling */
.account-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}
.profile-picture-upload{
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  color: #ffffff;
  font-weight: 600;
  font-size: 16px;
}

.form-control {
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 15px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--color-border);
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  outline: none;
}

.form-control:hover {
  border-color: rgba(192, 255, 107, 0.5);
}

/* Button styling */
.account-btn {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid var(--color-border);
  border-radius: 30px;
  padding: 12px 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
  text-decoration: none;
  text-align: center;
}

.account-btn:hover {
  background-color: var(--color-border);
  color: #000000;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
  transform: translateY(-3px);
}

.account-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.account-btn:hover:before {
  left: 100%;
}

/* Link styling */
.account-link {
  color: #ffffff;
  text-decoration: underline;
  transition: all 0.3s ease;
}

.account-link:hover {
  color: var(--color-border);
  text-shadow: 0 0 8px rgba(192, 255, 107, 0.5);
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-right {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-left {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

/* Profile page specific styles */
.profile-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 40px;
}

.profile-sidebar {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.profile-picture {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--color-border);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.profile-picture:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
}

.default-avatar {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: linear-gradient(135deg, #333333, #1a1a1a);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 80px;
  font-weight: bold;
  color: var(--color-border);
  border: 4px solid var(--color-border);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.profile-name {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #ffffff;
  text-align: center;
}

.profile-main {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.profile-section {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 1vh;
}

.profile-section:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

/* Notification list specific styles */
.notification-item {

  transition: all 0.3s ease;
}

.notification-item:hover {
  background-color: rgba(50, 50, 50, 0.7);
  transform: translateX(5px);
}

.notification-item.unread {
  border-left-color: #4CAF50;
}

/* Demo notification buttons */
.demo-notification-section {
  background: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  margin-top: 20px;
}

.demo-notification-section h3 {
  color: #ffffff;
  margin-bottom: 20px;
  font-size: 20px;
}

.demo-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.demo-btn {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(192, 255, 107, 0.2));
  color: #ffffff;
  border: 2px solid rgba(192, 255, 107, 0.5);
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.demo-btn:hover {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.2), rgba(192, 255, 107, 0.3));
  border-color: rgba(192, 255, 107, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.demo-btn.info { border-color: rgba(13, 110, 253, 0.5); }
.demo-btn.success { border-color: rgba(25, 135, 84, 0.5); }
.demo-btn.warning { border-color: rgba(255, 193, 7, 0.5); }
.demo-btn.error { border-color: rgba(220, 53, 69, 0.5); }
.demo-btn.system { border-color: rgba(111, 66, 193, 0.5); }
.demo-btn.invitation { border-color: rgba(32, 201, 151, 0.5); }

/* Mentorship Overview Styles */
.mentorship-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.mentorship-stat-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
}

.mentorship-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.mentorship-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.mentorship-section {
  background-color: rgba(40, 40, 40, 0.3);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.mentorship-section h3 {
  color: var(--color-border);
  margin-bottom: 20px;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.session-card {
  background-color: rgba(50, 50, 50, 0.5);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  transition: all 0.3s ease;
}

.session-card:hover {
  border-color: rgba(192, 255, 107, 0.3);
  transform: translateX(5px);
}

.session-card.completed {
  border-color: rgba(76, 175, 80, 0.3);
  background-color: rgba(76, 175, 80, 0.1);
}

.session-info h4 {
  color: #ffffff;
  margin-bottom: 10px;
  font-size: 1rem;
}

.session-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  font-size: 0.85rem;
  color: #b0b0b0;
  margin-bottom: 10px;
}

.session-date,
.session-time,
.session-duration {
  display: flex;
  align-items: center;
  gap: 5px;
}

.session-rating {
  display: flex;
  gap: 2px;
}

.star {
  color: #666;
  font-size: 0.9rem;
}

.star.filled {
  color: #ffd700;
}

.session-actions {
  text-align: right;
}

.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.feedback-card {
  background-color: rgba(50, 50, 50, 0.5);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.learner-name {
  color: #ffffff;
  font-weight: 600;
  font-size: 0.9rem;
}

.rating {
  display: flex;
  gap: 2px;
}

.feedback-text {
  color: #e0e0e0;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #888;
  font-style: italic;
}

.mentorship-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  grid-column: 1 / -1;
}

.mentorship-actions .btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.mentorship-actions .btn-primary {
  background-color: var(--color-border);
  color: #000;
  border: 2px solid var(--color-border);
}

.mentorship-actions .btn-primary:hover {
  background-color: transparent;
  color: var(--color-border);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.mentorship-actions .btn-secondary {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #666;
}

.mentorship-actions .btn-secondary:hover {
  background-color: #666;
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 102, 102, 0.3);
}

.mentorship-actions .btn-outline {
  background-color: transparent;
  color: var(--color-border);
  border: 2px solid var(--color-border);
}

.mentorship-actions .btn-outline:hover {
  background-color: var(--color-border);
  color: #000;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

/* Responsive styles */
@media (max-width: 768px) {
  .account-card {
    padding: 20px;
    margin: 0 15px;
  }

  .profile-container {
    grid-template-columns: 1fr;
  }

  .demo-buttons {
    grid-template-columns: 1fr;
  }

  .mentorship-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mentorship-stats {
    grid-template-columns: 1fr;
  }

  .session-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .mentorship-actions {
    flex-direction: column;
    align-items: center;
  }

  .mentorship-actions .btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}
