{% if show_monitor %}
<div id="security-monitor" class="security-monitor">
  <div class="security-monitor-header">
    <h4>🔒 Security Monitor</h4>
    <button id="toggle-security-monitor" class="btn-sm">−</button>
  </div>
  
  <div class="security-monitor-content">
    <div class="security-status">
      <div class="status-item">
        <span class="status-label">Protection Level:</span>
        <span id="protection-level" class="status-value">High</span>
      </div>
      <div class="status-item">
        <span class="status-label">Active Threats:</span>
        <span id="active-threats" class="status-value">0</span>
      </div>
      <div class="status-item">
        <span class="status-label">API Calls:</span>
        <span id="api-calls-count" class="status-value">0</span>
      </div>
    </div>
    
    <div class="security-controls">
      <button id="toggle-debug-mode" class="btn-sm btn-secondary">
        <i class="fas fa-bug"></i> Debug Mode
      </button>
      <button id="view-security-logs" class="btn-sm btn-info">
        <i class="fas fa-list"></i> Logs
      </button>
      <button id="security-settings" class="btn-sm btn-warning">
        <i class="fas fa-cog"></i> Settings
      </button>
    </div>
    
    <div class="recent-events">
      <h5>Recent Events</h5>
      <div id="recent-events-list" class="events-list">
        <!-- Events will be populated by JavaScript -->
      </div>
    </div>
  </div>
</div>

<!-- Security Settings Modal -->
<div id="security-settings-modal" class="modal security-modal">
  <div class="modal-content">
    <div class="modal-header">
      <h3>🔒 Security Settings</h3>
      <span class="close-modal">&times;</span>
    </div>
    
    <div class="modal-body">
      <div class="settings-section">
        <h4>Protection Settings</h4>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="obfuscation-enabled" checked>
            JavaScript Obfuscation
          </label>
          <small>Obfuscate JavaScript code for regular users</small>
        </div>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="encryption-enabled" checked>
            API Encryption
          </label>
          <small>Encrypt API requests and responses</small>
        </div>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="devtools-protection" checked>
            DevTools Protection
          </label>
          <small>Prevent access to browser developer tools</small>
        </div>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="console-protection" checked>
            Console Protection
          </label>
          <small>Disable console output for regular users</small>
        </div>
      </div>
      
      <div class="settings-section">
        <h4>Access Control</h4>
        
        <div class="setting-item">
          <label>
            Rate Limiting (requests/minute):
            <input type="number" id="rate-limit" value="100" min="10" max="1000">
          </label>
        </div>
        
        <div class="setting-item">
          <label>
            Session Timeout (minutes):
            <input type="number" id="session-timeout" value="60" min="5" max="480">
          </label>
        </div>
      </div>
      
      <div class="settings-section">
        <h4>Monitoring</h4>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="monitor-api-calls" checked>
            Monitor API Calls
          </label>
        </div>
        
        <div class="setting-item">
          <label>
            <input type="checkbox" id="monitor-file-access" checked>
            Monitor File Access
          </label>
        </div>
        
        <div class="setting-item">
          <label>
            Security Logging Level:
            <select id="logging-level">
              <option value="minimal">Minimal</option>
              <option value="standard" selected>Standard</option>
              <option value="verbose">Verbose</option>
              <option value="debug">Debug</option>
            </select>
          </label>
        </div>
      </div>
    </div>
    
    <div class="modal-footer">
      <button id="save-security-settings" class="btn btn-primary">Save Settings</button>
      <button id="reset-security-settings" class="btn btn-secondary">Reset to Defaults</button>
    </div>
  </div>
</div>

<!-- Security Logs Modal -->
<div id="security-logs-modal" class="modal security-modal">
  <div class="modal-content large">
    <div class="modal-header">
      <h3>🔍 Security Logs</h3>
      <span class="close-modal">&times;</span>
    </div>
    
    <div class="modal-body">
      <div class="logs-controls">
        <div class="filter-controls">
          <select id="log-filter-type">
            <option value="">All Events</option>
            <option value="devtools_open">DevTools Access</option>
            <option value="api_abuse">API Abuse</option>
            <option value="security_threat">Security Threats</option>
            <option value="login_attempt">Login Attempts</option>
          </select>
          
          <select id="log-filter-timeframe">
            <option value="1">Last Hour</option>
            <option value="24" selected>Last 24 Hours</option>
            <option value="168">Last Week</option>
            <option value="720">Last Month</option>
          </select>
          
          <button id="refresh-logs" class="btn-sm btn-primary">
            <i class="fas fa-sync"></i> Refresh
          </button>
          
          <button id="export-logs" class="btn-sm btn-secondary">
            <i class="fas fa-download"></i> Export
          </button>
        </div>
      </div>
      
      <div class="logs-container">
        <table id="security-logs-table" class="logs-table">
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>User</th>
              <th>Event Type</th>
              <th>IP Address</th>
              <th>Severity</th>
              <th>Details</th>
            </tr>
          </thead>
          <tbody id="security-logs-tbody">
            <!-- Logs will be populated by JavaScript -->
          </tbody>
        </table>
      </div>
      
      <div class="logs-pagination">
        <button id="prev-page" class="btn-sm">Previous</button>
        <span id="page-info">Page 1 of 1</span>
        <button id="next-page" class="btn-sm">Next</button>
      </div>
    </div>
  </div>
</div>

<style>
.security-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid #333;
  border-radius: 8px;
  color: #fff;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  z-index: 10000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.security-monitor-header {
  background: #1a1a1a;
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.security-monitor-header h4 {
  margin: 0;
  font-size: 14px;
  color: #00ff00;
}

.security-monitor-content {
  padding: 12px;
}

.security-status {
  margin-bottom: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.status-label {
  color: #ccc;
}

.status-value {
  color: #00ff00;
  font-weight: bold;
}

.security-controls {
  display: flex;
  gap: 4px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.security-controls .btn-sm {
  padding: 4px 8px;
  font-size: 10px;
  background: #333;
  border: 1px solid #555;
  color: #fff;
  border-radius: 3px;
  cursor: pointer;
}

.security-controls .btn-sm:hover {
  background: #555;
}

.recent-events h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #00ff00;
}

.events-list {
  max-height: 100px;
  overflow-y: auto;
  font-size: 10px;
}

.event-item {
  padding: 2px 0;
  border-bottom: 1px solid #333;
  color: #ccc;
}

.event-item:last-child {
  border-bottom: none;
}

.security-modal .modal-content {
  background: #1a1a1a;
  color: #fff;
  border: 1px solid #333;
}

.security-modal .modal-content.large {
  width: 90%;
  max-width: 1000px;
}

.settings-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #333;
}

.settings-section h4 {
  color: #00ff00;
  margin-bottom: 10px;
}

.setting-item {
  margin-bottom: 10px;
}

.setting-item label {
  display: block;
  color: #ccc;
}

.setting-item small {
  display: block;
  color: #888;
  margin-top: 2px;
}

.setting-item input, .setting-item select {
  background: #333;
  border: 1px solid #555;
  color: #fff;
  padding: 4px 8px;
  border-radius: 3px;
}

.logs-controls {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
}

.filter-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.logs-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
}

.logs-table th,
.logs-table td {
  padding: 6px 8px;
  border: 1px solid #333;
  text-align: left;
}

.logs-table th {
  background: #2a2a2a;
  color: #00ff00;
}

.logs-table tbody tr:nth-child(even) {
  background: #1a1a1a;
}

.logs-table tbody tr:hover {
  background: #333;
}

.logs-pagination {
  margin-top: 15px;
  text-align: center;
}

.logs-pagination button {
  margin: 0 5px;
}

.security-monitor.minimized .security-monitor-content {
  display: none;
}

.security-monitor.minimized {
  width: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .security-monitor {
    width: 250px;
    right: 10px;
    top: 10px;
  }
  
  .security-modal .modal-content.large {
    width: 95%;
  }
}
</style>

<script>
// Security Monitor JavaScript
document.addEventListener('DOMContentLoaded', function() {
  const securityMonitor = new SecurityMonitorWidget();
});

class SecurityMonitorWidget {
  constructor() {
    this.isMinimized = false;
    this.currentPage = 1;
    this.logsPerPage = 20;
    
    this.init();
  }
  
  init() {
    this.setupEventListeners();
    this.startMonitoring();
    this.loadInitialData();
  }
  
  setupEventListeners() {
    // Toggle monitor
    document.getElementById('toggle-security-monitor').addEventListener('click', () => {
      this.toggleMonitor();
    });
    
    // Control buttons
    document.getElementById('toggle-debug-mode').addEventListener('click', () => {
      this.toggleDebugMode();
    });
    
    document.getElementById('view-security-logs').addEventListener('click', () => {
      this.showLogsModal();
    });
    
    document.getElementById('security-settings').addEventListener('click', () => {
      this.showSettingsModal();
    });
    
    // Modal close buttons
    document.querySelectorAll('.close-modal').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.target.closest('.modal').style.display = 'none';
      });
    });
    
    // Settings modal
    document.getElementById('save-security-settings').addEventListener('click', () => {
      this.saveSecuritySettings();
    });
    
    document.getElementById('reset-security-settings').addEventListener('click', () => {
      this.resetSecuritySettings();
    });
    
    // Logs modal
    document.getElementById('refresh-logs').addEventListener('click', () => {
      this.loadSecurityLogs();
    });
    
    document.getElementById('export-logs').addEventListener('click', () => {
      this.exportLogs();
    });
  }
  
  startMonitoring() {
    // Update status every 5 seconds
    setInterval(() => {
      this.updateStatus();
    }, 5000);
    
    // Update recent events every 10 seconds
    setInterval(() => {
      this.updateRecentEvents();
    }, 10000);
  }
  
  async loadInitialData() {
    await this.updateStatus();
    await this.updateRecentEvents();
    await this.loadSecuritySettings();
  }
  
  toggleMonitor() {
    const monitor = document.getElementById('security-monitor');
    const toggleBtn = document.getElementById('toggle-security-monitor');
    
    this.isMinimized = !this.isMinimized;
    
    if (this.isMinimized) {
      monitor.classList.add('minimized');
      toggleBtn.textContent = '+';
    } else {
      monitor.classList.remove('minimized');
      toggleBtn.textContent = '−';
    }
  }
  
  async updateStatus() {
    try {
      // This would typically fetch from an API
      // For now, simulate the data
      const status = {
        protectionLevel: 'High',
        activeThreats: 0,
        apiCalls: Math.floor(Math.random() * 100)
      };
      
      document.getElementById('protection-level').textContent = status.protectionLevel;
      document.getElementById('active-threats').textContent = status.activeThreats;
      document.getElementById('api-calls-count').textContent = status.apiCalls;
      
    } catch (error) {
      console.error('Failed to update security status:', error);
    }
  }
  
  async updateRecentEvents() {
    try {
      // Simulate recent events
      const events = [
        { time: '10:30:15', event: 'API call authenticated' },
        { time: '10:29:45', event: 'User login successful' },
        { time: '10:28:30', event: 'File access granted' }
      ];
      
      const eventsList = document.getElementById('recent-events-list');
      eventsList.innerHTML = events.map(event => 
        `<div class="event-item">${event.time} - ${event.event}</div>`
      ).join('');
      
    } catch (error) {
      console.error('Failed to update recent events:', error);
    }
  }
  
  toggleDebugMode() {
    if (window.ForgeXSecurity) {
      const isDebug = window.ForgeXSecurity.canShowDebugInfo();
      console.log(`Debug mode is currently: ${isDebug ? 'ON' : 'OFF'}`);
      
      // This would typically toggle debug mode
      alert(`Debug mode is ${isDebug ? 'enabled' : 'disabled'} for your account`);
    }
  }
  
  showLogsModal() {
    document.getElementById('security-logs-modal').style.display = 'block';
    this.loadSecurityLogs();
  }
  
  showSettingsModal() {
    document.getElementById('security-settings-modal').style.display = 'block';
  }
  
  async loadSecurityLogs() {
    try {
      const response = await fetch('/accounts/api/security-dashboard/');
      const data = await response.json();
      
      const tbody = document.getElementById('security-logs-tbody');
      tbody.innerHTML = data.recent_events.map(event => `
        <tr>
          <td>${new Date(event.timestamp).toLocaleString()}</td>
          <td>${event.user}</td>
          <td>${event.event_type}</td>
          <td>${event.ip_address}</td>
          <td>Medium</td>
          <td>
            <button class="btn-sm" onclick="this.nextElementSibling.style.display = this.nextElementSibling.style.display === 'none' ? 'block' : 'none'">
              Details
            </button>
            <div style="display: none; margin-top: 5px; font-size: 10px; color: #888;">
              User Agent: ${event.user_agent}
            </div>
          </td>
        </tr>
      `).join('');
      
    } catch (error) {
      console.error('Failed to load security logs:', error);
    }
  }
  
  async loadSecuritySettings() {
    try {
      const response = await fetch('/accounts/api/security-settings/');
      const settings = await response.json();
      
      // Populate settings form
      document.getElementById('obfuscation-enabled').checked = settings.obfuscation_enabled;
      document.getElementById('encryption-enabled').checked = settings.encryption_enabled;
      document.getElementById('devtools-protection').checked = settings.devtools_protection;
      document.getElementById('console-protection').checked = settings.console_protection;
      document.getElementById('rate-limit').value = settings.max_requests_per_minute || 100;
      document.getElementById('session-timeout').value = settings.session_timeout || 60;
      document.getElementById('monitor-api-calls').checked = settings.monitor_api_calls;
      document.getElementById('monitor-file-access').checked = settings.monitor_file_access;
      document.getElementById('logging-level').value = settings.security_logging_level || 'standard';
      
    } catch (error) {
      console.error('Failed to load security settings:', error);
    }
  }
  
  async saveSecuritySettings() {
    try {
      const settings = {
        obfuscation_enabled: document.getElementById('obfuscation-enabled').checked,
        encryption_enabled: document.getElementById('encryption-enabled').checked,
        devtools_protection: document.getElementById('devtools-protection').checked,
        console_protection: document.getElementById('console-protection').checked,
        max_requests_per_minute: parseInt(document.getElementById('rate-limit').value),
        session_timeout: parseInt(document.getElementById('session-timeout').value),
        monitor_api_calls: document.getElementById('monitor-api-calls').checked,
        monitor_file_access: document.getElementById('monitor-file-access').checked,
        security_logging_level: document.getElementById('logging-level').value
      };
      
      const response = await fetch('/accounts/api/security-settings/update/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': document.querySelector('[name=csrf-token]').content
        },
        body: JSON.stringify(settings)
      });
      
      if (response.ok) {
        alert('Security settings saved successfully');
        document.getElementById('security-settings-modal').style.display = 'none';
      } else {
        alert('Failed to save security settings');
      }
      
    } catch (error) {
      console.error('Failed to save security settings:', error);
      alert('Failed to save security settings');
    }
  }
  
  resetSecuritySettings() {
    if (confirm('Reset all security settings to defaults?')) {
      // Reset to default values
      document.getElementById('obfuscation-enabled').checked = true;
      document.getElementById('encryption-enabled').checked = true;
      document.getElementById('devtools-protection').checked = true;
      document.getElementById('console-protection').checked = true;
      document.getElementById('rate-limit').value = 100;
      document.getElementById('session-timeout').value = 60;
      document.getElementById('monitor-api-calls').checked = true;
      document.getElementById('monitor-file-access').checked = true;
      document.getElementById('logging-level').value = 'standard';
    }
  }
  
  async exportLogs() {
    try {
      const days = document.getElementById('log-filter-timeframe').value;
      const response = await fetch(`/accounts/api/export-security-logs/?days=${days}`);
      const data = await response.json();
      
      // Create and download CSV
      const csv = this.convertToCSV(data.logs);
      const blob = new Blob([csv], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `security_logs_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Failed to export logs:', error);
      alert('Failed to export logs');
    }
  }
  
  convertToCSV(logs) {
    const headers = ['Timestamp', 'User', 'Event Type', 'IP Address', 'User Agent'];
    const rows = logs.map(log => [
      log.timestamp,
      log.user,
      log.event_type,
      log.ip_address,
      log.user_agent.replace(/"/g, '""')
    ]);
    
    return [headers, ...rows]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');
  }
}
</script>
{% endif %}
