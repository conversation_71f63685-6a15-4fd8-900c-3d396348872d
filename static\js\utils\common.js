/**
 * Common utilities for ForgeX
 */

// Common utility functions
export const utils = {
  formatDate: (date) => {
    return new Date(date).toLocaleDateString();
  },
  
  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
};

export default utils;
