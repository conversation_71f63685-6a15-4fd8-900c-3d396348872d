// Enhanced Yjs implementation for real collaboration
(function(global) {
  class Doc {
    constructor() {
      this.content = {};
      this.observers = {};
      this.updateCount = 0;
    }

    getText(name) {
      if (!this.content[name]) {
        this.content[name] = new Text(this, name);
      }
      return this.content[name];
    }

    on(event, callback) {
      if (!this.observers[event]) {
        this.observers[event] = [];
      }
      this.observers[event].push(callback);
    }

    off(event, callback) {
      if (this.observers[event]) {
        this.observers[event] = this.observers[event].filter(cb => cb !== callback);
      }
    }

    emit(event, ...args) {
      if (this.observers[event]) {
        this.observers[event].forEach(callback => callback(...args));
      }
    }

    // Generate an update that can be sent to other clients
    generateUpdate(origin) {
      // In a real implementation, this would create a binary update
      // For our simplified version, we'll just create a JSON representation
      const updates = [];

      for (const [name, text] of Object.entries(this.content)) {
        if (text.changed) {
          updates.push({
            name,
            value: text.value,
            version: ++this.updateCount
          });
          text.changed = false;
        }
      }

      if (updates.length > 0) {
        // Convert to Uint8Array to mimic real Yjs behavior
        const updateStr = JSON.stringify(updates);
        const encoder = new TextEncoder();
        const update = encoder.encode(updateStr);

        // Notify observers
        this.emit('update', update, origin);

        return update;
      }

      return null;
    }

    // Apply an update from another client
    applyUpdate(update, origin) {
      try {
        // Convert from Uint8Array back to JSON
        const decoder = new TextDecoder();
        const updateStr = decoder.decode(update);
        const updates = JSON.parse(updateStr);

        // Apply each text update
        for (const textUpdate of updates) {
          const text = this.getText(textUpdate.name);
          text.value = textUpdate.value;
          text.notifyObservers(origin);
        }
      } catch (error) {
        console.error('Error applying update:', error);
      }
    }
  }

  class Text {
    constructor(doc, name) {
      this.doc = doc;
      this.name = name;
      this.value = "";
      this.observers = [];
      this.changed = false;
    }

    toString() {
      return this.value;
    }

    insert(index, content, origin) {
      this.value = this.value.slice(0, index) + content + this.value.slice(index);
      this.changed = true;
      this.notifyObservers(origin);

      // Generate and emit update
      this.doc.generateUpdate(origin);
    }

    delete(index, length, origin) {
      this.value = this.value.slice(0, index) + this.value.slice(index + length);
      this.changed = true;
      this.notifyObservers(origin);

      // Generate and emit update
      this.doc.generateUpdate(origin);
    }

    notifyObservers(origin) {
      this.observers.forEach(observer => observer.callback(this, origin));
    }

    observe(callback) {
      this.observers.push({ callback });
      return {
        disconnect: () => this.unobserve(callback)
      };
    }

    unobserve(callback) {
      this.observers = this.observers.filter(
        observer => observer.callback !== callback
      );
    }
  }

  // Export to global scope
  global.Y = {
    Doc: Doc
  };
})(typeof window !== 'undefined' ? window : global);

console.log("Enhanced Yjs library loaded");