// Enhanced Y-Monaco implementation for real collaboration
(function(global) {
  class MonacoBinding {
    constructor(ytext, monacoModel, editors, awareness) {
      this.ytext = ytext;
      this.monacoModel = monacoModel;
      this.editors = editors;
      this.awareness = awareness;
      this._monacoChangeHandler = null;
      this._ytextObserver = null;
      this._mux = false; // Mutex to prevent infinite loops

      console.log("MonacoBinding initialized");

      // Initialize the binding
      this._init();
    }

    _init() {
      // Set initial value
      const initialValue = this.ytext.toString();
      if (initialValue) {
        this.monacoModel.setValue(initialValue);
      }

      // Listen for changes in Monaco
      this._monacoChangeHandler = this.monacoModel.onDidChangeContent(event => {
        if (this._mux) return; // Prevent infinite loops
        this._mux = true;

        try {
          // Apply changes to Yjs text
          const changes = event.changes;
          for (const change of changes) {
            const start = change.rangeOffset;

            // Handle text insertion
            if (change.text.length > 0) {
              this.ytext.insert(start, change.text, this);
            }

            // Handle text deletion
            if (change.rangeLength > 0) {
              this.ytext.delete(start, change.rangeLength, this);
            }
          }

          console.log("Monaco content changed and synced to Yjs");
        } catch (error) {
          console.error("Error handling Monaco change:", error);
        } finally {
          this._mux = false;
        }
      });

      // Listen for changes in Yjs
      this._ytextObserver = (ytext, origin) => {
        if (origin === this) return; // Prevent infinite loops
        if (this._mux) return;
        this._mux = true;

        try {
          // Apply Yjs text to Monaco
          const newText = ytext.toString();
          const oldText = this.monacoModel.getValue();

          if (newText !== oldText) {
            console.log("Syncing text from Yjs to Monaco:");
            console.log("Old text:", oldText.length > 100 ? oldText.substring(0, 100) + "..." : oldText);
            console.log("New text:", newText.length > 100 ? newText.substring(0, 100) + "..." : newText);

            // Use Monaco's edit operation to update the text
            this.monacoModel.pushEditOperations(
              [], // No selection changes
              [
                {
                  range: this.monacoModel.getFullModelRange(),
                  text: newText
                }
              ],
              () => [] // No cursor position changes
            );

            console.log("✅ Yjs content changed and synced to Monaco");
          }
        } catch (error) {
          console.error("Error handling Yjs change:", error);
        } finally {
          this._mux = false;
        }
      };

      this.ytext.observe(this._ytextObserver);

      // Set up awareness (cursor positions, etc.)
      this._setupAwareness();
    }

    _setupAwareness() {
      // Set up awareness for cursor positions
      this.editors.forEach(editor => {
        editor.onDidChangeCursorPosition(event => {
          // Update cursor position in awareness
          this.awareness.setLocalState({
            cursor: {
              position: event.position,
              username: this._getUsername()
            }
          });
        });

        // Set up awareness for selection changes
        editor.onDidChangeCursorSelection(event => {
          // Update selection in awareness
          this.awareness.setLocalState({
            cursor: {
              position: event.selection.getPosition(),
              selection: {
                startLineNumber: event.selection.startLineNumber,
                startColumn: event.selection.startColumn,
                endLineNumber: event.selection.endLineNumber,
                endColumn: event.selection.endColumn
              },
              username: this._getUsername()
            }
          });
        });
      });

      // Listen for awareness updates from other users
      this.awareness.on('update', updates => {
        // Render remote cursors and selections
        this._renderRemoteCursors();
      });
    }

    _getUsername() {
      // Try to get username from various sources
      return window.currentUsername ||
             localStorage.getItem('username') ||
             'Anonymous User';
    }

    _renderRemoteCursors() {
      // This would render cursors from other users
      // For simplicity, we'll just log the cursor positions
      const states = this.awareness.getStates();
      const remoteCursors = [];

      states.forEach((state, clientId) => {
        if (state.cursor && clientId !== this.awareness.provider.clientId) {
          remoteCursors.push({
            clientId,
            username: state.cursor.username || 'Anonymous',
            position: state.cursor.position,
            selection: state.cursor.selection
          });
        }
      });

      if (remoteCursors.length > 0) {
        console.log("Remote cursors:", remoteCursors);
      }
    }

    destroy() {
      // Clean up event listeners
      if (this._monacoChangeHandler) {
        this._monacoChangeHandler.dispose();
      }
      if (this._ytextObserver) {
        this.ytext.unobserve(this._ytextObserver);
      }
      console.log("MonacoBinding destroyed");
    }
  }

  // Export to global scope
  global.MonacoBinding = MonacoBinding;
})(typeof window !== 'undefined' ? window : global);

console.log("Enhanced Y-Monaco library loaded");