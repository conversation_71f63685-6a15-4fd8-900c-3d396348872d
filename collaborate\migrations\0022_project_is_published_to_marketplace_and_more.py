# Generated by Django 5.2.1 on 2025-06-04 18:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "collaborate",
            "0021_rename_collaborate_projectbranch_project_name_idx_collaborate_project_1dbd03_idx_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="project",
            name="is_published_to_marketplace",
            field=models.BooleanField(
                default=False,
                help_text="Whether this project is published to the marketplace",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="marketplace_description",
            field=models.TextField(
                blank=True, help_text="Enhanced description for marketplace display"
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="marketplace_image",
            field=models.ImageField(
                blank=True,
                help_text="Project showcase image for marketplace",
                null=True,
                upload_to="marketplace/images/",
            ),
        ),
        migrations.AddField(
            model_name="project",
            name="marketplace_published_at",
            field=models.DateTimeField(
                blank=True,
                help_text="When the project was published to marketplace",
                null=True,
            ),
        ),
        migrations.CreateModel(
            name="ProjectMarketplacePost",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "featured_image",
                    models.ImageField(
                        blank=True, null=True, upload_to="marketplace/featured/"
                    ),
                ),
                (
                    "showcase_description",
                    models.TextField(
                        help_text="Enhanced description for marketplace showcase"
                    ),
                ),
                (
                    "tags",
                    models.CharField(
                        blank=True,
                        help_text="Comma-separated tags for better discovery",
                        max_length=500,
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False, help_text="Featured posts appear at the top"
                    ),
                ),
                ("view_count", models.PositiveIntegerField(default=0)),
                ("published_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "project",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="marketplace_post",
                        to="collaborate.project",
                    ),
                ),
            ],
            options={
                "ordering": ["-is_featured", "-published_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectComment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_edited", models.BooleanField(default=False)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="replies",
                        to="collaborate.projectcomment",
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="collaborate.projectmarketplacepost",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="MarketplaceApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message",
                    models.TextField(help_text="Why do you want to join this project?"),
                ),
                (
                    "skills_offered",
                    models.TextField(
                        blank=True,
                        help_text="What skills can you bring to this project?",
                    ),
                ),
                (
                    "portfolio_link",
                    models.URLField(
                        blank=True, help_text="Link to your portfolio or relevant work"
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "reviewer_notes",
                    models.TextField(
                        blank=True, help_text="Notes from the project owner"
                    ),
                ),
                (
                    "applicant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="marketplace_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="marketplace_applications",
                        to="collaborate.projectmarketplacepost",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="ProjectReaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "reaction_type",
                    models.CharField(
                        choices=[
                            ("like", "👍 Like"),
                            ("love", "❤️ Love"),
                            ("wow", "😮 Wow"),
                            ("fire", "🔥 Fire"),
                            ("rocket", "🚀 Rocket"),
                        ],
                        default="like",
                        max_length=10,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="reactions",
                        to="collaborate.projectmarketplacepost",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name="projectmarketplacepost",
            index=models.Index(
                fields=["is_featured", "-published_at"],
                name="collaborate_is_feat_3f0951_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="projectmarketplacepost",
            index=models.Index(
                fields=["published_at"], name="collaborate_publish_65e042_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectcomment",
            index=models.Index(
                fields=["post", "created_at"], name="collaborate_post_id_97e428_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectcomment",
            index=models.Index(
                fields=["author", "created_at"], name="collaborate_author__7b82ca_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectcomment",
            index=models.Index(
                fields=["parent"], name="collaborate_parent__6d0d79_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="marketplaceapplication",
            index=models.Index(
                fields=["post", "status"], name="collaborate_post_id_f8eeb0_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="marketplaceapplication",
            index=models.Index(
                fields=["applicant", "status"], name="collaborate_applica_da6be2_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="marketplaceapplication",
            index=models.Index(
                fields=["status", "created_at"], name="collaborate_status_1ca41a_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="marketplaceapplication",
            unique_together={("post", "applicant")},
        ),
        migrations.AddIndex(
            model_name="projectreaction",
            index=models.Index(
                fields=["post", "reaction_type"], name="collaborate_post_id_2352e6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="projectreaction",
            index=models.Index(
                fields=["user", "created_at"], name="collaborate_user_id_60c597_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="projectreaction",
            unique_together={("post", "user")},
        ),
    ]
