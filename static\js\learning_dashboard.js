// Learning Dashboard JavaScript

function initializeDashboard() {
    console.log('Learning Dashboard initialized');
    initializeParticles();
    animateAnalyticsCards();
}

function animateAnalyticsCards() {
    const cards = document.querySelectorAll('.analytics-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
}

// AI Chat Modal Functions
function openAIChat() {
    document.getElementById('ai-chat-modal').style.display = 'block';
    document.getElementById('modal-chat-input').focus();
}

function closeAIChat() {
    document.getElementById('ai-chat-modal').style.display = 'none';
}

function sendModalMessage() {
    const chatInput = document.getElementById('modal-chat-input');
    const message = chatInput.value.trim();
    
    if (!message) return;
    
    // Add user message to modal chat
    addModalMessage(message, 'user');
    
    // Clear input
    chatInput.value = '';
    
    // Show typing indicator
    addModalTypingIndicator();
    
    // Send to AI
    fetch('/learn/ai/chat/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            message: message,
            type: 'question'
        })
    })
    .then(response => response.json())
    .then(data => {
        removeModalTypingIndicator();
        if (data.success) {
            addModalMessage(data.response, 'ai');
        } else {
            addModalMessage('Sorry, I encountered an error. Please try again.', 'ai');
        }
    })
    .catch(error => {
        removeModalTypingIndicator();
        addModalMessage('Sorry, I\'m having trouble connecting. Please try again.', 'ai');
        console.error('AI Chat error:', error);
    });
}

function addModalMessage(message, sender) {
    const chatMessages = document.getElementById('modal-chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = message;
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = 'Just now';
    
    messageDiv.appendChild(contentDiv);
    if (sender === 'user') {
        messageDiv.appendChild(timeDiv);
    }
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addModalTypingIndicator() {
    const chatMessages = document.getElementById('modal-chat-messages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.id = 'modal-typing-indicator';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = '🤖 Thinking...';
    
    typingDiv.appendChild(contentDiv);
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function removeModalTypingIndicator() {
    const typingIndicator = document.getElementById('modal-typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Code Analyzer Modal Functions
function openCodeAnalyzer() {
    document.getElementById('code-analyzer-modal').style.display = 'block';
    document.getElementById('modal-code-input').focus();
}

function closeCodeAnalyzer() {
    document.getElementById('code-analyzer-modal').style.display = 'none';
}

function analyzeModalCode() {
    const codeInput = document.getElementById('modal-code-input');
    const languageSelect = document.getElementById('modal-code-language');
    const resultsDiv = document.getElementById('modal-analysis-results');
    
    const code = codeInput.value.trim();
    if (!code) {
        alert('Please enter some code to analyze');
        return;
    }
    
    // Show loading
    resultsDiv.style.display = 'block';
    resultsDiv.innerHTML = '🔍 Analyzing your code...';
    
    fetch('/learn/ai/analyze-code/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            code: code,
            language: languageSelect.value,
            analysis_type: 'comprehensive'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayModalAnalysisResults(data.analysis);
        } else {
            resultsDiv.innerHTML = 'Error analyzing code: ' + data.error;
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = 'Error connecting to analysis service.';
        console.error('Code analysis error:', error);
    });
}

function displayModalAnalysisResults(analysis) {
    const resultsDiv = document.getElementById('modal-analysis-results');
    const score = analysis.overall_score;
    
    resultsDiv.innerHTML = `
        <div class="modal-analysis-content">
            <div class="score-display">
                <div class="score-circle" style="--score-angle: ${score * 3.6}deg;">
                    ${score}
                </div>
                <div class="score-label">Code Quality Score</div>
            </div>
            <div class="feedback-content">
                <h4>AI Feedback:</h4>
                <p>${analysis.feedback}</p>
            </div>
            <div class="suggestions-content">
                <h4>Suggestions:</h4>
                <div class="suggestions-list">
                    ${generateSuggestionsList(analysis)}
                </div>
            </div>
        </div>
    `;
}

function generateSuggestionsList(analysis) {
    const allSuggestions = [
        ...analysis.syntax_issues,
        ...analysis.best_practice_suggestions,
        ...analysis.performance_tips,
        ...analysis.security_concerns,
        ...analysis.style_improvements
    ];
    
    if (allSuggestions.length === 0) {
        return '<p>Great job! No major issues found.</p>';
    }
    
    return allSuggestions.map(suggestion => `
        <div class="suggestion-item">
            <div class="suggestion-type">${suggestion.type || 'General'}</div>
            <div class="suggestion-message">${suggestion.message || suggestion}</div>
            ${suggestion.suggestion ? `<div class="suggestion-fix">${suggestion.suggestion}</div>` : ''}
        </div>
    `).join('');
}

// Modal Event Handlers
document.addEventListener('DOMContentLoaded', function() {
    // Close modals when clicking outside
    window.onclick = function(event) {
        const aiModal = document.getElementById('ai-chat-modal');
        const codeModal = document.getElementById('code-analyzer-modal');
        
        if (event.target === aiModal) {
            closeAIChat();
        }
        if (event.target === codeModal) {
            closeCodeAnalyzer();
        }
    };
    
    // Keyboard shortcuts for modals
    document.addEventListener('keydown', function(event) {
        // Escape to close modals
        if (event.key === 'Escape') {
            closeAIChat();
            closeCodeAnalyzer();
        }
        
        // Ctrl/Cmd + Enter to send message in modal
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            if (document.activeElement.id === 'modal-chat-input') {
                sendModalMessage();
            }
        }
    });
});

// Utility function for CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize particles for dashboard
function initializeParticles() {
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 60,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": ["#C0ff6b", "#a0e066", "#80c055"]
                },
                "shape": {
                    "type": ["circle", "triangle"],
                    "stroke": {
                        "width": 0,
                        "color": "#000000"
                    }
                },
                "opacity": {
                    "value": 0.4,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 4,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 2,
                        "size_min": 0.5,
                        "sync": false
                    }
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#C0ff6b",
                    "opacity": 0.3,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 1.5,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false,
                    "attract": {
                        "enable": true,
                        "rotateX": 600,
                        "rotateY": 1200
                    }
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "bubble"
                    },
                    "onclick": {
                        "enable": true,
                        "mode": "push"
                    },
                    "resize": true
                },
                "modes": {
                    "bubble": {
                        "distance": 200,
                        "size": 6,
                        "duration": 2,
                        "opacity": 0.8,
                        "speed": 3
                    },
                    "push": {
                        "particles_nb": 4
                    }
                }
            },
            "retina_detect": true
        });
    }
}
