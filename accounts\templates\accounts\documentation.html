{% extends 'base.html' %}
{% load static %}

{% block title %}Documentation - Forge X{% endblock %}

{% block content %}
<style>
/* Documentation Page Specific Styles */
.documentation-container {
  padding: 40px 0;
  min-height: 80vh;
}

.documentation-hero {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.documentation-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.documentation-hero p {
  font-size: 1.2rem;
  color: #d5d5d5;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Documentation Navigation */
.doc-nav {
  background: linear-gradient(135deg, rgba(28,28,28,0.9) 0%, rgba(45,45,45,0.9) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.doc-nav h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.doc-nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.doc-nav-item {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  padding: 20px;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.3s ease;
  display: block;
}

.doc-nav-item:hover {
  background: rgba(192, 255, 107, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(192, 255, 107, 0.1);
  color: #ffffff;
  text-decoration: none;
}

.doc-nav-item h3 {
  color: #C0ff6b;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.doc-nav-item p {
  margin: 0;
  font-size: 0.9rem;
  color: #d5d5d5;
}

/* Documentation Sections */
.doc-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.doc-section h2 {
  color: #C0ff6b;
  margin-bottom: 25px;
  font-size: 2rem;
  border-bottom: 2px solid rgba(192, 255, 107, 0.3);
  padding-bottom: 10px;
}

.doc-section h3 {
  color: #ffffff;
  margin-top: 30px;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.doc-section h4 {
  color: #C0ff6b;
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.doc-section p {
  color: #d5d5d5;
  line-height: 1.6;
  margin-bottom: 15px;
}

.doc-section ul, .doc-section ol {
  color: #d5d5d5;
  line-height: 1.6;
  margin-bottom: 15px;
  padding-left: 20px;
}

.doc-section li {
  margin-bottom: 8px;
}

.doc-section code {
  background: rgba(192, 255, 107, 0.1);
  color: #C0ff6b;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.doc-section pre {
  background: rgba(0, 0, 0, 0.5);
  color: #C0ff6b;
  padding: 20px;
  border-radius: 12px;
  overflow-x: auto;
  margin: 20px 0;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.doc-section pre code {
  background: none;
  padding: 0;
}

/* Feature Cards */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin: 30px 0;
}

.feature-card {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.4);
  box-shadow: 0 10px 20px rgba(192, 255, 107, 0.1);
}

.feature-card h4 {
  color: #C0ff6b;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.feature-card p {
  color: #d5d5d5;
  margin: 0;
}

/* API Documentation */
.api-endpoint {
  background: rgba(0, 0, 0, 0.3);
  border-left: 4px solid #C0ff6b;
  padding: 15px 20px;
  margin: 15px 0;
  border-radius: 0 8px 8px 0;
}

.api-method {
  display: inline-block;
  background: #C0ff6b;
  color: #000000;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.8rem;
  margin-right: 10px;
}

.api-url {
  color: #ffffff;
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

/* Responsive Design */
@media (max-width: 768px) {
  .documentation-hero h1 {
    font-size: 2.5rem;
  }

  .documentation-hero p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .doc-nav-grid {
    grid-template-columns: 1fr;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .doc-section {
    padding: 25px 20px;
    margin: 0 10px 20px 10px;
  }

  .doc-nav {
    padding: 20px 15px;
    margin: 0 10px 20px 10px;
  }
}

@media (max-width: 480px) {
  .documentation-hero h1 {
    font-size: 2rem;
  }

  .doc-section {
    padding: 20px 15px;
  }

  .doc-nav {
    padding: 15px 10px;
  }
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<div class="documentation-container">
  <div class="tile-wrap">
    <!-- Hero Section -->
    <div class="documentation-hero fade-in">
      <h1>Documentation</h1>
      <p>Complete guide to using Forge X platform. Learn how to collaborate, build projects, find mentors, and master new skills with our comprehensive documentation.</p>
    </div>

    <!-- Quick Navigation -->
    <div class="doc-nav fade-in" data-delay="100">
      <h2>Quick Navigation</h2>
      <div class="doc-nav-grid">
        <a href="#getting-started" class="doc-nav-item">
          <h3>🚀 Getting Started</h3>
          <p>Set up your profile and start your journey</p>
        </a>
        <a href="#collaboration" class="doc-nav-item">
          <h3>🤝 Collaboration</h3>
          <p>Create and join projects with other developers</p>
        </a>
        <a href="#learning" class="doc-nav-item">
          <h3>📚 Learning</h3>
          <p>Access courses and educational content</p>
        </a>
        <a href="#mentorship" class="doc-nav-item">
          <h3>👨‍🏫 Mentorship</h3>
          <p>Find mentors or become one yourself</p>
        </a>
        <a href="#features" class="doc-nav-item">
          <h3>⚡ Features</h3>
          <p>Explore all platform capabilities</p>
        </a>
        <a href="#api" class="doc-nav-item">
          <h3>🔧 API Reference</h3>
          <p>Technical documentation for developers</p>
        </a>
      </div>
    </div>

    <!-- Getting Started Section -->
    <div class="doc-section fade-in" data-delay="200" id="getting-started">
      <h2>🚀 Getting Started</h2>

      <h3>1. Account Creation</h3>
      <p>Start your journey by creating an account on Forge X:</p>
      <ul>
        <li>Click <strong>Sign Up</strong> in the navigation bar</li>
        <li>Fill in your details: first name, last name, username, email, and password</li>
        <li>Verify your email address through the verification link sent to your inbox</li>
        <li>Complete your profile setup with skills, bio, and availability</li>
      </ul>

      <h3>2. Profile Setup</h3>
      <p>A complete profile helps you get better matches and opportunities:</p>
      <ul>
        <li><strong>Upload CV:</strong> Our AI will extract skills automatically</li>
        <li><strong>Add Skills:</strong> Specify your proficiency level (1-5) for each skill</li>
        <li><strong>Set Availability:</strong> Choose your timezone and working hours</li>
        <li><strong>Write Bio:</strong> Tell others about yourself and your goals</li>
      </ul>

      <h3>3. Dashboard Overview</h3>
      <p>Your dashboard provides a centralized view of all your activities:</p>
      <ul>
        <li><strong>Project Overview:</strong> See your active collaborations</li>
        <li><strong>Learning Progress:</strong> Track your course completions</li>
        <li><strong>Mentorship Sessions:</strong> Upcoming and past sessions</li>
        <li><strong>Notifications:</strong> Stay updated with platform activities</li>
      </ul>
    </div>

    <!-- Collaboration Section -->
    <div class="doc-section fade-in" data-delay="300" id="collaboration">
      <h2>🤝 Collaboration</h2>

      <h3>Creating Projects</h3>
      <p>Share your ideas and find collaborators:</p>
      <ol>
        <li>Navigate to <strong>Collaborate</strong> section</li>
        <li>Click <strong>Create Project</strong></li>
        <li>Fill in project details:
          <ul>
            <li>Project name and description</li>
            <li>Required skills and programming languages</li>
            <li>Team size and collaboration type</li>
            <li>Project timeline and goals</li>
          </ul>
        </li>
        <li>Enable AI matching for automatic team suggestions</li>
      </ol>

      <h3>Joining Projects</h3>
      <p>Find projects that match your interests and skills:</p>
      <ul>
        <li><strong>Browse Projects:</strong> View all available projects</li>
        <li><strong>Filter & Search:</strong> Find projects by skills, languages, or keywords</li>
        <li><strong>Apply:</strong> Submit applications with your motivation</li>
        <li><strong>AI Recommendations:</strong> Get personalized project suggestions</li>
      </ul>

      <h3>Real-time Collaboration</h3>
      <p>Work together seamlessly with built-in tools:</p>
      <div class="feature-grid">
        <div class="feature-card">
          <h4>Code Editor</h4>
          <p>Monaco-based collaborative editor with real-time synchronization, syntax highlighting, and multi-language support.</p>
        </div>
        <div class="feature-card">
          <h4>Voice & Video Chat</h4>
          <p>WebRTC-powered communication for team meetings, code reviews, and pair programming sessions.</p>
        </div>
        <div class="feature-card">
          <h4>File Management</h4>
          <p>Organize project files, share resources, and maintain version control within the platform.</p>
        </div>
        <div class="feature-card">
          <h4>Team Analytics</h4>
          <p>Track team performance, skill distribution, and project progress with detailed analytics.</p>
        </div>
      </div>
    </div>

    <!-- Learning Section -->
    <div class="doc-section fade-in" data-delay="400" id="learning">
      <h2>📚 Learning</h2>

      <h3>Course Catalog</h3>
      <p>Access a wide range of programming and technology courses:</p>
      <ul>
        <li><strong>Programming Languages:</strong> Python, JavaScript, Java, C++, and more</li>
        <li><strong>Web Development:</strong> Frontend, backend, and full-stack development</li>
        <li><strong>Data Science:</strong> Machine learning, AI, and data analysis</li>
        <li><strong>DevOps:</strong> CI/CD, containerization, and cloud technologies</li>
      </ul>

      <h3>Learning Path</h3>
      <p>Structured learning experience:</p>
      <ol>
        <li><strong>Choose Course:</strong> Browse and select courses that match your goals</li>
        <li><strong>Follow Chapters:</strong> Progress through organized learning modules</li>
        <li><strong>Complete Lessons:</strong> Interactive content with examples and exercises</li>
        <li><strong>Track Progress:</strong> Monitor your learning journey and achievements</li>
      </ol>

      <h3>Skill Development</h3>
      <p>Our platform helps you grow your technical skills:</p>
      <ul>
        <li><strong>Skill Assessment:</strong> Regular evaluation of your proficiency</li>
        <li><strong>Personalized Recommendations:</strong> AI suggests skills to learn based on market demand</li>
        <li><strong>Practical Projects:</strong> Apply your knowledge in real-world scenarios</li>
        <li><strong>Community Learning:</strong> Learn from peers and experienced developers</li>
      </ul>
    </div>

    <!-- Mentorship Section -->
    <div class="doc-section fade-in" data-delay="500" id="mentorship">
      <h2>👨‍🏫 Mentorship</h2>

      <h3>Finding Mentors</h3>
      <p>Connect with experienced professionals to accelerate your learning:</p>
      <ul>
        <li><strong>Browse Marketplace:</strong> Explore available mentors by expertise</li>
        <li><strong>Filter by Skills:</strong> Find mentors specializing in your areas of interest</li>
        <li><strong>Read Reviews:</strong> Check feedback from previous mentees</li>
        <li><strong>Book Sessions:</strong> Schedule one-on-one mentoring sessions</li>
      </ul>

      <h3>Becoming a Mentor</h3>
      <p>Share your expertise and help others grow:</p>
      <ol>
        <li>Complete your mentor profile with expertise areas</li>
        <li>Set your availability and session rates</li>
        <li>Get verified by our team</li>
        <li>Start receiving booking requests from learners</li>
      </ol>

      <h3>Session Features</h3>
      <div class="feature-grid">
        <div class="feature-card">
          <h4>Video Conferencing</h4>
          <p>High-quality video calls with screen sharing capabilities for effective mentoring sessions.</p>
        </div>
        <div class="feature-card">
          <h4>Session Recording</h4>
          <p>Optional recording feature to review sessions and track learning progress.</p>
        </div>
        <div class="feature-card">
          <h4>Payment Integration</h4>
          <p>Secure payment processing with automatic mentor payouts and learner billing.</p>
        </div>
        <div class="feature-card">
          <h4>Feedback System</h4>
          <p>Rate and review sessions to maintain quality and help others choose mentors.</p>
        </div>
      </div>
    </div>

    <!-- Features Section -->
    <div class="doc-section fade-in" data-delay="600" id="features">
      <h2>⚡ Platform Features</h2>

      <h3>AI-Powered Matching</h3>
      <p>Our intelligent algorithms help you find the perfect collaborators and opportunities:</p>
      <ul>
        <li><strong>Skill-based Matching:</strong> Find team members with complementary skills</li>
        <li><strong>Availability Sync:</strong> Match with people in compatible timezones</li>
        <li><strong>Project Recommendations:</strong> Get suggestions based on your interests</li>
        <li><strong>Learning Path Optimization:</strong> Personalized course recommendations</li>
      </ul>

      <h3>Real-time Communication</h3>
      <p>Stay connected with your team and mentors:</p>
      <ul>
        <li><strong>WebSocket Notifications:</strong> Instant updates on platform activities</li>
        <li><strong>Project Chat:</strong> Team communication within project workspace</li>
        <li><strong>Voice & Video Calls:</strong> Built-in communication tools</li>
        <li><strong>Screen Sharing:</strong> Share your screen during collaboration</li>
      </ul>

      <h3>Analytics & Insights</h3>
      <p>Track your progress and performance:</p>
      <ul>
        <li><strong>Skill Heatmap:</strong> Visualize market demand for different skills</li>
        <li><strong>Learning Analytics:</strong> Monitor your course progress and achievements</li>
        <li><strong>Team Performance:</strong> Analyze collaboration effectiveness</li>
        <li><strong>Mentorship Metrics:</strong> Track session outcomes and feedback</li>
      </ul>
    </div>

    <!-- API Reference Section -->
    <div class="doc-section fade-in" data-delay="700" id="api">
      <h2>🔧 API Reference</h2>

      <h3>Authentication</h3>
      <p>All API requests require authentication using session-based authentication or API tokens.</p>

      <div class="api-endpoint">
        <span class="api-method">POST</span>
        <span class="api-url">/accounts/login/</span>
        <p>Authenticate user and create session</p>
      </div>

      <div class="api-endpoint">
        <span class="api-method">POST</span>
        <span class="api-url">/accounts/logout/</span>
        <p>End user session</p>
      </div>

      <h3>Projects API</h3>
      <p>Manage collaborative projects programmatically:</p>

      <div class="api-endpoint">
        <span class="api-method">GET</span>
        <span class="api-url">/collaborate/api/projects/</span>
        <p>List all available projects</p>
      </div>

      <div class="api-endpoint">
        <span class="api-method">POST</span>
        <span class="api-url">/collaborate/api/projects/</span>
        <p>Create a new project</p>
      </div>

      <div class="api-endpoint">
        <span class="api-method">GET</span>
        <span class="api-url">/collaborate/api/projects/{id}/</span>
        <p>Get project details</p>
      </div>

      <h3>Learning API</h3>
      <p>Access course content and track progress:</p>

      <div class="api-endpoint">
        <span class="api-method">GET</span>
        <span class="api-url">/learn/api/courses/</span>
        <p>List available courses</p>
      </div>

      <div class="api-endpoint">
        <span class="api-method">GET</span>
        <span class="api-url">/learn/api/courses/{id}/progress/</span>
        <p>Get user progress for a course</p>
      </div>

      <h3>Mentorship API</h3>
      <p>Manage mentoring sessions and bookings:</p>

      <div class="api-endpoint">
        <span class="api-method">GET</span>
        <span class="api-url">/mentorship/api/mentors/</span>
        <p>List available mentors</p>
      </div>

      <div class="api-endpoint">
        <span class="api-method">POST</span>
        <span class="api-url">/mentorship/api/sessions/</span>
        <p>Book a mentoring session</p>
      </div>

      <h3>WebSocket Endpoints</h3>
      <p>Real-time communication endpoints:</p>
      <pre><code>ws://localhost:8001/ws/notifications/{user_id}/
ws://localhost:8001/ws/project/{project_id}/
ws://localhost:8001/ws/mentorship/{session_id}/</code></pre>
    </div>

    <!-- Support Section -->
    <div class="doc-section fade-in" data-delay="800">
      <h2>🆘 Support & Help</h2>

      <h3>Getting Help</h3>
      <p>Need assistance? Here are the best ways to get support:</p>
      <ul>
        <li><strong>Contact Form:</strong> Use our contact page for general inquiries</li>
        <li><strong>Community Forums:</strong> Ask questions and help others (coming soon)</li>
        <li><strong>Documentation:</strong> This comprehensive guide covers most topics</li>
        <li><strong>Video Tutorials:</strong> Step-by-step guides for common tasks (coming soon)</li>
      </ul>

      <h3>Troubleshooting</h3>
      <p>Common issues and solutions:</p>
      <ul>
        <li><strong>Login Issues:</strong> Check email verification and password reset</li>
        <li><strong>Profile Setup:</strong> Ensure all required fields are completed</li>
        <li><strong>Project Access:</strong> Verify you're a member or have proper permissions</li>
        <li><strong>Payment Problems:</strong> Contact support for billing-related issues</li>
      </ul>

      <h3>Platform Status</h3>
      <p>Stay informed about platform availability and updates:</p>
      <ul>
        <li><strong>System Status:</strong> Check our status page for service availability</li>
        <li><strong>Maintenance Windows:</strong> Scheduled maintenance notifications</li>
        <li><strong>Feature Updates:</strong> New feature announcements and changelogs</li>
        <li><strong>Known Issues:</strong> Current bugs and their resolution status</li>
      </ul>
    </div>
  </div>
</div>

<!-- JavaScript for animations and particles -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": "#C0ff6b"
        },
        "shape": {
          "type": "circle",
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": false,
          "anim": {
            "enable": false,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": false,
            "speed": 40,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 6,
          "direction": "none",
          "random": false,
          "straight": false,
          "out_mode": "out",
          "bounce": false,
          "attract": {
            "enable": false,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "repulse"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 400,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 400,
            "size": 40,
            "duration": 2,
            "opacity": 8,
            "speed": 3
          },
          "repulse": {
            "distance": 200,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
    });

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        const delay = parseInt(element.getAttribute('data-delay')) || 0;

        if (elementTop < windowHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('visible');
          }, delay);
        }
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up scroll event listener
    window.addEventListener('scroll', revealOnScroll);

    // Smooth scrolling for navigation links
    document.querySelectorAll('.doc-nav-item').forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const targetId = this.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    });
  });
</script>
{% endblock %}