from django.core.management.base import BaseCommand
from accounts.models import Skill

SKILLS = {
    # Programming Languages
    'Programming':
    ["Python", "JavaScript", "Java", "C#", "PHP", "C++", "TypeScript", "Ruby", "Go", "Swift"],

    
    # Web Development
    'Web Development':
    [
    "HTML", "CSS", "React", "Angular", "Vue.js", "Node.js", "Django", "Flask", "Laravel", "Spring Boot" ],

    
    # Data Science & AI
    'Data Science & AI':
    [
    "Data Analysis", "Machine Learning", "Deep Learning", "Natural Language Processing", "Data Visualization", 
    "TensorFlow", "PyTorch", "Pandas", "NumPy", "Scikit-learn" ],
    
    # Database
    'Database':
    [
    "SQL", "MongoDB", "PostgreSQL", "MySQL", "Redis", "SQLite", "Oracle", "Firebase"],
    
    # DevOps & Cloud
    'DevOps & Cloud':
    [
    "Docker", "Kubernetes", "AWS", "Azure", "Google Cloud", "CI/CD", "Git", "Linux", "Jenkins"],
    
    # Mobile Development
    'Mobile Development':
    [
    "Android", "iOS", "React Native", "Flutter", "Xamarin", "SwiftUI", "Kotlin"],
    
    # Other
    'Other':
    [
    "Agile", "Scrum", "Project Management", "UI/UX Design", "Technical Writing", "Testing", "Security" ]
}

class Command(BaseCommand):
    help = 'Populate the Skill model with predefined skills'

    def handle(self, *args, **kwargs):
        skills = [
            'Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Swift', 'Go', 'Rust', 'TypeScript', 'SQL', 'DevOps', 'UI/UX', 'Database Design'
        ]

        for skill_name in skills:
            skill, created = Skill.objects.get_or_create(name=skill_name)
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully added skill: {skill_name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Skill already exists: {skill_name}'))
