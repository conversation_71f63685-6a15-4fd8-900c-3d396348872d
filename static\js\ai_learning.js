// AI Learning Enhancement JavaScript

// Global variables
let startTime = Date.now();
let timeSpent = 0;
let isAssistantVisible = false;
let isCodeAnalysisVisible = false;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAIFeatures();
    startTimeTracking();
    initializeParticles();
});

function initializeAIFeatures() {
    // Initialize time tracking
    if (window.lessonData) {
        startTime = window.lessonData.startTime;
        timeSpent = window.lessonData.timeSpent || 0;
        updateTimeDisplay();
    }
    
    // Set up auto-save progress
    setInterval(saveProgress, 30000); // Save every 30 seconds
    
    // Set up keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    console.log('AI Learning features initialized');
}

function startTimeTracking() {
    setInterval(function() {
        timeSpent = Math.floor((Date.now() - startTime) / 60000) + (window.lessonData?.timeSpent || 0);
        updateTimeDisplay();
    }, 1000);
}

function updateTimeDisplay() {
    const timeDisplay = document.getElementById('time-display');
    if (timeDisplay) {
        timeDisplay.textContent = timeSpent + 'm';
    }
}

function handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + Enter to send chat message
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        if (document.activeElement.id === 'chat-input') {
            sendMessage();
        }
    }
    
    // Ctrl/Cmd + Shift + A to toggle assistant
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        toggleAssistant();
    }
    
    // Ctrl/Cmd + Shift + C to toggle code analysis
    if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        toggleCodeAnalysis();
    }
}

// AI Assistant Functions
function toggleAssistant() {
    const assistant = document.getElementById('ai-assistant');
    const toggleText = document.getElementById('assistant-toggle-text');
    
    if (isAssistantVisible) {
        assistant.style.display = 'none';
        toggleText.textContent = 'Show Assistant';
        isAssistantVisible = false;
    } else {
        assistant.style.display = 'block';
        toggleText.textContent = 'Hide Assistant';
        isAssistantVisible = true;
        
        // Focus on chat input
        setTimeout(() => {
            document.getElementById('chat-input').focus();
        }, 100);
    }
}

function sendMessage() {
    const chatInput = document.getElementById('chat-input');
    const message = chatInput.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat(message, 'user');
    
    // Clear input
    chatInput.value = '';
    
    // Show typing indicator
    addTypingIndicator();
    
    // Send to AI
    fetch('/learn/ai/chat/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            message: message,
            lesson_id: window.lessonData?.lessonId,
            type: 'question'
        })
    })
    .then(response => response.json())
    .then(data => {
        removeTypingIndicator();
        if (data.success) {
            addMessageToChat(data.response, 'ai');
        } else {
            addMessageToChat('Sorry, I encountered an error. Please try again.', 'ai');
        }
    })
    .catch(error => {
        removeTypingIndicator();
        addMessageToChat('Sorry, I\'m having trouble connecting. Please try again.', 'ai');
        console.error('AI Chat error:', error);
    });
}

function addMessageToChat(message, sender) {
    const chatMessages = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = message;
    
    const timeDiv = document.createElement('div');
    timeDiv.className = 'message-time';
    timeDiv.textContent = 'Just now';
    
    messageDiv.appendChild(contentDiv);
    if (sender === 'user') {
        messageDiv.appendChild(timeDiv);
    }
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addTypingIndicator() {
    const chatMessages = document.getElementById('chat-messages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message ai-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'message-content';
    contentDiv.innerHTML = '🤖 Thinking...';
    
    typingDiv.appendChild(contentDiv);
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function removeTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Code Analysis Functions
function toggleCodeAnalysis() {
    const analysis = document.getElementById('code-analysis');
    const toggleText = document.getElementById('analysis-toggle-text');
    
    if (isCodeAnalysisVisible) {
        analysis.style.display = 'none';
        toggleText.textContent = 'Show Code Analyzer';
        isCodeAnalysisVisible = false;
    } else {
        analysis.style.display = 'block';
        toggleText.textContent = 'Hide Code Analyzer';
        isCodeAnalysisVisible = true;
        
        // Focus on code input
        setTimeout(() => {
            document.getElementById('code-input').focus();
        }, 100);
    }
}

function analyzeCode() {
    const codeInput = document.getElementById('code-input');
    const languageSelect = document.getElementById('code-language');
    const analysisTypeSelect = document.getElementById('analysis-type');
    const resultsDiv = document.getElementById('analysis-results');
    
    const code = codeInput.value.trim();
    if (!code) {
        alert('Please enter some code to analyze');
        return;
    }
    
    // Show loading
    resultsDiv.style.display = 'block';
    document.getElementById('ai-feedback').innerHTML = '🔍 Analyzing your code...';
    
    fetch('/learn/ai/analyze-code/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            code: code,
            language: languageSelect.value,
            analysis_type: analysisTypeSelect.value,
            lesson_id: window.lessonData?.lessonId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResults(data.analysis);
        } else {
            document.getElementById('ai-feedback').innerHTML = 'Error analyzing code: ' + data.error;
        }
    })
    .catch(error => {
        document.getElementById('ai-feedback').innerHTML = 'Error connecting to analysis service.';
        console.error('Code analysis error:', error);
    });
}

function analyzeThisCode(button) {
    const codeBlock = button.closest('.code-example-container').querySelector('code');
    const language = button.getAttribute('data-language') || 'python';
    const code = codeBlock.textContent;
    
    // Fill the code analysis form
    document.getElementById('code-input').value = code;
    document.getElementById('code-language').value = language;
    
    // Show code analysis panel
    if (!isCodeAnalysisVisible) {
        toggleCodeAnalysis();
    }
    
    // Scroll to code analysis
    document.getElementById('code-analysis').scrollIntoView({ behavior: 'smooth' });
    
    // Auto-analyze
    setTimeout(analyzeCode, 500);
}

function displayAnalysisResults(analysis) {
    // Update score
    const scoreElement = document.getElementById('code-score');
    const score = analysis.overall_score;
    scoreElement.textContent = score;
    scoreElement.style.setProperty('--score-angle', (score * 3.6) + 'deg');
    
    // Update feedback
    document.getElementById('ai-feedback').innerHTML = analysis.feedback;
    
    // Update suggestions
    const suggestionsList = document.getElementById('suggestions-list');
    suggestionsList.innerHTML = '';
    
    const allSuggestions = [
        ...analysis.syntax_issues,
        ...analysis.best_practice_suggestions,
        ...analysis.performance_tips,
        ...analysis.security_concerns,
        ...analysis.style_improvements
    ];
    
    if (allSuggestions.length > 0) {
        const suggestionsTitle = document.createElement('h4');
        suggestionsTitle.textContent = 'Suggestions for Improvement:';
        suggestionsTitle.style.color = '#C0ff6b';
        suggestionsList.appendChild(suggestionsTitle);
        
        allSuggestions.forEach(suggestion => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'suggestion-item';
            suggestionDiv.innerHTML = `
                <div class="suggestion-type">${suggestion.type || 'General'}</div>
                <div class="suggestion-message">${suggestion.message || suggestion}</div>
                ${suggestion.suggestion ? `<div class="suggestion-fix">${suggestion.suggestion}</div>` : ''}
            `;
            suggestionsList.appendChild(suggestionDiv);
        });
    }
}

// Progress Functions
function markAsCompleted() {
    updateProgress('completed');
}

function rateLesson() {
    const rating = prompt('Rate the difficulty of this lesson (1-5, where 1 is very easy and 5 is very hard):');
    if (rating && rating >= 1 && rating <= 5) {
        updateProgress(null, parseFloat(rating));
    }
}

function updateProgress(status = null, difficultyRating = null) {
    const data = {
        lesson_id: window.lessonData?.lessonId,
        time_spent: Math.floor((Date.now() - startTime) / 60000)
    };
    
    if (status) data.status = status;
    if (difficultyRating) data.difficulty_rating = difficultyRating;
    
    fetch('/learn/ai/update-progress/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (status === 'completed') {
                alert('🎉 Lesson marked as completed! Great job!');
                // Update UI to reflect completion
                updateProgressBadge('completed');
            }
            if (difficultyRating) {
                alert('Thank you for your feedback!');
            }
        }
    })
    .catch(error => {
        console.error('Progress update error:', error);
    });
}

function saveProgress() {
    if (window.lessonData?.lessonId) {
        updateProgress();
    }
}

function updateProgressBadge(status) {
    const badge = document.querySelector('.progress-badge');
    if (badge) {
        badge.className = `progress-badge progress-${status}`;
        badge.textContent = status === 'completed' ? '✅ Completed' : 
                           status === 'in_progress' ? '🔄 In Progress' : 
                           status === 'mastered' ? '🏆 Mastered' : '📝 Not Started';
    }
}

// Utility Functions
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function copyCode(button) {
    const codeBlock = button.parentElement.nextElementSibling.querySelector('code');
    const text = codeBlock.textContent;

    navigator.clipboard.writeText(text).then(function() {
        button.textContent = '✅ Copied!';
        setTimeout(() => {
            button.textContent = '📋 Copy';
        }, 2000);
    });
}

function initializeParticles() {
    // Initialize particles.js if available
    if (typeof particlesJS !== 'undefined') {
        particlesJS('particles-js', {
            "particles": {
                "number": {
                    "value": 50,
                    "density": {
                        "enable": true,
                        "value_area": 800
                    }
                },
                "color": {
                    "value": ["#C0ff6b", "#a0e066"]
                },
                "shape": {
                    "type": "circle"
                },
                "opacity": {
                    "value": 0.3,
                    "random": true,
                    "anim": {
                        "enable": true,
                        "speed": 1,
                        "opacity_min": 0.1,
                        "sync": false
                    }
                },
                "size": {
                    "value": 3,
                    "random": true
                },
                "line_linked": {
                    "enable": true,
                    "distance": 150,
                    "color": "#C0ff6b",
                    "opacity": 0.2,
                    "width": 1
                },
                "move": {
                    "enable": true,
                    "speed": 1,
                    "direction": "none",
                    "random": true,
                    "straight": false,
                    "out_mode": "out",
                    "bounce": false
                }
            },
            "interactivity": {
                "detect_on": "canvas",
                "events": {
                    "onhover": {
                        "enable": true,
                        "mode": "grab"
                    },
                    "resize": true
                }
            },
            "retina_detect": true
        });
    }
}
