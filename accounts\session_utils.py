"""
Session management utilities for ForgeX
Provides tools for debugging and managing user sessions
"""

from django.contrib.sessions.models import Session
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)


def get_user_sessions(user):
    """Get all active sessions for a user"""
    if not user.is_authenticated:
        return []
    
    user_sessions = []
    sessions = Session.objects.filter(expire_date__gte=timezone.now())
    
    for session in sessions:
        data = session.get_decoded()
        if data.get('_auth_user_id') == str(user.id):
            user_sessions.append({
                'session_key': session.session_key,
                'expire_date': session.expire_date,
                'last_activity': data.get('last_activity', 'Unknown'),
                'remember_me': data.get('remember_me', False),
            })
    
    return user_sessions


def set_session_remember_me(request, remember_me=True):
    """Set remember me flag in session"""
    if remember_me:
        # Set session to expire in 30 days
        request.session.set_expiry(60 * 60 * 24 * 30)
        request.session['remember_me'] = True
        logger.info(f"Session set to remember user {request.user.username} for 30 days")
    else:
        # Set session to expire when browser closes
        request.session.set_expiry(0)
        request.session['remember_me'] = False
        logger.info(f"Session set to expire when browser closes for user {request.user.username}")


def update_session_activity(request):
    """Update last activity timestamp in session"""
    if request.user.is_authenticated:
        request.session['last_activity'] = timezone.now().isoformat()


def cleanup_expired_sessions():
    """Clean up expired sessions"""
    expired_count = Session.objects.filter(expire_date__lt=timezone.now()).count()
    Session.objects.filter(expire_date__lt=timezone.now()).delete()
    logger.info(f"Cleaned up {expired_count} expired sessions")
    return expired_count


def get_session_info(request):
    """Get detailed session information for debugging"""
    if not request.session.session_key:
        return None
    
    try:
        session = Session.objects.get(session_key=request.session.session_key)
        data = session.get_decoded()
        
        return {
            'session_key': session.session_key,
            'expire_date': session.expire_date,
            'time_until_expiry': session.expire_date - timezone.now(),
            'remember_me': data.get('remember_me', False),
            'last_activity': data.get('last_activity', 'Unknown'),
            'user_id': data.get('_auth_user_id'),
            'is_authenticated': request.user.is_authenticated,
            'username': request.user.username if request.user.is_authenticated else 'Anonymous',
        }
    except Session.DoesNotExist:
        return None


def force_logout_user(user):
    """Force logout a user by deleting all their sessions"""
    if not isinstance(user, User):
        return False
    
    sessions_deleted = 0
    sessions = Session.objects.filter(expire_date__gte=timezone.now())
    
    for session in sessions:
        data = session.get_decoded()
        if data.get('_auth_user_id') == str(user.id):
            session.delete()
            sessions_deleted += 1
    
    logger.info(f"Force logged out user {user.username}, deleted {sessions_deleted} sessions")
    return sessions_deleted


def extend_session(request, days=30):
    """Extend current session expiry"""
    if request.user.is_authenticated:
        new_expiry = timezone.now() + timedelta(days=days)
        request.session.set_expiry(60 * 60 * 24 * days)
        request.session['remember_me'] = True
        logger.info(f"Extended session for user {request.user.username} by {days} days")
        return True
    return False


class SessionMiddleware:
    """Custom middleware to track session activity"""
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Update session activity before processing request
        if request.user.is_authenticated:
            update_session_activity(request)
        
        response = self.get_response(request)
        return response
