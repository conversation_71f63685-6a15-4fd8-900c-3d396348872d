"""
Feedback collection utilities for the matching engine.
This module provides functions to collect and analyze feedback on team matches.
"""
from django.db.models import Avg, Count
from django.utils import timezone
from ..models import TeamMatchFeedback, TeamMatchLog, Project, ProjectMembership

def record_match_feedback(project, user, rating, feedback_text=None, match_aspects=None):
    """
    Record feedback on a team match.

    Args:
        project: Project object
        user: User providing the feedback
        rating: Rating from 1-5
        feedback_text: Optional text feedback
        match_aspects: Optional dict of specific aspects ratings

    Returns:
        TeamMatchFeedback: Created feedback object
    """
    # Validate rating
    if rating < 1 or rating > 5:
        raise ValueError("Rating must be between 1 and 5")

    # Create feedback object
    feedback = TeamMatchFeedback.objects.create(
        project=project,
        user=user,
        rating=rating,
        feedback_text=feedback_text,
        match_aspects=match_aspects or {},
        created_at=timezone.now()
    )

    return feedback

def get_project_feedback_summary(project):
    """
    Get a summary of feedback for a project.

    Args:
        project: Project object

    Returns:
        dict: Summary of feedback
    """
    # Get all feedback for this project
    feedback = TeamMatchFeedback.objects.filter(project=project)

    # Initialize default values for empty feedback
    if not feedback.exists():
        return {
            'project': project,
            'feedback_count': 0,
            'average_rating': 0,
            'rating_counts': {1: 0, 2: 0, 3: 0, 4: 0, 5: 0},
            'aspect_ratings': {
                'skill_match': 0,
                'timezone_compatibility': 0,
                'team_balance': 0
            },
            'recent_feedback': [],
            'sentiment': 'neutral',
            'improvement_areas': []
        }

    # Calculate average rating
    avg_rating = feedback.aggregate(Avg('rating'))['rating__avg'] or 0

    # Count feedback by rating
    rating_counts = {}
    for i in range(1, 6):
        rating_counts[i] = feedback.filter(rating=i).count()

    # Calculate aspect ratings if available
    aspect_ratings = {
        'skill_match': 0,
        'timezone_compatibility': 0,
        'team_balance': 0
    }

    aspect_counts = {
        'skill_match': 0,
        'timezone_compatibility': 0,
        'team_balance': 0
    }

    for fb in feedback:
        if fb.match_aspects:
            for aspect in aspect_ratings.keys():
                if aspect in fb.match_aspects:
                    try:
                        rating_value = int(fb.match_aspects[aspect])
                        aspect_ratings[aspect] += rating_value
                        aspect_counts[aspect] += 1
                    except (ValueError, TypeError):
                        # Skip invalid ratings
                        pass

    # Calculate averages for each aspect
    for aspect, total in aspect_ratings.items():
        if aspect_counts[aspect] > 0:
            aspect_ratings[aspect] = total / aspect_counts[aspect]

    # Get recent feedback (limit to 10) and convert to list of dictionaries
    recent_feedback_qs = feedback.order_by('-created_at')[:10]
    recent_feedback = []
    for fb in recent_feedback_qs:
        recent_feedback.append({
            'id': fb.id,
            'user': fb.user,
            'rating': fb.rating,
            'feedback_text': fb.feedback_text,
            'created_at': fb.created_at,
            'match_aspects': fb.match_aspects
        })

    # Calculate sentiment based on average rating
    sentiment = "positive" if avg_rating >= 4 else ("neutral" if avg_rating >= 3 else "negative")

    # Calculate improvement areas (aspects with lowest ratings)
    improvement_areas = []
    if any(aspect_counts.values()):  # If we have any aspect ratings
        sorted_aspects = sorted(aspect_ratings.items(), key=lambda x: x[1])
        for aspect, rating in sorted_aspects:
            if rating < 3.5 and aspect_counts[aspect] > 0:  # Only suggest improvements for aspects rated below 3.5
                if aspect == 'skill_match':
                    improvement_areas.append({
                        'aspect': 'Skill Match',
                        'rating': rating,
                        'suggestion': 'Consider refining the skill matching algorithm to better align team member skills with project requirements.'
                    })
                elif aspect == 'timezone_compatibility':
                    improvement_areas.append({
                        'aspect': 'Timezone Compatibility',
                        'rating': rating,
                        'suggestion': 'Improve timezone overlap detection to ensure team members have sufficient common working hours.'
                    })
                elif aspect == 'team_balance':
                    improvement_areas.append({
                        'aspect': 'Team Balance',
                        'rating': rating,
                        'suggestion': 'Adjust the algorithm to create more balanced teams with diverse experience levels.'
                    })

    return {
        'project': project,
        'feedback_count': feedback.count(),
        'average_rating': avg_rating,
        'rating_counts': rating_counts,
        'aspect_ratings': aspect_ratings,
        'recent_feedback': recent_feedback,
        'sentiment': sentiment,
        'improvement_areas': improvement_areas
    }

def get_user_feedback_summary(user):
    """
    Get a summary of feedback provided by a user.

    Args:
        user: User object

    Returns:
        dict: Summary of feedback
    """
    # Get all feedback from this user
    feedback = TeamMatchFeedback.objects.filter(user=user)

    # Calculate average rating
    avg_rating = feedback.aggregate(Avg('rating'))['rating__avg'] or 0

    # Get projects rated
    projects_rated = Project.objects.filter(
        id__in=feedback.values_list('project_id', flat=True)
    ).distinct()

    return {
        'user': user,
        'feedback_count': feedback.count(),
        'average_rating': avg_rating,
        'projects_rated': projects_rated
    }

def analyze_feedback_trends():
    """
    Analyze trends in feedback data.

    Returns:
        dict: Analysis of feedback trends
    """
    # Get all feedback
    all_feedback = TeamMatchFeedback.objects.all()

    # Calculate overall average rating
    overall_avg = all_feedback.aggregate(Avg('rating'))['rating__avg'] or 0

    # Calculate average rating by month
    # This would require more complex queries with date extraction
    # For simplicity, we'll just return the overall average

    # Get projects with highest and lowest ratings
    projects_with_ratings = Project.objects.annotate(
        avg_rating=Avg('teammatchfeedback__rating'),
        feedback_count=Count('teammatchfeedback')
    ).filter(feedback_count__gt=0)

    highest_rated = projects_with_ratings.order_by('-avg_rating')[:5]
    lowest_rated = projects_with_ratings.order_by('avg_rating')[:5]

    return {
        'overall_average': overall_avg,
        'total_feedback_count': all_feedback.count(),
        'highest_rated_projects': highest_rated,
        'lowest_rated_projects': lowest_rated
    }

def correlate_feedback_with_match_quality(project):
    """
    Correlate feedback ratings with match quality metrics.

    Args:
        project: Project object

    Returns:
        dict: Correlation analysis
    """
    # Get feedback for this project
    feedback = TeamMatchFeedback.objects.filter(project=project)

    # Get match logs for this project
    match_logs = TeamMatchLog.objects.filter(project=project)

    # Get team members
    team_members = ProjectMembership.objects.filter(project=project)

    # Calculate skill coverage from the most recent match log
    latest_log = match_logs.order_by('-timestamp').first()
    skill_coverage = latest_log.match_data.get('skill_coverage_percent', 0) if latest_log else 0

    # Calculate average feedback rating
    avg_rating = feedback.aggregate(Avg('rating'))['rating__avg'] or 0

    # Simple correlation: is higher skill coverage correlated with higher ratings?
    correlation = {
        'skill_coverage': skill_coverage,
        'avg_rating': avg_rating,
        'team_size': team_members.count(),
        'feedback_count': feedback.count(),
        'has_correlation': False,  # This would require more sophisticated analysis
        'correlation_strength': 0  # This would require statistical calculation
    }

    return correlation
