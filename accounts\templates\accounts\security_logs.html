{% extends 'base.html' %}
{% load static %}

{% block title %}Security Logs - ForgeX{% endblock %}

{% block extra_css %}
<style>
    .security-dashboard {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        color: white;
        padding: 2rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.1);
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .filters-section {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .log-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .severity-high { color: #dc3545; font-weight: bold; }
    .severity-critical { color: #721c24; font-weight: bold; background: #f8d7da; }
    .severity-medium { color: #856404; }
    .severity-low { color: #155724; }
    
    .event-details {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .user-info {
        font-weight: 500;
    }
    
    .timestamp {
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .pagination {
        margin-top: 2rem;
    }
    
    .alert-security {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="security-dashboard">
        <h1><i class="fas fa-shield-alt"></i> Security Logs Dashboard</h1>
        <p>Monitor and analyze security events across the ForgeX platform</p>
        
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ total_logs }}</span>
                <span class="stat-label">Total Events</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ devtools_attempts }}</span>
                <span class="stat-label">DevTools Attempts</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ console_attempts }}</span>
                <span class="stat-label">Console Access</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ high_severity }}</span>
                <span class="stat-label">High/Critical Events</span>
            </div>
        </div>
    </div>

    {% if high_severity > 0 %}
    <div class="alert-security">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>Security Alert:</strong> {{ high_severity }} high or critical security events detected. Review immediately.
    </div>
    {% endif %}

    <!-- Filters Section -->
    <div class="filters-section">
        <h5><i class="fas fa-filter"></i> Filters</h5>
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="event_type" class="form-label">Event Type</label>
                <select name="event_type" id="event_type" class="form-select">
                    <option value="">All Events</option>
                    {% for value, label in event_types %}
                        <option value="{{ value }}" {% if current_filters.event_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="user" class="form-label">User</label>
                <input type="text" name="user" id="user" class="form-control" 
                       placeholder="Username" value="{{ current_filters.user|default:'' }}">
            </div>
            <div class="col-md-3">
                <label for="severity" class="form-label">Severity</label>
                <select name="severity" id="severity" class="form-select">
                    <option value="">All Severities</option>
                    <option value="low" {% if current_filters.severity == 'low' %}selected{% endif %}>Low</option>
                    <option value="medium" {% if current_filters.severity == 'medium' %}selected{% endif %}>Medium</option>
                    <option value="high" {% if current_filters.severity == 'high' %}selected{% endif %}>High</option>
                    <option value="critical" {% if current_filters.severity == 'critical' %}selected{% endif %}>Critical</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="{% url 'accounts:security_logs' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Security Logs Table -->
    <div class="log-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>Timestamp</th>
                        <th>User</th>
                        <th>Event Type</th>
                        <th>Severity</th>
                        <th>Details</th>
                        <th>IP Address</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in page_obj %}
                    <tr>
                        <td class="timestamp">
                            {{ log.timestamp|date:"M d, Y H:i:s" }}
                        </td>
                        <td class="user-info">
                            {% if log.user %}
                                <i class="fas fa-user"></i> {{ log.user.username }}
                            {% else %}
                                <i class="fas fa-user-slash text-muted"></i> Anonymous
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ log.get_event_type_display }}</span>
                        </td>
                        <td>
                            <span class="severity-{{ log.severity }}">
                                {% if log.severity == 'critical' %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elif log.severity == 'high' %}
                                    <i class="fas fa-exclamation-circle"></i>
                                {% elif log.severity == 'medium' %}
                                    <i class="fas fa-info-circle"></i>
                                {% else %}
                                    <i class="fas fa-check-circle"></i>
                                {% endif %}
                                {{ log.severity|title }}
                            </span>
                        </td>
                        <td class="event-details">
                            {{ log.event_data.details|default:"No details" }}
                            {% if log.event_data.url %}
                                <br><small class="text-muted">{{ log.event_data.url }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <code>{{ log.ip_address }}</code>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="showLogDetails({{ log.id }}, '{{ log.event_data|escapejs }}')">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No security logs found matching your criteria.</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="Security logs pagination">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_filters.event_type %}&event_type={{ current_filters.event_type }}{% endif %}{% if current_filters.user %}&user={{ current_filters.user }}{% endif %}{% if current_filters.severity %}&severity={{ current_filters.severity }}{% endif %}">
                        Previous
                    </a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if current_filters.event_type %}&event_type={{ current_filters.event_type }}{% endif %}{% if current_filters.user %}&user={{ current_filters.user }}{% endif %}{% if current_filters.severity %}&severity={{ current_filters.severity }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_filters.event_type %}&event_type={{ current_filters.event_type }}{% endif %}{% if current_filters.user %}&user={{ current_filters.user }}{% endif %}{% if current_filters.severity %}&severity={{ current_filters.severity }}{% endif %}">
                        Next
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Log Details Modal -->
<div class="modal fade" id="logDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Security Log Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="logDetailsContent"></pre>
            </div>
        </div>
    </div>
</div>

<script>
function showLogDetails(logId, eventData) {
    try {
        const data = JSON.parse(eventData);
        document.getElementById('logDetailsContent').textContent = JSON.stringify(data, null, 2);
        new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
    } catch (e) {
        document.getElementById('logDetailsContent').textContent = eventData;
        new bootstrap.Modal(document.getElementById('logDetailsModal')).show();
    }
}
</script>
{% endblock %}
