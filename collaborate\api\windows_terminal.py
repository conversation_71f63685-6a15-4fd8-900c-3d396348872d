"""
Windows Terminal Handler - Cross-platform terminal implementation for Windows
This module provides a Windows-compatible terminal implementation using pexpect-like functionality
"""

import os
import asyncio
import threading
import subprocess
import json
import time
import queue
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger("windows_terminal")

class WindowsTerminalSession:
    """
    A Windows-compatible terminal session that mimics PTY behavior
    """
    
    def __init__(self, terminal_id: str, project_hash: str):
        self.terminal_id = terminal_id
        self.project_hash = project_hash
        self.process: Optional[subprocess.Popen] = None
        self.is_active = False
        self.output_queue = queue.Queue()
        self.reader_thread: Optional[threading.Thread] = None
        self.websocket = None
        
    async def start(self, websocket):
        """Start the terminal session"""
        self.websocket = websocket
        self.is_active = True

        try:
            # Determine the project directory path
            project_dir = f"/app/projects/{self.project_hash}"

            # Handle project directory creation and path formatting
            # Check if we're running in a Docker container (Unix-like) or native Windows
            if os.path.exists('/app'):
                # Running in Docker container - use Unix paths
                if not os.path.exists(project_dir):
                    os.makedirs(project_dir, exist_ok=True)
                    logger.info(f"Created project directory: {project_dir}")
            else:
                # Running on native Windows - use Windows paths
                project_dir = project_dir.replace('/', '\\')
                if not os.path.exists(project_dir):
                    try:
                        os.makedirs(project_dir, exist_ok=True)
                        logger.info(f"Created Windows project directory: {project_dir}")
                    except Exception as e:
                        logger.error(f"Failed to create Windows project directory: {e}")
                        # Fallback to current directory if project path doesn't work
                        project_dir = os.getcwd()

            # Set up environment
            env = os.environ.copy()
            env["TERM"] = "xterm-256color"

            # Start cmd.exe with proper settings
            self.process = subprocess.Popen(
                ["cmd.exe"],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=0,  # Unbuffered
                universal_newlines=True,
                creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                cwd=project_dir,  # Start in project directory
                env=env
            )
            
            # Start output reader thread
            self.reader_thread = threading.Thread(target=self._read_output, daemon=True)
            self.reader_thread.start()
            
            # Send welcome message
            await self._send_output(f"Windows Terminal {self.terminal_id} ready\r\n")
            await self._send_output(f"Project Directory: {project_dir}\r\n")
            await self._send_output(f"Microsoft Windows [Version {os.sys.getwindowsversion().major}.{os.sys.getwindowsversion().minor}]\r\n")
            await self._send_output("(c) Microsoft Corporation. All rights reserved.\r\n\r\n")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Windows terminal: {e}")
            return False
    
    def _read_output(self):
        """Read output from the process in a separate thread"""
        while self.is_active and self.process and self.process.poll() is None:
            try:
                # Read character by character for real-time output
                char = self.process.stdout.read(1)
                if char:
                    # Queue the output for async sending
                    self.output_queue.put(char)
                else:
                    time.sleep(0.01)  # Small delay to prevent busy waiting
            except Exception as e:
                logger.error(f"Error reading terminal output: {e}")
                break
    
    async def _send_output(self, data: str):
        """Send output to WebSocket"""
        if self.websocket and data:
            try:
                await self.websocket.send_text(json.dumps({
                    "type": "output",
                    "data": data
                }))
            except Exception as e:
                logger.error(f"Error sending output to WebSocket: {e}")
    
    async def process_output_queue(self):
        """Process queued output and send to WebSocket"""
        buffer = ""
        while self.is_active:
            try:
                # Collect characters from queue
                while not self.output_queue.empty():
                    char = self.output_queue.get_nowait()
                    buffer += char
                
                # Send buffer if it has content
                if buffer:
                    await self._send_output(buffer)
                    buffer = ""
                
                # Small delay to batch characters
                await asyncio.sleep(0.01)
                
            except Exception as e:
                logger.error(f"Error processing output queue: {e}")
                break
    
    async def send_input(self, data: str):
        """Send input to the terminal"""
        if self.process and self.process.poll() is None:
            try:
                self.process.stdin.write(data)
                self.process.stdin.flush()
            except Exception as e:
                logger.error(f"Error sending input to terminal: {e}")
    
    async def resize(self, cols: int, rows: int):
        """Handle terminal resize (limited support on Windows)"""
        # Windows doesn't have easy terminal resizing like Unix PTY
        # We can try to set console buffer size, but it's limited
        try:
            # This is a placeholder - Windows console resizing is complex
            logger.info(f"Terminal resize requested: {cols}x{rows} (limited support on Windows)")
        except Exception as e:
            logger.error(f"Error resizing terminal: {e}")
    
    async def close(self):
        """Close the terminal session"""
        self.is_active = False
        
        if self.process:
            try:
                # Terminate the process gracefully
                self.process.terminate()
                
                # Wait for process to end
                try:
                    self.process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't terminate
                    self.process.kill()
                    self.process.wait()
                    
            except Exception as e:
                logger.error(f"Error closing terminal process: {e}")
        
        if self.reader_thread and self.reader_thread.is_alive():
            self.reader_thread.join(timeout=1.0)
        
        logger.info(f"Windows terminal {self.terminal_id} closed")

class WindowsTerminalManager:
    """
    Manager for Windows terminal sessions
    """
    
    def __init__(self):
        self.sessions: Dict[str, WindowsTerminalSession] = {}
    
    async def create_session(self, terminal_id: str, project_hash: str, websocket) -> bool:
        """Create a new terminal session"""
        if terminal_id in self.sessions:
            await self.close_session(terminal_id)
        
        session = WindowsTerminalSession(terminal_id, project_hash)
        success = await session.start(websocket)
        
        if success:
            self.sessions[terminal_id] = session
            # Start output processing task
            asyncio.create_task(session.process_output_queue())
        
        return success
    
    async def send_input(self, terminal_id: str, data: str):
        """Send input to a terminal session"""
        session = self.sessions.get(terminal_id)
        if session:
            await session.send_input(data)
    
    async def resize_terminal(self, terminal_id: str, cols: int, rows: int):
        """Resize a terminal session"""
        session = self.sessions.get(terminal_id)
        if session:
            await session.resize(cols, rows)
    
    async def close_session(self, terminal_id: str):
        """Close a terminal session"""
        session = self.sessions.get(terminal_id)
        if session:
            await session.close()
            del self.sessions[terminal_id]
    
    async def close_all_sessions(self):
        """Close all terminal sessions"""
        for terminal_id in list(self.sessions.keys()):
            await self.close_session(terminal_id)

# Global manager instance
windows_terminal_manager = WindowsTerminalManager()
