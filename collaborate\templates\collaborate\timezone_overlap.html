{% extends 'base.html' %}

{% block content %}
<style>
  .timezone-card {
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 24px;
    border: none;
    background-color: #ffffff;
  }

  .timezone-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.12);
  }

  .timezone-card .card-header {
    border-radius: 12px 12px 0 0;
    font-weight: 600;
    padding: 14px 20px;
    background-color: #f1f4f9;
    color: #212529;
  }

  .timezone-card .card-body {
    padding: 20px;
    background-color: #fafbfc;
    color: black;
  }

  .timezone-chart {
    height: 300px;
    width: 100%;
    margin-bottom: 20px;
  }

  .overlap-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 4px;
    margin-bottom: 15px;
  }

  .overlap-table th, .overlap-table td {
    text-align: center;
    padding: 10px;
    border-radius: 6px;
    font-weight: 500;
  }

  .overlap-table th {
    background-color: #e9ecef;
    color: #343a40;
  }

  .overlap-high {
    background-color: #198754;
    color: #fff;
  }

  .overlap-medium {
    background-color: #ffc107;
    color: #212529;
  }

  .overlap-low {
    background-color: #fd7e14;
    color: #fff;
  }

  .overlap-none {
    background-color: #ced4da;
    color: #495057;
  }

  .user-timezone-badge {
    display: inline-block;
    padding: 6px 12px;
    margin: 4px 4px 8px;
    border-radius: 20px;
    background-color: #adb5bd;
    color: black;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease;
    font-size: 0.9rem;
  }

  .user-timezone-badge:hover {
    transform: scale(1.05);
  }

  .user-timezone-badge.available {
    background-color: #198754;
  }

  .user-timezone-badge.unavailable {
    background-color: #dc3545;
  }

  .time-block {
    display: inline-block;
    width: 26px;
    height: 26px;
    margin: 2px;
    border-radius: 4px;
    transition: transform 0.2s ease;
  }

  .time-block:hover {
    transform: scale(1.15);
  }

  .time-block.available {
    background-color: #28a745;
  }

  .time-block.unavailable {
    background-color: #dee2e6;
  }

  .time-block.overlap {
    background-color: #0d6efd;
  }

  .meeting-suggestion {
    border-left: 6px solid #0dcaf0;
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 0 8px 8px 0;
    background-color: #eaf7fb;
    transition: transform 0.2s ease;
  }

  .meeting-suggestion:hover {
    transform: translateX(5px);
  }

  .meeting-time {
    font-size: 1.25rem;
    font-weight: bold;
    color: #0dcaf0;
    margin-bottom: 8px;
  }

  .member-card {
    transition: transform 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
    height: 100%;
    background-color: #ffffff;
  }

  .member-card:hover {
    transform: translateY(-5px);
  }

  .member-card .card-title {
    font-weight: 600;
    color: #212529;
  }

  .current-time {
    font-weight: bold;
    color: #0d6efd;
  }

  .availability-info {
    padding: 10px 14px;
    border-radius: 6px;
    background-color: #f1f3f5;
    margin-top: 12px;
    font-size: 0.95rem;
  }

  .timezone-info {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }

  .timezone-info i {
    margin-right: 8px;
    color: #6c757d;
  }

  .alert-info {
    border-left: 6px solid #0dcaf0;
    background-color: #f0f8ff;
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 20px;
    color: #0c5460;
  }

 .btn {
  font-size: 0.95rem;
  border-radius: 8px;
  padding: 0.5rem 1rem;
}

.btn.btn-primary {
  background-color: #4e73df;
  border-color: #4e73df;
  color: #fff;
}

.btn.btn-danger {
  background-color: #e74a3b;
  border-color: #e74a3b;
  color: #fff;
}

.btn-outline-primary {
  border-color: #4e73df;
  color: #4e73df;
}

.btn-outline-primary:hover {
  background-color: #4e73df;
  color: #fff;
}

.btn-outline-info {
  border-color: #36b9cc;
  color: #36b9cc;
}

.btn-outline-info:hover {
  background-color: #36b9cc;
  color: #fff;
}

.btn-primary,
.btn-secondary{
  text-decoration: none;
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  align-self: center;
  text-align: center;
}
</style>


<div class="timezone">
  <div class="content-wrap">
    <div class="container mt-5">
  <h2>Timezone Analysis for "{{ project.title }}" Team</h2>
  <br>
  <div class="row mt-4">
    <!-- Current Time Map -->
    <div class="col-md-6">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Current Time for Team Members</h5>
        </div>
        <div class="card-body">
          <div id="worldTimeMap" class="timezone-chart"></div>
        </div>
      </div>
    </div>

    <!-- Availability Overview -->
    <div class="col-md-6">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Team Availability Overview</h5>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            This chart shows when team members are available based on their timezone and availability settings.
          </div>
          <div id="availabilityChart" class="timezone-chart"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Timezone Difference Matrix -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Timezone Difference Matrix</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="overlap-table">
              <thead>
                <tr>
                  <th></th>
                  {% for user in timezone_data.users %}
                    <th>{{ user.username }}</th>
                  {% endfor %}
                </tr>
              </thead>
              <tbody>
                {% for user in timezone_data.users %}
                  <tr>
                    <th>{{ user.username }}</th>
                    <!-- Using the new template-friendly data structure -->
                    {% for tz_row in timezone_data.tz_matrix_rows %}
                      {% if tz_row.username == user.username %}
                        {% for diff in tz_row.differences %}
                          <td class="{% if diff == 0 %}overlap-high{% elif diff <= 3 %}overlap-medium{% elif diff <= 6 %}overlap-low{% else %}overlap-none{% endif %}">
                            {{ diff }} hr{% if diff != 1 %}s{% endif %}
                          </td>
                        {% endfor %}
                      {% endif %}
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Availability Overlap Matrix -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Availability Overlap Matrix</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="overlap-table">
              <thead>
                <tr>
                  <th></th>
                  {% for user in timezone_data.users %}
                    <th>{{ user.username }}</th>
                  {% endfor %}
                </tr>
              </thead>
              <tbody>
                {% for user in timezone_data.users %}
                  <tr>
                    <th>{{ user.username }}</th>
                    <!-- Using the new template-friendly data structure -->
                    {% for avail_row in timezone_data.availability_matrix_rows %}
                      {% if avail_row.username == user.username %}
                        {% for overlap in avail_row.overlaps %}
                          <td class="{% if overlap >= 4 %}overlap-high{% elif overlap >= 2 %}overlap-medium{% elif overlap > 0 %}overlap-low{% else %}overlap-none{% endif %}">
                            {{ overlap|floatformat:1 }} hr{% if overlap != 1 %}s{% endif %}
                          </td>
                        {% endfor %}
                      {% endif %}
                    {% endfor %}
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Team Member Timezones -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-users me-2"></i> Team Member Timezones</h5>
        </div>
        <div class="card-body">
          <div class="row">
            {% for user in timezone_data.users %}
              <div class="col-md-4 mb-3">
                <div class="card member-card">
                  <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                      <i class="fas fa-user me-2"></i>{{ user.username }}
                      {% if user.current_hour >= 9 and user.current_hour < 17 %}
                        <span class="badge bg-success float-end" title="Working hours in their timezone">
                          <i class="fas fa-business-time me-1"></i> Working Hours
                        </span>
                      {% elif user.current_hour >= 22 or user.current_hour < 6 %}
                        <span class="badge bg-dark float-end" title="Likely sleeping in their timezone">
                          <i class="fas fa-moon me-1"></i> Night Time
                        </span>
                      {% else %}
                        <span class="badge bg-secondary float-end">
                          <i class="fas fa-home me-1"></i> Off Hours
                        </span>
                      {% endif %}
                    </h5>
                  </div>
                  <div class="card-body">
                    <div class="timezone-info">
                      <i class="fas fa-globe-americas"></i>
                      <strong>Timezone:</strong> <span class="ms-1">{{ user.timezone }}</span>
                    </div>

                    <div class="timezone-info">
                      <i class="far fa-clock"></i>
                      <strong>Current Time:</strong> <span class="ms-1 current-time">{{ user.current_time }}</span>
                    </div>

                    <div class="timezone-info">
                      <i class="far fa-calendar-alt"></i>
                      <strong>Availability:</strong>
                      <span class="ms-1">
                        {% if user.availability_start and user.availability_end %}
                          <span class="badge bg-success">
                            {{ user.availability_start }} - {{ user.availability_end }}
                          </span>
                        {% else %}
                          <span class="badge bg-warning">Not specified</span>
                        {% endif %}
                      </span>
                    </div>

                    {% if user.availability_start and user.availability_end %}
                      <div class="availability-info mt-2">
                        <div class="small text-muted mb-1">Daily Availability (Local Time):</div>
                        <div class="d-flex flex-wrap">
                          {% for hour in "012345678910111213141516171819202122232425"|make_list %}
                            {% if forloop.counter0 < 24 %}
                              {% with hour_int=forloop.counter0 %}
                                {% with start_hour=user.availability_start|slice:":2"|stringformat:"s"|add:"0"|slice:"-2:"|stringformat:"s" %}
                                  {% with end_hour=user.availability_end|slice:":2"|stringformat:"s"|add:"0"|slice:"-2:"|stringformat:"s" %}
                                    {% if start_hour <= end_hour %}
                                      <div class="time-block {% if hour_int >= start_hour|add:'0' and hour_int < end_hour|add:'0' %}available{% else %}unavailable{% endif %}"
                                           title="{{ hour_int }}:00">
                                      </div>
                                    {% else %}
                                      <div class="time-block {% if hour_int >= start_hour|add:'0' or hour_int < end_hour|add:'0' %}available{% else %}unavailable{% endif %}"
                                           title="{{ hour_int }}:00">
                                      </div>
                                    {% endif %}
                                  {% endwith %}
                                {% endwith %}
                              {% endwith %}
                            {% endif %}
                          {% endfor %}
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                          <span class="small text-muted">00:00</span>
                          <span class="small text-muted">12:00</span>
                          <span class="small text-muted">23:59</span>
                        </div>
                      </div>
                    {% endif %}
                  </div>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Suggested Meeting Times -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card timezone-card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i> Suggested Meeting Times</h5>
        </div>
        <div class="card-body">
          <div class="alert alert-info mb-4">
            <i class="fas fa-info-circle me-2"></i>
            <strong>How this works:</strong> We analyze the availability windows of all team members across different timezones and suggest optimal meeting times when most team members are available.
          </div>

          {% if meeting_suggestions.has_suggestions %}
            <div class="row">
              {% for suggestion in meeting_suggestions.suggestions %}
                <div class="col-md-4 mb-3">
                  <div class="card meeting-suggestion h-100">
                    <div class="card-body">
                      <div class="meeting-time">
                        <i class="far fa-clock me-2"></i>{{ suggestion.start_time }} - {{ suggestion.end_time }} UTC
                      </div>
                      <p class="mb-3">{{ suggestion.description }}</p>

                      <h6 class="mb-2"><i class="fas fa-user-check text-success me-2"></i>Available:</h6>
                      <div class="mb-3">
                        {% for user in suggestion.available_users %}
                          <span class="user-timezone-badge available">{{ user }}</span>
                        {% endfor %}
                      </div>

                      {% if suggestion.unavailable_users %}
                        <h6 class="mb-2"><i class="fas fa-user-times text-danger me-2"></i>Unavailable:</h6>
                        <div>
                          {% for user in suggestion.unavailable_users %}
                            <span class="user-timezone-badge unavailable">{{ user }}</span>
                          {% endfor %}
                        </div>
                      {% endif %}
                    </div>
                    <div class="card-footer bg-transparent">
                      <button class="btn btn-sm btn-outline-primary copy-time-btn"
                              data-time="{{ suggestion.start_time }} - {{ suggestion.end_time }} UTC"
                              onclick="copyToClipboard(this)">
                        <i class="far fa-copy me-1"></i> Copy Time
                      </button>

                      <a href="https://www.timeanddate.com/worldclock/converter.html?iso={{ suggestion.start_time|slice:":2" }}{{ suggestion.start_time|slice:"3:5" }}&p1=0"
                         target="_blank" class="btn btn-sm btn-outline-info ms-2">
                        <i class="fas fa-globe me-1"></i> Convert Time
                      </a>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="alert alert-warning">
              <i class="fas fa-exclamation-triangle me-2"></i>
              <strong>No common meeting times found!</strong>
              <p class="mb-0 mt-2">{{ meeting_suggestions.message }}</p>
              <div class="mt-3">
                <p>Suggestions to improve team availability:</p>
                <ul>
                  <li>Ask team members to update their availability settings</li>
                  <li>Consider splitting meetings for different subgroups</li>
                  <li>Use asynchronous communication tools when real-time meetings are difficult</li>
                </ul>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
  </div>


  <script>
    function copyToClipboard(button) {
      const text = button.getAttribute('data-time');
      navigator.clipboard.writeText(text).then(() => {
        // Change button text temporarily
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i> Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-primary');

        // Reset after 2 seconds
        setTimeout(() => {
          button.innerHTML = originalText;
          button.classList.remove('btn-success');
          button.classList.add('btn-outline-primary');
        }, 2000);
      });
    }
  </script>

  <div class="mt-4">
    <a href="{% url 'collaborate:project_detail' project.id %}" class="btn btn-secondary">
      Back to Project
    </a>
    <a href="{% url 'collaborate:team_balance' project.id %}" class="btn btn-primary">
      View Team Balance
    </a>
  </div>
</div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Define a consistent color palette for users
    const colorPalette = [
      '#4e73df', // Primary blue
      '#1cc88a', // Success green
      '#f6c23e', // Warning yellow
      '#e74a3b', // Danger red
      '#36b9cc', // Info teal
      '#6f42c1', // Purple
      '#fd7e14', // Orange
      '#20c9a6', // Teal
      '#5a5c69', // Gray
      '#858796', // Secondary gray
      '#5603ad', // Indigo
      '#8bc34a', // Light green
    ];

    // Availability Chart
    const availabilityCtx = document.getElementById('availabilityChart').getContext('2d');
    const availabilityChart = new Chart(availabilityCtx, {
      type: 'bar',
      data: {
        labels: Array.from({length: 24}, (_, i) => ${i}:00),
        datasets: [
          {% for user in timezone_data.users %}
          {
            label: '{{ user.username }}',
            data: generateAvailabilityData('{{ user.availability_start }}', '{{ user.availability_end }}', '{{ user.timezone }}'),
            backgroundColor: colorPalette[{{ forloop.counter0 }} % colorPalette.length],
            borderWidth: 1,
            borderRadius: 4,
            barPercentage: 0.8,
            categoryPercentage: 0.9
          }{% if not forloop.last %},{% endif %}
          {% endfor %}
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            max: 1.1, // Slightly higher to make room for labels
            grid: {
              display: false
            },
            ticks: {
              callback: function(value) {
                return value === 1 ? 'Available' : (value === 0 ? 'Unavailable' : '');
              },
              font: {
                weight: 'bold'
              }
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            },
            ticks: {
              maxRotation: 0,
              autoSkip: true,
              autoSkipPadding: 10,
              font: {
                size: 10
              }
            }
          }
        },
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              usePointStyle: true,
              padding: 20,
              boxWidth: 10
            }
          },
          tooltip: {
            callbacks: {
              title: function(tooltipItems) {
                return Time: ${tooltipItems[0].label};
              },
              label: function(context) {
                const value = context.raw;
                const username = context.dataset.label;
                return ${username}: ${value === 1 ? 'Available' : 'Unavailable'};
              }
            }
          }
        },
        animation: {
          duration: 1000,
          easing: 'easeOutQuart'
        }
      }
    });

    // Helper function to generate availability data
    function generateAvailabilityData(startTime, endTime, timezone) {
      const data = Array(24).fill(0);

      if (!startTime || !endTime) return data;

      // Parse hours and minutes
      const startParts = startTime.split(':');
      const endParts = endTime.split(':');

      const start = parseInt(startParts[0]);
      const end = parseInt(endParts[0]);

      // Add minute precision
      const startMinutes = parseInt(startParts[1] || 0);
      const endMinutes = parseInt(endParts[1] || 0);

      if (end > start || (end === start && endMinutes > startMinutes)) {
        // Normal time range (e.g., 9:00 - 17:00)
        for (let i = start; i < end; i++) {
          data[i] = 1;
        }
        // Handle the end hour based on minutes
        if (endMinutes > 0) {
          data[end] = 1;
        }
      } else {
        // Overnight time range (e.g., 22:00 - 6:00)
        for (let i = start; i < 24; i++) {
          data[i] = 1;
        }
        for (let i = 0; i < end; i++) {
          data[i] = 1;
        }
        // Handle the end hour based on minutes
        if (endMinutes > 0) {
          data[end] = 1;
        }
      }

      return data;
    }

    // Update current time every minute
    function updateCurrentTimes() {
      const timeElements = document.querySelectorAll('.current-time');
      const now = new Date();

      timeElements.forEach(el => {
        // Add a subtle animation
        el.classList.add('text-primary');
        setTimeout(() => {
          el.classList.remove('text-primary');
        }, 500);
      });
    }

    // Update times initially and then every minute
    updateCurrentTimes();
    setInterval(updateCurrentTimes, 60000);
  });
</script>
{% endblock %}