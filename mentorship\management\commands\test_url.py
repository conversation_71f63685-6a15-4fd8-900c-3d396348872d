from django.core.management.base import BaseCommand
from django.test import Client
from django.contrib.auth.models import User
from mentorship.models import MentorshipSession
from django.urls import reverse


class Command(BaseCommand):
    help = 'Test URL routing for chat history'

    def handle(self, *args, **options):
        # Get the first session
        session = MentorshipSession.objects.first()
        if not session:
            self.stdout.write(self.style.ERROR('No mentorship sessions found'))
            return

        # Get the first user
        user = User.objects.first()
        if not user:
            self.stdout.write(self.style.ERROR('No users found'))
            return

        # Create a test client
        client = Client()
        client.force_login(user)

        # Test different URL formats
        room_id = str(session.room_id)
        
        urls_to_test = [
            f'/mentorship/api/chat-history/{room_id}/',
            f'/mentorship/api/chat-history/{room_id}',
        ]
        
        for url in urls_to_test:
            self.stdout.write(f'Testing URL: {url}')
            response = client.get(url)
            self.stdout.write(f'Status: {response.status_code}')
            if response.status_code != 200:
                self.stdout.write(f'Content: {response.content.decode()[:200]}...')
            self.stdout.write('---')
            
        # Also test if we can access the session room
        session_url = f'/mentorship/session/{room_id}/'
        self.stdout.write(f'Testing session room URL: {session_url}')
        response = client.get(session_url)
        self.stdout.write(f'Session room status: {response.status_code}')
