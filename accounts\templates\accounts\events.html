{% extends 'base.html' %}
{% load static %}
{% block title %}Events - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Events Header -->
    <div class="dashboard-welcome">
      <h1>🎉 Events & Announcements</h1>
      <p class="welcome-subtitle">Stay updated with the latest happenings at Forge X</p>
    </div>

    <!-- Quick Stats -->
    <div class="dashboard-stats">
      <div class="stat-card">
        <div class="stat-icon">📅</div>
        <div class="stat-content">
          <h3>{{ upcoming_events|length }}</h3>
          <p>Upcoming Events</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-content">
          <h3>{{ past_events|length }}</h3>
          <p>Past Events</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <h3>{{ upcoming_events|length|add:past_events|length }}</h3>
          <p>Total Events</p>
        </div>
      </div>
    </div>

    <!-- Admin Actions -->
    {% if user.is_superuser %}
    <div class="quick-actions">
      <h2>Event Management</h2>
      <div class="action-grid">
        <a href="{% url 'accounts:create_event' %}" class="action-card">
          <div class="action-icon">➕</div>
          <h3>Create Event</h3>
          <p>Add a new event or announcement for the community</p>
        </a>
      </div>
    </div>
    {% endif %}

    <!-- Upcoming Events Section -->
    <div class="dashboard-section">
      <h2>📅 Upcoming Events</h2>
      {% if upcoming_events %}
        <div class="project-grid">
          {% for event in upcoming_events %}
            <div class="project-card">
              <div class="project-header">
                <h3>{{ event.title }}</h3>
                {% if user.is_superuser %}
                  <div class="btn-group">
                    <a href="{% url 'accounts:edit_event' event.id %}" class="btn btn-sm btn-secondary">
                      <i class="fas fa-edit"></i>
                    </a>
                    <a href="{% url 'accounts:delete_event' event.id %}" class="btn btn-sm btn-danger">
                      <i class="fas fa-trash"></i>
                    </a>
                  </div>
                {% endif %}
              </div>

              <div class="project-body">
                <div class="event-meta">
                  <p><i class="fas fa-clock"></i> {{ event.event_date|date:"F d, Y" }} at {{ event.event_date|date:"g:i A" }}</p>
                  {% if event.location %}
                    <p><i class="fas fa-map-marker-alt"></i> {{ event.location }}</p>
                  {% endif %}
                </div>
                <p>{{ event.description|truncatewords:20 }}</p>
              </div>

              <div class="project-actions">
                <small style="color: #b0b0b0;">
                  Created by {{ event.created_by.get_full_name|default:event.created_by.username }}
                </small>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="no-projects-message">
          <div class="no-projects-icon">📅</div>
          <h3>No Upcoming Events</h3>
          <p>There are no events scheduled at the moment. Check back later for updates!</p>
          {% if user.is_superuser %}
            <a href="{% url 'accounts:create_event' %}" class="btn btn-primary">Create First Event</a>
          {% endif %}
        </div>
      {% endif %}
    </div>

    <!-- Past Events Section -->
    {% if past_events %}
    <div class="dashboard-section">
      <h2>📚 Recent Past Events</h2>
      <div class="project-grid">
        {% for event in past_events %}
          <div class="project-card" style="opacity: 0.8; border-left: 3px solid #6c757d;">
            <div class="project-header">
              <h3>{{ event.title }}</h3>
              {% if user.is_superuser %}
                <div class="btn-group">
                  <a href="{% url 'accounts:edit_event' event.id %}" class="btn btn-sm btn-secondary">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{% url 'accounts:delete_event' event.id %}" class="btn btn-sm btn-danger">
                    <i class="fas fa-trash"></i>
                  </a>
                </div>
              {% endif %}
            </div>

            <div class="project-body">
              <div class="event-meta">
                <p><i class="fas fa-clock"></i> {{ event.event_date|date:"F d, Y" }} at {{ event.event_date|date:"g:i A" }}</p>
                {% if event.location %}
                  <p><i class="fas fa-map-marker-alt"></i> {{ event.location }}</p>
                {% endif %}
              </div>
              <p>{{ event.description|truncatewords:20 }}</p>
            </div>

            <div class="project-actions">
              <small style="color: #b0b0b0;">
                Created by {{ event.created_by.get_full_name|default:event.created_by.username }}
              </small>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .project-card, .stat-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
  });
</script>

<style>
/* Event-specific styling */
.event-meta p {
  margin-bottom: 8px;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.event-meta i {
  color: var(--color-border);
  margin-right: 8px;
  width: 16px;
}

/* Dashboard specific styles for events page */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  color: #b0b0b0;
  font-size: 1.1rem;
  margin: 0;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-content h3 {
  font-size: 2rem;
  color: var(--color-border);
  margin-bottom: 5px;
}

.stat-content p {
  color: #b0b0b0;
  margin: 0;
}

.dashboard-section {
  margin-bottom: 40px;
}

.dashboard-section h2 {
  color: #ffffff;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.quick-actions {
  margin-bottom: 40px;
}

.quick-actions h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
  text-decoration: none;
  color: inherit;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--color-border);
}

.action-card h3 {
  color: var(--color-border);
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.action-card p {
  color: #b0b0b0;
  margin: 0;
  font-size: 0.9rem;
}

.no-projects-message {
  text-align: center;
  padding: 60px 20px;
  color: #b0b0b0;
}

.no-projects-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--color-border);
}

.no-projects-message h3 {
  color: #ffffff;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.no-projects-message p {
  margin-bottom: 25px;
  font-size: 1.1rem;
}

/* Button styling for events page */
.btn-sm {
  padding: 8px 12px;
  font-size: 0.8rem;
}

.btn-group {
  display: flex;
  gap: 8px;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #2196F3;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.5);
  color: #FFC107;
}

.alert-error {
  background-color: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

{% endblock %}
