{% extends 'base.html' %}
{% load static %}

{% block title %}Forms - Forge X{% endblock %}

{% block content %}
<style>
/* Forms Page Specific Styles */
.forms-container {
  padding: 40px 0;
  min-height: 80vh;
}

.forms-hero {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.forms-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.forms-hero p {
  font-size: 1.2rem;
  color: #d5d5d5;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Forms Grid */
.forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.form-category {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.form-category:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.4);
  box-shadow: 0 15px 30px rgba(192, 255, 107, 0.1);
}

.form-category h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-category .icon {
  font-size: 1.5rem;
}

.form-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-item {
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.form-item:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.3);
}

.form-link {
  text-decoration: none;
  color: #ffffff;
  display: block;
}

.form-link:hover {
  color: #C0ff6b;
  text-decoration: none;
}

.form-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: #C0ff6b;
}

.form-description {
  font-size: 0.9rem;
  color: #d5d5d5;
  margin: 0;
  line-height: 1.4;
}

.form-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-top: 8px;
}

.status-available {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-coming-soon {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-admin-only {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Quick Access Section */
.quick-access {
  background: linear-gradient(135deg, rgba(28,28,28,0.9) 0%, rgba(45,45,45,0.9) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.quick-access h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.quick-access-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.quick-access-btn {
  background: rgba(192, 255, 107, 0.1);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  padding: 15px 20px;
  text-decoration: none;
  color: #ffffff;
  text-align: center;
  transition: all 0.3s ease;
  display: block;
}

.quick-access-btn:hover {
  background: rgba(192, 255, 107, 0.2);
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(192, 255, 107, 0.1);
  color: #ffffff;
  text-decoration: none;
}

.quick-access-btn i {
  display: block;
  font-size: 1.5rem;
  margin-bottom: 8px;
  color: #C0ff6b;
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Particles Container */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .forms-hero h1 {
    font-size: 2.5rem;
  }

  .forms-hero p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .forms-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .form-category {
    padding: 20px;
    margin: 0 10px;
  }

  .quick-access {
    padding: 20px;
    margin: 0 10px 20px 10px;
  }

  .quick-access-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .forms-hero h1 {
    font-size: 2rem;
  }

  .form-category {
    padding: 15px;
  }

  .quick-access {
    padding: 15px;
  }
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<div class="forms-container">
  <div class="tile-wrap">
    <!-- Hero Section -->
    <div class="forms-hero fade-in">
      <h1>Forms Hub</h1>
      <p>Access all forms and applications available on the Forge X platform. From project creation to mentorship applications, find everything you need in one place.</p>
    </div>

    <!-- Quick Access Section -->
    <div class="quick-access fade-in" data-delay="100">
      <h2>🚀 Quick Access</h2>
      <div class="quick-access-grid">
        {% if user.is_authenticated %}
          <a href="{% url 'collaborate:create_project' %}" class="quick-access-btn">
            <i class="fas fa-plus-circle"></i>
            Create Project
          </a>
          <a href="{% url 'mentorship:marketplace' %}" class="quick-access-btn">
            <i class="fas fa-user-graduate"></i>
            Find Mentor
          </a>
          <a href="{% url 'accounts:profile' %}" class="quick-access-btn">
            <i class="fas fa-user-edit"></i>
            Edit Profile
          </a>
          <a href="{% url 'accounts:contact' %}" class="quick-access-btn">
            <i class="fas fa-envelope"></i>
            Contact Us
          </a>
        {% else %}
          <a href="{% url 'accounts:signup' %}" class="quick-access-btn">
            <i class="fas fa-user-plus"></i>
            Sign Up
          </a>
          <a href="{% url 'accounts:login' %}" class="quick-access-btn">
            <i class="fas fa-sign-in-alt"></i>
            Login
          </a>
          <a href="{% url 'accounts:contact' %}" class="quick-access-btn">
            <i class="fas fa-envelope"></i>
            Contact Us
          </a>
        {% endif %}
      </div>
    </div>

    <!-- Forms Categories -->
    <div class="forms-grid">
      <!-- Account & Profile Forms -->
      <div class="form-category fade-in" data-delay="200">
        <h2><span class="icon">👤</span> Account & Profile</h2>
        <ul class="form-list">
          {% if not user.is_authenticated %}
          <li class="form-item">
            <a href="{% url 'accounts:signup' %}" class="form-link">
              <div class="form-title">User Registration</div>
              <div class="form-description">Create a new account to join the Forge X community</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          <li class="form-item">
            <a href="{% url 'accounts:login' %}" class="form-link">
              <div class="form-title">User Login</div>
              <div class="form-description">Sign in to your existing account</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          {% else %}
          <li class="form-item">
            <a href="{% url 'accounts:profile' %}" class="form-link">
              <div class="form-title">Profile Management</div>
              <div class="form-description">Update your profile information, skills, and preferences</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          <li class="form-item">
            <a href="{% url 'accounts:profile_setup' %}" class="form-link">
              <div class="form-title">Profile Setup</div>
              <div class="form-description">Complete your profile with CV upload and skill assessment</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          {% endif %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Password Reset</div>
              <div class="form-description">Reset your password if you've forgotten it</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Collaboration Forms -->
      <div class="form-category fade-in" data-delay="300">
        <h2><span class="icon">🤝</span> Collaboration</h2>
        <ul class="form-list">
          {% if user.is_authenticated %}
          <li class="form-item">
            <a href="{% url 'collaborate:create_project' %}" class="form-link">
              <div class="form-title">Create Project</div>
              <div class="form-description">Start a new collaborative project and find team members</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Join Project Application</div>
              <div class="form-description">Apply to join existing projects that match your skills</div>
              <span class="form-status status-available">Available</span>
            </div>
          </li>
          {% else %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Create Project</div>
              <div class="form-description">Start a new collaborative project (requires login)</div>
              <span class="form-status status-available">Login Required</span>
            </div>
          </li>
          {% endif %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Team Feedback</div>
              <div class="form-description">Provide feedback on team collaboration and project outcomes</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Project Report</div>
              <div class="form-description">Report issues or concerns about projects or team members</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Learning Forms -->
      <div class="form-category fade-in" data-delay="400">
        <h2><span class="icon">📚</span> Learning</h2>
        <ul class="form-list">
          {% if user.is_superuser %}
          <li class="form-item">
            <a href="{% url 'learn:create_course' %}" class="form-link">
              <div class="form-title">Create Course</div>
              <div class="form-description">Add new courses to the learning platform</div>
              <span class="form-status status-admin-only">Admin Only</span>
            </a>
          </li>
          {% endif %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Course Feedback</div>
              <div class="form-description">Rate and review courses you've completed</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Learning Path Request</div>
              <div class="form-description">Request specific courses or learning paths</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Skill Assessment</div>
              <div class="form-description">Take assessments to validate your technical skills</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Mentorship Forms -->
      <div class="form-category fade-in" data-delay="500">
        <h2><span class="icon">👨‍🏫</span> Mentorship</h2>
        <ul class="form-list">
          {% if user.is_authenticated %}
          <li class="form-item">
            <a href="{% url 'mentorship:marketplace' %}" class="form-link">
              <div class="form-title">Find Mentor</div>
              <div class="form-description">Browse and book sessions with experienced mentors</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Become a Mentor</div>
              <div class="form-description">Apply to become a mentor and share your expertise</div>
              <span class="form-status status-available">Available</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Session Booking</div>
              <div class="form-description">Book one-on-one mentoring sessions</div>
              <span class="form-status status-available">Available</span>
            </div>
          </li>
          {% else %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Mentorship Access</div>
              <div class="form-description">Access mentorship features (requires login)</div>
              <span class="form-status status-available">Login Required</span>
            </div>
          </li>
          {% endif %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Session Feedback</div>
              <div class="form-description">Rate and review your mentoring sessions</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Support & Admin Forms -->
      <div class="form-category fade-in" data-delay="600">
        <h2><span class="icon">🛠️</span> Support & Admin</h2>
        <ul class="form-list">
          <li class="form-item">
            <a href="{% url 'accounts:contact' %}" class="form-link">
              <div class="form-title">Contact Us</div>
              <div class="form-description">Get in touch with our support team</div>
              <span class="form-status status-available">Available</span>
            </a>
          </li>
          {% if user.is_superuser %}
          <li class="form-item">
            <a href="{% url 'accounts:create_event' %}" class="form-link">
              <div class="form-title">Create Event</div>
              <div class="form-description">Create platform events and announcements</div>
              <span class="form-status status-admin-only">Admin Only</span>
            </a>
          </li>
          {% endif %}
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Bug Report</div>
              <div class="form-description">Report bugs or technical issues</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Feature Request</div>
              <div class="form-description">Suggest new features for the platform</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Newsletter Subscription</div>
              <div class="form-description">Subscribe to platform updates and news</div>
              <span class="form-status status-available">Available in Footer</span>
            </div>
          </li>
        </ul>
      </div>

      <!-- Community Forms -->
      <div class="form-category fade-in" data-delay="700">
        <h2><span class="icon">🌟</span> Community</h2>
        <ul class="form-list">
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Community Guidelines</div>
              <div class="form-description">Report violations of community guidelines</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Forum Post</div>
              <div class="form-description">Create posts in community forums</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Event Registration</div>
              <div class="form-description">Register for platform events and workshops</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
          <li class="form-item">
            <div class="form-link">
              <div class="form-title">Success Story</div>
              <div class="form-description">Share your success stories with the community</div>
              <span class="form-status status-coming-soon">Coming Soon</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for animations and particles -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 60,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": "#C0ff6b"
        },
        "shape": {
          "type": "circle",
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.4,
          "random": false,
          "anim": {
            "enable": false,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": false,
            "speed": 40,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.3,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 4,
          "direction": "none",
          "random": false,
          "straight": false,
          "out_mode": "out",
          "bounce": false,
          "attract": {
            "enable": false,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "repulse"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 400,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 400,
            "size": 40,
            "duration": 2,
            "opacity": 8,
            "speed": 3
          },
          "repulse": {
            "distance": 150,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
    });

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        const delay = parseInt(element.getAttribute('data-delay')) || 0;

        if (elementTop < windowHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('visible');
          }, delay);
        }
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up scroll event listener
    window.addEventListener('scroll', revealOnScroll);

    // Add hover effects for form items
    const formItems = document.querySelectorAll('.form-item');
    formItems.forEach(item => {
      item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
      });

      item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
      });
    });

    // Add click tracking for analytics (optional)
    const formLinks = document.querySelectorAll('.form-link[href]');
    formLinks.forEach(link => {
      link.addEventListener('click', function() {
        const formTitle = this.querySelector('.form-title').textContent;
        console.log('Form accessed:', formTitle);
        // You can add analytics tracking here
      });
    });
  });
</script>
{% endblock %}
