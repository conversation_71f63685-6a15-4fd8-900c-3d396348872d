"""
Utility functions for the collaborate app.
"""
import random
from typing import List, Dict, Set, Any, Tu<PERSON>, Optional

def calculate_skill_match(user_skills, project_required_skills):
    """
    Calculate how well a user's skills match with project required skills.
    
    Args:
        user_skills (list): List of user's skills
        project_required_skills (list): List of required skills for the project
        
    Returns:
        dict: Dictionary containing match statistics:
            - match_count: Number of matching skills
            - match_score: Match percentage (0.0 - 1.0)
            - overlapping_skills: List of skills user already has
            - missing_skills: List of skills user doesn't have
    
    Example:
        >>> user_skills = ["python", "django", "rest"]
        >>> project_required_skills = ["python", "django", "react", "aws"]
        >>> calculate_skill_match(user_skills, project_required_skills)
        {
            "match_count": 2,
            "match_score": 0.5,
            "overlapping_skills": ["python", "django"],
            "missing_skills": ["react", "aws"]
        }
    """
    # Handle empty lists
    if not user_skills:
        user_skills = []
    if not project_required_skills:
        project_required_skills = []
    
    # Normalize skills (lowercase) and remove duplicates
    user_skills_set = {skill.lower() for skill in user_skills}
    project_skills_set = {skill.lower() for skill in project_required_skills}
    
    # Calculate overlapping and missing skills
    overlapping_skills = user_skills_set.intersection(project_skills_set)
    missing_skills = project_skills_set - user_skills_set
    
    # Calculate match statistics
    match_count = len(overlapping_skills)
    
    # Calculate match score (avoid division by zero)
    if len(project_skills_set) > 0:
        match_score = match_count / len(project_skills_set)
    else:
        match_score = 1.0  # If no skills required, it's a perfect match
    
    # Return the result dictionary
    return {
        "match_count": match_count,
        "match_score": match_score,
        "overlapping_skills": list(overlapping_skills),
        "missing_skills": list(missing_skills)
    }

def assemble_best_team(users_queryset, project_required_skills, team_size):
    """
    Assemble the best team from a pool of users based on skill coverage.
    
    Args:
        users_queryset: QuerySet of User or UserProfile objects
        project_required_skills (list): List of required skills for the project
        team_size (int): Maximum number of team members to select (1-6)
        
    Returns:
        List of selected user objects sorted by usefulness
        
    This function tries to optimize for maximum skill coverage, avoiding duplication
    and prioritizing users who contribute the most to the required skill set.
    """
    if not project_required_skills:
        # If no skills required, return random selection
        users_list = list(users_queryset)
        random.shuffle(users_list)
        return users_list[:team_size]
    
    # Normalize required skills (lowercase)
    required_skills = {skill.lower() for skill in project_required_skills}
    
    # Track best team and remaining skills
    selected_users = []
    remaining_skills = set(required_skills)
    
    # Process each user
    user_scores = []
    for user in users_queryset:
        # Extract user skills based on available attributes
        user_skills = []
        if hasattr(user, 'skills'):
            user_skills = user.skills
        elif hasattr(user, 'profile') and hasattr(user.profile, 'skills'):
            user_skills = user.profile.skills
        elif hasattr(user, 'userprofile') and hasattr(user.userprofile, 'skills'):
            user_skills = user.userprofile.skills
            
        # Get skill match details
        match_details = calculate_skill_match(user_skills, project_required_skills)
        
        # Calculate bonus skills (skills beyond required)
        user_skills_set = {skill.lower() for skill in user_skills}
        bonus_skills = user_skills_set - required_skills
        
        # Store user with score info
        user_scores.append({
            'user': user,
            'match_count': match_details['match_count'],
            'match_score': match_details['match_score'],
            'overlapping_skills': match_details['overlapping_skills'],
            'bonus_skills': list(bonus_skills),
            'bonus_count': len(bonus_skills),
            'availability_score': _get_availability_score(user)  # Helper for tiebreaking
        })
    
    # Sort users by: 1) Match count (desc), 2) Bonus skills (desc), 3) Availability (desc)
    user_scores.sort(key=lambda x: (
        x['match_count'],
        x['bonus_count'],
        x['availability_score']
    ), reverse=True)
    
    # Greedy algorithm to select users with maximum contribution
    while len(selected_users) < team_size and remaining_skills and user_scores:
        # Find the user who contributes the most to remaining skills
        best_user = None
        best_contribution = 0
        best_user_index = -1
        
        for i, user_data in enumerate(user_scores):
            # Skip users who don't contribute any remaining skills
            user_overlapping = set(user_data['overlapping_skills'])
            contribution = len(user_overlapping.intersection(remaining_skills))
            
            if contribution > best_contribution:
                best_contribution = contribution
                best_user = user_data
                best_user_index = i
        
        # If no user contributes remaining skills, select based on match score
        if best_user is None:
            if not selected_users:  # If team is empty, take highest scoring user
                best_user = user_scores[0]
                best_user_index = 0
            else:
                break  # No more useful users to add
        
        # Add the best user to the team
        selected_users.append(best_user['user'])
        
        # Remove the skills this user covers from remaining skills
        remaining_skills -= set(best_user['overlapping_skills'])
        
        # Remove the user from consideration
        user_scores.pop(best_user_index)
    
    # If we haven't filled the team and still have candidates
    # Add remaining users based on original sorting
    remaining_slots = team_size - len(selected_users)
    if remaining_slots > 0 and user_scores:
        # Add remaining top users until team is full
        for i in range(min(remaining_slots, len(user_scores))):
            selected_users.append(user_scores[i]['user'])
    
    return selected_users

def _get_availability_score(user) -> float:
    """
    Helper function to calculate an availability score for a user.
    Higher score means better availability.
    
    Returns a float between 0 and 1
    """
    # Default score if availability can't be determined
    default_score = 0.5
    
    # Try to get availability from user profile
    try:
        if hasattr(user, 'availability'):
            # Direct availability field
            availability = user.availability
            if isinstance(availability, (int, float)):
                return min(1.0, max(0.0, float(availability)))
        
        elif hasattr(user, 'profile') and hasattr(user.profile, 'availability'):
            # Availability on profile object
            availability = user.profile.availability
            if isinstance(availability, (int, float)):
                return min(1.0, max(0.0, float(availability)))
        
        elif hasattr(user, 'userprofile') and hasattr(user.userprofile, 'availability'):
            # Availability on userprofile object
            availability = user.userprofile.availability
            if isinstance(availability, (int, float)):
                return min(1.0, max(0.0, float(availability)))
            
            # Check if it's a more complex availability model
            # Try availability_hours if it exists
            if hasattr(user.userprofile, 'availability_hours'):
                hours = user.userprofile.availability_hours
                if isinstance(hours, (int, float)):
                    # Normalize hours to 0-1 range (assuming 40 hours is full availability)
                    return min(1.0, max(0.0, float(hours) / 40.0))
    except:
        # If any error occurs, use default
        pass
        
    return default_score

# Example usage
if __name__ == "__main__":
    # Example 1
    user_skills = ["python", "django", "rest"]
    project_required_skills = ["python", "django", "react", "aws"]
    result = calculate_skill_match(user_skills, project_required_skills)
    print("Example 1:")
    print(result)
    
    # Example 2: Case insensitivity
    user_skills = ["Python", "DJANGO", "rest"]
    project_required_skills = ["python", "django", "react", "AWS"]
    result = calculate_skill_match(user_skills, project_required_skills)
    print("\nExample 2 (case insensitivity):")
    print(result)
    
    # Example 3: Empty lists
    user_skills = []
    project_required_skills = ["python", "django"]
    result = calculate_skill_match(user_skills, project_required_skills)
    print("\nExample 3 (empty user skills):")
    print(result)
    
    # Example 4: Perfect match
    user_skills = ["python", "django", "aws", "react", "extra skill"]
    project_required_skills = ["python", "django", "react", "aws"]
    result = calculate_skill_match(user_skills, project_required_skills)
    print("\nExample 4 (perfect match):")
    print(result)
    
    # Example 5: No skills required
    user_skills = ["python", "django"]
    project_required_skills = []
    result = calculate_skill_match(user_skills, project_required_skills)
    print("\nExample 5 (no skills required):")
    print(result)