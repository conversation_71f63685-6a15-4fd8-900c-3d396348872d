import json
import stripe
from decimal import Decimal
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse, HttpResponseForbidden
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.utils import timezone
from .models import MentorshipSession

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', '')


@login_required
def payment_page(request, session_id):
    """Display payment page for a mentorship session"""

    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user,
        status='scheduled'
    )

    # Check if payment is already completed
    if session.is_paid:
        messages.info(request, 'Payment for this session has already been completed.')
        return redirect('mentorship:my_sessions')

    # Check if payment is required
    if not session.is_payment_required():
        # Free session, mark as paid and redirect
        session.is_paid = True
        session.payment_status = 'succeeded'
        session.save()
        messages.success(request, 'Session booked successfully!')
        return redirect('mentorship:my_sessions')

    context = {
        'session': session,
        'stripe_public_key': getattr(settings, 'STRIPE_PUBLIC_KEY', ''),
        'amount_cents': int(session.total_amount * 100),  # Convert to cents
    }

    return render(request, 'mentorship/payment.html', context)


@login_required
@require_http_methods(["POST"])
def create_payment_intent(request, session_id):
    """Create a Stripe Payment Intent for the session"""

    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user,
        status='scheduled'
    )

    # Check if payment is already completed
    if session.is_paid:
        return JsonResponse({'error': 'Payment already completed'}, status=400)

    try:
        # Create Payment Intent
        intent = stripe.PaymentIntent.create(
            amount=int(session.total_amount * 100),  # Amount in cents
            currency='usd',
            metadata={
                'session_id': session.id,
                'learner_id': request.user.id,
                'mentor_id': session.mentor.id,
            },
            description=f'Mentorship Session with {session.mentor.get_full_name() or session.mentor.username}',
        )

        # Update session with payment intent ID
        session.payment_intent_id = intent.id
        session.payment_status = 'processing'
        session.save()

        return JsonResponse({
            'client_secret': intent.client_secret,
            'payment_intent_id': intent.id
        })

    except stripe.error.StripeError as e:
        return JsonResponse({'error': str(e)}, status=400)
    except Exception as e:
        return JsonResponse({'error': 'An unexpected error occurred'}, status=500)


@login_required
@require_http_methods(["POST"])
def confirm_payment(request, session_id):
    """Confirm payment completion and update session status"""

    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user,
        status='scheduled'
    )

    try:
        data = json.loads(request.body)
        payment_intent_id = data.get('payment_intent_id')

        if not payment_intent_id or payment_intent_id != session.payment_intent_id:
            return JsonResponse({'error': 'Invalid payment intent'}, status=400)

        # Retrieve payment intent from Stripe
        intent = stripe.PaymentIntent.retrieve(payment_intent_id)

        if intent.status == 'succeeded':
            # Update session payment status
            session.is_paid = True
            session.payment_status = 'succeeded'
            session.payment_method_id = intent.payment_method
            session.save()

            return JsonResponse({
                'success': True,
                'redirect_url': reverse('mentorship:payment_success', args=[session.id])
            })
        else:
            session.payment_status = 'failed'
            session.save()
            return JsonResponse({'error': 'Payment not completed'}, status=400)

    except stripe.error.StripeError as e:
        return JsonResponse({'error': str(e)}, status=400)
    except Exception as e:
        return JsonResponse({'error': 'An unexpected error occurred'}, status=500)


@login_required
def payment_success(request, session_id):
    """Display payment success page"""

    # Get the session and check payment status
    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user
    )

    # Check if we need to verify payment with Stripe
    payment_intent_id = request.GET.get('payment_intent')
    if payment_intent_id and not session.is_paid:
        try:
            # Verify payment with Stripe
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            if intent.status == 'succeeded':
                session.is_paid = True
                session.payment_status = 'succeeded'
                session.payment_intent_id = payment_intent_id
                session.payment_method_id = intent.payment_method
                session.save()

                # Update mentor earnings
                if hasattr(session.mentor, 'mentor_profile'):
                    mentor_profile = session.mentor.mentor_profile
                    mentor_profile.total_earnings += session.mentor_earnings
                    mentor_profile.save()
        except stripe.error.StripeError as e:
            messages.error(request, f'Payment verification failed: {str(e)}')
            return redirect('mentorship:payment_failed', session_id=session.id)

    context = {
        'session': session,
    }

    return render(request, 'mentorship/payment_success.html', context)


@login_required
def payment_failed(request, session_id):
    """Display payment failed page"""

    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user
    )

    context = {
        'session': session,
    }

    return render(request, 'mentorship/payment_failed.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def stripe_webhook(request):
    """Handle Stripe webhooks"""

    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
    endpoint_secret = getattr(settings, 'STRIPE_WEBHOOK_SECRET', '')

    # Log webhook attempt
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Webhook received: {request.META.get('CONTENT_TYPE', 'unknown')}")

    if not endpoint_secret:
        logger.warning("STRIPE_WEBHOOK_SECRET not configured")
        return HttpResponse("Webhook secret not configured", status=400)

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, endpoint_secret
        )
        logger.info(f"Webhook event verified: {event['type']}")
    except ValueError as e:
        logger.error(f"Invalid payload: {str(e)}")
        return HttpResponse("Invalid payload", status=400)
    except stripe.error.SignatureVerificationError as e:
        logger.error(f"Invalid signature: {str(e)}")
        return HttpResponse("Invalid signature", status=400)

    # Handle the event
    try:
        if event['type'] == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            handle_payment_success(payment_intent)
            logger.info(f"Payment succeeded: {payment_intent['id']}")
        elif event['type'] == 'payment_intent.payment_failed':
            payment_intent = event['data']['object']
            handle_payment_failure(payment_intent)
            logger.info(f"Payment failed: {payment_intent['id']}")
        elif event['type'] == 'payment_intent.canceled':
            payment_intent = event['data']['object']
            handle_payment_canceled(payment_intent)
            logger.info(f"Payment canceled: {payment_intent['id']}")
        else:
            logger.info(f"Unhandled event type: {event['type']}")
    except Exception as e:
        logger.error(f"Error handling webhook: {str(e)}")
        return HttpResponse("Error processing webhook", status=500)

    return HttpResponse("Webhook processed successfully", status=200)


def handle_payment_success(payment_intent):
    """Handle successful payment"""
    try:
        session_id = payment_intent['metadata'].get('session_id')
        if session_id:
            session = MentorshipSession.objects.get(
                id=session_id,
                payment_intent_id=payment_intent['id']
            )
            session.is_paid = True
            session.payment_status = 'succeeded'
            session.payment_method_id = payment_intent.get('payment_method')
            session.save()

            # Update mentor's total earnings
            mentor_profile = session.mentor.mentor_profile
            mentor_profile.total_earnings += session.mentor_earnings
            mentor_profile.save()

    except MentorshipSession.DoesNotExist:
        pass  # Log this error in production


def handle_payment_failure(payment_intent):
    """Handle failed payment"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        session_id = payment_intent['metadata'].get('session_id')
        if session_id:
            session = MentorshipSession.objects.get(
                id=session_id,
                payment_intent_id=payment_intent['id']
            )
            session.payment_status = 'failed'
            session.save()
            logger.info(f"Session {session_id} marked as payment failed")

    except MentorshipSession.DoesNotExist:
        logger.error(f"Session not found for payment intent {payment_intent['id']}")
    except Exception as e:
        logger.error(f"Error handling payment failure: {str(e)}")


def handle_payment_canceled(payment_intent):
    """Handle canceled payment"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        session_id = payment_intent['metadata'].get('session_id')
        if session_id:
            session = MentorshipSession.objects.get(
                id=session_id,
                payment_intent_id=payment_intent['id']
            )
            session.payment_status = 'canceled'
            session.save()
            logger.info(f"Session {session_id} marked as payment canceled")

    except MentorshipSession.DoesNotExist:
        logger.error(f"Session not found for payment intent {payment_intent['id']}")
    except Exception as e:
        logger.error(f"Error handling payment cancellation: {str(e)}")
