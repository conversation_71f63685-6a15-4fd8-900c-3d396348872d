import os
import asyncio
import threading
import platform
import json
from fastapi import WebSocket, WebSocketDisconnect, APIRouter
import logging

# Import platform-specific modules conditionally
if platform.system() != 'Windows':
    import pty
    import select
else:
    from .windows_terminal import windows_terminal_manager

logger = logging.getLogger("terminal_ws")
sessions = {}  # Tracks active terminal sessions

router = APIRouter()

@router.websocket("/ws/terminal/{project_hash}/{terminal_id}/")
async def websocket_endpoint(websocket: WebSocket, project_hash: str, terminal_id: str):
    await websocket.accept()
    try:
        if platform.system() == 'Windows':
            await handle_windows_terminal(websocket, project_hash, terminal_id)
        else:
            await handle_unix_terminal(websocket, project_hash, terminal_id)
    except Exception as e:
        logger.error(f"WebSocket error: {str(e)}")
    finally:
        await websocket.close()

async def handle_windows_terminal(websocket: WebSocket, project_hash: str, terminal_id: str):
    """
    Handles a terminal session over WebSocket on Windows using the improved Windows terminal manager.
    Each terminal gets its own isolated session.
    """
    try:
        # Create terminal session using the Windows terminal manager
        success = await windows_terminal_manager.create_session(terminal_id, project_hash, websocket)

        if not success:
            await websocket.send_text(json.dumps({
                "type": "output",
                "data": "Failed to create Windows terminal session\r\n"
            }))
            return

        # Store session info for cleanup
        sessions[terminal_id] = {
            "active": True,
            "project_hash": project_hash,
            "platform": "windows"
        }

        try:
            # Main loop to receive input from the WebSocket
            while True:
                try:
                    message = await websocket.receive_text()
                    data = json.loads(message)

                    if data.get("type") == "input":
                        input_data = data.get("data", "")
                        await windows_terminal_manager.send_input(terminal_id, input_data)
                    elif data.get("type") == "resize":
                        # Handle terminal resize
                        cols = data.get("data", {}).get("cols", 80)
                        rows = data.get("data", {}).get("rows", 24)
                        await windows_terminal_manager.resize_terminal(terminal_id, cols, rows)

                except WebSocketDisconnect:
                    break
                except json.JSONDecodeError:
                    # Handle raw text input for backward compatibility
                    await windows_terminal_manager.send_input(terminal_id, message)
                except Exception as e:
                    logger.error(f"Error in Windows terminal loop: {str(e)}")
                    break
        finally:
            # Clean up
            sessions[terminal_id]["active"] = False
            await windows_terminal_manager.close_session(terminal_id)
            logger.info(f"Windows Terminal {terminal_id} closed and cleaned up.")

    except Exception as e:
        logger.exception(f"Error handling Windows terminal {terminal_id}: {str(e)}")

async def handle_unix_terminal(websocket: WebSocket, project_hash: str, terminal_id: str):
    """
    Handles a terminal session over WebSocket using PTY and subprocess.
    Each terminal gets its own isolated session connected to the project directory.
    """
    # Determine the project directory path
    project_dir = f"/app/projects/{project_hash}"

    # Ensure the project directory exists
    if not os.path.exists(project_dir):
        os.makedirs(project_dir, exist_ok=True)
        logger.info(f"Created project directory: {project_dir}")

    # Open a new pseudo-terminal
    master_fd, slave_fd = pty.openpty()

    try:
        # Fork a new process
        pid = os.fork()
        if pid == 0:
            # Child process
            os.close(master_fd)
            os.setsid()

            # Change to the project directory BEFORE setting up the terminal
            try:
                os.chdir(project_dir)
                logger.info(f"Terminal {terminal_id} working directory set to: {project_dir}")
            except Exception as e:
                logger.error(f"Failed to change to project directory {project_dir}: {str(e)}")
                # Fallback to /app if project directory doesn't work
                os.chdir("/app")

            # Set up environment variables for better terminal experience
            os.environ["TERM"] = "xterm-256color"
            os.environ["PS1"] = f"\\[\\033[01;32m\\]user@forgex\\[\\033[00m\\]:\\[\\033[01;34m\\]\\w\\[\\033[00m\\]$ "

            os.dup2(slave_fd, 0)  # stdin
            os.dup2(slave_fd, 1)  # stdout
            os.dup2(slave_fd, 2)  # stderr
            os.execvp("/bin/bash", ["/bin/bash", "-i"])
        else:
            # Parent process
            os.close(slave_fd)

            # Initialize session with project directory info
            sessions[terminal_id] = {
                "active": True,
                "pid": pid,
                "project_hash": project_hash,
                "project_dir": project_dir,
                "created_at": asyncio.get_event_loop().time()
            }

            # Function to read from the PTY and send to WebSocket
            def read_terminal():
                while sessions.get(terminal_id, {}).get("active", False):
                    try:
                        r, _, _ = select.select([master_fd], [], [], 0.1)
                        if r:
                            data = os.read(master_fd, 1024)
                            if data:
                                asyncio.run_coroutine_threadsafe(
                                    websocket.send_text(json.dumps({
                                        "type": "output",
                                        "data": data.decode("utf-8", errors="replace")
                                    })),
                                    asyncio.get_event_loop(),
                                )
                    except Exception as e:
                        logger.error(f"Error reading from terminal: {str(e)}")
                        break

            # Start the reader thread
            reader_thread = threading.Thread(target=read_terminal, daemon=True)
            reader_thread.start()

            try:
                # Main loop to receive input from the WebSocket
                while True:
                    try:
                        message = await websocket.receive_text()
                        try:
                            data = json.loads(message)
                            if data.get("type") == "input":
                                input_data = data.get("data", "")
                                os.write(master_fd, input_data.encode("utf-8"))
                            elif data.get("type") == "resize":
                                # Handle terminal resize
                                cols = data.get("data", {}).get("cols", 80)
                                rows = data.get("data", {}).get("rows", 24)
                                # Set terminal size (requires termios on Unix)
                                try:
                                    import termios
                                    import struct
                                    import fcntl
                                    s = struct.pack("HHHH", rows, cols, 0, 0)
                                    fcntl.ioctl(master_fd, termios.TIOCSWINSZ, s)
                                except ImportError:
                                    pass  # termios not available
                        except json.JSONDecodeError:
                            # Handle raw text input for backward compatibility
                            os.write(master_fd, message.encode("utf-8"))
                    except WebSocketDisconnect:
                        break
                    except Exception as e:
                        logger.error(f"Error in terminal loop: {str(e)}")
                        break
            finally:
                # Clean up
                sessions[terminal_id]["active"] = False
                reader_thread.join(timeout=1.0)
                os.close(master_fd)
                try:
                    os.kill(pid, 9)  # SIGKILL
                except Exception as e:
                    logger.error(f"Failed to kill terminal process {pid}: {str(e)}")
                logger.info(f"Terminal {terminal_id} closed and cleaned up.")

    except Exception as e:
        logger.exception(f"Error handling terminal {terminal_id}: {str(e)}")