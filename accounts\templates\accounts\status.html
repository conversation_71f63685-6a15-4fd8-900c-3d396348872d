{% extends 'base.html' %}
{% load static %}

{% block title %}System Status - Forge X{% endblock %}

{% block content %}
<style>
/* Status Page Specific Styles */
.status-container {
  padding: 40px 0;
  min-height: 80vh;
}

.status-hero {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.status-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-hero p {
  font-size: 1.2rem;
  color: #d5d5d5;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Overall Status Banner */
.overall-status {
  background: linear-gradient(135deg, rgba(28,28,28,0.9) 0%, rgba(45,45,45,0.9) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 40px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 15px;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.status-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-operational {
  background-color: #28a745;
  box-shadow: 0 0 20px rgba(40, 167, 69, 0.5);
}

.status-degraded {
  background-color: #ffc107;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
}

.status-down {
  background-color: #dc3545;
  box-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

/* Service Status Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.service-card {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 25px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.service-card:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.4);
  box-shadow: 0 10px 20px rgba(192, 255, 107, 0.1);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.service-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffffff;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.service-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 20px;
}

.metric {
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #C0ff6b;
  display: block;
}

.metric-label {
  font-size: 0.9rem;
  color: #d5d5d5;
  margin-top: 5px;
}

/* Performance Charts */
.performance-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.performance-section h2 {
  color: #C0ff6b;
  margin-bottom: 30px;
  font-size: 2rem;
  text-align: center;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.chart-container {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
}

.chart-title {
  color: #ffffff;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.chart-placeholder {
  width: 100%;
  height: 150px;
  background: linear-gradient(45deg, rgba(192, 255, 107, 0.1) 0%, rgba(192, 255, 107, 0.3) 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #C0ff6b;
  font-weight: 600;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

/* Incident History */
.incidents-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 40px;
  backdrop-filter: blur(10px);
}

.incidents-section h2 {
  color: #C0ff6b;
  margin-bottom: 30px;
  font-size: 2rem;
}

.incident-item {
  background: rgba(0, 0, 0, 0.3);
  border-left: 4px solid;
  border-radius: 0 10px 10px 0;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.incident-item:hover {
  transform: translateX(5px);
  background: rgba(0, 0, 0, 0.5);
}

.incident-resolved {
  border-left-color: #28a745;
}

.incident-investigating {
  border-left-color: #ffc107;
}

.incident-critical {
  border-left-color: #dc3545;
}

.incident-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.incident-title {
  color: #ffffff;
  font-weight: 600;
  font-size: 1.1rem;
}

.incident-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-resolved {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
}

.status-investigating {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
}

.status-critical {
  background-color: rgba(220, 53, 69, 0.2);
  color: #dc3545;
}

.incident-description {
  color: #d5d5d5;
  line-height: 1.5;
  margin-bottom: 10px;
}

.incident-time {
  color: #999;
  font-size: 0.9rem;
}

/* Uptime Stats */
.uptime-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.uptime-section h2 {
  color: #C0ff6b;
  margin-bottom: 30px;
  font-size: 2rem;
  text-align: center;
}

.uptime-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
}

.uptime-card {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.uptime-percentage {
  font-size: 2.5rem;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 10px;
}

.uptime-period {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 5px;
}

.uptime-details {
  color: #d5d5d5;
  font-size: 0.9rem;
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Particles Container */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-hero h1 {
    font-size: 2.5rem;
  }

  .status-hero p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .uptime-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .service-metrics {
    grid-template-columns: 1fr;
  }

  .incident-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .status-hero h1 {
    font-size: 2rem;
  }

  .overall-status,
  .performance-section,
  .incidents-section,
  .uptime-section {
    margin: 0 10px 20px 10px;
    padding: 25px 15px;
  }
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<div class="status-container">
  <div class="tile-wrap">
    <!-- Hero Section -->
    <div class="status-hero fade-in">
      <h1>System Status</h1>
      <p>Real-time monitoring of Forge X platform services, performance metrics, and system health. Stay informed about our service availability and any ongoing incidents.</p>
    </div>

    <!-- Overall Status -->
    <div class="overall-status fade-in" data-delay="100">
      <div class="status-indicator">
        <div class="status-dot status-operational" id="overall-status-dot"></div>
        <span id="overall-status-text">All Systems Operational</span>
      </div>
      <p style="color: #d5d5d5; margin: 0;">All services are running smoothly. Last updated: <span id="last-updated"></span></p>
    </div>

    <!-- Services Status -->
    <div class="services-grid fade-in" data-delay="200">
      <div class="service-card">
        <div class="service-header">
          <div class="service-name">Web Application</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">Main Django web application serving the platform</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="web-response-time">45ms</span>
            <div class="metric-label">Response Time</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="web-uptime">99.9%</span>
            <div class="metric-label">Uptime (24h)</div>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-header">
          <div class="service-name">Database</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">PostgreSQL database cluster</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="db-response-time">12ms</span>
            <div class="metric-label">Query Time</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="db-connections">45/100</span>
            <div class="metric-label">Connections</div>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-header">
          <div class="service-name">WebSocket Server</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">Real-time communication and notifications</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="ws-connections">234</span>
            <div class="metric-label">Active Connections</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="ws-latency">8ms</span>
            <div class="metric-label">Latency</div>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-header">
          <div class="service-name">AI Services</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">FastAPI microservices for AI features</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="ai-response-time">120ms</span>
            <div class="metric-label">Processing Time</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="ai-queue">3</span>
            <div class="metric-label">Queue Length</div>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-header">
          <div class="service-name">File Storage</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">Static files and user uploads</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="storage-usage">67%</span>
            <div class="metric-label">Storage Used</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="storage-speed">2.1GB/s</span>
            <div class="metric-label">Transfer Speed</div>
          </div>
        </div>
      </div>

      <div class="service-card">
        <div class="service-header">
          <div class="service-name">Payment Gateway</div>
          <div class="service-status">
            <div class="status-dot status-operational"></div>
            <span style="color: #28a745;">Operational</span>
          </div>
        </div>
        <p style="color: #d5d5d5; margin-bottom: 15px;">Stripe payment processing</p>
        <div class="service-metrics">
          <div class="metric">
            <span class="metric-value" id="payment-success">99.8%</span>
            <div class="metric-label">Success Rate</div>
          </div>
          <div class="metric">
            <span class="metric-value" id="payment-time">1.2s</span>
            <div class="metric-label">Avg Process Time</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Metrics -->
    <div class="performance-section fade-in" data-delay="300">
      <h2>Performance Metrics</h2>
      <div class="charts-grid">
        <div class="chart-container">
          <div class="chart-title">Response Time (24h)</div>
          <div class="chart-placeholder">
            📊 Real-time Chart<br>
            <small style="opacity: 0.7;">Avg: 45ms</small>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-title">Active Users</div>
          <div class="chart-placeholder">
            👥 Live Users<br>
            <small style="opacity: 0.7;">Current: 1,247</small>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-title">Server Load</div>
          <div class="chart-placeholder">
            ⚡ CPU Usage<br>
            <small style="opacity: 0.7;">Current: 23%</small>
          </div>
        </div>
        <div class="chart-container">
          <div class="chart-title">Memory Usage</div>
          <div class="chart-placeholder">
            💾 RAM Usage<br>
            <small style="opacity: 0.7;">Used: 4.2GB/8GB</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Incidents -->
    <div class="incidents-section fade-in" data-delay="400">
      <h2>Recent Incidents</h2>

      <div class="incident-item incident-resolved">
        <div class="incident-header">
          <div class="incident-title">Database Connection Pool Optimization</div>
          <div class="incident-status status-resolved">Resolved</div>
        </div>
        <div class="incident-description">
          Optimized database connection pooling to improve performance during peak hours. All services are now operating normally.
        </div>
        <div class="incident-time">Resolved 2 hours ago • Duration: 15 minutes</div>
      </div>

      <div class="incident-item incident-resolved">
        <div class="incident-header">
          <div class="incident-title">WebSocket Server Maintenance</div>
          <div class="incident-status status-resolved">Resolved</div>
        </div>
        <div class="incident-description">
          Scheduled maintenance to upgrade WebSocket server infrastructure. Real-time features were temporarily unavailable.
        </div>
        <div class="incident-time">Resolved 1 day ago • Duration: 30 minutes</div>
      </div>

      <div class="incident-item incident-resolved">
        <div class="incident-header">
          <div class="incident-title">AI Service Latency Issues</div>
          <div class="incident-status status-resolved">Resolved</div>
        </div>
        <div class="incident-description">
          Experienced increased latency in AI-powered features due to high demand. Scaled infrastructure to handle the load.
        </div>
        <div class="incident-time">Resolved 3 days ago • Duration: 45 minutes</div>
      </div>

      <div class="incident-item incident-resolved">
        <div class="incident-header">
          <div class="incident-title">Payment Gateway Timeout</div>
          <div class="incident-status status-resolved">Resolved</div>
        </div>
        <div class="incident-description">
          Some payment transactions experienced timeouts due to external gateway issues. All affected payments have been processed.
        </div>
        <div class="incident-time">Resolved 1 week ago • Duration: 1 hour 20 minutes</div>
      </div>

      <div style="text-align: center; margin-top: 30px;">
        <p style="color: #d5d5d5;">
          <i class="fas fa-check-circle" style="color: #28a745; margin-right: 8px;"></i>
          No major incidents in the past 30 days
        </p>
      </div>
    </div>

    <!-- Uptime Statistics -->
    <div class="uptime-section fade-in" data-delay="500">
      <h2>Uptime Statistics</h2>
      <div class="uptime-grid">
        <div class="uptime-card">
          <div class="uptime-percentage">99.98%</div>
          <div class="uptime-period">Last 24 Hours</div>
          <div class="uptime-details">23h 59m uptime</div>
        </div>
        <div class="uptime-card">
          <div class="uptime-percentage">99.95%</div>
          <div class="uptime-period">Last 7 Days</div>
          <div class="uptime-details">6d 23h 58m uptime</div>
        </div>
        <div class="uptime-card">
          <div class="uptime-percentage">99.92%</div>
          <div class="uptime-period">Last 30 Days</div>
          <div class="uptime-details">29d 22h 45m uptime</div>
        </div>
        <div class="uptime-card">
          <div class="uptime-percentage">99.89%</div>
          <div class="uptime-period">Last 90 Days</div>
          <div class="uptime-details">89d 18h 32m uptime</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for animations, particles, and real-time updates -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": "#C0ff6b"
        },
        "shape": {
          "type": "circle",
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": false,
          "anim": {
            "enable": false,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": false,
            "speed": 40,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 6,
          "direction": "none",
          "random": false,
          "straight": false,
          "out_mode": "out",
          "bounce": false,
          "attract": {
            "enable": false,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "repulse"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 400,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 400,
            "size": 40,
            "duration": 2,
            "opacity": 8,
            "speed": 3
          },
          "repulse": {
            "distance": 200,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
    });

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        const delay = parseInt(element.getAttribute('data-delay')) || 0;

        if (elementTop < windowHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('visible');
          }, delay);
        }
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up scroll event listener
    window.addEventListener('scroll', revealOnScroll);

    // Update last updated time
    function updateLastUpdated() {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      document.getElementById('last-updated').textContent = timeString;
    }

    // Update every second
    updateLastUpdated();
    setInterval(updateLastUpdated, 1000);

    // Simulate real-time metric updates
    function updateMetrics() {
      // Web response time (40-60ms)
      const webResponseTime = Math.floor(Math.random() * 20) + 40;
      document.getElementById('web-response-time').textContent = webResponseTime + 'ms';

      // Database response time (8-20ms)
      const dbResponseTime = Math.floor(Math.random() * 12) + 8;
      document.getElementById('db-response-time').textContent = dbResponseTime + 'ms';

      // WebSocket connections (200-300)
      const wsConnections = Math.floor(Math.random() * 100) + 200;
      document.getElementById('ws-connections').textContent = wsConnections;

      // WebSocket latency (5-15ms)
      const wsLatency = Math.floor(Math.random() * 10) + 5;
      document.getElementById('ws-latency').textContent = wsLatency + 'ms';

      // AI response time (100-150ms)
      const aiResponseTime = Math.floor(Math.random() * 50) + 100;
      document.getElementById('ai-response-time').textContent = aiResponseTime + 'ms';

      // AI queue (0-10)
      const aiQueue = Math.floor(Math.random() * 11);
      document.getElementById('ai-queue').textContent = aiQueue;

      // Storage usage (60-75%)
      const storageUsage = Math.floor(Math.random() * 15) + 60;
      document.getElementById('storage-usage').textContent = storageUsage + '%';

      // Payment success rate (99.5-99.9%)
      const paymentSuccess = (99.5 + Math.random() * 0.4).toFixed(1);
      document.getElementById('payment-success').textContent = paymentSuccess + '%';

      // Payment processing time (1.0-2.0s)
      const paymentTime = (1.0 + Math.random() * 1.0).toFixed(1);
      document.getElementById('payment-time').textContent = paymentTime + 's';
    }

    // Update metrics every 5 seconds
    updateMetrics();
    setInterval(updateMetrics, 5000);

    // Add hover effects to service cards
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-5px)';
      });
    });

    // Add click effect to chart placeholders
    const chartPlaceholders = document.querySelectorAll('.chart-placeholder');
    chartPlaceholders.forEach(chart => {
      chart.addEventListener('click', function() {
        this.style.transform = 'scale(0.95)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 150);
      });
    });
  });
</script>
{% endblock %}