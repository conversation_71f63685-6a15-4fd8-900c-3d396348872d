/**
 * ForgeX Main Application Bundle
 * Entry point for the security-enhanced ForgeX application
 */

// Import security system first
import ForgeXSecurity from './security/security-bundle.js';
import SecureAPIClient from './api/api-client.js';

// Import utility modules
import './utils/common.js';
import './utils/notifications.js';

class ForgeXApp {
  constructor() {
    this.security = null;
    this.apiClient = null;
    this.initialized = false;
    this.modules = new Map();
    
    this.init();
  }
  
  async init() {
    console.log('🚀 Initializing ForgeX Application...');
    
    try {
      // Initialize security system first
      await this.initializeSecurity();
      
      // Initialize API client
      await this.initializeAPIClient();
      
      // Initialize core modules
      await this.initializeModules();
      
      // Set up global event handlers
      this.setupGlobalHandlers();
      
      // Mark as initialized
      this.initialized = true;
      
      console.log('✅ ForgeX Application initialized successfully');
      
      // Dispatch initialization complete event
      window.dispatchEvent(new CustomEvent('forgex:initialized', {
        detail: { app: this }
      }));
      
    } catch (error) {
      console.error('❌ Failed to initialize ForgeX Application:', error);
      this.handleInitializationError(error);
    }
  }
  
  async initializeSecurity() {
    console.log('🔒 Initializing security system...');
    
    // Security system is already initialized via import
    this.security = window.ForgeXSecurity;
    
    if (!this.security) {
      throw new Error('Security system failed to initialize');
    }
    
    // Wait for security system to be ready
    let attempts = 0;
    while (!this.security.initialized && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    if (!this.security.initialized) {
      console.warn('⚠️ Security system initialization timeout, continuing...');
    }
    
    console.log('✅ Security system ready');
  }
  
  async initializeAPIClient() {
    console.log('🔗 Initializing API client...');
    
    this.apiClient = window.SecureAPIClient;
    
    if (!this.apiClient) {
      throw new Error('API client failed to initialize');
    }
    
    console.log('✅ API client ready');
  }
  
  async initializeModules() {
    console.log('📦 Initializing application modules...');
    
    // Initialize modules based on current page
    const currentPath = window.location.pathname;
    
    if (currentPath.includes('/collaborate/')) {
      await this.loadModule('collaborate');
    }
    
    if (currentPath.includes('/learn/')) {
      await this.loadModule('learn');
    }
    
    if (currentPath.includes('/mentorship/')) {
      await this.loadModule('mentorship');
    }
    
    if (currentPath.includes('/accounts/')) {
      await this.loadModule('accounts');
    }
    
    // Always load common modules
    await this.loadModule('notifications');
    await this.loadModule('websockets');
    
    console.log('✅ Modules initialized');
  }
  
  async loadModule(moduleName) {
    try {
      console.log(`📦 Loading module: ${moduleName}`);
      
      let module;
      switch (moduleName) {
        case 'collaborate':
          module = await import('./collaborate-bundle.js');
          break;
        case 'learn':
          module = await import('./learn-bundle.js');
          break;
        case 'mentorship':
          module = await import('./mentorship-bundle.js');
          break;
        case 'accounts':
          module = await import('./accounts-bundle.js');
          break;
        case 'notifications':
          module = await import('./utils/notifications.js');
          break;
        case 'websockets':
          module = await import('./utils/websockets.js');
          break;
        default:
          console.warn(`Unknown module: ${moduleName}`);
          return;
      }
      
      this.modules.set(moduleName, module);
      console.log(`✅ Module loaded: ${moduleName}`);
      
    } catch (error) {
      console.error(`❌ Failed to load module ${moduleName}:`, error);
    }
  }
  
  setupGlobalHandlers() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.handleGlobalError(event.error, event);
    });
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      this.handleGlobalError(event.reason, event);
    });
    
    // Page visibility change handler
    document.addEventListener('visibilitychange', () => {
      this.handleVisibilityChange();
    });
    
    // Before unload handler
    window.addEventListener('beforeunload', (event) => {
      this.handleBeforeUnload(event);
    });
    
    // Security event handlers
    if (this.security) {
      window.addEventListener('forgex:security-threat', (event) => {
        this.handleSecurityThreat(event.detail);
      });
    }
  }
  
  handleGlobalError(error, event) {
    // Log error securely
    if (this.security && this.security.canShowDebugInfo()) {
      console.error('Global error:', error, event);
    }
    
    // Log to security system
    if (this.security) {
      this.security.logSecurityEvent(`Global error: ${error.message || error}`);
    }
    
    // Show user-friendly error message
    this.showErrorMessage('An unexpected error occurred. Please refresh the page.');
  }
  
  handleVisibilityChange() {
    if (document.hidden) {
      // Page is hidden - pause non-essential operations
      this.pauseOperations();
    } else {
      // Page is visible - resume operations
      this.resumeOperations();
    }
  }
  
  handleBeforeUnload(event) {
    // Clean up resources
    this.cleanup();
    
    // Check for unsaved changes
    if (this.hasUnsavedChanges()) {
      event.preventDefault();
      event.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
      return event.returnValue;
    }
  }
  
  handleSecurityThreat(threat) {
    console.warn('🚨 Security threat detected:', threat);
    
    // Take appropriate action based on threat level
    if (threat.level === 'critical') {
      this.lockApplication();
    } else if (threat.level === 'high') {
      this.showSecurityWarning(threat.message);
    }
  }
  
  handleInitializationError(error) {
    // Show fallback interface
    document.body.innerHTML = `
      <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                  background: #1a1a1a; color: #fff; display: flex; align-items: center; 
                  justify-content: center; z-index: 999999; font-family: monospace;">
        <div style="text-align: center; max-width: 500px; padding: 20px;">
          <h1 style="color: #ff6b6b;">⚠️ Application Error</h1>
          <p>ForgeX failed to initialize properly.</p>
          <p style="color: #888; font-size: 0.9em;">Error: ${error.message}</p>
          <button onclick="location.reload()" 
                  style="padding: 10px 20px; margin-top: 20px; background: #007acc; 
                         color: white; border: none; border-radius: 5px; cursor: pointer;">
            Reload Application
          </button>
        </div>
      </div>
    `;
  }
  
  pauseOperations() {
    // Pause WebSocket connections
    this.modules.forEach(module => {
      if (module.pause && typeof module.pause === 'function') {
        module.pause();
      }
    });
  }
  
  resumeOperations() {
    // Resume WebSocket connections
    this.modules.forEach(module => {
      if (module.resume && typeof module.resume === 'function') {
        module.resume();
      }
    });
  }
  
  cleanup() {
    // Clean up modules
    this.modules.forEach(module => {
      if (module.cleanup && typeof module.cleanup === 'function') {
        module.cleanup();
      }
    });
    
    // Clean up security system
    if (this.security && this.security.cleanup) {
      this.security.cleanup();
    }
  }
  
  hasUnsavedChanges() {
    // Check if any module has unsaved changes
    for (const [name, module] of this.modules) {
      if (module.hasUnsavedChanges && module.hasUnsavedChanges()) {
        return true;
      }
    }
    return false;
  }
  
  lockApplication() {
    document.body.innerHTML = `
      <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; 
                  background: #000; color: #fff; display: flex; align-items: center; 
                  justify-content: center; z-index: 999999; font-family: monospace;">
        <div style="text-align: center;">
          <h1 style="color: #ff6b6b;">🔒 Application Locked</h1>
          <p>A critical security threat was detected.</p>
          <p>Please contact your administrator.</p>
        </div>
      </div>
    `;
  }
  
  showSecurityWarning(message) {
    // Create warning notification
    const warning = document.createElement('div');
    warning.style.cssText = `
      position: fixed; top: 20px; right: 20px; background: #ff6b6b; color: white;
      padding: 15px; border-radius: 5px; z-index: 10000; max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3); font-family: sans-serif;
    `;
    warning.innerHTML = `
      <strong>🚨 Security Warning</strong><br>
      ${message}
      <button onclick="this.parentElement.remove()" 
              style="float: right; background: none; border: none; color: white; 
                     cursor: pointer; font-size: 16px; margin-left: 10px;">×</button>
    `;
    
    document.body.appendChild(warning);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
      if (warning.parentElement) {
        warning.remove();
      }
    }, 10000);
  }
  
  showErrorMessage(message) {
    // Create error notification
    const error = document.createElement('div');
    error.style.cssText = `
      position: fixed; top: 20px; left: 50%; transform: translateX(-50%);
      background: #ff6b6b; color: white; padding: 15px; border-radius: 5px;
      z-index: 10000; max-width: 400px; text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3); font-family: sans-serif;
    `;
    error.innerHTML = `
      <strong>❌ Error</strong><br>
      ${message}
      <button onclick="this.parentElement.remove()" 
              style="float: right; background: none; border: none; color: white; 
                     cursor: pointer; font-size: 16px; margin-left: 10px;">×</button>
    `;
    
    document.body.appendChild(error);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (error.parentElement) {
        error.remove();
      }
    }, 5000);
  }
  
  // Public API
  getModule(name) {
    return this.modules.get(name);
  }
  
  isInitialized() {
    return this.initialized;
  }
  
  getSecurity() {
    return this.security;
  }
  
  getAPIClient() {
    return this.apiClient;
  }
}

// Initialize application when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.ForgeXApp = new ForgeXApp();
  });
} else {
  window.ForgeXApp = new ForgeXApp();
}

// Export for module use
export default ForgeXApp;
