{% extends 'base.html' %}
{% load static %}

{% block title %}Security Events - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Page Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🚨 Security Events</h1>
      <p class="welcome-subtitle">Monitor and manage all security events across the ForgeX platform</p>
    </div>

    <!-- Filters -->
    <div class="dashboard-section">
      <h2>🔍 Filter Events</h2>
      <form method="get" class="filter-form">
        <div class="filter-grid">
          <div class="filter-item">
            <label for="event_type">Event Type:</label>
            <select name="event_type" id="event_type" class="form-control">
              <option value="">All Types</option>
              <option value="dev_tools_access" {% if request.GET.event_type == 'dev_tools_access' %}selected{% endif %}>Dev Tools Access</option>
              <option value="console_access" {% if request.GET.event_type == 'console_access' %}selected{% endif %}>Console Access</option>
              <option value="suspicious_activity" {% if request.GET.event_type == 'suspicious_activity' %}selected{% endif %}>Suspicious Activity</option>
              <option value="failed_login" {% if request.GET.event_type == 'failed_login' %}selected{% endif %}>Failed Login</option>
            </select>
          </div>
          <div class="filter-item">
            <label for="severity">Severity:</label>
            <select name="severity" id="severity" class="form-control">
              <option value="">All Severities</option>
              <option value="warning" {% if request.GET.severity == 'warning' %}selected{% endif %}>Warning</option>
              <option value="final_warning" {% if request.GET.severity == 'final_warning' %}selected{% endif %}>Final Warning</option>
              <option value="critical" {% if request.GET.severity == 'critical' %}selected{% endif %}>Critical</option>
            </select>
          </div>
          <div class="filter-item">
            <label for="user_filter">User:</label>
            <input type="text" name="user_filter" id="user_filter" value="{{ request.GET.user_filter }}" placeholder="Username" class="form-control">
          </div>
          <div class="filter-item">
            <button type="submit" class="btn btn-primary">Apply Filters</button>
            <a href="{% url 'accounts:security_events_list' %}" class="btn btn-secondary">Clear</a>
          </div>
        </div>
      </form>
    </div>

    <!-- Events List -->
    <div class="dashboard-section">
      <h2>📋 Security Events ({{ page_obj.object_list|length }} found)</h2>

      {% if page_obj.object_list %}
      <div class="events-table-container">
        <table class="events-table">
          <thead>
            <tr>
              <th>Date/Time</th>
              <th>User</th>
              <th>Event Type</th>
              <th>Severity</th>
              <th>IP Address</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for event in page_obj.object_list %}
            <tr class="event-row severity-{{ event.severity }}">
              <td class="event-time">{{ event.timestamp|date:"M d, Y H:i:s" }}</td>
              <td class="event-user">
                {% if event.user %}
                  <strong>{{ event.user.username }}</strong>
                {% else %}
                  <em>Anonymous</em>
                {% endif %}
              </td>
              <td class="event-type">{{ event.get_event_type_display }}</td>
              <td class="event-severity">
                <span class="severity-badge severity-{{ event.severity }}">{{ event.severity|title }}</span>
              </td>
              <td class="event-ip">{{ event.ip_address }}</td>
              <td class="event-actions">
                {% if event.user %}
                <button class="btn btn-sm btn-warning" onclick="showWarningModal({{ event.user.id }}, {{ event.id }})">
                  <i class="fas fa-exclamation-triangle"></i> Warn
                </button>
                <button class="btn btn-sm btn-danger" onclick="showBlockModal('{{ event.ip_address }}', {{ event.user.id }}, [{{ event.id }}])">
                  <i class="fas fa-ban"></i> Block
                </button>
                <a href="{% url 'accounts:user_security_profile' event.user.id %}" class="btn btn-sm btn-info">
                  <i class="fas fa-user"></i> Profile
                </a>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {% if is_paginated %}
      <div class="pagination-container">
        <div class="pagination">
          {% if page_obj.has_previous %}
            <a href="?page=1{% if request.GET.event_type %}&event_type={{ request.GET.event_type }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}" class="page-link">&laquo; First</a>
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.event_type %}&event_type={{ request.GET.event_type }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}" class="page-link">Previous</a>
          {% endif %}
          
          <span class="page-info">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
          </span>
          
          {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.event_type %}&event_type={{ request.GET.event_type }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}" class="page-link">Next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.event_type %}&event_type={{ request.GET.event_type }}{% endif %}{% if request.GET.severity %}&severity={{ request.GET.severity }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}" class="page-link">Last &raquo;</a>
          {% endif %}
        </div>
      </div>
      {% endif %}

      {% else %}
      <div class="no-events-message">
        <div class="no-events-icon">🛡️</div>
        <h3>No Events Found</h3>
        <p>No security events match your current filters. Try adjusting your search criteria.</p>
      </div>
      {% endif %}
    </div>

    <!-- Back to Dashboard -->
    <div class="dashboard-section">
      <div class="back-to-dashboard">
        <a href="{% url 'accounts:threat_management_dashboard' %}" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Security Dashboard
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Security Warning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="warningForm">
                    <input type="hidden" id="warning-user-id">
                    <input type="hidden" id="warning-security-log-id">
                    
                    <div class="mb-3">
                        <label for="warning-template" class="form-label">Warning Template</label>
                        <select class="form-select" id="warning-template" onchange="loadTemplate()">
                            <option value="">Custom Message</option>
                            {% for key, template in warning_templates.items %}
                            <option value="{{ key }}">{{ template.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="warning-title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-message" class="form-label">Message</label>
                        <textarea class="form-control" id="warning-message" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-severity" class="form-label">Severity</label>
                        <select class="form-select" id="warning-severity" required>
                            <option value="warning">Warning</option>
                            <option value="final_warning">Final Warning</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="sendWarning()">Send Warning</button>
            </div>
        </div>
    </div>
</div>

<!-- Block IP Modal -->
<div class="modal fade" id="blockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Block IP Address</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="blockForm">
                    <input type="hidden" id="block-user-id">
                    <input type="hidden" id="block-security-log-ids">
                    
                    <div class="mb-3">
                        <label for="block-ip" class="form-label">IP Address</label>
                        <input type="text" class="form-control" id="block-ip" required readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="block-type" class="form-label">Block Type</label>
                        <select class="form-select" id="block-type" onchange="toggleDuration()">
                            <option value="temporary">Temporary</option>
                            <option value="permanent">Permanent</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="duration-field">
                        <label for="block-duration" class="form-label">Duration (hours)</label>
                        <input type="number" class="form-control" id="block-duration" value="24" min="1">
                    </div>
                    
                    <div class="mb-3">
                        <label for="block-reason" class="form-label">Reason</label>
                        <select class="form-select" id="block-reason">
                            <option value="security_violation">Security Violation</option>
                            <option value="repeated_warnings">Repeated Warnings Ignored</option>
                            <option value="malicious_activity">Malicious Activity</option>
                            <option value="spam">Spam/Abuse</option>
                            <option value="manual_block">Manual Block</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="block-custom-reason" class="form-label">Additional Details</label>
                        <textarea class="form-control" id="block-custom-reason" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="blockIP()">Block IP</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Section Styling - Match Main Dashboard */
    .dashboard-section {
      margin-bottom: 40px;
    }

    .dashboard-section h2 {
      color: #ffffff;
      margin-bottom: 30px;
      font-size: 1.8rem;
      font-weight: 600;
    }

    /* Filter Form Styling */
    .filter-form {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      margin-bottom: 30px;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      align-items: end;
    }

    .filter-item label {
      display: block;
      color: #ffffff;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-control {
      background-color: rgba(60, 60, 60, 0.8);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      padding: 10px 15px;
      color: #ffffff;
      font-size: 0.9rem;
    }

    .form-control:focus {
      border-color: var(--color-border);
      box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
      outline: none;
    }

    /* Events Table Styling */
    .events-table-container {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .events-table {
      width: 100%;
      border-collapse: collapse;
    }

    .events-table th {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      padding: 15px;
      text-align: left;
      font-weight: 600;
      border-bottom: 1px solid rgba(192, 255, 107, 0.3);
    }

    .events-table td {
      padding: 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    .event-row:hover {
      background-color: rgba(192, 255, 107, 0.05);
    }

    .severity-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .severity-warning {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .severity-final_warning {
      background: rgba(255, 107, 107, 0.2);
      color: #ff6b6b;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }

    .severity-critical {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
      animation: pulse-critical 2s infinite;
    }

    @keyframes pulse-critical {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }

    .event-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 8px 16px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-block;
      border: none;
      cursor: pointer;
      font-size: 0.9rem;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.8rem;
    }

    .btn-primary {
      background-color: var(--color-border);
      color: #000;
    }

    .btn-secondary {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
    }

    .btn-warning {
      background-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .btn-danger {
      background-color: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .btn-info {
      background-color: rgba(23, 162, 184, 0.2);
      color: #17a2b8;
      border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Pagination Styling */
    .pagination-container {
      text-align: center;
      margin-top: 30px;
    }

    .pagination {
      display: inline-flex;
      gap: 10px;
      align-items: center;
    }

    .page-link {
      padding: 8px 16px;
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .page-link:hover {
      background-color: rgba(192, 255, 107, 0.2);
      transform: translateY(-2px);
    }

    .page-info {
      color: #ffffff;
      font-weight: 500;
      margin: 0 15px;
    }

    /* No Events Message */
    .no-events-message {
      text-align: center;
      padding: 60px 20px;
      background-color: rgba(40, 40, 40, 0.3);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .no-events-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    .no-events-message h3 {
      color: var(--color-border);
      margin-bottom: 10px;
    }

    .no-events-message p {
      color: #b0b0b0;
    }

    /* Back to Dashboard */
    .back-to-dashboard {
      text-align: center;
      margin-top: 30px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .filter-grid {
        grid-template-columns: 1fr;
      }

      .events-table-container {
        overflow-x: auto;
      }

      .events-table {
        min-width: 800px;
      }

      .event-actions {
        flex-direction: column;
        gap: 5px;
      }

      .pagination {
        flex-direction: column;
        gap: 10px;
      }

      .dashboard-section h2 {
        font-size: 1.5rem;
      }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<script>
// Warning templates data
const warningTemplates = {{ warning_templates|safe }};

function showWarningModal(userId, securityLogId = null) {
    document.getElementById('warning-user-id').value = userId;
    document.getElementById('warning-security-log-id').value = securityLogId || '';
    document.getElementById('warning-template').value = '';
    document.getElementById('warning-title').value = '';
    document.getElementById('warning-message').value = '';
    document.getElementById('warning-severity').value = 'warning';

    new bootstrap.Modal(document.getElementById('warningModal')).show();
}

function showBlockModal(ipAddress, userId = null, securityLogIds = []) {
    document.getElementById('block-ip').value = ipAddress;
    document.getElementById('block-user-id').value = userId || '';
    document.getElementById('block-security-log-ids').value = JSON.stringify(securityLogIds);
    document.getElementById('block-type').value = 'temporary';
    document.getElementById('block-duration').value = '24';
    document.getElementById('block-reason').value = 'security_violation';
    document.getElementById('block-custom-reason').value = '';

    new bootstrap.Modal(document.getElementById('blockModal')).show();
}

function loadTemplate() {
    const templateKey = document.getElementById('warning-template').value;
    if (templateKey && warningTemplates[templateKey]) {
        const template = warningTemplates[templateKey];
        document.getElementById('warning-title').value = template.title;
        document.getElementById('warning-message').value = template.message;
        document.getElementById('warning-severity').value = template.severity;
    }
}

function toggleDuration() {
    const blockType = document.getElementById('block-type').value;
    const durationField = document.getElementById('duration-field');
    durationField.style.display = blockType === 'temporary' ? 'block' : 'none';
}

function sendWarning() {
    const formData = {
        user_id: document.getElementById('warning-user-id').value,
        template: document.getElementById('warning-template').value,
        custom_title: document.getElementById('warning-title').value,
        custom_message: document.getElementById('warning-message').value,
        severity: document.getElementById('warning-severity').value,
        security_log_id: document.getElementById('warning-security-log-id').value || null
    };

    fetch('{% url "accounts:api_send_warning" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Warning sent successfully!');
            bootstrap.Modal.getInstance(document.getElementById('warningModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error sending warning: ' + error.message);
    });
}

function blockIP() {
    const formData = {
        ip_address: document.getElementById('block-ip').value,
        block_type: document.getElementById('block-type').value,
        duration_hours: parseInt(document.getElementById('block-duration').value),
        reason: document.getElementById('block-reason').value,
        custom_reason: document.getElementById('block-custom-reason').value,
        user_id: document.getElementById('block-user-id').value || null,
        security_log_ids: JSON.parse(document.getElementById('block-security-log-ids').value || '[]')
    };

    fetch('{% url "accounts:api_block_ip" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('IP blocked successfully!');
            bootstrap.Modal.getInstance(document.getElementById('blockModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error blocking IP: ' + error.message);
    });
}
</script>
{% endblock %}
