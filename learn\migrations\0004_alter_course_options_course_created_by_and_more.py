# Generated by Django 5.2.1 on 2025-05-31 15:27

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("learn", "0003_alter_course_updated_at"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="course",
            options={"ordering": ["-created_at", "name"]},
        ),
        migrations.AddField(
            model_name="course",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="created_courses",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="is_approved",
            field=models.BooleanField(
                default=False, help_text="Admin approval for mentor-created courses"
            ),
        ),
        migrations.AddField(
            model_name="course",
            name="status",
            field=models.CharField(
                choices=[
                    ("draft", "Draft"),
                    ("published", "Published"),
                    ("archived", "Archived"),
                ],
                default="draft",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="course",
            name="image",
            field=models.ImageField(blank=True, null=True, upload_to="learn/images"),
        ),
    ]
