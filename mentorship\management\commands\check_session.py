from django.core.management.base import BaseCommand
from mentorship.models import MentorshipSession


class Command(BaseCommand):
    help = 'Check session details'

    def handle(self, *args, **options):
        sessions = MentorshipSession.objects.all()
        
        for session in sessions:
            self.stdout.write(f'Session {session.id}:')
            self.stdout.write(f'  Room ID: {session.room_id}')
            self.stdout.write(f'  Status: {session.status}')
            self.stdout.write(f'  Learner: {session.learner.username}')
            self.stdout.write(f'  Mentor: {session.mentor.username}')
            self.stdout.write(f'  Messages: {session.chat_messages.count()}')
            self.stdout.write('---')
