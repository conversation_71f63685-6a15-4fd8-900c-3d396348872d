"""
Custom template tags and filters for the collaborate app.
"""
from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using a variable key.

    Usage:
        {{ dictionary|get_item:key }}
    """
    if not dictionary:
        return None

    try:
        return dictionary[key]
    except (<PERSON><PERSON><PERSON><PERSON>, TypeError):
        return None

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument.

    Usage:
        {{ value|multiply:arg }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def divide(value, arg):
    """
    Divide the value by the argument.

    Usage:
        {{ value|divide:arg }}
    """
    try:
        return float(value) / float(arg)
    except (Value<PERSON>rro<PERSON>, TypeError, ZeroDivisionError):
        return 0
