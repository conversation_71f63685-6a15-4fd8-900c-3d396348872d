{% extends 'base.html' %}
{% load static %}

{% block title %}Session Details - ForgeX{% endblock %}

{% block content %}
<!-- DEBUG: Template is loading -->
<script>console.log('DEBUG: session_details.html template loaded for session:', {{ session.id|default:'NO_SESSION' }});</script>
<div class="session-details-page">
    <div class="container">
        <!-- Header -->
        <div class="details-header">
            <div class="header-content">
                <div class="back-button">
                    <a href="{% url 'mentorship:my_sessions' %}" class="btn-back">
                        <i class="fas fa-arrow-left"></i>
                        Back to Sessions
                    </a>
                </div>
                <div class="session-status-badge">
                    <span class="status-badge status-{{ session.status }}">
                        {{ session.get_status_display|upper }}
                    </span>
                </div>
            </div>
            <h1>Session Details</h1>
        </div>

        <!-- Main Content -->
        <div class="details-content">
            <!-- Session Overview Card -->
            <div class="details-card overview-card">
                <div class="card-header">
                    <h2><i class="fas fa-info-circle"></i> Session Overview</h2>
                </div>
                <div class="card-content">
                    <div class="session-info-grid">
                        <div class="info-item">
                            <span class="label">Session ID</span>
                            <span class="value">#{{ session.id }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Room ID</span>
                            <span class="value">{{ session.room_id }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Date & Time</span>
                            <span class="value">{{ session.scheduled_time|date:"M d, Y" }} at {{ session.scheduled_time|time:"g:i A" }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Duration</span>
                            <span class="value">{{ session.get_duration_hours }} hour{{ session.get_duration_hours|pluralize }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Status</span>
                            <span class="value status-{{ session.status }}">{{ session.get_status_display }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Created</span>
                            <span class="value">{{ session.created_at|date:"M d, Y g:i A" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Participants Card -->
            <div class="details-card participants-card">
                <div class="card-header">
                    <h2><i class="fas fa-users"></i> Participants</h2>
                </div>
                <div class="card-content">
                    <div class="participants-grid">
                        <!-- Mentor Info -->
                        <div class="participant mentor-participant">
                            <div class="participant-avatar">
                                {% if session.mentor.profile and session.mentor.profile.profile_picture %}
                                    <img src="{{ session.mentor.profile.profile_picture.url }}"
                                         alt="{{ session.mentor.get_full_name }}"
                                         class="profile-image">
                                {% else %}
                                    <div class="avatar-initials">
                                        {{ session.mentor.first_name|first|upper }}{{ session.mentor.last_name|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="participant-info">
                                <h3>{{ session.mentor.get_full_name|default:session.mentor.username }}</h3>
                                <p class="role">Mentor</p>
                                {% if mentor_profile %}
                                    <p class="expertise">{{ mentor_profile.expertise_areas|truncatewords:5 }}</p>
                                    <div class="rating">
                                        <span class="stars">
                                            {% for i in "12345" %}
                                                <i class="fas fa-star{% if forloop.counter > mentor_profile.average_rating %} inactive{% endif %}"></i>
                                            {% endfor %}
                                        </span>
                                        <span class="rating-text">({{ mentor_profile.average_rating|floatformat:1 }})</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Learner Info -->
                        <div class="participant learner-participant">
                            <div class="participant-avatar">
                                {% if session.learner.profile and session.learner.profile.profile_picture %}
                                    <img src="{{ session.learner.profile.profile_picture.url }}"
                                         alt="{{ session.learner.get_full_name }}"
                                         class="profile-image">
                                {% else %}
                                    <div class="avatar-initials">
                                        {{ session.learner.first_name|first|upper }}{{ session.learner.last_name|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="participant-info">
                                <h3>{{ session.learner.get_full_name|default:session.learner.username }}</h3>
                                <p class="role">Learner</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment & Pricing Card -->
            <div class="details-card payment-card">
                <div class="card-header">
                    <h2><i class="fas fa-credit-card"></i> Payment & Pricing</h2>
                </div>
                <div class="card-content">
                    <div class="payment-info-grid">
                        <div class="info-item">
                            <span class="label">Hourly Rate</span>
                            <span class="value">${{ session.hourly_rate }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Duration</span>
                            <span class="value">{{ session.duration_minutes }} minutes</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Subtotal</span>
                            <span class="value">${{ session.subtotal_amount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Platform Fee</span>
                            <span class="value">${{ session.platform_fee }}</span>
                        </div>
                        <div class="info-item total">
                            <span class="label">Total Amount</span>
                            <span class="value">${{ session.total_amount }}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Payment Status</span>
                            <span class="value payment-status {% if session.is_paid %}paid{% else %}pending{% endif %}">
                                {% if session.is_paid %}
                                    <i class="fas fa-check-circle"></i> Paid
                                {% else %}
                                    <i class="fas fa-clock"></i> Pending
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Timing Card -->
            <div class="details-card timing-card">
                <div class="card-header">
                    <h2><i class="fas fa-clock"></i> Session Timing</h2>
                </div>
                <div class="card-content">
                    <div class="timing-info">
                        {% if time_until_session %}
                            <div class="timing-item upcoming">
                                <i class="fas fa-hourglass-start"></i>
                                <div>
                                    <h4>Session starts in</h4>
                                    <p class="time-value">
                                        {% if time_until_session.days > 0 %}
                                            {{ time_until_session.days }} day{{ time_until_session.days|pluralize }},
                                        {% endif %}
                                        {{ time_until_session.seconds|floatformat:0|time:"H:i" }}
                                    </p>
                                </div>
                            </div>
                        {% elif time_since_session %}
                            <div class="timing-item past">
                                <i class="fas fa-history"></i>
                                <div>
                                    <h4>Session was</h4>
                                    <p class="time-value">
                                        {% if time_since_session.days > 0 %}
                                            {{ time_since_session.days }} day{{ time_since_session.days|pluralize }} ago
                                        {% else %}
                                            {{ time_since_session.seconds|floatformat:0|time:"H:i" }} ago
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        {% endif %}

                        {% if session.started_at %}
                            <div class="timing-item">
                                <i class="fas fa-play"></i>
                                <div>
                                    <h4>Started at</h4>
                                    <p class="time-value">{{ session.started_at|time:"g:i A" }}</p>
                                </div>
                            </div>
                        {% endif %}

                        {% if session.ended_at %}
                            <div class="timing-item">
                                <i class="fas fa-stop"></i>
                                <div>
                                    <h4>Ended at</h4>
                                    <p class="time-value">{{ session.ended_at|time:"g:i A" }}</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Feedback Card (if exists) -->
            {% if feedback %}
            <div class="details-card feedback-card">
                <div class="card-header">
                    <h2><i class="fas fa-star"></i> Session Feedback</h2>
                </div>
                <div class="card-content">
                    <div class="feedback-content">
                        <div class="rating-display">
                            <span class="rating-stars">
                                {% for i in "12345" %}
                                    <i class="fas fa-star{% if forloop.counter > feedback.rating %} inactive{% endif %}"></i>
                                {% endfor %}
                            </span>
                            <span class="rating-value">{{ feedback.rating }}/5</span>
                        </div>
                        {% if feedback.comment %}
                            <div class="feedback-comment">
                                <h4>Comment:</h4>
                                <p>{{ feedback.comment }}</p>
                            </div>
                        {% endif %}
                        <div class="feedback-meta">
                            <small>Submitted on {{ feedback.created_at|date:"M d, Y g:i A" }}</small>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="action-buttons">
                {% if is_payment_required %}
                    <a href="{% url 'mentorship:payment_page' session.id %}" class="btn btn-warning">
                        <i class="fas fa-credit-card"></i> Complete Payment
                    </a>
                {% elif session.status == 'scheduled' and can_join %}
                    <a href="{% url 'mentorship:session_room' session.room_id %}" class="btn btn-primary">
                        <i class="fas fa-video"></i> Join Session
                    </a>
                {% elif session.status == 'active' %}
                    <a href="{% url 'mentorship:session_room' session.room_id %}" class="btn btn-success">
                        <i class="fas fa-video"></i> Continue Session
                    </a>
                {% elif session.status == 'completed' and user_role == 'learner' and not feedback %}
                    <a href="{% url 'mentorship:session_feedback' session.id %}" class="btn btn-secondary">
                        <i class="fas fa-star"></i> Leave Feedback
                    </a>
                {% endif %}

                <!-- Debug Join Button (Always Visible for Testing) -->
                {% if session.status == 'scheduled' or session.status == 'active' %}
                    <a href="{% url 'mentorship:session_room' session.room_id %}" class="btn btn-debug">
                        <i class="fas fa-bug"></i> Debug Join
                    </a>
                {% endif %}

                <a href="{% url 'mentorship:my_sessions' %}" class="btn btn-outline">
                    <i class="fas fa-list"></i> Back to Sessions
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.session-details-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 2rem 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.details-header {
    margin-bottom: 3rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.btn-back {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #C0ff6b;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-back:hover {
    color: #a0e066;
    transform: translateX(-5px);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-scheduled { background: #3498db; color: white; }
.status-active { background: #2ecc71; color: white; }
.status-completed { background: #95a5a6; color: white; }
.status-cancelled { background: #e74c3c; color: white; }

.details-header h1 {
    font-size: 2.5rem;
    color: #C0ff6b;
    margin: 0;
    text-align: center;
}

.details-content {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
}

.details-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    border: 1px solid rgba(192,255,107,0.2);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card-header {
    background: rgba(192,255,107,0.1);
    padding: 1.5rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.card-header h2 {
    margin: 0;
    color: #C0ff6b;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-content {
    padding: 2rem;
}

.session-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item .label {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item .value {
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
}

.participants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.participant {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(255,255,255,0.03);
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.1);
}

.participant-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #C0ff6b;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
    overflow: hidden;
    flex-shrink: 0;
}

.participant-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.participant-info h3 {
    margin: 0 0 0.25rem 0;
    color: #ffffff;
    font-size: 1.2rem;
}

.participant-info .role {
    margin: 0 0 0.5rem 0;
    color: #C0ff6b;
    font-weight: 500;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.participant-info .expertise {
    margin: 0 0 0.5rem 0;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stars i {
    color: #ffd700;
    font-size: 0.9rem;
}

.stars i.inactive {
    color: rgba(255,255,255,0.3);
}

.rating-text {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.payment-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.info-item.total {
    grid-column: 1 / -1;
    padding-top: 1rem;
    border-top: 1px solid rgba(255,255,255,0.2);
}

.info-item.total .value {
    font-size: 1.3rem;
    color: #C0ff6b;
}

.payment-status.paid {
    color: #2ecc71;
}

.payment-status.pending {
    color: #f39c12;
}

.timing-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.timing-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.03);
    border-radius: 8px;
}

.timing-item i {
    font-size: 1.5rem;
    color: #C0ff6b;
    width: 30px;
    text-align: center;
}

.timing-item h4 {
    margin: 0 0 0.25rem 0;
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.time-value {
    margin: 0;
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem;
}

.timing-item.upcoming i {
    color: #3498db;
}

.timing-item.past i {
    color: #95a5a6;
}

.feedback-content {
    text-align: center;
}

.rating-display {
    margin-bottom: 1.5rem;
}

.rating-stars {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.rating-stars i {
    color: #ffd700;
}

.rating-stars i.inactive {
    color: rgba(255,255,255,0.3);
}

.rating-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #C0ff6b;
}

.feedback-comment {
    text-align: left;
    margin-bottom: 1rem;
}

.feedback-comment h4 {
    color: #C0ff6b;
    margin-bottom: 0.5rem;
}

.feedback-comment p {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
}

.feedback-meta {
    color: rgba(255,255,255,0.6);
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.btn-primary {
    background: #C0ff6b;
    color: #1a1a1a;
}

.btn-primary:hover {
    background: #a0e066;
    transform: translateY(-2px);
}

.btn-success {
    background: #2ecc71;
    color: white;
}

.btn-success:hover {
    background: #27ae60;
    transform: translateY(-2px);
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.btn-debug {
    background: #e74c3c;
    color: white;
}

.btn-debug:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-outline {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-outline:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .details-header h1 {
        font-size: 2rem;
    }

    .session-info-grid {
        grid-template-columns: 1fr;
    }

    .participants-grid {
        grid-template-columns: 1fr;
    }

    .participant {
        flex-direction: column;
        text-align: center;
    }

    .payment-info-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}
</style>
