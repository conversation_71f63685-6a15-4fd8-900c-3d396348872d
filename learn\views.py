from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponseForbidden, JsonResponse
from django.db.models import Q, Avg
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
import json
from .models import (
    Course, Chapter, Lesson, LessonContent, UserLearningProfile,
    LessonProgress, AILearningAssistant, SmartRecommendation, CodeAnalysis
)
from .forms import CourseForm, ChapterForm, LessonForm, LessonContentForm
from .ai_services import AILearningService, CodeAnalysisService

# Helper functions
def is_verified_mentor(user):
    """Check if user is a verified mentor"""
    if not user.is_authenticated:
        return False
    return hasattr(user, 'mentor_profile') and user.mentor_profile.verified

def can_create_courses(user):
    """Check if user can create courses (superuser or verified mentor)"""
    return user.is_superuser or is_verified_mentor(user)

# Create your views here.
def course_list(request):
    # Check if user wants to access the dashboard
    if request.GET.get('dashboard') == 'true' and request.user.is_authenticated:
        return redirect('learn:unified_dashboard')

    # Filter courses based on user permissions
    if request.user.is_superuser:
        # Superusers can see all courses
        courses = Course.objects.all()
    else:
        # Regular users can only see approved published courses and their own courses
        courses_filter = Q(status='published', is_approved=True)
        if request.user.is_authenticated and is_verified_mentor(request.user):
            # Verified mentors can also see their own courses
            courses_filter |= Q(created_by=request.user)
        courses = Course.objects.filter(courses_filter)

    # Check if this is a first-time Google sign-in user
    if request.user.is_authenticated:
        # Check if user just signed in (you can use session or other methods)
        if request.GET.get('welcome') == 'google':
            messages.success(request, f'Welcome to Forge X, {request.user.username}! 🎉 You\'ve successfully signed in with Google.')
        elif request.GET.get('welcome') == 'new':
            messages.info(request, f'Welcome back, {request.user.username}! Ready to continue learning?')

    # AI Features Integration
    ai_features = {}
    if request.user.is_authenticated:
        try:
            ai_service = AILearningService()

            # Get user progress analytics
            progress_analytics = ai_service.analyze_user_progress(request.user)

            # Get personalized course recommendations
            recommendations = ai_service.generate_personalized_recommendations(request.user, limit=3)

            ai_features = {
                'progress_analytics': progress_analytics,
                'recommendations': recommendations,
                'ai_enabled': True,
            }

        except Exception as e:
            ai_features = {
                'ai_enabled': False,
                'setup_required': True,
            }

    context = {
        'courses': courses,
        'is_superuser': request.user.is_superuser,
        'is_verified_mentor': is_verified_mentor(request.user) if request.user.is_authenticated else False,
        'can_create_courses': can_create_courses(request.user) if request.user.is_authenticated else False,
        **ai_features,  # Merge AI features
    }
    return render(request, 'learn/course_list.html', context)
def course_details(request, id):
    course = Course.objects.get(id=id)
    chapters = Chapter.objects.filter(course=course).order_by('order', 'id')

    # Check if user can manage this course
    can_manage_course = False
    if request.user.is_authenticated:
        can_manage_course = course.can_be_edited_by(request.user)

    context = {
        'course': course,
        'chapters': chapters,
        'is_superuser': request.user.is_superuser,
        'can_manage_course': can_manage_course,
        'is_verified_mentor': is_verified_mentor(request.user) if request.user.is_authenticated else False,
    }
    return render(request, 'learn/course_details.html', context)
@login_required
def chapter_details(request, id):
    chapter = Chapter.objects.get(id=id)
    lessons = Lesson.objects.filter(chapter=chapter).order_by('order', 'id')
    course = chapter.course

    # Check if user can manage this course
    can_manage_course = False
    if request.user.is_authenticated:
        can_manage_course = course.can_be_edited_by(request.user)

    context = {
        'chapter': chapter,
        'lessons': lessons,
        'course': course,
        'is_superuser': request.user.is_superuser,
        'can_manage_course': can_manage_course,
    }
    return render(request, 'learn/chapter_details.html', context)
@login_required
def lesson_details(request, id):
    lesson = get_object_or_404(Lesson, id=id)
    course = lesson.chapter.course
    all_lessons = Lesson.objects.filter(chapter__course=course).order_by('chapter__order', 'chapter__id', 'order', 'id')
    lesson_list = list(all_lessons)
    current_index = lesson_list.index(lesson)
    prev_lesson = lesson_list[current_index - 1] if current_index > 0 else None
    next_lesson = lesson_list[current_index + 1] if current_index < len(lesson_list) - 1 else None

    # Check if user can manage this course
    can_manage_course = False
    if request.user.is_authenticated:
        can_manage_course = course.can_be_edited_by(request.user)

    # AI Features Integration
    ai_features = {}
    try:
        ai_service = AILearningService()

        # Get or create user learning profile
        learning_profile = ai_service.get_or_create_learning_profile(request.user)

        # Get or create lesson progress
        lesson_progress, created = LessonProgress.objects.get_or_create(
            user=request.user,
            lesson=lesson,
            defaults={'status': 'in_progress', 'started_at': timezone.now()}
        )

        # Update last accessed time
        lesson_progress.last_accessed = timezone.now()
        lesson_progress.save()

        # Get AI recommendations
        recommendations = ai_service.generate_personalized_recommendations(request.user, limit=3)

        # Get recent AI conversations for this lesson
        recent_conversations = AILearningAssistant.objects.filter(
            user=request.user,
            lesson=lesson
        ).order_by('-created_at')[:5]

        # Get user progress analytics
        progress_analytics = ai_service.analyze_user_progress(request.user)

        ai_features = {
            'learning_profile': learning_profile,
            'lesson_progress': lesson_progress,
            'recommendations': recommendations,
            'recent_conversations': recent_conversations,
            'progress_analytics': progress_analytics,
            'ai_enabled': True,
        }

    except Exception as e:
        # AI features not available (migrations not run)
        ai_features = {
            'ai_enabled': False,
            'setup_required': True,
        }

    context = {
        'lesson': lesson,
        'lesson_content': lesson.lessoncontent_set.all().order_by('order', 'id'),
        'is_superuser': request.user.is_superuser,
        'can_manage_course': can_manage_course,
        'prev_lesson': prev_lesson,
        'next_lesson': next_lesson,
        **ai_features,  # Merge AI features into context
    }
    return render(request, 'learn/lesson_details.html', context)
@login_required
def lesson_content(request, id):
    lesson = Lesson.objects.get(id=id)
    lesson_content = LessonContent.objects.filter(lesson=lesson)
    return render(request, 'learn/lesson_content.html', {'lesson': lesson, 'lesson_content': lesson_content})


# Course creation (Admin and Verified Mentors)
@login_required
def create_course(request):
    # Check if user can create courses
    if not can_create_courses(request.user):
        messages.error(request, 'You need to be a verified mentor to create courses.')
        return redirect('learn:course_list')

    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, user=request.user)
        if form.is_valid():
            course = form.save(commit=False)
            course.created_by = request.user

            # Auto-approve courses created by superusers
            if request.user.is_superuser:
                course.is_approved = True
            else:
                # Mentor courses need admin approval
                course.is_approved = False
                messages.info(request, 'Your course has been submitted for review. It will be visible to students once approved by an admin.')

            course.save()
            return redirect('learn:course_list')
    else:
        form = CourseForm(user=request.user)

    return render(request, 'learn/create_course.html', {'form': form})
@login_required
def create_chapter(request, course_id):
    course = get_object_or_404(Course, id=course_id)

    # Check if user can edit this course
    if not course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:course_details', id=course.id)

    if request.method == 'POST':
        form = ChapterForm(request.POST)
        if form.is_valid():
            chapter = form.save(commit=False)
            chapter.course = course
            chapter.save()
            return redirect('learn:chapter_details', id=chapter.id)
        # If form is invalid, chapter does not exist yet
        return render(request, 'learn/create_chapter.html', {'form': form, 'course': course})
    else:
        form = ChapterForm()
    return render(request, 'learn/create_chapter.html', {'form': form, 'course': course})
@login_required
def create_lesson(request, chapter_id):
    chapter = get_object_or_404(Chapter, id=chapter_id)

    # Check if user can edit this course
    if not chapter.course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:chapter_details', id=chapter.id)

    if request.method == 'POST':
        form = LessonForm(request.POST)
        if form.is_valid():
            lesson = form.save(commit=False)
            lesson.chapter = chapter
            lesson.save()
            return redirect('learn:lesson_details', id=lesson.id)
    else:
        form = LessonForm()
    return render(request, 'learn/create_lesson.html', {'form': form, 'chapter': chapter})

@login_required
def create_lesson_content(request, lesson_id):
    lesson = get_object_or_404(Lesson, id=lesson_id)

    # Check if user can edit this course
    if not lesson.chapter.course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:lesson_details', id=lesson.id)

    if request.method == 'POST':
        form = LessonContentForm(request.POST)
        if form.is_valid():
            lesson_content = form.save(commit=False)
            lesson_content.lesson = lesson
            lesson_content.save()
            return redirect('learn:lesson_details', id=lesson.id)
    else:
        form = LessonContentForm()
    return render(request, 'learn/create_lesson_content.html', {'form': form, 'lesson': lesson})

# Course approval management (Admin only)
@login_required
def manage_course_approvals(request):
    if not request.user.is_superuser:
        messages.error(request, 'You do not have permission to access this page.')
        return redirect('learn:course_list')

    # Get courses that need approval
    pending_courses = Course.objects.filter(is_approved=False, created_by__isnull=False).order_by('-created_at')

    context = {
        'pending_courses': pending_courses,
    }
    return render(request, 'learn/manage_approvals.html', context)

@login_required
def approve_course(request, course_id):
    if not request.user.is_superuser:
        messages.error(request, 'You do not have permission to perform this action.')
        return redirect('learn:course_list')

    course = get_object_or_404(Course, id=course_id)
    course.is_approved = True
    course.save()

    messages.success(request, f'Course "{course.name}" has been approved and is now visible to students.')
    return redirect('learn:manage_course_approvals')

@login_required
def reject_course(request, course_id):
    if not request.user.is_superuser:
        messages.error(request, 'You do not have permission to perform this action.')
        return redirect('learn:course_list')

    course = get_object_or_404(Course, id=course_id)
    course.status = 'draft'
    course.is_approved = False
    course.save()

    messages.warning(request, f'Course "{course.name}" has been rejected and moved back to draft status.')
    return redirect('learn:manage_course_approvals')

# delte
from django.urls import reverse

@login_required
def delete_course(request, id):
    course = get_object_or_404(Course, id=id)
    # Allow superusers and course creators to delete
    if request.user.is_superuser or course.created_by == request.user:
        course.delete()
        messages.success(request, f'Course "{course.name}" has been deleted successfully.')
    else:
        messages.error(request, 'You do not have permission to delete this course.')
    return redirect('learn:course_list')

@login_required
def delete_chapter(request, id):
    chapter = get_object_or_404(Chapter, id=id)
    course_id = chapter.course.id
    course = chapter.course
    # Allow superusers and course creators to delete
    if request.user.is_superuser or course.created_by == request.user:
        chapter.delete()
        messages.success(request, f'Chapter "{chapter.name}" has been deleted successfully.')
    else:
        messages.error(request, 'You do not have permission to delete this chapter.')
    return redirect('learn:course_details', id=course_id)

@login_required
def delete_lesson(request, id):
    lesson = get_object_or_404(Lesson, id=id)
    chapter_id = lesson.chapter.id
    course = lesson.chapter.course
    # Allow superusers and course creators to delete
    if request.user.is_superuser or course.created_by == request.user:
        lesson.delete()
        messages.success(request, f'Lesson "{lesson.name}" has been deleted successfully.')
    else:
        messages.error(request, 'You do not have permission to delete this lesson.')
    return redirect('learn:chapter_details', id=chapter_id)

@login_required
def delete_lesson_content(request, id):
    lesson_content = get_object_or_404(LessonContent, id=id)
    lesson_id = lesson_content.lesson.id
    course = lesson_content.lesson.chapter.course
    # Allow superusers and course creators to delete
    if request.user.is_superuser or course.created_by == request.user:
        lesson_content.delete()
        messages.success(request, 'Lesson content has been deleted successfully.')
    else:
        messages.error(request, 'You do not have permission to delete this content.')
    return redirect('learn:lesson_details', id=lesson_id)


#  Edit form
@login_required
def edit_course(request, id):
    course = get_object_or_404(Course, id=id)

    # Check if user can edit this course
    if not course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:course_details', id=course.id)

    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, instance=course, user=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, f'Course "{course.name}" has been updated successfully.')
            return redirect('learn:course_details', id=course.id)
    else:
        form = CourseForm(instance=course, user=request.user)
    return render(request, 'learn/edit_course.html', {'form': form, 'course': course})

@login_required
def edit_chapter(request, id):
    chapter = get_object_or_404(Chapter, id=id)
    course = chapter.course  # Get the course related to the chapter

    # Check if user can edit this course
    if not course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:chapter_details', id=chapter.id)

    if request.method == 'POST':
        form = ChapterForm(request.POST, instance=chapter)
        if form.is_valid():
            form.save()
            messages.success(request, f'Chapter "{chapter.name}" has been updated successfully.')
            return redirect('learn:chapter_details', id=chapter.id)
    else:
        form = ChapterForm(instance=chapter)
    return render(request, 'learn/edit_chapter.html', {'form': form, 'chapter': chapter, 'course': course})

@login_required
def edit_lesson(request, id):
    lesson = get_object_or_404(Lesson, id=id)
    chapter = lesson.chapter  # Get the chapter related to the lesson
    course = chapter.course  # Get the course related to the chapter

    # Check if user can edit this course
    if not course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:lesson_details', id=lesson.id)

    if request.method == 'POST':
        form = LessonForm(request.POST, instance=lesson)
        if form.is_valid():
            form.save()
            messages.success(request, f'Lesson "{lesson.name}" has been updated successfully.')
            return redirect('learn:lesson_details', id=lesson.id)
    else:
        form = LessonForm(instance=lesson)
    return render(request, 'learn/edit_lesson.html', {'form': form, 'lesson': lesson, 'chapter': chapter, 'course': course})

@login_required
def edit_lesson_content(request, id):
    lesson_content = get_object_or_404(LessonContent, id=id)
    lesson = lesson_content.lesson  # Get the lesson related to the lesson content
    chapter = lesson.chapter  # Get the chapter related to the lesson
    course = chapter.course  # Get the course related to the chapter

    # Check if user can edit this course
    if not course.can_be_edited_by(request.user):
        messages.error(request, 'You do not have permission to edit this course.')
        return redirect('learn:lesson_details', id=lesson.id)

    if request.method == 'POST':
        form = LessonContentForm(request.POST, instance=lesson_content)
        if form.is_valid():
            form.save()
            messages.success(request, 'Lesson content has been updated successfully.')
            return redirect('learn:lesson_details', id=lesson_content.lesson.id)
    else:
        form = LessonContentForm(instance=lesson_content)
    return render(request, 'learn/edit_lesson_content.html', {'form': form, 'lesson_content': lesson_content, 'lesson': lesson, 'chapter': chapter, 'course': course})


# AI-Powered Learning Views
@login_required
def lesson_details_ai(request, id):
    """Enhanced lesson details with AI features"""
    lesson = get_object_or_404(Lesson, id=id)
    course = lesson.chapter.course
    all_lessons = Lesson.objects.filter(chapter__course=course).order_by('chapter__order', 'chapter__id', 'order', 'id')
    lesson_list = list(all_lessons)
    current_index = lesson_list.index(lesson)
    prev_lesson = lesson_list[current_index - 1] if current_index > 0 else None
    next_lesson = lesson_list[current_index + 1] if current_index < len(lesson_list) - 1 else None

    # Check if user can manage this course
    can_manage_course = False
    if request.user.is_authenticated:
        can_manage_course = course.can_be_edited_by(request.user)

    # AI Services
    ai_service = AILearningService()

    # Get or create user learning profile
    learning_profile = ai_service.get_or_create_learning_profile(request.user)

    # Get or create lesson progress
    lesson_progress, created = LessonProgress.objects.get_or_create(
        user=request.user,
        lesson=lesson,
        defaults={'status': 'in_progress', 'started_at': timezone.now()}
    )

    # Update last accessed time
    lesson_progress.last_accessed = timezone.now()
    lesson_progress.save()

    # Get AI recommendations
    recommendations = ai_service.generate_personalized_recommendations(request.user, limit=3)

    # Get recent AI conversations for this lesson
    recent_conversations = AILearningAssistant.objects.filter(
        user=request.user,
        lesson=lesson
    ).order_by('-created_at')[:5]

    # Get user progress analytics
    progress_analytics = ai_service.analyze_user_progress(request.user)

    context = {
        'lesson': lesson,
        'lesson_content': lesson.lessoncontent_set.all().order_by('order', 'id'),
        'is_superuser': request.user.is_superuser,
        'can_manage_course': can_manage_course,
        'prev_lesson': prev_lesson,
        'next_lesson': next_lesson,
        'learning_profile': learning_profile,
        'lesson_progress': lesson_progress,
        'recommendations': recommendations,
        'recent_conversations': recent_conversations,
        'progress_analytics': progress_analytics,
        'ai_enabled': True,
    }
    return render(request, 'learn/lesson_details_ai.html', context)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def ai_chat(request):
    """Handle AI chat interactions"""
    try:
        data = json.loads(request.body)
        user_message = data.get('message', '').strip()
        lesson_id = data.get('lesson_id')
        interaction_type = data.get('type', 'question')

        if not user_message:
            return JsonResponse({'error': 'Message is required'}, status=400)

        # Get lesson if provided
        lesson = None
        if lesson_id:
            try:
                lesson = Lesson.objects.get(id=lesson_id)
            except Lesson.DoesNotExist:
                pass

        # Initialize AI service
        ai_service = AILearningService()

        # Prepare context
        context = {
            'user': request.user.username,
            'lesson': lesson.name if lesson else None,
            'course': lesson.chapter.course.name if lesson else None,
        }

        # Generate AI response
        ai_response = ai_service.generate_ai_response(user_message, context)

        # Save conversation
        conversation = AILearningAssistant.objects.create(
            user=request.user,
            lesson=lesson,
            interaction_type=interaction_type,
            user_message=user_message,
            ai_response=ai_response,
            context_data=context
        )

        return JsonResponse({
            'success': True,
            'response': ai_response,
            'conversation_id': conversation.id,
            'timestamp': conversation.created_at.isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def analyze_code(request):
    """AI-powered code analysis"""
    try:
        data = json.loads(request.body)
        code = data.get('code', '').strip()
        language = data.get('language', 'python').lower()
        analysis_type = data.get('analysis_type', 'comprehensive')
        lesson_id = data.get('lesson_id')

        if not code:
            return JsonResponse({'error': 'Code is required'}, status=400)

        # Get lesson if provided
        lesson = None
        if lesson_id:
            try:
                lesson = Lesson.objects.get(id=lesson_id)
            except Lesson.DoesNotExist:
                pass

        # Initialize code analysis service
        code_service = CodeAnalysisService()

        # Analyze code
        analysis_result = code_service.analyze_code(code, language, analysis_type)

        # Save analysis
        code_analysis = CodeAnalysis.objects.create(
            user=request.user,
            lesson=lesson,
            code_content=code,
            language=language,
            analysis_type=analysis_type,
            ai_feedback=analysis_result['feedback'],
            suggestions=analysis_result['best_practice_suggestions'] +
                       analysis_result['performance_tips'] +
                       analysis_result['style_improvements'],
            score=analysis_result['overall_score']
        )

        return JsonResponse({
            'success': True,
            'analysis': analysis_result,
            'analysis_id': code_analysis.id,
            'timestamp': code_analysis.created_at.isoformat()
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@csrf_exempt
@require_http_methods(["POST"])
def update_lesson_progress(request):
    """Update lesson progress and learning analytics"""
    try:
        data = json.loads(request.body)
        lesson_id = data.get('lesson_id')
        status = data.get('status', 'in_progress')
        time_spent = data.get('time_spent', 0)
        difficulty_rating = data.get('difficulty_rating')

        if not lesson_id:
            return JsonResponse({'error': 'Lesson ID is required'}, status=400)

        lesson = get_object_or_404(Lesson, id=lesson_id)

        # Update lesson progress
        progress, created = LessonProgress.objects.get_or_create(
            user=request.user,
            lesson=lesson,
            defaults={'status': status, 'started_at': timezone.now()}
        )

        progress.status = status
        progress.time_spent += int(time_spent)
        progress.attempts += 1
        progress.last_accessed = timezone.now()

        if difficulty_rating:
            progress.difficulty_rating = float(difficulty_rating)

        if status == 'completed':
            progress.completed_at = timezone.now()
            # Calculate comprehension score based on time spent and attempts
            expected_time = 15  # Default expected time in minutes
            if lesson.estimated_time:
                try:
                    expected_time = int(lesson.estimated_time.split()[0])
                except:
                    pass

            # Simple scoring algorithm
            time_efficiency = min(1.0, expected_time / max(1, progress.time_spent))
            attempt_efficiency = min(1.0, 1.0 / max(1, progress.attempts - 1))
            progress.score = (time_efficiency * 0.4 + attempt_efficiency * 0.6) * 100

        progress.save()

        # Update user learning profile
        profile = progress.user.learning_profile
        profile.total_study_time += int(time_spent)
        profile.save()

        return JsonResponse({
            'success': True,
            'progress': {
                'status': progress.status,
                'time_spent': progress.time_spent,
                'score': progress.score,
                'attempts': progress.attempts
            }
        })

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def learning_dashboard(request):
    """AI-powered learning dashboard"""
    try:
        ai_service = AILearningService()

        # Get user analytics
        progress_analytics = ai_service.analyze_user_progress(request.user)

        # Get personalized recommendations
        recommendations = ai_service.generate_personalized_recommendations(request.user, limit=6)

        # Get recent activity
        recent_progress = LessonProgress.objects.filter(
            user=request.user
        ).order_by('-last_accessed')[:10]

        # Get recent AI conversations
        recent_conversations = AILearningAssistant.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]

        # Get learning profile
        learning_profile = ai_service.get_or_create_learning_profile(request.user)

        context = {
            'progress_analytics': progress_analytics,
            'recommendations': recommendations,
            'recent_progress': recent_progress,
            'recent_conversations': recent_conversations,
            'learning_profile': learning_profile,
        }

    except Exception as e:
        # Fallback for when AI models haven't been migrated yet
        messages.warning(request, 'AI features are being set up. Please run migrations to enable full functionality.')

        # Provide basic fallback data
        context = {
            'progress_analytics': {
                'total_lessons': 0,
                'completed_lessons': 0,
                'mastered_lessons': 0,
                'completion_rate': 0,
                'mastery_rate': 0,
                'average_score': 0,
                'total_study_time': 0,
                'current_level': 'beginner',
                'learning_style': 'visual'
            },
            'recommendations': [],
            'recent_progress': [],
            'recent_conversations': [],
            'learning_profile': None,
            'setup_required': True,
        }

    return render(request, 'learn/learning_dashboard.html', context)


@login_required
def unified_dashboard(request):
    """Unified dashboard combining AI learning features and course management"""

    # Determine user role and permissions
    user_role = 'student'
    if request.user.is_superuser:
        user_role = 'admin'
    elif is_verified_mentor(request.user):
        user_role = 'mentor'

    # AI Features
    ai_features = {}
    try:
        ai_service = AILearningService()

        # Get user analytics
        progress_analytics = ai_service.analyze_user_progress(request.user)

        # Get personalized recommendations
        recommendations = ai_service.generate_personalized_recommendations(request.user, limit=6)

        # Get recent activity
        recent_progress = LessonProgress.objects.filter(
            user=request.user
        ).order_by('-last_accessed')[:10]

        # Get recent AI conversations
        recent_conversations = AILearningAssistant.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]

        # Get learning profile
        learning_profile = ai_service.get_or_create_learning_profile(request.user)

        ai_features = {
            'progress_analytics': progress_analytics,
            'recommendations': recommendations,
            'recent_progress': recent_progress,
            'recent_conversations': recent_conversations,
            'learning_profile': learning_profile,
            'ai_enabled': True,
        }

    except Exception as e:
        ai_features = {
            'ai_enabled': False,
            'setup_required': True,
            'progress_analytics': {
                'total_lessons': 0,
                'completed_lessons': 0,
                'mastered_lessons': 0,
                'completion_rate': 0,
                'mastery_rate': 0,
                'average_score': 0,
                'total_study_time': 0,
                'current_level': 'beginner',
                'learning_style': 'visual'
            },
            'recommendations': [],
            'recent_progress': [],
            'recent_conversations': [],
            'learning_profile': None,
        }

    # Course Management Features
    management_features = {}

    if user_role in ['admin', 'mentor']:
        # Get user's courses
        if user_role == 'admin':
            user_courses = Course.objects.all().order_by('-created_at')[:10]
            pending_courses = Course.objects.filter(is_approved=False, created_by__isnull=False).count()
        else:  # mentor
            user_courses = Course.objects.filter(created_by=request.user).order_by('-created_at')[:10]
            pending_courses = 0

        # Course statistics
        total_courses = Course.objects.filter(created_by=request.user).count() if user_role == 'mentor' else Course.objects.count()
        published_courses = Course.objects.filter(
            created_by=request.user if user_role == 'mentor' else None,
            status='published'
        ).count() if user_role == 'mentor' else Course.objects.filter(status='published').count()

        # Recent enrollments/activity (simplified)
        recent_lessons = Lesson.objects.filter(
            chapter__course__created_by=request.user if user_role == 'mentor' else None
        ).order_by('-id')[:5] if user_role == 'mentor' else Lesson.objects.order_by('-id')[:5]

        management_features = {
            'user_courses': user_courses,
            'pending_courses': pending_courses,
            'total_courses': total_courses,
            'published_courses': published_courses,
            'recent_lessons': recent_lessons,
            'can_manage': True,
        }
    else:
        management_features = {
            'can_manage': False,
        }

    context = {
        'user_role': user_role,
        'is_superuser': request.user.is_superuser,
        'is_verified_mentor': is_verified_mentor(request.user),
        'can_create_courses': can_create_courses(request.user),
        **ai_features,
        **management_features,
    }

    return render(request, 'learn/unified_dashboard.html', context)
