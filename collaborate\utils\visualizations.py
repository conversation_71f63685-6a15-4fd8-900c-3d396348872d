"""
Visualization utilities for the matching engine.
This module provides functions to generate visualizations for team compatibility,
timezone overlaps, and skill coverage.
"""
import json
import math
from datetime import datetime, time, timedelta
import pytz
from django.utils import timezone
from ..models import UserSkill, Project
from accounts.models import UserProfile

def generate_skill_coverage_data(project, team_members):
    """
    Generate data for a skill coverage visualization.

    Args:
        project: Project object
        team_members: List of User objects

    Returns:
        dict: Data for skill coverage visualization
    """
    # Get project required skills
    required_skills = list(project.required_skills.all())
    critical_skills = list(project.critical_skills.all())

    # Initialize skill coverage data
    skill_data = []

    # Process each required skill
    for skill in required_skills:
        # Find team members with this skill
        members_with_skill = []
        highest_proficiency = 0

        for member in team_members:
            user_skill = UserSkill.objects.filter(user=member, skill=skill).first()
            if user_skill:
                members_with_skill.append({
                    'username': member.username,
                    'proficiency': user_skill.proficiency,
                    'proficiency_name': user_skill.get_proficiency_display()
                })
                highest_proficiency = max(highest_proficiency, user_skill.proficiency)

        # Add skill data
        skill_data.append({
            'name': skill.name,
            'is_critical': skill in critical_skills,
            'covered': len(members_with_skill) > 0,
            'team_members': members_with_skill,
            'highest_proficiency': highest_proficiency,
            'redundancy': len(members_with_skill)
        })

    # Sort skills: first by covered (uncovered first), then by critical, then by name
    skill_data.sort(key=lambda x: (x['covered'], not x['is_critical'], x['name']))

    return {
        'skills': skill_data,
        'total_skills': len(required_skills),
        'covered_skills': sum(1 for skill in skill_data if skill['covered']),
        'critical_skills': len(critical_skills),
        'covered_critical': sum(1 for skill in skill_data if skill['covered'] and skill['is_critical'])
    }

def generate_timezone_overlap_data(team_members):
    """
    Generate data for timezone overlap visualization.

    Args:
        team_members: List of User objects

    Returns:
        dict: Data for timezone overlap visualization
    """
    # Get user profiles with timezone info
    user_timezones = []
    for member in team_members:
        try:
            profile = UserProfile.objects.get(user=member)
            if profile.timezone:
                user_timezones.append({
                    'username': member.username,
                    'timezone': profile.timezone,
                    'availability_start': profile.availability_start,
                    'availability_end': profile.availability_end
                })
        except UserProfile.DoesNotExist:
            continue

    # Calculate current time in each timezone
    now_utc = datetime.now(pytz.UTC)
    for user in user_timezones:
        tz = pytz.timezone(user['timezone'])
        local_time = now_utc.astimezone(tz)
        user['current_time'] = local_time.strftime('%H:%M')
        user['current_hour'] = local_time.hour

    # Calculate timezone differences matrix
    tz_matrix = []
    for user1 in user_timezones:
        row = []
        for user2 in user_timezones:
            tz1 = pytz.timezone(user1['timezone'])
            tz2 = pytz.timezone(user2['timezone'])

            # Get the current offset for each timezone
            now = datetime.now(pytz.UTC)
            tz1_offset = now.astimezone(tz1).utcoffset().total_seconds() / 3600
            tz2_offset = now.astimezone(tz2).utcoffset().total_seconds() / 3600

            # Calculate difference
            diff = abs(tz1_offset - tz2_offset)
            row.append(diff)
        tz_matrix.append(row)

    # Calculate availability overlaps
    availability_matrix = []
    for user1 in user_timezones:
        row = []
        for user2 in user_timezones:
            # Skip if any user doesn't have availability set
            if not (user1['availability_start'] and user1['availability_end'] and
                    user2['availability_start'] and user2['availability_end']):
                row.append(0)
                continue

            # Convert times to UTC for comparison
            def convert_to_utc_minutes(local_time, timezone_str):
                # Create a datetime object for today with the given time
                local_dt = datetime.combine(datetime.now().date(), local_time)
                # Localize it to the user's timezone
                local_tz = pytz.timezone(timezone_str)
                local_dt = local_tz.localize(local_dt)
                # Convert to UTC
                utc_dt = local_dt.astimezone(pytz.UTC)
                # Return minutes since midnight
                return utc_dt.hour * 60 + utc_dt.minute

            # Get start and end times in UTC minutes
            start1 = convert_to_utc_minutes(user1['availability_start'], user1['timezone'])
            end1 = convert_to_utc_minutes(user1['availability_end'], user1['timezone'])
            start2 = convert_to_utc_minutes(user2['availability_start'], user2['timezone'])
            end2 = convert_to_utc_minutes(user2['availability_end'], user2['timezone'])

            # Handle wrap-around cases
            if end1 < start1:
                end1 += 24 * 60  # Add 24 hours
            if end2 < start2:
                end2 += 24 * 60

            # Calculate overlap
            overlap_start = max(start1, start2)
            overlap_end = min(end1, end2)

            if overlap_end <= overlap_start:
                row.append(0)
            else:
                # Convert overlap from minutes to hours
                overlap_hours = (overlap_end - overlap_start) / 60
                row.append(overlap_hours)

        availability_matrix.append(row)

    # Create a more template-friendly data structure
    # Instead of a matrix that requires indexing, create a list of rows
    tz_matrix_rows = []
    for i, user in enumerate(user_timezones):
        tz_matrix_rows.append({
            'username': user['username'],
            'differences': tz_matrix[i]
        })

    availability_matrix_rows = []
    for i, user in enumerate(user_timezones):
        availability_matrix_rows.append({
            'username': user['username'],
            'overlaps': availability_matrix[i]
        })

    return {
        'users': user_timezones,
        'timezone_matrix': tz_matrix,
        'availability_matrix': availability_matrix,
        'tz_matrix_rows': tz_matrix_rows,
        'availability_matrix_rows': availability_matrix_rows
    }

def generate_team_balance_data(team_members, project):
    """
    Generate data about team balance in terms of experience levels.

    Args:
        team_members: List of User objects
        project: Project object

    Returns:
        dict: Data about team balance
    """
    # Initialize counters
    experience_levels = {
        'beginner': 0,      # Avg proficiency 1-2
        'intermediate': 0,  # Avg proficiency 2-3.5
        'expert': 0         # Avg proficiency 3.5-5
    }

    # Get project required skills
    required_skills = list(project.required_skills.all())

    # Calculate average proficiency for each team member
    member_data = []
    for member in team_members:
        # Get user skills that match project requirements
        user_skills = UserSkill.objects.filter(
            user=member,
            skill__in=required_skills
        )

        if user_skills.exists():
            # Calculate average proficiency
            total_proficiency = sum(us.proficiency for us in user_skills)
            avg_proficiency = total_proficiency / user_skills.count()

            # Determine experience level
            if avg_proficiency < 2:
                level = 'beginner'
            elif avg_proficiency < 3.5:
                level = 'intermediate'
            else:
                level = 'expert'

            experience_levels[level] += 1

            # Store member data
            member_data.append({
                'username': member.username,
                'avg_proficiency': avg_proficiency,
                'level': level,
                'skills_count': user_skills.count(),
                'required_skills_count': len(required_skills)
            })

    return {
        'experience_levels': experience_levels,
        'member_data': member_data,
        'is_balanced': all(count > 0 for count in experience_levels.values()) if len(team_members) >= 3 else True
    }
