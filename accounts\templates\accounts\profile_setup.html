<!-- filepath: d:\Project_Main\django-template\accounts\templates\accounts\profile_setup.html -->
{% extends 'base.html' %}
{% load static %}
{% block content %}

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="account-page">
  <div class="account-card fade-in">
    <h2>Complete Your Profile</h2>

    <p>
      Welcome, {{ user.username }}! Please fill in your profile information below.
      Adding skills is optional but recommended.
    </p>

    {% if messages %}
    <div class="message-container slide-right">
      {% for message in messages %}
        <div class="message-alert {% if message.tags %}{{ message.tags }}{% endif %}">
          {{ message }}
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <form method="post" enctype="multipart/form-data" class="account-form slide-left">
      {% csrf_token %}

      <!-- User Account Information Section -->
      <div class="form-section">
        <h3>Account Information</h3>

        <div class="form-group">
          <label for="{{ user_form.username.id_for_label }}">{{ user_form.username.label }}:</label>
          {{ user_form.username }}
          {% if user_form.username.errors %}
            <div class="error-message">{{ user_form.username.errors }}</div>
          {% endif %}
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="{{ user_form.first_name.id_for_label }}">{{ user_form.first_name.label }}:</label>
            {{ user_form.first_name }}
            {% if user_form.first_name.errors %}
              <div class="error-message">{{ user_form.first_name.errors }}</div>
            {% endif %}
          </div>

          <div class="form-group">
            <label for="{{ user_form.last_name.id_for_label }}">{{ user_form.last_name.label }}:</label>
            {{ user_form.last_name }}
            {% if user_form.last_name.errors %}
              <div class="error-message">{{ user_form.last_name.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="form-group">
          <label for="{{ user_form.email.id_for_label }}">{{ user_form.email.label }}:</label>
          {{ user_form.email }}
          {% if user_form.email.errors %}
            <div class="error-message">{{ user_form.email.errors }}</div>
          {% endif %}
          {% if user_form.email.help_text %}
            <small class="form-text">{{ user_form.email.help_text }}</small>
          {% endif %}
        </div>
      </div>

      <!-- Profile Information Section -->
      <div class="form-section">
        <h3>Profile Information</h3>

        <div class="form-group">
          <label for="{{ profile_form.bio.id_for_label }}">About yourself:</label>
          {{ profile_form.bio }}
          {% if profile_form.bio.errors %}
            <div class="error-message">{{ profile_form.bio.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ profile_form.profile_picture.id_for_label }}">Profile Picture:</label>
          <div class="profile-picture-upload">
            <div class="current-picture">
              {% if user_profile.profile_picture %}
                <img src="{{ user_profile.profile_picture.url }}" alt="Current profile picture" class="profile-preview">
              {% else %}
                <div class="no-picture">No profile picture</div>
              {% endif %}
            </div>
            {{ profile_form.profile_picture }}
            <small class="form-text">Upload a square image for best results. Maximum size: 5MB.</small>
          </div>
          {% if profile_form.profile_picture.errors %}
            <div class="error-message">{{ profile_form.profile_picture.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ profile_form.professional_title.id_for_label }}">{{ profile_form.professional_title.label }}:</label>
          {{ profile_form.professional_title }}
          {% if profile_form.professional_title.errors %}
            <div class="error-message">{{ profile_form.professional_title.errors }}</div>
          {% endif %}
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="{{ profile_form.phone_number.id_for_label }}">{{ profile_form.phone_number.label }}:</label>
            {{ profile_form.phone_number }}
            {% if profile_form.phone_number.errors %}
              <div class="error-message">{{ profile_form.phone_number.errors }}</div>
            {% endif %}
          </div>

          <div class="form-group">
            <label for="{{ profile_form.country.id_for_label }}">Country:</label>
            {{ profile_form.country }}
            {% if profile_form.country.errors %}
              <div class="error-message">{{ profile_form.country.errors }}</div>
            {% endif %}
          </div>
        </div>

        <div class="form-group">
          <label for="{{ profile_form.linkedin_url.id_for_label }}">{{ profile_form.linkedin_url.label }}:</label>
          {{ profile_form.linkedin_url }}
          {% if profile_form.linkedin_url.errors %}
            <div class="error-message">{{ profile_form.linkedin_url.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ profile_form.github_url.id_for_label }}">{{ profile_form.github_url.label }}:</label>
          {{ profile_form.github_url }}
          {% if profile_form.github_url.errors %}
            <div class="error-message">{{ profile_form.github_url.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ profile_form.website_url.id_for_label }}">{{ profile_form.website_url.label }}:</label>
          {{ profile_form.website_url }}
          {% if profile_form.website_url.errors %}
            <div class="error-message">{{ profile_form.website_url.errors }}</div>
          {% endif %}
        </div>
      </div>

      <!-- Skill Level Formset -->
      <div class="form-group">
        <label>Skills with Proficiency Levels: <span class="optional-label">(Optional)</span></label>

        <datalist id="skill-list">
          {% for skill in all_skills %}
            <option value="{{ skill }}">
          {% endfor %}
        </datalist>

        {{ skill_formset.management_form }}

        <div id="skill-formset" class="skill-formset">
          {% for skill_form in skill_formset %}
            <div class="skill-form-row">
              <div class="skill-name-field">
                {{ skill_form.skill }}
                {% if skill_form.skill.errors %}
                  <small class="error-message">{{ skill_form.skill.errors }}</small>
                {% endif %}
              </div>
              <div class="skill-level-field">
                {{ skill_form.level }}
                {% if skill_form.level.errors %}
                  <small class="error-message">{{ skill_form.level.errors }}</small>
                {% endif %}
              </div>
              <div class="skill-remove-btn">
                <button type="button" class="remove-skill">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          {% endfor %}
        </div>

        <div class="skill-actions">
          <button type="button" id="add-skill" class="add-skill-btn">
            <i class="fas fa-plus"></i> Add Another Skill
          </button>
        </div>

        {% if skill_formset.non_form_errors %}
          <div class="error-message">{{ skill_formset.non_form_errors }}</div>
        {% endif %}
      </div>

      <!-- Additional Profile Information Section -->
      <div class="form-section">
        <h3>Additional Information</h3>

        <div class="form-group">
          <label for="{{ profile_form.cv.id_for_label }}">Upload CV:</label>
          {{ profile_form.cv }}
          {% if profile_form.cv.errors %}
            <div class="error-message">{{ profile_form.cv.errors }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ profile_form.timezone.id_for_label }}">Timezone:</label>
          {{ profile_form.timezone }}
          {% if profile_form.timezone.errors %}
            <div class="error-message">{{ profile_form.timezone.errors }}</div>
          {% endif %}
        </div>
      </div>

        <!-- Availability Section -->
        <div class="form-section">
          <h3>Availability</h3>

          <div class="form-group">
            <label for="{{ profile_form.availability_type.id_for_label }}">Availability Type:</label>
            {{ profile_form.availability_type }}
            {% if profile_form.availability_type.errors %}
              <div class="error-message">{{ profile_form.availability_type.errors }}</div>
            {% endif %}
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="{{ profile_form.availability_start.id_for_label }}">Available From:</label>
              {{ profile_form.availability_start }}
              {% if profile_form.availability_start.errors %}
                <div class="error-message">{{ profile_form.availability_start.errors }}</div>
              {% endif %}
            </div>

            <div class="form-group">
              <label for="{{ profile_form.availability_end.id_for_label }}">Available Until:</label>
              {{ profile_form.availability_end }}
              {% if profile_form.availability_end.errors %}
                <div class="error-message">{{ profile_form.availability_end.errors }}</div>
              {% endif %}
            </div>
            
          </div>
          
          <button type="submit" class="account-btn">Save Profile</button>
        </div>
        
      </div>

    </form>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
        },
        "shape": {
          "type": ["circle", "triangle"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<style>
  /* Profile setup specific styles */
  .message-container {
    margin-bottom: 20px;
    width: 100%;
  }

  .message-alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: rgba(40, 40, 40, 0.5);
    border-left: 3px solid var(--color-border);
  }

  .message-alert.success {
    border-left-color: #4CAF50;
  }

  .message-alert.error {
    border-left-color: #f44336;
  }

  .optional-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
  }

  .skill-formset {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 15px 0;
  }

  .skill-form-row {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 15px;
    align-items: center;
    background-color: rgba(40, 40, 40, 0.3);
    padding: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .skill-form-row:hover {
    background-color: rgba(50, 50, 50, 0.5);
  }

  .skill-name-field, .skill-level-field {
    width: 100%;
  }

  .remove-skill {
    background-color: transparent;
    border: 1px solid rgba(244, 67, 54, 0.5);
    color: #f44336;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .remove-skill:hover {
    background-color: rgba(244, 67, 54, 0.2);
  }

  .skill-actions {
    margin-top: 10px;
  }

  .add-skill-btn {
    background-color: transparent;
    border: 1px solid var(--color-border);
    color: var(--color-border);
    border-radius: 20px;
    padding: 8px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .add-skill-btn:hover {
    background-color: rgba(192, 255, 107, 0.2);
  }

  .error-message {
    color: #f44336;
    font-size: 14px;
    margin-top: 5px;
  }

  /* jQuery UI Autocomplete custom styling */
  .ui-autocomplete {
    background-color: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(192, 255, 107, 0.3);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    max-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1000;
  }

  .ui-autocomplete .ui-menu-item {
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .ui-autocomplete .ui-menu-item:last-child {
    border-bottom: none;
  }

  .ui-autocomplete .ui-menu-item:hover,
  .ui-autocomplete .ui-menu-item.ui-state-focus {
    background-color: rgba(192, 255, 107, 0.2);
  }

  .ui-helper-hidden-accessible {
    display: none;
  }

  /* Form sections styling */
  .form-section {
    background-color: rgba(40, 40, 40, 0.3);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid rgba(192, 255, 107, 0.2);
    transition: all 0.3s ease;
  }

  .form-section:hover {
    border-color: rgba(192, 255, 107, 0.4);
    background-color: rgba(40, 40, 40, 0.4);
  }

  .form-section h3 {
    color: var(--color-border);
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    border-bottom: 2px solid rgba(192, 255, 107, 0.3);
    padding-bottom: 10px;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 15px;
  }

  @media (max-width: 768px) {
    .form-row {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .form-section {
      padding: 20px;
      margin-bottom: 20px;
    }
  }

  .form-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-top: 5px;
  }
</style>

<script type="text/javascript">
  $(document).ready(function() {
    // Profile picture preview
    $('#id_profile_picture').change(function() {
      const file = this.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
          $('.current-picture').html('<img src="' + e.target.result + '" class="profile-preview">');
        }
        reader.readAsDataURL(file);
      }
    });

    // Initialize skill inputs with autocomplete
    function initializeSkillAutocomplete() {
      $('.skill-form-row input[name*="skill"]').each(function() {
        $(this).autocomplete({
          source: function(request, response) {
            // Get all skills from datalist
            var skills = [];
            $('#skill-list option').each(function() {
              skills.push($(this).val());
            });

            // Filter skills based on input
            var matcher = new RegExp($.ui.autocomplete.escapeRegex(request.term), "i");
            response($.grep(skills, function(item) {
              return matcher.test(item);
            }));
          },
          minLength: 1,
          select: function(event, ui) {
            // Set the input value to the selected item
            $(this).val(ui.item.value);
            return false;
          },
          focus: function(event, ui) {
            // Update the input value on focus
            $(this).val(ui.item.value);
            return false;
          }
        });
      });
    }

    // Initialize autocomplete for existing skill inputs
    if ($.fn.autocomplete) {
      initializeSkillAutocomplete();
    } else {
      // If jQuery UI autocomplete is not available, load it dynamically
      var script = document.createElement('script');
      script.src = 'https://code.jquery.com/ui/1.12.1/jquery-ui.min.js';
      script.onload = function() {
        // Also load the CSS
        $('<link>')
          .appendTo('head')
          .attr({
            type: 'text/css',
            rel: 'stylesheet',
            href: 'https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css'
          });

        initializeSkillAutocomplete();
      };
      document.head.appendChild(script);
    }

    // Add skill form row
    $('#add-skill').click(function() {
      // Get the total number of forms
      var total = parseInt($('#id_skills-TOTAL_FORMS').val());

      // If there are existing forms, clone the first one
      if ($('.skill-form-row').length > 0) {
        // Clone the first form
        var newForm = $('.skill-form-row:first').clone(true);

        // Clear the input values
        newForm.find('input').val('');
        newForm.find('select').val(1); // Set to Beginner

        // Update form index
        newForm.find(':input').each(function() {
          var name = $(this).attr('name');
          if (name) {
            name = name.replace(/-\d+-/, '-' + total + '-');
            $(this).attr('name', name);
            $(this).attr('id', 'id_' + name);
          }
        });

        // Append to the formset
        $('#skill-formset').append(newForm);
      } else {
        // If no forms exist, create a new one from scratch
        var newForm = $('<div class="skill-form-row"></div>');

        // Create skill name field
        var skillNameField = $('<div class="skill-name-field"></div>');
        var skillInput = $('<input type="text" name="skills-' + total + '-skill" id="id_skills-' + total + '-skill" class="form-control" list="skill-list" autocomplete="off">');
        skillNameField.append(skillInput);

        // Create skill level field
        var skillLevelField = $('<div class="skill-level-field"></div>');
        var levelSelect = $('<select name="skills-' + total + '-level" id="id_skills-' + total + '-level" class="form-control"></select>');

        // Add options to the select
        levelSelect.append('<option value="1">Beginner</option>');
        levelSelect.append('<option value="2">Intermediate</option>');
        levelSelect.append('<option value="3">Advanced</option>');
        levelSelect.append('<option value="4">Expert</option>');
        levelSelect.append('<option value="5">Master</option>');

        skillLevelField.append(levelSelect);

        // Create remove button
        var removeBtn = $('<div class="skill-remove-btn"><button type="button" class="remove-skill"><i class="fas fa-times"></i></button></div>');

        // Append all elements to the form row
        newForm.append(skillNameField);
        newForm.append(skillLevelField);
        newForm.append(removeBtn);

        // Append to the formset
        $('#skill-formset').append(newForm);

        // Initialize autocomplete for the new input
        if ($.fn.autocomplete) {
          skillInput.autocomplete({
            source: function(request, response) {
              var skills = [];
              $('#skill-list option').each(function() {
                skills.push($(this).val());
              });
              var matcher = new RegExp($.ui.autocomplete.escapeRegex(request.term), "i");
              response($.grep(skills, function(item) {
                return matcher.test(item);
              }));
            },
            minLength: 1,
            select: function(event, ui) {
              $(this).val(ui.item.value);
              return false;
            },
            focus: function(event, ui) {
              $(this).val(ui.item.value);
              return false;
            }
          });
        }
      }

      // Update form count
      $('#id_skills-TOTAL_FORMS').val(total + 1);

      // Initialize autocomplete for the new input if we cloned an existing form
      if ($.fn.autocomplete && $('.skill-form-row').length > 1) {
        newForm.find('input[name*="skill"]').autocomplete({
          source: function(request, response) {
            var skills = [];
            $('#skill-list option').each(function() {
              skills.push($(this).val());
            });
            var matcher = new RegExp($.ui.autocomplete.escapeRegex(request.term), "i");
            response($.grep(skills, function(item) {
              return matcher.test(item);
            }));
          },
          minLength: 1,
          select: function(event, ui) {
            // Set the input value to the selected item
            $(this).val(ui.item.value);
            return false;
          },
          focus: function(event, ui) {
            // Update the input value on focus
            $(this).val(ui.item.value);
            return false;
          }
        });
      }
    });

    // Remove the direct event handler to avoid duplicate handlers
    // We'll use event delegation instead for all remove buttons

    // Add event delegation for remove buttons (both existing and dynamically added)
    $('#skill-formset').on('click', '.remove-skill', function() {
      var parent = $(this).closest('.skill-form-row');

      // Always allow removal, even if it's the last one
      // This ensures users can remove all skills if they want
      parent.remove();

      // Update form count
      var total = parseInt($('#id_skills-TOTAL_FORMS').val());
      $('#id_skills-TOTAL_FORMS').val(total - 1);

      // Renumber remaining forms
      $('.skill-form-row').each(function(index) {
        $(this).find(':input').each(function() {
          var name = $(this).attr('name');
          if (name) {
            name = name.replace(/-\d+-/, '-' + index + '-');
            $(this).attr('name', name);
            $(this).attr('id', 'id_' + name);
          }
        });
      });

      // If no skill forms remain, add an empty one
      if ($('.skill-form-row').length === 0) {
        $('#add-skill').click();
      }
    });
  });
</script>
{% endblock %}