"""
Team analysis utilities for the matching engine.
This module provides functions to analyze team composition, balance, and skill gaps.
"""
from collections import Counter
from django.db.models import Count, Avg, Q
from ..models import UserSkill, Project, ProjectMembership
from accounts.models import UserProfile

def analyze_team_balance(team_members, project):
    """
    Analyze the balance of a team in terms of experience levels and skill distribution.

    Args:
        team_members: List of User objects
        project: Project object

    Returns:
        dict: Analysis of team balance
    """
    # Get project required skills
    required_skills = list(project.required_skills.all())
    critical_skills = list(project.critical_skills.all())

    # Initialize counters
    experience_levels = {
        'beginner': 0,      # Avg proficiency 1-2
        'intermediate': 0,  # Avg proficiency 2-3.5
        'expert': 0         # Avg proficiency 3.5-5
    }

    # Analyze each team member
    member_data = []
    skill_coverage = {}

    for member in team_members:
        # Get user skills that match project requirements
        user_skills = UserSkill.objects.filter(
            user=member,
            skill__in=required_skills
        ).select_related('skill')

        if user_skills.exists():
            # Calculate average proficiency
            total_proficiency = sum(us.proficiency for us in user_skills)
            avg_proficiency = total_proficiency / user_skills.count()

            # Determine experience level
            if avg_proficiency < 2:
                level = 'beginner'
            elif avg_proficiency < 3.5:
                level = 'intermediate'
            else:
                level = 'expert'

            experience_levels[level] += 1

            # Track skill coverage
            for us in user_skills:
                if us.skill.id not in skill_coverage:
                    skill_coverage[us.skill.id] = []
                skill_coverage[us.skill.id].append({
                    'user_id': member.id,
                    'username': member.username,
                    'proficiency': us.proficiency
                })

            # Store member data
            member_data.append({
                'user_id': member.id,
                'username': member.username,
                'avg_proficiency': avg_proficiency,
                'level': level,
                'skills_count': user_skills.count(),
                'required_skills_count': len(required_skills),
                'skills': [{'id': us.skill.id, 'name': us.skill.name, 'proficiency': us.proficiency}
                          for us in user_skills]
            })

    # Analyze skill redundancy and single points of failure
    skill_analysis = []
    single_points_of_failure = []
    redundant_skills = []

    for skill in required_skills:
        users_with_skill = skill_coverage.get(skill.id, [])
        is_critical = skill in critical_skills

        skill_info = {
            'id': skill.id,
            'name': skill.name,
            'is_critical': is_critical,
            'coverage_count': len(users_with_skill),
            'covered': len(users_with_skill) > 0,
            'users': users_with_skill
        }

        # Check for single points of failure (critical skills with only one team member)
        if is_critical and len(users_with_skill) == 1:
            single_points_of_failure.append(skill_info)

        # Check for redundant skills (non-critical skills with many team members)
        if not is_critical and len(users_with_skill) > 2:
            redundant_skills.append(skill_info)

        skill_analysis.append(skill_info)

    # Calculate overall balance metrics
    team_size = len(team_members)
    balance_score = 0

    # Ideal distribution depends on team size
    if team_size >= 3:
        # For larger teams, we want a mix of experience levels
        has_beginner = experience_levels['beginner'] > 0
        has_intermediate = experience_levels['intermediate'] > 0
        has_expert = experience_levels['expert'] > 0

        if has_beginner and has_intermediate and has_expert:
            balance_score = 100  # Perfect balance
        elif has_intermediate and (has_beginner or has_expert):
            balance_score = 75   # Good balance
        elif has_beginner and has_expert:
            balance_score = 50   # Moderate balance
        else:
            balance_score = 25   # Poor balance
    else:
        # For smaller teams, we prioritize higher experience
        if experience_levels['expert'] >= 1:
            balance_score = 100  # At least one expert
        elif experience_levels['intermediate'] >= 1:
            balance_score = 75   # At least one intermediate
        else:
            balance_score = 50   # Only beginners

    # Adjust score based on single points of failure
    if single_points_of_failure:
        balance_score -= min(25, 5 * len(single_points_of_failure))

    return {
        'experience_levels': experience_levels,
        'member_data': member_data,
        'skill_analysis': skill_analysis,
        'single_points_of_failure': single_points_of_failure,
        'redundant_skills': redundant_skills,
        'balance_score': max(0, balance_score),
        'is_balanced': balance_score >= 75
    }

def identify_skill_gaps(team_members, project):
    """
    Identify skill gaps in a team and suggest improvements.

    Args:
        team_members: List of User objects
        project: Project object

    Returns:
        dict: Analysis of skill gaps with suggestions
    """
    # Get project required skills
    required_skills = list(project.required_skills.all())
    critical_skills = list(project.critical_skills.all())

    # Get skills covered by the team
    covered_skills = set()
    skill_proficiency = {}

    for member in team_members:
        user_skills = UserSkill.objects.filter(
            user=member,
            skill__in=required_skills
        ).select_related('skill')

        for us in user_skills:
            covered_skills.add(us.skill.id)
            if us.skill.id not in skill_proficiency:
                skill_proficiency[us.skill.id] = []
            skill_proficiency[us.skill.id].append(us.proficiency)

    # Identify missing skills
    missing_skills = []
    for skill in required_skills:
        if skill.id not in covered_skills:
            missing_skills.append({
                'id': skill.id,
                'name': skill.name,
                'is_critical': skill in critical_skills
            })

    # Identify skills with low proficiency
    low_proficiency_skills = []
    for skill_id, proficiencies in skill_proficiency.items():
        avg_proficiency = sum(proficiencies) / len(proficiencies)
        if avg_proficiency < 3:  # Below "Advanced" level
            skill = next((s for s in required_skills if s.id == skill_id), None)
            if skill:
                low_proficiency_skills.append({
                    'id': skill.id,
                    'name': skill.name,
                    'is_critical': skill in critical_skills,
                    'avg_proficiency': avg_proficiency,
                    'proficiency_level': get_proficiency_name(avg_proficiency)
                })

    # Generate suggestions
    suggestions = []

    # Suggest adding members with missing critical skills first
    critical_missing = [s for s in missing_skills if s['is_critical']]
    if critical_missing:
        suggestions.append({
            'type': 'add_member',
            'priority': 'high',
            'message': f"Add team member(s) with these critical missing skills: {', '.join(s['name'] for s in critical_missing)}"
        })

    # Suggest adding members with missing non-critical skills
    non_critical_missing = [s for s in missing_skills if not s['is_critical']]
    if non_critical_missing:
        suggestions.append({
            'type': 'add_member',
            'priority': 'medium',
            'message': f"Add team member(s) with these missing skills: {', '.join(s['name'] for s in non_critical_missing)}"
        })

    # Suggest improving low proficiency critical skills
    critical_low_prof = [s for s in low_proficiency_skills if s['is_critical']]
    if critical_low_prof:
        suggestions.append({
            'type': 'improve_skills',
            'priority': 'high',
            'message': f"Improve team proficiency in these critical skills: {', '.join(s['name'] for s in critical_low_prof)}"
        })

    return {
        'missing_skills': missing_skills,
        'low_proficiency_skills': low_proficiency_skills,
        'suggestions': suggestions,
        'has_critical_gaps': bool(critical_missing or critical_low_prof)
    }

def get_proficiency_name(level):
    """Get the name of a proficiency level."""
    if level >= 4.5:
        return "Master"
    elif level >= 3.5:
        return "Expert"
    elif level >= 2.5:
        return "Advanced"
    elif level >= 1.5:
        return "Intermediate"
    else:
        return "Beginner"

def suggest_optimal_meeting_times(team_members):
    """
    Suggest optimal meeting times based on team member availability.

    Args:
        team_members: List of User objects

    Returns:
        dict: Suggested meeting times
    """
    import pytz
    from datetime import datetime, time, timedelta

    # Get user profiles with timezone and availability info
    user_availability = []
    for member in team_members:
        try:
            profile = UserProfile.objects.get(user=member)
            if profile.timezone and profile.availability_start and profile.availability_end:
                user_availability.append({
                    'user_id': member.id,
                    'username': member.username,
                    'timezone': profile.timezone,
                    'availability_start': profile.availability_start,
                    'availability_end': profile.availability_end
                })
        except UserProfile.DoesNotExist:
            continue

    # If we don't have enough data, return empty results
    if len(user_availability) < 2:
        return {
            'has_suggestions': False,
            'message': "Not enough availability data to suggest meeting times."
        }

    # Convert all availability windows to UTC for comparison
    utc_availability = []
    for user in user_availability:
        try:
            # Create datetime objects for today with the given times
            local_tz = pytz.timezone(user['timezone'])
            today = datetime.now().date()

            # Convert start time to UTC
            start_dt = datetime.combine(today, user['availability_start'])
            start_dt = local_tz.localize(start_dt)
            start_utc = start_dt.astimezone(pytz.UTC)

            # Convert end time to UTC
            end_dt = datetime.combine(today, user['availability_end'])
            end_dt = local_tz.localize(end_dt)
            end_utc = end_dt.astimezone(pytz.UTC)

            # Handle overnight availability (end time earlier than start time)
            if user['availability_end'] < user['availability_start']:
                end_dt = datetime.combine(today + timedelta(days=1), user['availability_end'])
                end_dt = local_tz.localize(end_dt)
                end_utc = end_dt.astimezone(pytz.UTC)

            utc_availability.append({
                'username': user['username'],
                'start_utc': start_utc,
                'end_utc': end_utc
            })
        except Exception as e:
            # Skip users with invalid timezone or availability data
            continue

    # If we don't have enough valid data after conversion, return empty results
    if len(utc_availability) < 2:
        return {
            'has_suggestions': False,
            'message': "Could not process availability data. Please check timezone settings."
        }

    # Find overlapping time slots
    # We'll check each hour of the day and see how many users are available
    suggestions = []

    # Start from current UTC time and check the next 24 hours
    now_utc = datetime.now(pytz.UTC)
    start_hour = now_utc.replace(minute=0, second=0, microsecond=0)

    for hour_offset in range(24):
        check_time = start_hour + timedelta(hours=hour_offset)
        available_users = []
        unavailable_users = []

        for user in utc_availability:
            # Check if this hour is within the user's availability window
            user_start = user['start_utc']
            user_end = user['end_utc']

            # Handle overnight availability
            if user_end < user_start:
                user_end = user_end + timedelta(days=1)

            # Check if the current hour is within the user's availability
            if user_start <= check_time <= user_end or user_start <= (check_time + timedelta(hours=1)) <= user_end:
                available_users.append(user['username'])
            else:
                unavailable_users.append(user['username'])

        # If more than half the team is available, suggest this time slot
        if len(available_users) >= len(utc_availability) / 2:
            # Format the time for display
            start_time = check_time.strftime('%H:%M')
            end_time = (check_time + timedelta(hours=1)).strftime('%H:%M')

            # Create a description based on availability
            if len(available_users) == len(utc_availability):
                description = "All team members are available at this time."
            else:
                available_percent = int((len(available_users) / len(utc_availability)) * 100)
                description = f"{available_percent}% of team members are available at this time."

            suggestions.append({
                'start_time': start_time,
                'end_time': end_time,
                'description': description,
                'available_users': available_users,
                'unavailable_users': unavailable_users,
                'availability_score': len(available_users) / len(utc_availability)
            })

    # Sort suggestions by availability score (highest first)
    suggestions.sort(key=lambda x: x['availability_score'], reverse=True)

    # Take the top 3 suggestions
    top_suggestions = suggestions[:3] if suggestions else []

    return {
        'has_suggestions': bool(top_suggestions),
        'message': "No common availability found." if not top_suggestions else "",
        'suggestions': top_suggestions
    }
