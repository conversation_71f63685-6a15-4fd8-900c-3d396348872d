{% extends 'base.html' %}
{% load static %}

{% block title %}{{ mentor_profile.user.get_full_name }} - <PERSON><PERSON> Profile - ForgeX{% endblock %}

{% block content %}
<div class="mentor-detail-page">
    <!-- Mentor Profile Header -->
    <section class="mentor-header">
        <div class="container">
            <div class="mentor-profile-card">
                <div class="mentor-avatar-section">
                    <div class="mentor-avatar large">
                        {% if mentor_profile.user.profile and mentor_profile.user.profile.profile_picture %}
                            <img src="{{ mentor_profile.user.profile.profile_picture.url }}"
                                 alt="{{ mentor_profile.user.get_full_name }}"
                                 class="profile-image">
                        {% else %}
                            <div class="avatar-initials">
                                {{ mentor_profile.user.first_name|first|upper }}{{ mentor_profile.user.last_name|first|upper }}
                            </div>
                        {% endif %}
                    </div>
                    {% if mentor_profile.verified %}
                        <div class="verified-badge">
                            <i class="fas fa-check-circle"></i>
                            Verified Mentor
                        </div>
                    {% endif %}
                </div>

                <div class="mentor-info-section">
                    <h1>{{ mentor_profile.user.get_full_name|default:mentor_profile.user.username }}</h1>

                    <div class="mentor-rating">
                        {% for i in "12345" %}
                            <span class="star {% if mentor_profile.average_rating >= i|add:0 %}filled{% endif %}">★</span>
                        {% endfor %}
                        <span class="rating-text">({{ mentor_profile.average_rating }}/5.0 from {{ mentor_profile.total_sessions }} sessions)</span>
                    </div>

                    <div class="mentor-stats">
                        <div class="stat-item">
                            <span class="stat-number">{{ mentor_profile.total_sessions }}</span>
                            <span class="stat-label">Sessions Completed</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">${{ mentor_profile.hourly_rate }}</span>
                            <span class="stat-label">Per Hour</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">{{ mentor_profile.average_rating }}</span>
                            <span class="stat-label">Average Rating</span>
                        </div>
                    </div>

                    <div class="mentor-specializations">
                        {% for skill in mentor_profile.specializations.all %}
                            <span class="skill-tag">{{ skill.name }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="booking-section">
                    <div class="price-display">
                        <span class="price">${{ mentor_profile.hourly_rate }}</span>
                        <span class="price-label">per hour</span>
                    </div>
                    <button class="btn btn-primary btn-large" onclick="scrollToBooking()">
                        Book a Session
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="mentor-about">
        <div class="container">
            <div class="content-grid">
                <div class="main-content">
                    <div class="about-section">
                        <h2>About {{ mentor_profile.user.first_name }}</h2>
                        <p>{{ mentor_profile.bio|linebreaks }}</p>
                    </div>

                    <!-- Recent Feedback -->
                    {% if recent_feedback %}
                        <div class="feedback-section">
                            <h2>Recent Feedback</h2>
                            <div class="feedback-list">
                                {% for feedback in recent_feedback %}
                                    <div class="feedback-item">
                                        <div class="feedback-header">
                                            <div class="learner-info">
                                                <strong>{{ feedback.session.learner.get_full_name|default:feedback.session.learner.username }}</strong>
                                                <span class="feedback-date">{{ feedback.created_at|date:"M d, Y" }}</span>
                                            </div>
                                            <div class="feedback-rating">
                                                {% for i in "12345" %}
                                                    <span class="star {% if feedback.get_average_rating >= i|add:0 %}filled{% endif %}">★</span>
                                                {% endfor %}
                                            </div>
                                        </div>
                                        {% if feedback.additional_comments %}
                                            <p class="feedback-comment">{{ feedback.additional_comments }}</p>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Booking Sidebar -->
                <div class="booking-sidebar" id="book">
                    <div class="booking-card">
                        {% if user != mentor_profile.user %}
                            <h3>Book a Session</h3>

                            <div class="session-options">
                                <div class="session-option" data-duration="60">
                                    <div class="option-header">
                                        <span class="duration">1 Hour Session</span>
                                        <span class="price">${{ mentor_profile.hourly_rate }}</span>
                                    </div>
                                    <p>Perfect for quick questions and focused learning</p>
                                </div>

                                <div class="session-option" data-duration="120">
                                    <div class="option-header">
                                        <span class="duration">2 Hour Session</span>
                                        <span class="price">${{ mentor_profile.hourly_rate|add:mentor_profile.hourly_rate }}</span>
                                    </div>
                                    <p>Ideal for deep dives and project reviews</p>
                                </div>
                            </div>

                            {% if available_slots %}
                                <div class="available-slots">
                                    <h4>Available Times</h4>
                                    <div class="slots-list" id="slotsList">
                                        {% for slot in available_slots %}
                                            <div class="time-slot {% if forloop.counter > 8 %}hidden-slot{% endif %}" data-slot-id="{{ slot.id }}" data-duration="{{ slot.duration_hours }}">
                                                <div class="slot-info">
                                                    <span class="slot-date">{{ slot.date|date:"M d" }}</span>
                                                    <span class="slot-time">{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                </div>
                                                <span class="slot-duration">
                                                    {% if slot.duration_hours == 2 %}
                                                        <i class="fas fa-clock"></i> 2h
                                                    {% else %}
                                                        <i class="fas fa-clock"></i> 1h
                                                    {% endif %}
                                                </span>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    {% if available_slots|length > 8 %}
                                        <button class="show-more-btn" onclick="toggleMoreSlots()">
                                            <span class="show-more-text">Show More Slots</span>
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            {% else %}
                                <div class="no-slots">
                                    <p>No available slots at the moment. Please check back later.</p>
                                </div>
                            {% endif %}

                            <button class="btn btn-primary btn-full" onclick="bookSession()" id="bookButton" disabled>
                                Select Time & Duration
                            </button>

                            <div class="payment-info">
                                <i class="fas fa-info-circle"></i>
                                <span>Payment will be required to confirm your booking</span>
                            </div>

                            {% if not user.is_authenticated %}
                                <p class="login-note">
                                    <a href="{% url 'accounts:login' %}">Login</a> to book a session
                                </p>
                            {% endif %}
                        {% else %}
                            <h3>Your Profile</h3>
                            <div class="own-profile-message">
                                <i class="fas fa-user-circle" style="font-size: 3rem; color: #C0ff6b; margin-bottom: 1rem;"></i>
                                <p>This is your mentor profile. You cannot book sessions with yourself.</p>
                                <a href="{% url 'mentorship:my_sessions' %}" class="btn btn-primary btn-full">
                                    <i class="fas fa-calendar-alt"></i>
                                    View My Sessions
                                </a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.mentor-detail-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.mentor-header {
    background: linear-gradient(135deg, #1c1c1c 0%, #656565 100%);
    color: #ffffff;
    padding: 3rem 0;
    border-bottom: 2px solid #C0ff6b;
}

.mentor-profile-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 2rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.mentor-avatar-section {
    text-align: center;
}

.mentor-avatar.large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #000000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    overflow: hidden;
    position: relative;
    border: 3px solid #C0ff6b;
}

.mentor-avatar.large .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mentor-avatar.large .avatar-initials {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.verified-badge {
    background: rgba(192,255,107,0.2);
    color: #C0ff6b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid #C0ff6b;
}

.mentor-info-section h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
    color: #C0ff6b;
    text-shadow: 0 0 10px rgba(192,255,107,0.3);
}

.mentor-rating {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.star {
    color: #ccc;
    font-size: 1.2rem;
    margin-right: 0.2rem;
}

.star.filled {
    color: #ffd700;
}

.rating-text {
    margin-left: 0.5rem;
    opacity: 0.9;
    color: #d5d5d5;
}

.mentor-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: #C0ff6b;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    color: #d5d5d5;
}

.mentor-specializations {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: rgba(192,255,107,0.2);
    color: #C0ff6b;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid rgba(192,255,107,0.4);
}

.booking-section {
    text-align: center;
}

.price-display {
    margin-bottom: 1.5rem;
}

.price {
    display: block;
    font-size: 3rem;
    font-weight: 700;
    color: #C0ff6b;
    text-shadow: 0 0 15px rgba(192,255,107,0.4);
}

.price-label {
    opacity: 0.9;
    color: #d5d5d5;
}

.mentor-about {
    padding: 4rem 0;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
}

.about-section h2,
.feedback-section h2 {
    color: #C0ff6b;
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.about-section {
    margin-bottom: 3rem;
}

.feedback-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feedback-item {
    background: rgba(255,255,255,0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(192,255,107,0.2);
}

.feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.feedback-date {
    opacity: 0.7;
    font-size: 0.9rem;
    margin-left: 0.5rem;
}

.feedback-rating .star {
    font-size: 1rem;
}

.booking-card {
    background: rgba(255,255,255,0.05);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(192,255,107,0.2);
    position: sticky;
    top: 2rem;
}

.booking-card h3 {
    color: #C0ff6b;
    margin-bottom: 1.5rem;
    text-align: center;
}

.session-options {
    margin-bottom: 2rem;
}

.session-option {
    background: rgba(255,255,255,0.05);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.session-option:hover,
.session-option.selected {
    border-color: #C0ff6b;
    background: rgba(192,255,107,0.1);
}

.option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.duration {
    font-weight: 600;
    color: #C0ff6b;
}

.session-option .price {
    font-weight: 700;
    color: #ffffff;
}

.available-slots h4 {
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.slots-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.time-slot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255,255,255,0.05);
    border-radius: 6px;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.3s ease;
}

.time-slot:hover {
    border-color: rgba(192,255,107,0.5);
    background: rgba(192,255,107,0.05);
}

.slot-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.slot-date {
    font-weight: 600;
    color: #C0ff6b;
}

.slot-time {
    font-size: 0.9rem;
    opacity: 0.8;
}

.slot-duration {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    font-weight: 600;
    color: #C0ff6b;
    background: rgba(192,255,107,0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.time-slot.compatible {
    border-color: rgba(192,255,107,0.3);
    background: rgba(192,255,107,0.02);
}

.time-slot.compatible:hover {
    border-color: rgba(192,255,107,0.6);
    background: rgba(192,255,107,0.08);
}

.time-slot.incompatible {
    opacity: 0.4;
    border-color: rgba(255,107,107,0.3);
    background: rgba(255,107,107,0.02);
    cursor: not-allowed;
}

.time-slot.incompatible:hover {
    border-color: rgba(255,107,107,0.5);
    background: rgba(255,107,107,0.05);
}

/* Selected state should override compatible/incompatible */
.time-slot.selected {
    border-color: #C0ff6b !important;
    background: rgba(192,255,107,0.15) !important;
    box-shadow: 0 0 10px rgba(192,255,107,0.3) !important;
    opacity: 1 !important;
}

.time-slot.hidden-slot {
    display: none !important;
}

.show-more-btn {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.6);
    border-radius: 6px;
    color: #C0ff6b;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    font-weight: 600;
}

.show-more-btn:hover {
    background: rgba(192,255,107,0.15);
    border-color: #C0ff6b;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(192,255,107,0.2);
}

.show-more-btn i {
    transition: transform 0.3s ease;
}

.show-more-btn.expanded i {
    transform: rotate(180deg);
}

/* Custom scrollbar for slots list */
.slots-list::-webkit-scrollbar {
    width: 6px;
}

.slots-list::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
    border-radius: 3px;
}

.slots-list::-webkit-scrollbar-thumb {
    background: rgba(192,255,107,0.3);
    border-radius: 3px;
}

.slots-list::-webkit-scrollbar-thumb:hover {
    background: rgba(192,255,107,0.5);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-primary:disabled,
.btn-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: linear-gradient(135deg, #666, #555) !important;
    color: #ccc !important;
    box-shadow: none !important;
}

.btn-primary:disabled:hover,
.btn-disabled:hover {
    transform: none;
    background: linear-gradient(135deg, #666, #555) !important;
    box-shadow: none !important;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-full {
    width: 100%;
}

.login-note {
    text-align: center;
    margin-top: 1rem;
    opacity: 0.8;
}

.login-note a {
    color: #C0ff6b;
    text-decoration: none;
}

.payment-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(192,255,107,0.1);
    border: 1px solid rgba(192,255,107,0.2);
    border-radius: 6px;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
}

.payment-info i {
    color: #C0ff6b;
}

.own-profile-message {
    text-align: center;
    padding: 2rem 0;
}

.own-profile-message p {
    margin-bottom: 1.5rem;
    opacity: 0.8;
    line-height: 1.5;
}

.own-profile-message .btn {
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .mentor-profile-card {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mentor-stats {
        justify-content: center;
    }
}
</style>

<script>
let selectedDuration = null;
let selectedSlot = null;

document.addEventListener('DOMContentLoaded', function() {
    // Store original duration badges
    storeOriginalDurations();

    // Initialize slots display
    highlightCompatibleSlots();

    // Session option selection
    document.querySelectorAll('.session-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.session-option').forEach(opt => opt.classList.remove('selected'));
            this.classList.add('selected');
            selectedDuration = parseInt(this.dataset.duration);
            console.log('Selected duration:', selectedDuration);

            // Clear slot selection and highlight compatible slots
            selectedSlot = null;
            document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));
            highlightCompatibleSlots();
            updateBookButton();
        });
    });

    // Time slot selection
    document.querySelectorAll('.time-slot').forEach(slot => {
        slot.addEventListener('click', function() {
            // Skip if slot is hidden (incompatible slots are hidden now)
            if (this.style.display === 'none') {
                return;
            }

            // Remove selected class from all slots
            document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));

            // Add selected class to clicked slot
            this.classList.add('selected');
            selectedSlot = parseInt(this.dataset.slotId);

            const slotDuration = parseInt(this.dataset.duration);
            console.log('Selected slot:', selectedSlot, 'Duration:', slotDuration + 'h');
            updateBookButton();
        });
    });
});

function storeOriginalDurations() {
    // Store original duration text and time display for each slot
    document.querySelectorAll('.time-slot').forEach(slot => {
        const durationBadge = slot.querySelector('.slot-duration');
        const timeDisplay = slot.querySelector('.slot-time');

        if (durationBadge && !slot.dataset.originalDuration) {
            slot.dataset.originalDuration = durationBadge.innerHTML;
        }

        if (timeDisplay && !slot.dataset.originalTime) {
            slot.dataset.originalTime = timeDisplay.textContent;
        }
    });
}

function restoreOriginalDurations() {
    // Restore original duration text and time display for each slot
    document.querySelectorAll('.time-slot').forEach(slot => {
        const durationBadge = slot.querySelector('.slot-duration');
        const timeDisplay = slot.querySelector('.slot-time');

        if (durationBadge && slot.dataset.originalDuration) {
            durationBadge.innerHTML = slot.dataset.originalDuration;
        }

        if (timeDisplay && slot.dataset.originalTime) {
            timeDisplay.textContent = slot.dataset.originalTime;
        }
    });
}

function updateTimeDisplay(slot, selectedDurationHours) {
    const timeDisplay = slot.querySelector('.slot-time');
    if (!timeDisplay || !slot.dataset.originalTime) return;

    // Parse the original time range
    const originalTime = slot.dataset.originalTime;
    const timeMatch = originalTime.match(/(\d{1,2}:\d{2}\s*[AP]M)\s*-\s*(\d{1,2}:\d{2}\s*[AP]M)/);

    if (timeMatch) {
        const startTime = timeMatch[1].trim();
        const endTime = timeMatch[2].trim();

        // If selected duration is less than slot duration, calculate new end time
        const slotDuration = parseInt(slot.dataset.duration);

        if (selectedDurationHours < slotDuration) {
            // Calculate new end time based on selected duration
            const newEndTime = calculateEndTime(startTime, selectedDurationHours);
            timeDisplay.textContent = `${startTime} - ${newEndTime}`;
        } else {
            // Use original time range
            timeDisplay.textContent = originalTime;
        }
    }
}

function calculateEndTime(startTimeStr, durationHours) {
    // Parse start time
    const timeMatch = startTimeStr.match(/(\d{1,2}):(\d{2})\s*([AP]M)/);
    if (!timeMatch) return startTimeStr;

    let hours = parseInt(timeMatch[1]);
    const minutes = parseInt(timeMatch[2]);
    const ampm = timeMatch[3];

    // Convert to 24-hour format
    if (ampm === 'PM' && hours !== 12) hours += 12;
    if (ampm === 'AM' && hours === 12) hours = 0;

    // Add duration
    hours += durationHours;

    // Convert back to 12-hour format
    let newAmPm = 'AM';
    if (hours >= 12) {
        newAmPm = 'PM';
        if (hours > 12) hours -= 12;
    }
    if (hours === 0) hours = 12;

    // Format the time
    const formattedHours = hours.toString();
    const formattedMinutes = minutes.toString().padStart(2, '0');

    return `${formattedHours}:${formattedMinutes} ${newAmPm}`;
}

function highlightCompatibleSlots() {
    if (!selectedDuration) {
        // Show all slots when no duration is selected and restore original durations
        restoreOriginalDurations();
        document.querySelectorAll('.time-slot').forEach(slot => {
            // Only show if not in hidden-slot class or if show more is expanded
            if (!slot.classList.contains('hidden-slot')) {
                slot.style.display = 'flex';
            }
            slot.classList.remove('compatible', 'incompatible');
        });
        updateMoreSlotsMessage();
        return;
    }

    const selectedDurationHours = selectedDuration / 60;

    document.querySelectorAll('.time-slot').forEach(slot => {
        const slotDuration = parseInt(slot.dataset.duration);

        // Show slots that can accommodate the selected duration
        // For 1-hour sessions: show both 1h and 2h slots (2h slots can accommodate 1h sessions)
        // For 2-hour sessions: show only 2h slots
        if (slotDuration >= selectedDurationHours) {
            // Only show if not in hidden-slot class or if show more is expanded
            if (!slot.classList.contains('hidden-slot')) {
                slot.style.display = 'flex';
            }
            slot.classList.add('compatible');
            slot.classList.remove('incompatible');

            // Update the display to show the selected duration instead of slot duration
            const durationBadge = slot.querySelector('.slot-duration');
            if (durationBadge) {
                durationBadge.innerHTML = `<i class="fas fa-clock"></i> ${selectedDurationHours}h`;
            }

            // Update the time display to show the correct duration
            updateTimeDisplay(slot, selectedDurationHours);
        } else {
            // Hide incompatible slots
            slot.style.display = 'none';
            slot.classList.add('incompatible');
            slot.classList.remove('compatible');
        }
    });

    updateMoreSlotsMessage();
}

function updateMoreSlotsMessage() {
    // This function is no longer needed as we use the show more button
    // But keeping it for compatibility
}

function toggleMoreSlots() {
    const hiddenSlots = document.querySelectorAll('.time-slot.hidden-slot');
    const showMoreBtn = document.querySelector('.show-more-btn');
    const showMoreText = document.querySelector('.show-more-text');

    if (hiddenSlots.length > 0) {
        // Check if any hidden slots are currently visible
        const isExpanded = Array.from(hiddenSlots).some(slot =>
            !slot.classList.contains('hidden-slot') ||
            slot.style.display === 'flex'
        );

        if (isExpanded) {
            // Collapse: hide slots beyond the first 8
            hiddenSlots.forEach(slot => {
                slot.classList.add('hidden-slot');
                slot.style.display = 'none';
            });
            showMoreText.textContent = 'Show More Slots';
            showMoreBtn.classList.remove('expanded');
        } else {
            // Expand: show all slots
            hiddenSlots.forEach(slot => {
                slot.classList.remove('hidden-slot');
                if (!selectedDuration || slot.style.display !== 'none') {
                    slot.style.display = 'flex';
                }
            });
            showMoreText.textContent = 'Show Less';
            showMoreBtn.classList.add('expanded');
        }

        // Re-apply duration filtering if a duration is selected
        if (selectedDuration) {
            highlightCompatibleSlots();
        }
    }
}

function updateBookButton() {
    const button = document.getElementById('bookButton');
    console.log('Updating button - Duration:', selectedDuration, 'Slot:', selectedSlot);

    if (selectedDuration && selectedSlot) {
        button.disabled = false;
        button.classList.remove('btn-disabled');
        const hours = selectedDuration / 60;
        button.textContent = `Book & Pay for ${hours} Hour${hours > 1 ? 's' : ''} Session`;
    } else if (selectedDuration && !selectedSlot) {
        button.disabled = true;
        button.classList.add('btn-disabled');
        button.textContent = 'Select a Compatible Time Slot';
    } else if (!selectedDuration && selectedSlot) {
        button.disabled = true;
        button.classList.add('btn-disabled');
        button.textContent = 'Select Session Duration';
    } else {
        button.disabled = true;
        button.classList.add('btn-disabled');
        button.textContent = 'Select Time & Duration';
    }
}

function scrollToBooking() {
    document.getElementById('book').scrollIntoView({ behavior: 'smooth' });
}

async function bookSession() {
    if (!selectedDuration || !selectedSlot) {
        alert('Please select both duration and time slot');
        return;
    }

    {% if not user.is_authenticated %}
        window.location.href = '{% url "accounts:login" %}';
        return;
    {% endif %}

    try {
        const response = await fetch('{% url "mentorship:book_session" mentor_profile.id %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                availability_id: selectedSlot,
                duration_minutes: selectedDuration
            })
        });

        const result = await response.json();

        if (result.success) {
            // Redirect to payment page
            window.location.href = result.redirect_url;
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}
</script>
{% endblock %}
