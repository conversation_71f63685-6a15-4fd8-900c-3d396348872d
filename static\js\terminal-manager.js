/**
 * Terminal Manager - Handles multiple terminal instances with WebSocket connections
 * 
 * This class manages multiple terminal tabs and their WebSocket connections
 * for real-time terminal functionality in the editor.
 */
class TerminalManager {
    constructor(projectHash) {
        this.projectHash = projectHash;
        this.terminals = new Map(); // Map of terminal_id -> terminal instance
        this.activeTerminalId = null;
        this.terminalCounter = 0;
        
        // DOM elements
        this.terminalContainer = document.getElementById('terminal-container');
        this.terminalTabsContainer = document.getElementById('terminal-tabs');
        this.addTerminalBtn = document.getElementById('add-terminal-btn');
        
        this.init();
    }
    
    init() {
        // Set up event listeners
        if (this.addTerminalBtn) {
            this.addTerminalBtn.addEventListener('click', () => this.createNewTerminal());
        }
        
        // Create first terminal
        this.createNewTerminal();
    }
    
    createNewTerminal() {
        this.terminalCounter++;
        const terminalId = `terminal-${this.terminalCounter}-${Date.now()}`;
        
        // Create terminal container
        const terminalDiv = document.createElement('div');
        terminalDiv.id = `terminal-content-${terminalId}`;
        terminalDiv.className = 'terminal-content';
        terminalDiv.style.display = 'none';
        this.terminalContainer.appendChild(terminalDiv);
        
        // Create terminal tab
        const tab = this.createTerminalTab(terminalId);
        
        // Create terminal instance
        const terminal = new TerminalInstance(terminalId, this.projectHash);
        
        // Store terminal
        this.terminals.set(terminalId, {
            instance: terminal,
            element: terminalDiv,
            tab: tab,
            active: false
        });
        
        // Switch to new terminal
        this.switchToTerminal(terminalId);
        
        return terminalId;
    }
    
    createTerminalTab(terminalId) {
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminalId;
        
        const tabLabel = document.createElement('span');
        tabLabel.textContent = `Terminal ${this.terminalCounter}`;
        tabLabel.className = 'terminal-tab-label';
        
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.className = 'terminal-tab-close';
        closeBtn.title = 'Close terminal';
        
        tab.appendChild(tabLabel);
        tab.appendChild(closeBtn);
        
        // Event listeners
        tab.addEventListener('click', (e) => {
            if (!e.target.classList.contains('terminal-tab-close')) {
                this.switchToTerminal(terminalId);
            }
        });
        
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTerminal(terminalId);
        });
        
        // Insert before the add button
        this.terminalTabsContainer.insertBefore(tab, this.addTerminalBtn);
        
        return tab;
    }
    
    switchToTerminal(terminalId) {
        // Hide all terminals
        this.terminals.forEach((terminal, id) => {
            terminal.element.style.display = 'none';
            terminal.tab.classList.remove('active');
            terminal.active = false;
            terminal.instance.deactivate();
        });
        
        // Show selected terminal
        const terminal = this.terminals.get(terminalId);
        if (terminal) {
            terminal.element.style.display = 'block';
            terminal.tab.classList.add('active');
            terminal.active = true;
            terminal.instance.activate();
            this.activeTerminalId = terminalId;
        }
    }
    
    closeTerminal(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;
        
        // Don't close if it's the last terminal
        if (this.terminals.size <= 1) {
            console.log('Cannot close the last terminal');
            return;
        }
        
        // Clean up terminal instance
        terminal.instance.close();
        
        // Remove DOM elements
        terminal.element.remove();
        terminal.tab.remove();
        
        // Remove from map
        this.terminals.delete(terminalId);
        
        // Switch to another terminal if this was active
        if (this.activeTerminalId === terminalId) {
            const remainingTerminals = Array.from(this.terminals.keys());
            if (remainingTerminals.length > 0) {
                this.switchToTerminal(remainingTerminals[0]);
            }
        }
    }
    
    getActiveTerminal() {
        return this.terminals.get(this.activeTerminalId);
    }
    
    executeCommandInActive(command) {
        const activeTerminal = this.getActiveTerminal();
        if (activeTerminal) {
            activeTerminal.instance.executeCommand(command);
        }
    }
    
    clearActiveTerminal() {
        const activeTerminal = this.getActiveTerminal();
        if (activeTerminal) {
            activeTerminal.instance.clear();
        }
    }
    
    resizeAllTerminals() {
        this.terminals.forEach(terminal => {
            terminal.instance.resize();
        });
    }
    
    closeAllTerminals() {
        this.terminals.forEach((terminal, terminalId) => {
            terminal.instance.close();
            terminal.element.remove();
            terminal.tab.remove();
        });
        this.terminals.clear();
        this.activeTerminalId = null;
    }
}

/**
 * Enhanced Terminal Instance with WebSocket connection to Django ASGI server
 */
class TerminalInstance {
    constructor(terminalId, projectHash) {
        this.terminalId = terminalId;
        this.projectHash = projectHash;
        this.containerId = `terminal-content-${terminalId}`;
        this.isActive = false;
        this.isInitialized = false;
        this.socket = null;
        this.term = null;
        this.fitAddon = null;
        
        // Terminal options
        this.options = {
            theme: {
                background: '#1e1e1e',
                foreground: '#ffffff',
                cursor: '#ffffff',
                selection: 'rgba(255, 255, 255, 0.3)',
                black: '#000000',
                red: '#e06c75',
                green: '#98c379',
                yellow: '#e5c07b',
                blue: '#61afef',
                magenta: '#c678dd',
                cyan: '#56b6c2',
                white: '#dcdfe4'
            },
            fontSize: 14,
            fontFamily: 'Consolas, "Courier New", monospace',
            cursorBlink: true,
            scrollback: 1000,
            convertEol: true
        };
    }
    
    initialize() {
        if (this.isInitialized) return;
        
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Terminal container #${this.containerId} not found`);
            return;
        }
        
        // Create xterm.js terminal
        this.term = new Terminal(this.options);
        
        // Create fit addon
        this.fitAddon = new FitAddon.FitAddon();
        this.term.loadAddon(this.fitAddon);
        
        // Open terminal in container
        this.term.open(container);
        
        // Connect to WebSocket
        this.connectWebSocket();
        
        // Add resize handler
        this.resizeHandler = () => this.resize();
        window.addEventListener('resize', this.resizeHandler);
        
        // Initial resize
        this.resize();
        
        this.isInitialized = true;
    }
    
    connectWebSocket() {
        // Use port 8001 for ASGI server
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//localhost:8001/ws/terminal/${this.projectHash}/${this.terminalId}/`;
        
        // Close existing connection
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
            this.socket.close();
        }
        
        // Create new WebSocket connection
        this.socket = new WebSocket(wsUrl);
        
        this.socket.onopen = () => {
            this.term.writeln('\r\n\x1b[32m✓ Terminal connected\x1b[0m\r\n');
            
            // Handle terminal input
            this.term.onData(data => {
                if (this.socket.readyState === WebSocket.OPEN) {
                    this.socket.send(JSON.stringify({
                        type: 'input',
                        data: data
                    }));
                }
            });
        };
        
        this.socket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                if (message.type === 'output') {
                    this.term.write(message.data);
                }
            } catch (e) {
                // Fallback for raw data
                this.term.write(event.data);
            }
        };
        
        this.socket.onerror = (error) => {
            console.error('Terminal WebSocket Error:', error);
            this.term.writeln('\r\n\x1b[31m✗ Connection error\x1b[0m\r\n');
        };
        
        this.socket.onclose = () => {
            this.term.writeln('\r\n\x1b[33m⚠ Connection closed\x1b[0m\r\n');
            
            // Attempt reconnection after delay
            if (this.isActive) {
                setTimeout(() => {
                    this.term.writeln('\r\nReconnecting...\r\n');
                    this.connectWebSocket();
                }, 3000);
            }
        };
    }
    
    activate() {
        this.isActive = true;
        if (!this.isInitialized) {
            this.initialize();
        } else {
            this.resize();
            this.term.focus();
        }
    }
    
    deactivate() {
        this.isActive = false;
    }
    
    resize() {
        if (this.isInitialized && this.fitAddon) {
            try {
                this.fitAddon.fit();
                
                // Send resize info to server
                if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                    const { cols, rows } = this.term;
                    this.socket.send(JSON.stringify({
                        type: 'resize',
                        data: { cols, rows }
                    }));
                }
            } catch (e) {
                console.warn('Error fitting terminal:', e);
            }
        }
    }
    
    clear() {
        if (this.isInitialized) {
            this.term.clear();
        }
    }
    
    executeCommand(command) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify({
                type: 'command',
                command: command
            }));
        }
    }
    
    close() {
        // Remove resize handler
        if (this.resizeHandler) {
            window.removeEventListener('resize', this.resizeHandler);
        }
        
        // Close WebSocket
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
            this.socket.close();
        }
        
        // Dispose terminal
        if (this.term) {
            try {
                this.term.dispose();
            } catch (e) {
                console.warn('Error disposing terminal:', e);
            }
        }
        
        this.isInitialized = false;
        this.isActive = false;
    }
}
