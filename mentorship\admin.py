from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    MentorProfile, MentorshipSession, SessionFeedback, MentorAvailability,
    SessionChatMessage, SessionChatAttachment
)

@admin.register(MentorProfile)
class MentorProfileAdmin(admin.ModelAdmin):
    list_display = [
        'user', 'hourly_rate', 'total_sessions', 'average_rating',
        'total_earnings', 'is_active', 'verified', 'created_at'
    ]
    list_filter = ['is_active', 'verified', 'profile_completed', 'created_at']
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'user__email']
    readonly_fields = ['total_sessions', 'average_rating', 'total_earnings', 'created_at', 'updated_at']
    filter_horizontal = ['specializations']

    fieldsets = (
        ('Basic Information', {
            'fields': ('user', 'bio', 'hourly_rate')
        }),
        ('Specializations', {
            'fields': ('specializations',)
        }),
        ('Availability', {
            'fields': ('available_slots',)
        }),
        ('Status & Verification', {
            'fields': ('is_active', 'verified', 'profile_completed')
        }),
        ('Statistics', {
            'fields': ('total_sessions', 'average_rating', 'total_earnings'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user').prefetch_related('specializations')

@admin.register(MentorshipSession)
class MentorshipSessionAdmin(admin.ModelAdmin):
    list_display = [
        'id', 'learner', 'mentor', 'scheduled_time', 'duration_display',
        'status', 'total_amount', 'is_paid', 'rating'
    ]
    list_filter = ['status', 'is_paid', 'duration_minutes', 'scheduled_time', 'created_at']
    search_fields = [
        'learner__username', 'learner__email',
        'mentor__username', 'mentor__email',
        'room_id'
    ]
    readonly_fields = [
        'room_id', 'total_amount', 'commission_amount', 'mentor_earnings',
        'created_at', 'updated_at', 'session_link'
    ]
    date_hierarchy = 'scheduled_time'

    fieldsets = (
        ('Session Details', {
            'fields': ('learner', 'mentor', 'scheduled_time', 'duration_minutes', 'status')
        }),
        ('Room & Access', {
            'fields': ('room_id', 'session_link')
        }),
        ('Pricing', {
            'fields': ('hourly_rate', 'total_amount', 'commission_amount', 'mentor_earnings')
        }),
        ('Payment', {
            'fields': ('is_paid', 'payment_intent_id')
        }),
        ('Session Content', {
            'fields': ('session_notes', 'learner_feedback', 'mentor_feedback', 'rating'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'started_at', 'ended_at'),
            'classes': ('collapse',)
        }),
    )

    def duration_display(self, obj):
        return f"{obj.duration_minutes} min"
    duration_display.short_description = "Duration"

    def session_link(self, obj):
        if obj.room_id:
            url = reverse('mentorship:session_room', args=[obj.room_id])
            return format_html('<a href="{}" target="_blank">Open Session Room</a>', url)
        return "No room assigned"
    session_link.short_description = "Session Room"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('learner', 'mentor')

@admin.register(SessionFeedback)
class SessionFeedbackAdmin(admin.ModelAdmin):
    list_display = [
        'session', 'communication_rating', 'knowledge_rating',
        'helpfulness_rating', 'would_recommend', 'created_at'
    ]
    list_filter = [
        'communication_rating', 'knowledge_rating', 'helpfulness_rating',
        'would_recommend', 'created_at'
    ]
    search_fields = [
        'session__learner__username', 'session__mentor__username',
        'what_went_well', 'areas_for_improvement'
    ]
    readonly_fields = ['created_at', 'average_rating_display']

    fieldsets = (
        ('Session', {
            'fields': ('session',)
        }),
        ('Ratings', {
            'fields': (
                'communication_rating', 'knowledge_rating', 'helpfulness_rating',
                'would_recommend', 'average_rating_display'
            )
        }),
        ('Detailed Feedback', {
            'fields': ('what_went_well', 'areas_for_improvement', 'additional_comments')
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )

    def average_rating_display(self, obj):
        return f"{obj.get_average_rating()}/5.0"
    average_rating_display.short_description = "Average Rating"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('session__learner', 'session__mentor')

@admin.register(MentorAvailability)
class MentorAvailabilityAdmin(admin.ModelAdmin):
    list_display = ['mentor', 'date', 'start_time', 'end_time', 'is_booked', 'session']
    list_filter = ['is_booked', 'date', 'created_at']
    search_fields = ['mentor__username', 'mentor__email']
    readonly_fields = ['created_at']
    date_hierarchy = 'date'

    fieldsets = (
        ('Availability Details', {
            'fields': ('mentor', 'date', 'start_time', 'end_time')
        }),
        ('Booking Status', {
            'fields': ('is_booked', 'session')
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('mentor', 'session')


class SessionChatAttachmentInline(admin.TabularInline):
    """Inline admin for session chat attachments"""
    model = SessionChatAttachment
    extra = 0
    readonly_fields = ['uploaded_at', 'file_size']


@admin.register(SessionChatMessage)
class SessionChatMessageAdmin(admin.ModelAdmin):
    """Admin for session chat messages with history"""
    list_display = [
        'session', 'sender', 'message_preview', 'message_type', 'created_at'
    ]
    list_filter = ['message_type', 'created_at', 'session__status']
    search_fields = [
        'session__room_id', 'sender__username', 'message',
        'session__learner__username', 'session__mentor__username'
    ]
    readonly_fields = ['created_at']
    inlines = [SessionChatAttachmentInline]
    date_hierarchy = 'created_at'

    fieldsets = (
        ('Message Details', {
            'fields': ('session', 'sender', 'message', 'message_type')
        }),
        ('Timestamp', {
            'fields': ('created_at',)
        }),
    )

    def message_preview(self, obj):
        """Show preview of message content"""
        return obj.message[:50] + "..." if len(obj.message) > 50 else obj.message
    message_preview.short_description = "Message"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'session', 'sender', 'session__learner', 'session__mentor'
        )


@admin.register(SessionChatAttachment)
class SessionChatAttachmentAdmin(admin.ModelAdmin):
    """Admin for session chat attachments"""
    list_display = [
        'message', 'filename', 'file_size_display', 'uploaded_at'
    ]
    list_filter = ['uploaded_at']
    search_fields = ['filename', 'message__session__room_id']
    readonly_fields = ['uploaded_at', 'file_size']

    fieldsets = (
        ('Attachment Details', {
            'fields': ('message', 'file', 'filename', 'file_size')
        }),
        ('Timestamp', {
            'fields': ('uploaded_at',)
        }),
    )

    def file_size_display(self, obj):
        """Display file size in human readable format"""
        size = obj.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    file_size_display.short_description = "File Size"

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('message__session')
