{% extends 'base.html' %}

{% block title %}Notifications{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/notifications.css' %}">
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="card-title mb-0">
                            <i class="fas fa-bell me-2"></i> Notifications
                            <span id="notification-status" class="bg-secondary" title="Notification status"></span>
                        </h1>
                    </div>

                    <div>
                        {% if user.is_staff %}
                        <a href="{% url 'collaborate:send_system_notification' %}" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-bullhorn"></i> Send System Notification
                        </a>
                        {% endif %}

                        {% if has_unread_notifications %}
                        <form method="post" class="d-inline">
                            {% csrf_token %}
                            <input type="hidden" name="mark_all_read" value="true">
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-check-double"></i> Mark All as Read
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>

                <div class="card-body">
                    {% if messages %}
                        <div class="alert alert-info">
                            {% for message in messages %}
                                {{ message }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    {% if notifications %}
                        <div class="list-group">
                            {% for notification in notifications %}
                                <div class="notification-list-item list-group-item list-group-item-action {{ notification.notification_type }} {% if not notification.is_read %}unread{% endif %}">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">
                                            <i class="
                                                {% if notification.notification_type == 'info' %}fas fa-info-circle
                                                {% elif notification.notification_type == 'success' %}fas fa-check-circle
                                                {% elif notification.notification_type == 'warning' %}fas fa-exclamation-triangle
                                                {% elif notification.notification_type == 'error' %}fas fa-times-circle
                                                {% elif notification.notification_type == 'system' %}fas fa-bullhorn
                                                {% elif notification.notification_type == 'invitation' %}fas fa-user-plus
                                                {% else %}fas fa-bell
                                                {% endif %}
                                            "></i>
                                            {{ notification.title }}
                                            {% if not notification.is_read %}
                                                <span class="badge bg-danger notification-badge-animated">New</span>
                                            {% endif %}
                                        </h5>
                                        <small>{{ notification.created_at|date:"M d, Y H:i" }}</small>
                                    </div>
                                    <p class="mb-1">{{ notification.message }}</p>

                                    <div class="d-flex mt-2">
                                        {% if notification.is_accepted is None and notification.project %}
                                            <!-- Invitation that requires response -->
                                            <form method="post" action="{% url 'collaborate:handle_invite' notification.id %}" class="me-2">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="accept">
                                                <button type="submit" class="btn btn-success btn-sm">
                                                    <i class="fas fa-check"></i> Accept
                                                </button>
                                            </form>
                                            <form method="post" action="{% url 'collaborate:handle_invite' notification.id %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="decline">
                                                <button type="submit" class="btn btn-danger btn-sm">
                                                    <i class="fas fa-times"></i> Decline
                                                </button>
                                            </form>
                                        {% elif notification.is_accepted is not None %}
                                            {% if notification.is_accepted %}
                                                <div class="alert alert-success mt-1 mb-1 py-1">
                                                    <i class="fas fa-check-circle"></i> You have accepted this project invitation!
                                                </div>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    {{ notification.get_is_accepted_display|title }}
                                                </span>
                                            {% endif %}
                                        {% endif %}

                                        <!-- Project link if applicable -->
                                        {% if notification.project %}
                                            {% if notification.is_accepted %}
                                                <a href="{% url 'collaborate:project_detail' notification.project.id %}" class="btn btn-primary btn-sm ms-auto">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                            {% else %}
                                                <a href="{% url 'collaborate:project_detail' notification.project.id %}" class="btn btn-outline-primary btn-sm ms-auto">
                                                    <i class="fas fa-project-diagram"></i> View Project
                                                </a>
                                            {% endif %}
                                        {% endif %}

                                        <!-- Mark as read button for unread notifications -->
                                        {% if not notification.is_read %}
                                            <form method="post" class="ms-2">
                                                {% csrf_token %}
                                                <input type="hidden" name="notification_id" value="{{ notification.id }}">
                                                <input type="hidden" name="mark_read" value="true">
                                                <button type="submit" class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-check"></i> Mark as Read
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No notifications yet.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // Initialize notification connection status indicator
    document.addEventListener('DOMContentLoaded', function() {
        const statusIndicator = document.getElementById('notification-status');

        // Check WebSocket connection status
        if (window.notificationSocket && window.notificationSocket.readyState === WebSocket.OPEN) {
            statusIndicator.classList.remove('bg-secondary', 'bg-danger');
            statusIndicator.classList.add('bg-success');
            statusIndicator.title = 'Notification service connected';
        } else {
            statusIndicator.classList.remove('bg-secondary', 'bg-success');
            statusIndicator.classList.add('bg-danger');
            statusIndicator.title = 'Notification service disconnected';
        }
    });
</script>
{% endblock %}