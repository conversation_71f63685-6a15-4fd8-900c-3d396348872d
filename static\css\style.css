@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&display=swap');


:root {
  /* color pallete */
  --color-bg: #000000;
  --color-container: #656565;
  --color-border: #C0ff6b;
  --color-secondary: #d5d5d5;


  /* typegraphy */
  --head-one-font-family: OpenSans, sans-serif;
  --head-one-font-size: 48px;
  --head-one-line-height: normal;
  --head-one-font-weight: 700;
  --head-one-font-style: normal;
  --head-two-font-family: OpenSans, sans-serif;
  --head-two-font-size: 32px;
  --head-two-line-height: normal;
  --head-two-font-weight: 700;
  --head-two-font-style: normal;
  --head-three-font-family: OpenSans, sans-serif;
  --head-three-font-size: 24px;
  --head-three-line-height: normal;
  --head-three-font-weight: 700;
  --head-three-font-style: normal;
  --text-body-font-family: OpenSans, sans-serif;
  --text-body-font-size: 16px;
  --text-body-line-height: normal;
  --text-body-font-weight: 400;
  --text-body-font-style: normal;
  --text-smallbody-font-family: OpenSans, sans-serif;
  --text-smallbody-font-size: 10px;
  --text-smallbody-line-height: normal;
  --text-smallbody-font-weight: 400;
  --text-smallbody-font-style: normal;
}

.header-one {
  font-family: var(--head-one-font-family);
  font-size: var(--head-one-font-size);
  line-height: var(--head-one-line-height);
  font-weight: var(--head-one-font-weight);
  font-style: var(--head-one-font-style);
}

.header-second {
  font-family: var(--head-two-font-family);
  font-size: var(--head-two-font-size);
  line-height: var(--head-two-line-height);
  font-weight: var(--head-two-font-weight);
  font-style: var(--head-two-font-style);
}

.header-third {
  font-family: var(--head-three-font-family);
  font-size: var(--head-three-font-size);
  line-height: var(--head-three-line-height);
  font-weight: var(--head-three-font-weight);
  font-style: var(--head-three-font-style);
}

.text-body-main {
  font-family: var(--text-body-font-family);
  font-size: var(--text-body-font-size);
  line-height: var(--text-body-line-height);
  font-weight: var(--text-body-font-weight);
  font-style: var(--text-body-font-style);
}

.text-body-common {
  font-family: var(--text-smallbody-font-family);
  font-size: var(--text-smallbody-font-size);
  line-height: var(--text-smallbody-line-height);
  font-weight: var(--text-smallbody-font-weight);
  font-style: var(--text-smallbody-font-style);
}














* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  overflow-x: hidden;
}

body {
  background-color: #1c1c1c;
  height: 100%;
  width: 100%;
  color: #fff;
  font-family: OpenSans, sans-serif;
}


.tile-wrap {

  max-width: 1536px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  position: relative;
  /* border: red 2px solid; */
}

/* debug */
/* .border-container {
  border: yellow 2px solid;
} */

/* base.html styling  */

.header-main {
   background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(28,28,28,0.95) 100%);
   backdrop-filter: blur(10px);
   width: 100%;
   height: 80px;
   border-bottom: 1px solid rgba(192, 255, 107, 0.2);
   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
   position: sticky;
   top: 0;
   z-index: 1000;
 }

 .header-main .tile-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 2rem;
 }

 .logo-container {
  display: flex;
  align-items: center;
 }

.logo-container .logo-anchor {
  color: white;
  -webkit-text-stroke: 0.5px var(--color-border);
  text-decoration: none;
  text-shadow: 0 0 5px var(--color-border),
               0 0 10px var(--color-border),
               0 0 15px var(--color-border);
  animation: glowBlink 2s infinite alternate;
  transition: all 0.3s ease;
  font-weight: 800;
  letter-spacing: 1px;
  white-space: nowrap;
  display: inline-block;
}

.logo-container .logo-anchor:hover {
  transform: scale(1.05);
  text-shadow: 0 0 8px var(--color-border),
               0 0 15px var(--color-border),
               0 0 25px var(--color-border);
}

/* Animation for glow and blink */
@keyframes glowBlink {
  0% {
    text-shadow: 0 0 5px var(--color-border),
                 0 0 10px var(--color-border),
                 0 0 15px var(--color-border);
    opacity: 1;
  }
  50% {
    text-shadow: 0 0 8px var(--color-border),
                 0 0 15px var(--color-border),
                 0 0 25px var(--color-border);
    opacity: 1;
  }
  100% {
    text-shadow: 0 0 5px var(--color-border),
                 0 0 10px var(--color-border),
                 0 0 15px var(--color-border);
    opacity: 0.8;
  }
}

.navbar {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  margin: 0 2rem;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 2.5rem;
  justify-content: center;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-item .nav-link {
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.nav-item .nav-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-border), #fff);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--color-border);
  transform: translateY(-2px);
  background: rgba(192, 255, 107, 0.1);
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.2);
}

.nav-link:hover::before {
  width: 100%;
}

/* Notification item normal styling */
.notification-item .nav-link {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  width: auto;
  height: auto;
  justify-content: center;
}

.notification-item .nav-link:hover {
  background: rgba(192, 255, 107, 0.1);
  transform: translateY(-2px);
}

.notification-item .fas.fa-bell {
  font-size: 1rem;
  margin-right: 0;
}

.profile-nav {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  list-style: none;
}

.profile-nav .nav-item .nav-link {
  background: linear-gradient(135deg, var(--color-border), #a8e063);
  color: #000;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
}

.profile-nav .nav-item .nav-link:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.4);
  background: linear-gradient(135deg, #a8e063, var(--color-border));
}

.profile-nav .nav-item .nav-link i {
  margin-right: 0.5rem;
}

/* Notification badge improvements */
.notification-item .position-relative .badge {
  font-size: 0.65rem;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #1c1c1c;
  top: -5px !important;
  right: -5px !important;
}

/* Active navigation state */
.nav-item.active .nav-link,
.nav-item .nav-link.active {
  color: var(--color-border);
  background: rgba(192, 255, 107, 0.15);
}

.nav-item.active .nav-link::before,
.nav-item .nav-link.active::before {
  width: 100%;
}

/* Hamburger Menu Styles */
.hamburger-menu {
  display: none;
  flex-direction: column;
  justify-content: space-around;
  width: 30px;
  height: 30px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 1001;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.hamburger-menu:hover {
  background: rgba(192, 255, 107, 0.1);
}

.hamburger-menu:focus {
  outline: 2px solid var(--color-border);
  outline-offset: 2px;
}

.hamburger-line {
  width: 100%;
  height: 3px;
  background-color: var(--color-border);
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-menu.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-menu.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Body scroll lock when mobile menu is open */
body.nav-open {
  overflow: hidden;
}

/* Enhanced header animations and transitions */
.header-main {
  transition: all 0.3s ease;
}

.header-main.scrolled {
  background: linear-gradient(135deg, rgba(0,0,0,0.95) 0%, rgba(28,28,28,0.98) 100%);
  backdrop-filter: blur(15px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

.navbar {
  transition: all 0.3s ease;
}

.profile-nav {
  transition: all 0.3s ease;
}

/* Enhanced Responsive Design for All Breakpoints */

/* 2XL Breakpoint - 1536px */
@media (max-width: 1536px) {
  .header-main .tile-wrap {
    padding: 0 1.5rem;
  }

  .navbar-nav {
    gap: 2.2rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.8rem;
  }
}

/* XL Breakpoint - 1280px */
@media (max-width: 1280px) {
  .header-main .tile-wrap {
    padding: 0 1.2rem;
  }

  .navbar-nav {
    gap: 1.8rem;
  }

  .nav-item .nav-link {
    padding: 0.5rem 0.9rem;
    font-size: 0.95rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.7rem;
  }
}

/* LG Breakpoint - 1024px */
@media (max-width: 1024px) {
  .navbar-nav {
    gap: 1.2rem;
  }

  .header-main .tile-wrap {
    padding: 0 0.8rem;
  }

  .navbar {
    margin: 0 0.5rem;
  }

  .nav-item .nav-link {
    padding: 0.4rem 0.7rem;
    font-size: 0.88rem;
  }

  .profile-nav .nav-item .nav-link {
    padding: 0.5rem 0.9rem;
    font-size: 0.82rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.5rem;
    white-space: nowrap;
  }

  .notification-item .nav-link {
    padding: 0.4rem 0.6rem;
  }
}

/* MD Breakpoint - 768px */
@media (max-width: 768px) {
  .header-main {
    height: 65px;
  }

  .header-main .tile-wrap {
    padding: 0 0.6rem;
  }

  .navbar {
    margin: 0 0.3rem;
  }

  .navbar-nav {
    gap: 0.8rem;
  }

  .nav-item .nav-link {
    padding: 0.35rem 0.6rem;
    font-size: 0.82rem;
  }

  .profile-nav .nav-item .nav-link {
    padding: 0.45rem 0.7rem;
    font-size: 0.78rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.3rem;
    white-space: nowrap;
  }

  .notification-item .nav-link {
    padding: 0.35rem 0.6rem;
    width: auto;
    height: auto;
  }

  /* Tablet landscape optimizations */
  .navbar-nav {
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .navbar-nav::-webkit-scrollbar {
    display: none;
  }
}

/* SM Breakpoint - 640px */
@media (max-width: 640px) {
  .header-main {
    height: 60px;
  }

  .header-main .tile-wrap {
    padding: 0 0.5rem;
  }

  .navbar {
    margin: 0 0.2rem;
  }

  .navbar-nav {
    gap: 0.6rem;
  }

  .nav-item .nav-link {
    padding: 0.3rem 0.5rem;
    font-size: 0.78rem;
  }

  .profile-nav .nav-item .nav-link {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.2rem;
    white-space: nowrap;
  }

  .notification-item .nav-link {
    padding: 0.3rem 0.5rem;
  }
}

/* XS Breakpoint - 475px */
@media (max-width: 475px) {
  .header-main {
    height: 58px;
  }

  .header-main .tile-wrap {
    padding: 0 0.4rem;
  }

  .navbar {
    margin: 0 0.1rem;
  }

  .navbar-nav {
    gap: 0.4rem;
  }

  .nav-item .nav-link {
    padding: 0.25rem 0.4rem;
    font-size: 0.75rem;
  }

  .profile-nav .nav-item .nav-link {
    padding: 0.35rem 0.5rem;
    font-size: 0.72rem;
  }

  .logo-container .logo-anchor {
    font-size: 1.1rem;
    white-space: nowrap;
  }

  .notification-item .nav-link {
    padding: 0.25rem 0.4rem;
  }
}

/* Mobile Hamburger Breakpoint - 468px */
@media (max-width: 468px) {
  .hamburger-menu {
    display: flex;
    position: relative;
    z-index: 1002;
  }

  .header-main {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1002;
  }

  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, rgba(0,0,0,0.98) 0%, rgba(28,28,28,0.98) 100%);
    backdrop-filter: blur(20px);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1001;
    margin: 0;
    overflow-y: auto;
    border-right: 2px solid rgba(192, 255, 107, 0.3);
    padding-top: 80px;
  }

  .navbar.active {
    transform: translateX(0);
  }

  .navbar-nav {
    flex-direction: column;
    padding: 1rem;
    gap: 0;
    height: calc(100vh - 160px);
    justify-content: flex-start;
  }

  .nav-item {
    width: 100%;
    border-bottom: 1px solid rgba(192, 255, 107, 0.1);
  }

  .nav-item .nav-link {
    display: block;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: #fff;
    border-radius: 0;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
  }

  .nav-item .nav-link:hover {
    background: rgba(192, 255, 107, 0.1);
    border-left-color: var(--color-border);
    transform: translateX(5px);
  }

  .notification-item .nav-link {
    display: flex;
    align-items: center;
    gap: 0.8rem;
  }

  .notification-item .fas.fa-bell {
    font-size: 1.1rem;
  }

  .profile-nav {
    position: fixed;
    bottom: -80px;
    left: 0;
    width: 100%;
    background: rgba(28, 28, 28, 0.95);
    border-top: 2px solid rgba(192, 255, 107, 0.3);
    padding: 1rem;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1001;
  }

  .profile-nav.mobile-active {
    transform: translateX(0);
  }

  .profile-nav .nav-item .nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--color-border), #a8e063);
    color: #000;
    border-radius: 25px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .header-main .tile-wrap {
    justify-content: space-between;
    padding: 0 1rem;
    height: 60px;
  }

  .logo-container .logo-anchor {
    font-size: 1.1rem;
  }

  /* Add padding to main content to account for fixed header */
  main {
    padding-top: 60px;
    margin-top: -60px;
  }
}

/* Ultra Small Mobile - 320px */
@media (max-width: 320px) {
  .header-main .tile-wrap {
    padding: 0 0.5rem;
  }

  .logo-container .logo-anchor {
    font-size: 1rem;
  }

  .hamburger-menu {
    width: 25px;
    height: 25px;
  }

  .hamburger-line {
    height: 2px;
  }
}


/* login page */

.card-signup .tile-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  gap: 4vh;
}

.google-auth {
  width: 30%;
  display: flex;
  justify-content: center;
  align-self: center;
  background-color: var(--color-container);
  gap: 1vw;
  height: 50px;
  border-radius: 15px;
  transition: 0.4s ease-in-out all;
}

.google-button {
  text-decoration: none;
  color: #fff;
  transition: 0.3s ease-in-out all;
}

.google-signin-holder {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1vw;
}

.google-auth:hover {
  color: var(--color-border);
  transform: scale(1.1);
  box-shadow: 0 0 5px var(--color-border),
               0 0 10px var(--color-border),
               0 0 15px var(--color-border);
}


.form-signup {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2vh;
  width: 40%;
}

.form-control {
  width: 100%;
  height: 50px;
  background-color: transparent;
  border-radius: 15px;
  padding-left: 1vw;
  border: none;
  color: #fff;
  border: white 1px solid;
  transition: 0.3s ease-in-out all;

}

.form-control:hover {
  box-shadow: 0 0 5px var(--color-border),
               0 0 10px var(--color-border),
               0 0 15px var(--color-border);
}




.form-group {
  display: flex;
  justify-content: center;
  align-items: start;
  flex-direction: column;
  gap: 1vh;
  width: 100%;
  color: #fff;

}

.account-exist {
  text-decoration: underline;
  color: #fff;
  transition: 0.3s ease-in-out all;
}

.account-exist:hover {
  color: var(--color-border);
  transform: scale(1.1);

}

.form-submit {
  width: 120px;
  height: 40px;
  background-color: var(--color-container);
  border: none;
  border-radius: 15px;
  color: #fff;
  transition: 0.4s ease-in-out all;
}

.form-submit:hover {
  background-color: var(--color-border);
  color: #000;
  transform: scale(1.1);
  box-shadow: 0 0 5px var(--color-border),
               0 0 10px var(--color-border),
               0 0 15px var(--color-border);
}


/* signup page */

.text-container-signup {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2vw;
  flex-direction: column;
}


/* profile page styles */

.profile-page-container .tile-wrap {
  display: flex;
  justify-content: space-between;
  align-items:flex-start;
  height: 100vh;
  /* gap: 4vh; */


}

/* home.html styling  */


  .hero {
    display: flex;
    align-items: center;
    gap: 26px;
    padding: 64px 100px;
  }

  .hero-content {
    max-width: 576px;
    display: flex;
    flex-direction: column;
    flex-wrap: row;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 38px;
  }

  .hero-content h1 {
    font-size: 48px;
    font-weight: 700;
  }

  .hero-content h2 {
    font-size: 24px;
    font-weight: 700;
  }

  .hero-image {
    position: relative;
    max-width: 844px;
    height: 634px;
  }

  .platform-interface {
    position: relative;
  }

  .platform-interface img {
    width: 724px;
    height: 589px;
    object-fit: cover;
  }

  .user-profiles {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 10px;
  }

  .user-profiles img {
    width: 54px;
    height: 55px;
    border-radius: 50%;
  }


  /* Features Section */
  .features {
    padding: 64px 100px;
  }

  .feature-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 90px;
    margin-bottom: 32px;
  }

  .feature-content {
    max-width: 500px;
  }

  .feature-content h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 26px;
  }

  .feature-image {
    flex-shrink: 0;
  }

  .feature-image .img {
    width: 674px;
    height: 674px;
    object-fit: cover;
  }

  .collaboration-image {
    width: 673px;
    height: 439px;
    border: 3px solid #1a1b26;
    border-radius: 10px;
    background: url('/picture.png') 50% 50% / cover;
  }

  /* Testimonials Section */
  .testimonials {
    padding: 64px 100px;
    text-align: center;
  }

  .testimonials h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 32px;
  }

  .testimonials-grid {
    display: flex;
    gap: 32px;
    justify-content: center;
  }

  .testimonial-card {
    position: relative;
    max-width: 376px;
  }

  .quote-open {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
  }

  .testimonial-content {
    background: var(--bg-container);
    padding: 40px 30px;
    margin: 10px 0 0 26px;
  }

  .quote-close {
    position: absolute;
    /* bottom: 64px; */
    left: 380px;
    width: 20px;
    height: 20px;
  }

  .testimonial-author {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 16px;
  }

  .testimonial-author img {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    margin-bottom: 8px;
  }

  /* Footer */
  .footer {
    padding: 64px 100px;
    background: transparent;
  }

  .footer-content {
    display: flex;
    justify-content: space-between;
    gap: 32px;
    margin-bottom: 32px;
  }

  .footer-section {
    max-width: 407px;
  }

  .footer-section h3 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 25px;
  }

  .footer-section nav {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .footer-section a {
    color: white;
    text-decoration: none;
  }

  .footer-section a:hover {
    text-decoration: underline;
  }

  .social-icons {
    display: flex;
    gap: 24px;
    margin-top: 25px;
  }

  .icon {
    width: 20px;
    height: 20px;
    background-size: contain;
    background-repeat: no-repeat;
  }

  .footer-separator {
    height: 2px;
    background: rgba(255, 255, 255, 0.2);
    margin: 32px 0;
  }

  .footer-copyright {
    text-align: center;
    font-size: 10px;
  }

/* Enhanced Footer Styles */
.main-footer {
  background: linear-gradient(135deg, rgba(28, 28, 28, 0.95), rgba(40, 40, 40, 0.95));
  backdrop-filter: blur(15px);
  border-top: 2px solid rgba(192, 255, 107, 0.3);
  padding: 60px 0 30px;
  margin-top: 80px;
  position: relative;
  overflow: hidden;
}

.main-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(192, 255, 107, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(192, 255, 107, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.footer-section h3 {
  color: #ffffff;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.footer-section:hover h3::after {
  width: 60px;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-nav a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 0;
}

.footer-nav a:hover {
  color: var(--color-border);
  padding-left: 10px;
  transform: translateX(5px);
}

.footer-nav a::before {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 1px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.footer-nav a:hover::before {
  width: 8px;
}

.footer-link-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.footer-link-disabled:hover {
  color: rgba(255, 255, 255, 0.5) !important;
  padding-left: 0 !important;
  transform: none !important;
}

.footer-link-disabled:hover::before {
  width: 0 !important;
}

/* Newsletter Section */
.newsletter-section {
  max-width: 300px;
}

.newsletter-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 20px 0;
}

.newsletter-form {
  margin-bottom: 30px;
}

.newsletter-input-group {
  display: flex;
  border-radius: 25px;
  overflow: hidden;
  border: 1px solid rgba(192, 255, 107, 0.3);
  background-color: rgba(40, 40, 40, 0.8);
  transition: all 0.3s ease;
}

.newsletter-input-group:focus-within {
  border-color: var(--color-border);
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
}

.newsletter-input {
  flex: 1;
  background: transparent;
  border: none;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 14px;
  outline: none;
}

.newsletter-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.newsletter-btn {
  background-color: var(--color-border);
  border: none;
  padding: 12px 16px;
  color: #000000;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.newsletter-btn:hover {
  background-color: #a8e055;
  transform: scale(1.05);
}

.newsletter-btn i {
  font-size: 14px;
}

/* Social Links */
.social-links h4 {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 15px 0;
}

.social-icons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 50%;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-border);
  transform: scale(0);
  transition: transform 0.3s ease;
  border-radius: 50%;
}

.social-link:hover::before {
  transform: scale(1);
}

.social-link:hover {
  color: #000000;
  border-color: var(--color-border);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.social-link i {
  position: relative;
  z-index: 1;
  font-size: 16px;
}

/* Footer Bottom */
.footer-separator {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.3), transparent);
  margin: 40px 0 30px;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.footer-bottom-left {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-logo a {
  color: var(--color-border);
  font-size: 24px;
  font-weight: 700;
  text-decoration: none;
  transition: all 0.3s ease;
}

.footer-logo a:hover {
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
  transform: scale(1.05);
}

.copyright-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin: 0;
}

.footer-bottom-right {
  display: flex;
  align-items: center;
}

.footer-legal {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-legal a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
}

.footer-legal a:hover {
  color: var(--color-border);
}

.footer-legal a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.footer-legal a:hover::after {
  width: 100%;
}

/* Footer Responsive Design */
@media (max-width: 768px) {
  .main-footer {
    padding: 40px 0 20px;
    margin-top: 60px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .footer-section {
    align-items: center;
  }

  .footer-section h3::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .footer-section:hover h3::after {
    width: 60px;
  }

  .newsletter-section {
    max-width: 100%;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .footer-bottom-left {
    flex-direction: column;
    gap: 10px;
  }

  .footer-legal {
    justify-content: center;
    gap: 15px;
  }

  .social-icons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-legal {
    flex-direction: column;
    gap: 10px;
  }

  .newsletter-input-group {
    flex-direction: column;
    border-radius: 12px;
  }

  .newsletter-input {
    border-bottom: 1px solid rgba(192, 255, 107, 0.3);
  }

  .newsletter-btn {
    border-radius: 0 0 12px 12px;
    padding: 15px;
  }
}

  @media (max-width: 1024px) {
    .main-nav,
    .hero,
    .features,
    .testimonials,
    .footer {
      padding-left: 16px;
      padding-right: 16px;
    }

    .hero,
    .feature-card {
      flex-direction: column;
    }

    .feature-image img,
    .collaboration-image {
      width: 100%;
      height: auto;
    }

    .testimonials-grid {
      flex-direction: column;
      align-items: center;
    }

    .footer-content {
      flex-direction: column;
    }
  }













































/* breakpoints  */

@media (max-width: 1536px) {


  .tile-wrap {
    max-width: 1024px;
  }

}


@media (max-width: 1280px) {

  .tile-wrap {
    max-width: 1024px;
  }

}


@media (max-width: 1024px) {

  .tile-wrap {
    max-width: 768px;
  }

}

@media (max-width: 768px) {
.tile-wrap {
    max-width: 640px;
  }
}

@media (max-width: 640px) {
.tile-wrap {
    max-width: 475px;
  }
}


@media (max-width: 475px) {
.tile-wrap {
    max-width: 400px;
  }
}












































































































































/*  editor styles  */
:root {
  --primary-color: #4361ee;
  --primary-dark: #3a56d4;
  --secondary-color: #3f37c9;
  --dark-bg: #1a1a2e;
  --darker-bg: #16213e;
  --editor-bg: #0f3460;
  --sidebar-bg: #1a1a2e;
  --text-color: #e6e6e6;
  --text-muted: #b8b8b8;
  --border-color: #2d4059;
  --success-color: #4cc9f0;
  --error-color: #f72585;
  --warning-color: #f8961e;
  --terminal-bg: #0d1117;
  --terminal-text: #c9d1d9;
}




.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.editor-header {
  padding: 12px 20px;
  background-color: var(--darker-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.editor-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 500;
}

.editor-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.editor-controls select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--darker-bg);
  color: var(--text-color);
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 120px;
}

.editor-controls button {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  font-family: inherit;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.2s ease;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-success {
  background-color: var(--success-color);
  color: #111;
}

.btn-info {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary {
  background-color: var(--darker-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color) !important;
}

.btn-sm {
  padding: 6px 10px !important;
  font-size: 0.8rem !important;
}

.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* File explorer enhanced styling */
.file-explorer {
  width: 250px;
  background-color: #1a1a2e;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex-shrink: 0;
}

.file-explorer-header {
  padding: 12px 15px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-explorer-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--text-color);
}

.file-controls {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.file-controls button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex: 1;
}

.file-controls button:hover {
  background-color: var(--primary-dark);
}

.file-controls button i {
  font-size: 12px;
}

.file-search {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--dark-bg);
  color: var(--text-color);
  font-family: inherit;
}

.file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

/* File and folder items with enhanced icons */
.file-item, .folder-item {
  padding: 6px 10px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border-radius: 3px;
  margin: 2px 5px;
}

.file-item:hover, .folder-item:hover {
  background-color: rgba(67, 97, 238, 0.15);
}

.folder-item.has-children {
  position: relative;
}

.folder-item i {
  color: #ffd166;
  transition: transform 0.2s ease;
  width: 18px;
  text-align: center;
}

.folder-item i.toggle-icon {
  color: #aaa;
  font-size: 10px;
  margin-right: -2px;
  width: 10px;
}

.file-item i {
  width: 18px;
  text-align: center;
}

/* File type specific icons */
.file-item[data-ext="py"] i {
  color: #4584b6; /* Python blue */
}

.file-item[data-ext="js"] i {
  color: #f7df1e; /* JavaScript yellow */
}

.file-item[data-ext="html"] i {
  color: #e34c26; /* HTML orange */
}

.file-item[data-ext="css"] i {
  color: #264de4; /* CSS blue */
}

.file-item[data-ext="json"] i {
  color: #5b5b5b; /* JSON gray */
}

.file-item[data-ext="md"] i {
  color: #42b883; /* Markdown green */
}

.file-item[data-ext="jpg"],
.file-item[data-ext="png"],
.file-item[data-ext="gif"],
.file-item[data-ext="svg"] {
  color: #ff9f43; /* Image files orange */
}

.folder-children {
  padding-left: 16px;
  display: none;
  border-left: 1px dashed rgba(255, 255, 255, 0.1);
  margin-left: 4px;
}

.folder-children.expanded {
  display: block;
}

.expanded > i.fa-folder:before {
  content: "\f07c"; /* fa-folder-open */
}

.editor-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: #34495e;
}

#monaco-editor {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  flex: 1;
  height: 70%;
  background-color: #1e272e;
}

.panel-container {
  height: 30%;
  background-color: var(--darker-bg);
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: #34495e;
}

.panel-tab {
  padding: 10px 20px;
  background: none;
  border: none;
  color: var(--text-muted);
  font-family: inherit;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  flex: 1;
  text-align: center;
  color: white;
}

.panel-tab:hover {
  color: var(--text-color);
  background-color: rgba(255, 255, 255, 0.05);
}

.panel-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: rgba(67, 97, 238, 0.1);
  background-color: #1abc9c;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: none;
  overflow-y: auto;
  padding: 10px;
  /* background-color: black; */
  color: white;
}

.panel-content.active {
  display: flex;
  flex-direction: column;
  display: block;
}

.panel-header {
  padding: 8px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 500;
}

#output-content {
  flex: 1;
  padding: 10px 15px;
  overflow-y: auto;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  white-space: pre-wrap;
  background-color: black;
}

#output-content div {
  margin-bottom: 5px;
}

#terminal-container {
  flex: 1;
  padding: 5px;
  background-color: var(--terminal-bg);
  height: 100%;
  background-color: #1e272e;
  color: #ffffff;
}

.xterm {
  padding: 10px;
  height: 100%;
}

.context-menu {
  position: absolute;
  background-color: var(--darker-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  display: none;
  min-width: 180px;
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 5px 0;
}

.context-menu li {
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu li:hover {
  background-color: rgba(67, 97, 238, 0.2);
}

.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: var(--darker-bg);
  border-radius: 6px;
  width: 400px;
  max-width: 90%;
  padding: 20px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
}

.modal-content h2 {
  margin: 0 0 20px;
  font-size: 1.3rem;
  font-weight: 500;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.close-modal {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 1.5rem;
  color: var(--text-muted);
  cursor: pointer;
  transition: color 0.2s ease;
  background: none;
  border: none;
}

.close-modal:hover {
  color: var(--text-color);
}

#file-name-input {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--dark-bg);
  color: var(--text-color);
  font-family: inherit;
  margin-bottom: 5px;
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--sidebar-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

@media (max-width: 1200px) {
  .editor-main {
    flex-direction: column;
  }

  .file-explorer {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }
}

@media (max-width: 768px) {
  .editor-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 10px;
  }

  .editor-controls {
    width: 100%;
    flex-wrap: wrap;
  }

  .panel-container {
    height: 200px;
  }

  .editor-controls select,
  .editor-controls button {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
}

.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #2c3e50;
  color: white;
}

.editor-controls button {
  margin-left: 5px;
}

.editor-main {
  display: flex;
  flex: 1;
}

.editor-content {
  flex: 1;
  background-color: #34495e;
}

.panel-container {
  display: flex;
  flex-direction: column;
  height: 30%;
  background-color: #2c3e50;
}

.panel-tabs {
  display: flex;
  background-color: #34495e;
}

.panel-tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  color: white;
}

.panel-tab.active {
  background-color: #1abc9c;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  color: white;
}

.panel-content.active {
  display: block;
}

.panel-content:not(.active) {
  display: none;
}

.file-search {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--dark-bg);
  color: var(--text-color);
  font-family: inherit;
}

.file-tabs {
  display: flex;
  align-items: center;
  background-color: var(--darker-bg);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
  white-space: nowrap;
}

.file-tab {
  padding: 8px 15px;
  cursor: pointer;
  background-color: var(--sidebar-bg);
  color: var(--text-muted);
  border-right: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.file-tab.active {
  background-color: var(--primary-color);
  color: white;
}

.file-tab:hover {
  background-color: var(--primary-dark);
}

/* Terminal styling */
#terminal-container {
  height: 250px;
  background: #222;
  color: #eee;
  padding: 10px;
  border-radius: 4px 4px 0 0;
  font-family: 'Consolas', 'Monaco', monospace;
  overflow-y: auto;
  white-space: pre;
  font-size: 14px;
  line-height: 1.5;
}

#terminal-container.focused {
  border: 1px solid #007bff;
}

#terminal-container div {
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

#terminal-input-div {
  display: flex;
  background: #222;
  color: #eee;
  padding: 5px 10px;
  border-radius: 0 0 4px 4px;
  margin-top: 0;
  border-top: 1px solid #444;
  width: 100%;
  box-sizing: border-box;
}

#terminal-prompt {
  color: #0f0;
  margin-right: 5px;
  font-family: 'Consolas', 'Monaco', monospace;
  white-space: nowrap;
  font-size: 14px;
}

#terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #eee;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  outline: none;
  width: 100%;
}

.panel-content {
  height: 100%;
  overflow: hidden;
  display: none;
}

.panel-content.active {
  display: flex;
  flex-direction: column;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

#output-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: black;
  color: #fff;
  font-family: monospace;
}

/* File explorer styling */
.file-explorer {
  width: 250px;
  border-right: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: black;
}

.file-explorer-header {
  padding: 10px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-controls {
  display: flex;
  gap: 5px;
  align-items: center;
}

.file-search {
  flex: 1;
  padding: 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.file-item, .folder-item {
  padding: 4px;
  margin: 2px 0;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.file-item:hover, .folder-item:hover {
  background-color: #e9ecef;
}

.has-children {
  position: relative;
}

.folder-children {
  margin-left: 20px;
  display: none;
}

.expanded > .folder-children {
  display: block;
}

.expanded > i.fa-folder:before {
  content: "\f07c"; /* fa-folder-open */
}

/* Modal styling */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #ffffff;
  margin: 15% auto;
  padding: 20px;
  border-radius: 5px;
  width: 400px;
  max-width: 80%;
}

.close-modal {
  float: right;
  font-size: 24px;
  cursor: pointer;
}

.modal input {
  width: 100%;
  padding: 8px;
  margin: 15px 0;
  border: 1px solid #ced4da;
  border-radius: 4px;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Context menu styling */
.context-menu {
  display: none;
  position: absolute;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.context-menu li {
  padding: 8px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.context-menu li:hover {
  background-color: #f1f3f5;
}

/* Tabs styling */
.file-tabs {
  display: flex;
  overflow-x: auto;
  background-color: #f1f3f5;
  border-bottom: 1px solid #dee2e6;
}

.file-tab {
  padding: 5px 10px;
  background-color: #e9ecef;
  border-right: 1px solid #dee2e6;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  align-items: center;
}

.file-tab:hover {
  background-color: #dee2e6;
}
































/* deek seek  */
/* Editor Container */
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #1e1e1e;
  color: #e0e0e0;
}

.editor-header {
  background-color: #252526;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #1e1e1e;
}

.editor-header h1 {
  font-size: 14px;
  margin: 0;
  font-weight: normal;
}

.editor-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.editor-controls select,
.editor-controls button {
  background-color: #333;
  color: #fff;
  border: 1px solid #3c3c3c;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.editor-controls button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.editor-controls button:hover {
  background-color: #3c3c3c;
}

/* Editor Main Layout */
.editor-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* File Explorer */
.file-explorer {
  width: 250px;
  background-color: #252526;
  border-right: 1px solid #1e1e1e;
  display: flex;
  flex-direction: column;
}

.file-explorer-header {
  padding: 8px;
  border-bottom: 1px solid #1e1e1e;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-explorer-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: normal;
  color: #bbbbbb;
}

.file-controls {
  display: flex;
  gap: 4px;
}

.file-search {
  flex: 1;
  background-color: #333;
  border: 1px solid #3c3c3c;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.file-tree {
  flex: 1;
  overflow-y: auto;
  padding: 4px 0;
}

/* File Tree Items */
.file-item,
.folder-item {
  padding: 4px 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  user-select: none;
  position: relative;
}

.file-item:hover,
.folder-item:hover {
  background-color: #2a2d2e;
}

.file-item.selected,
.folder-item.selected {
  background-color: #37373d;
}

.folder-item.has-children {
  padding-left: 0;
}

.toggle-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s ease;
  color: #7a7a7a;
}

.folder-item.expanded > .toggle-icon {
  transform: rotate(90deg);
}

.folder-children {
  padding-left: 16px;
  display: none;
}

.folder-item.expanded > .folder-children {
  display: block;
}

/* File Icons */
.file-item i,
.folder-item i {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.folder-item i {
  color: #4b8bbe;
}

/* File type specific icons */
.file-item[data-ext="py"] i { color: #3572A5; }
.file-item[data-ext="js"] i { color: #f1e05a; }
.file-item[data-ext="jsx"] i { color: #61dafb; }
.file-item[data-ext="ts"] i { color: #3178c6; }
.file-item[data-ext="html"] i { color: #e34c26; }
.file-item[data-ext="css"] i { color: #563d7c; }
.file-item[data-ext="json"] i { color: #f5de19; }
.file-item[data-ext="md"] i { color: #6a737d; }
.file-item[data-ext="java"] i { color: #b07219; }
.file-item[data-ext="go"] i { color: #00add8; }
.file-item[data-ext="php"] i { color: #4F5D95; }
.file-item[data-ext="rb"] i { color: #701516; }
.file-item[data-ext="c"] i,
.file-item[data-ext="cpp"] i,
.file-item[data-ext="h"] i { color: #f34b7d; }
.file-item[data-ext="sh"] i { color: #4eaa25; }
.file-item[data-ext="yml"] i,
.file-item[data-ext="yaml"] i { color: #cb171e; }

/* Editor Content */
.editor-content {
  flex: 1;
  overflow: hidden;
}

/* Panel Container */
.panel-container {
  height: 200px;
  background-color: #1e1e1e;
  border-top: 1px solid #1e1e1e;
  display: flex;
  flex-direction: column;
}

.panel-tabs {
  display: flex;
  background-color: #252526;
  border-bottom: 1px solid #1e1e1e;
}

.panel-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  color: #bbbbbb;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  border-bottom: 2px solid transparent;
}

.panel-tab.active {
  color: #fff;
  border-bottom-color: #007acc;
  background-color: #1e1e1e;
}

.panel-content {
  flex: 1;
  overflow: auto;
  display: none;
  padding: 8px;
  font-family: 'Consolas', monospace;
  font-size: 13px;
}

.panel-content.active {
  display: block;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.panel-header h3 {
  margin: 0;
  font-size: 12px;
  font-weight: normal;
}

/* Terminal */
#terminal-container {
  padding: 8px;
  height: calc(100% - 40px);
  overflow-y: auto;
  font-family: 'Consolas', monospace;
  font-size: 13px;
  white-space: pre-wrap;
}

#terminal-input-div {
  padding: 8px;
  display: flex;
  align-items: center;
  background-color: #252526;
  border-top: 1px solid #1e1e1e;
}

#terminal-prompt {
  margin-right: 8px;
  color: #4cc9f0;
}

#terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  font-family: 'Consolas', monospace;
  font-size: 13px;
  outline: none;
}

/* Context Menu */
.context-menu {
  position: absolute;
  background-color: #252526;
  border: 1px solid #454545;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  min-width: 150px;
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 4px 0;
}

.context-menu li {
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.context-menu li:hover {
  background-color: #094771;
}

.context-menu i {
  width: 16px;
  text-align: center;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #252526;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #454545;
  width: 400px;
  border-radius: 4px;
}

.modal-content h2 {
  margin-top: 0;
  font-size: 16px;
  font-weight: normal;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.close-modal {
  float: right;
  cursor: pointer;
  font-size: 20px;
}

/* File Tabs */
#file-tabs {
  display: flex;
  background-color: #252526;
  border-bottom: 1px solid #1e1e1e;
  overflow-x: auto;
}

.file-tab {
  padding: 8px 16px;
  background-color: #1e1e1e;
  color: #bbbbbb;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  border-right: 1px solid #252526;
  white-space: nowrap;
}

.file-tab.active {
  background-color: #1e1e1e;
  color: #fff;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #252526;
}

::-webkit-scrollbar-thumb {
  background: #454545;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Tag Input Styles */
.tag-input-container {
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px;
  background-color: var(--primarydark-medium);
  width: 100%;
  min-height: 45px;
}

.tag-input {
  border: none;
  outline: none;
  flex-grow: 1;
  padding: 5px;
  font-size: 14px;
  background-color: transparent;
  color: var(--textdark-primary);
}

.tag {
  display: inline-flex;
  align-items: center;
  background-color: var(--secondarydark-light);
  color: var(--textdark-secondary);
  padding: 5px 10px;
  border-radius: 20px;
  margin: 2px;
  font-size: 14px;
}

.tag-remove {
  margin-left: 5px;
  cursor: pointer;
  font-weight: bold;
}

.suggestions-container {
  position: absolute;
  background-color: var(--primarydark-medium);
  border: 1px solid #ccc;
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  width: 100%;
  z-index: 10;
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
}

.suggestion-item:hover {
  background-color: var(--primarydark-light);
}

/* Tag Input System CSS */
.tag-input-container {
  position: relative;
  width: 80%;
  margin-bottom: 15px;
}

.tag-input {
  width: 100%;
  min-height: 45px;
  padding: 8px 10px;
  border-radius: 35px;
  background-color: transparent;
  color: var(--primarylight-dark);
  border: 1px solid var(--primarydark-light);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
}

.tag {
  display: inline-flex;
  align-items: center;
  background-color: var(--primarydark-light);
  color: var(--textdark-primary);
  padding: 4px 8px;
  border-radius: 16px;
  margin: 2px;
}

.tag-text {
  margin-right: 5px;
}

.tag-close {
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
}

.tag-input-field {
  flex: 1;
  min-width: 60px;
  background: transparent;
  border: none;
  outline: none;
  color: var(--primarylight-dark);
  padding: 4px 0;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  background-color: var(--primarydark-medium);
  border: 1px solid var(--primarydark-light);
  border-radius: 5px;
  z-index: 10;
  display: none;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
}

.tag-suggestion-item:hover {
  background-color: var(--primarydark-light);
}

/* Hide the original select elements that we're replacing */
.tag-select-hidden {
  position: absolute;
  left: -9999px;
  visibility: hidden;
}


 /* File explorer enhancements */
 .file-tree {
  padding: 8px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

.file-item, .folder-item {
  display: flex;
  align-items: center;
  padding: 4px 0;
  cursor: pointer;
  border-radius: 4px;
  position: relative;
  white-space: nowrap;
}

.file-item:hover, .folder-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.file-item.selected, .folder-item.selected {
  background-color: rgba(100, 100, 255, 0.2);
}

.folder-item {
  font-weight: bold;
}

.folder-children {
  overflow: hidden;
  margin-left: 20px;
  padding-left: 8px;
  border-left: 1px dotted rgba(255, 255, 255, 0.2);
  max-height: 0;
  opacity: 0;
  transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out;
}

.folder-children.expanded {
  display: block;
  max-height: 1000px; /* Large enough value to accommodate content */
  opacity: 1;
}

.toggle-icon {
  margin-right: 5px;
  transition: transform 0.3s ease;
  color: #888;
  width: 12px;
  display: inline-block;
  text-align: center;
}

.folder-item.expanded > .toggle-icon {
  transform: rotate(90deg);
}

/* Icon styles */
.fa-folder {
  color: #e3a817;
}

.folder-item.expanded > .fa-folder {
  color: #e9b71a;
}

.fa-file-code, .fa-file-alt {
  color: #8bb3dd;
}

.fa-html5 {
  color: #e44d26;
}

.fa-css3-alt {
  color: #264de4;
}

.fa-js {
  color: #f7df1e;
}

.fa-python {
  color: #3572a5;
}

/* File and folder icons */
i.fas, i.fab {
  margin-right: 6px;
  width: 16px;
  text-align: center;
}

/* Context menu styles */
.context-menu {
  position: absolute;
  display: none;
  background-color: #252526;
  border: 1px solid #454545;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  min-width: 180px;
  overflow: hidden;
}

.context-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.context-menu li {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.context-menu li:hover {
  background-color: #2a2d2e;
}

.context-menu li i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
  color: #cccccc;
}

.context-menu hr {
  margin: 4px 0;
  border: none;
  border-top: 1px solid #454545;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  overflow: scroll;

  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #252526;
  margin: 15% auto;
  padding: 20px;
  border: 1px solid #454545;
  border-radius: 6px;
  width: 400px;
  left: 30%;
  max-width: 80%;
  position: relative;
}

.close-modal {
  position: absolute;
  right: 10px;
  top: 10px;
  font-size: 20px;
  cursor: pointer;
}

.modal h2 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #e0e0e0;
}

.modal input {
  width: 100%;
  padding: 8px;
  margin-bottom: 16px;
  background-color: #3c3c3c;
  border: 1px solid #555555;
  border-radius: 4px;
  color: #e0e0e0;
  font-size: 14px;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* Inline rename styles */
.item-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.rename-input {
  background-color: #3c3c3c;
  border: 1px solid #007acc;
  color: #e0e0e0;
  outline: none;
  padding: 2px 4px;
  margin: -3px 0;
  font-family: inherit;
  font-size: inherit;
  width: calc(100% - 20px);
  max-width: 150px;
  border-radius: 2px;
}

.rename-input.error {
  border-color: #e74c3c;
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Drag and drop upload styles */
.drag-over {
  background-color: rgba(65, 105, 225, 0.2) !important;
  outline: 2px dashed royalblue !important;
  outline-offset: -2px;
}

.upload-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #252526;
  border: 1px solid #454545;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  max-width: 300px;
}

.upload-progress h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.upload-progress h3 i {
  margin-right: 8px;
  color: #4cc9f0;
}

.upload-progress-bar {
  height: 6px;
  background-color: #3c3c3c;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.upload-progress-bar-inner {
  height: 100%;
  background-color: #4cc9f0;
  width: 0%;
  transition: width 0.3s ease;
}

.upload-item {
  font-size: 12px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.upload-item.success {
  color: #4cc9f0;
}

.upload-item.error {
  color: #ff6b6b;
}

/* Add styles for terminal tabs and buttons */
#terminal-tabs {
display: flex;
align-items: center;
background-color: #2d2d2d;
border-bottom: 1px solid #444;
padding: 5px;
}

.terminal-tab {
padding: 5px 10px;
margin-right: 5px;
background-color: #3c3c3c;
color: #fff;
border-radius: 3px;
cursor: pointer;
display: flex;
align-items: center;
}

.terminal-tab.active {
background-color: #007acc;
color: #fff;
}

.terminal-tab .close-tab {
margin-left: 5px;
color: #ccc;
cursor: pointer;
}

#add-terminal-btn {
padding: 5px 10px;
background-color: #007acc;
color: #fff;
border: none;
border-radius: 3px;
cursor: pointer;
}

#add-terminal-btn:hover {
background-color: #005f99;
}

/* Terminal split view buttons */
#split-horizontal, #split-vertical {
padding: 5px 10px;
background-color: #3c3c3c;
color: #fff;
border: none;
border-radius: 3px;
cursor: pointer;
margin-right: 5px;
}

#split-horizontal:hover, #split-vertical:hover {
background-color: #555;
}

/* Terminal container styles */
.terminal-pane {
display: none;
background-color: #1e1e1e;
color: #fff;
padding: 10px;
overflow-y: auto;
}

.terminal-pane.active {
display: block;
}

.terminal-split-horizontal {
display: flex;
flex-direction: row;
}

.terminal-split-vertical {
display: flex;
flex-direction: column;
}

.terminal-instance {
  width: 100%;
  height: 100%;
  padding: 5px;
  display: none;
}

.terminal-instance.active {
  display: block;
}

#terminal-container {
  width: 100%;
  height: 300px;
  position: relative;
  background-color: #1e1e1e;
  border-radius: 4px;
  overflow: hidden;
}

/* Terminal split view styling */
#terminal-container.split-horizontal {
  display: flex;
  flex-direction: row;
}

#terminal-container.split-vertical {
  display: flex;
  flex-direction: column;
}

#terminal-container.split-horizontal .terminal-instance {
  width: 50%;
  height: 100%;
}

#terminal-container.split-vertical .terminal-instance {
  width: 100%;
  height: 50%;
}

/* Terminal tabs styling */
#terminal-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.terminal-tab-button {
  background-color: #333;
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 3px 3px 0 0;
  cursor: pointer;
  position: relative;
}

.terminal-tab-button.active {
  background-color: #0078d7;
}

.terminal-tab-button .close-tab {
  margin-left: 8px;
  opacity: 0.7;
}

.terminal-tab-button .close-tab:hover {
  opacity: 1;
}

/* Terminal controls styling */
#terminal-split-controls {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

#terminal-split-controls button {
  background-color: #333;
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

#terminal-split-controls button:hover {
  background-color: #555;
}

#add-terminal-btn {
  background-color: #0078d7;
  color: #fff;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: auto;
}

#add-terminal-btn:hover {
  background-color: #0063b1;
}

/* Learn Dropdown Styling */
.dropdown-menu {
  background: linear-gradient(135deg, rgba(28, 28, 28, 0.98), rgba(40, 40, 40, 0.98));
  border: 2px solid rgba(192, 255, 107, 0.4);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(192, 255, 107, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 12px;
  min-width: 280px;
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 15px 0;
  overflow: hidden;
}

.dropdown-menu::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid rgba(192, 255, 107, 0.4);
}

.dropdown-menu::after {
  content: '';
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(28, 28, 28, 0.98);
}

.dropdown-menu.show {
  display: block;
  animation: dropdownSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-15px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

.dropdown {
  position: relative;
}

.dropdown-item {
  color: #ffffff;
  padding: 16px 25px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 0;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 15px;
  text-decoration: none;
  font-weight: 500;
  font-size: 15px;
  position: relative;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
  transform: scaleY(0);
  transition: transform 0.3s ease;
  border-radius: 0 2px 2px 0;
}

.dropdown-item:hover {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.15), rgba(160, 224, 102, 0.1));
  color: #C0ff6b;
  transform: translateX(8px);
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.2);
}

.dropdown-item:hover::before {
  transform: scaleY(1);
}

.dropdown-item:active {
  transform: translateX(6px) scale(0.98);
}

.dropdown-item i {
  width: 20px;
  text-align: center;
  font-size: 16px;
  color: #C0ff6b;
  transition: all 0.3s ease;
}

.dropdown-item:hover i {
  transform: scale(1.1);
  text-shadow: 0 0 8px rgba(192, 255, 107, 0.5);
}

.dropdown-divider {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.3), transparent);
  margin: 12px 20px;
  position: relative;
}

.dropdown-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: #C0ff6b;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(192, 255, 107, 0.5);
}

.dropdown-toggle::after {
  border-top-color: #C0ff6b !important;
  border-left: 0.3em solid transparent;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-top: 0.3em solid;
  transition: transform 0.3s ease;
  margin-left: 0.5em;
  vertical-align: 0.1em;
}

.dropdown-toggle[aria-expanded="true"]::after {
  transform: rotate(180deg);
}

/* Ensure dropdown toggle has proper styling */
.nav-item.dropdown .nav-link.dropdown-toggle {
  display: flex;
  align-items: center;
  position: relative;
}

.nav-item.dropdown .nav-link.dropdown-toggle:hover {
  color: #C0ff6b;
}

.nav-item.dropdown .nav-link.dropdown-toggle:focus {
  outline: none;
  color: #C0ff6b;
}

/* Hover-based dropdown as fallback */
.nav-item.dropdown:hover .dropdown-menu {
  display: block;
  animation: dropdownFadeIn 0.3s ease;
}

/* Ensure dropdown stays open when hovering over menu */
.dropdown-menu:hover {
  display: block;
}

/* Override any conflicting styles for dropdown */
.nav-item.dropdown .nav-link.dropdown-toggle::before {
  display: none !important;
}

.nav-item.dropdown .nav-link.dropdown-toggle:hover::before {
  display: none !important;
}

/* Ensure dropdown menu is properly positioned */
.nav-item.dropdown {
  position: relative !important;
}

.nav-item.dropdown .dropdown-menu {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: auto !important;
  z-index: 1050 !important;
  margin-top: 0.125rem !important;
}

/* Custom dropdown arrow styling */
.dropdown-arrow {
  font-size: 0.8em;
  margin-left: 0.6em;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: #C0ff6b;
  display: inline-block;
  text-shadow: 0 0 5px rgba(192, 255, 107, 0.3);
}

.nav-item.dropdown:hover .dropdown-arrow,
.nav-item.dropdown.show .dropdown-arrow {
  transform: rotate(180deg) scale(1.1);
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.6);
}

/* Hide Bootstrap's default arrow */
.nav-item.dropdown .dropdown-toggle::after {
  display: none !important;
}

/* Enhanced dropdown toggle styling */
.nav-item.dropdown .nav-link.dropdown-toggle {
  position: relative;
  overflow: hidden;
}

.nav-item.dropdown .nav-link.dropdown-toggle::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #C0ff6b, #a0e066);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-item.dropdown:hover .nav-link.dropdown-toggle::before,
.nav-item.dropdown.show .nav-link.dropdown-toggle::before {
  width: 100%;
}

/* Enhanced dropdown visibility */
.nav-item.dropdown:hover .dropdown-menu,
.nav-item.dropdown.show .dropdown-menu {
  display: block !important;
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0) scale(1);
}

.nav-item.dropdown .dropdown-menu {
  opacity: 0;
  visibility: hidden;
  transform: translateX(-50%) translateY(-15px) scale(0.95);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Mobile dropdown adjustments */
@media (max-width: 768px) {
  .dropdown-menu {
    min-width: 260px;
    left: 0;
    transform: translateX(0);
    margin-top: 8px;
  }

  .dropdown-menu::before,
  .dropdown-menu::after {
    left: 20px;
    transform: translateX(0);
  }

  .nav-item.dropdown:hover .dropdown-menu,
  .nav-item.dropdown.show .dropdown-menu {
    transform: translateX(0) translateY(0) scale(1);
  }

  .nav-item.dropdown .dropdown-menu {
    transform: translateX(0) translateY(-15px) scale(0.95);
  }
}

@media (max-width: 468px) {
  .dropdown-menu {
    position: static !important;
    transform: none !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(192, 255, 107, 0.3);
    background: linear-gradient(135deg, rgba(28, 28, 28, 0.95), rgba(40, 40, 40, 0.95));
    margin-top: 10px;
    min-width: auto;
    width: 100%;
    border-radius: 10px;
    padding: 10px 0;
  }

  .dropdown-menu::before,
  .dropdown-menu::after {
    display: none;
  }

  .dropdown-item {
    margin: 0;
    border-radius: 0;
    padding: 15px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .dropdown-item:hover {
    transform: translateX(5px);
  }

  .nav-item.dropdown:hover .dropdown-menu,
  .nav-item.dropdown.show .dropdown-menu {
    transform: none;
  }

  .nav-item.dropdown .dropdown-menu {
    transform: none;
  }
}

/* Security Dashboard Link Styling */
.security-dashboard-link {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(168, 224, 99, 0.1)) !important;
  border-left: 3px solid var(--color-border) !important;
  color: var(--color-border) !important;
  font-weight: 600 !important;
  position: relative;
  overflow: hidden;
}

.security-dashboard-link:hover {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.2), rgba(168, 224, 99, 0.2)) !important;
  color: #fff !important;
  border-left-color: #a8e063 !important;
  transform: translateX(8px) !important;
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3) !important;
}

.security-dashboard-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.1), transparent);
  transition: left 0.5s ease;
}

.security-dashboard-link:hover::before {
  left: 100%;
}

.security-dashboard-link .fas.fa-shield-alt {
  animation: shield-pulse 2s infinite;
  margin-right: 8px;
}

@keyframes shield-pulse {
  0% {
    transform: scale(1);
    color: var(--color-border);
  }
  50% {
    transform: scale(1.1);
    color: #a8e063;
  }
  100% {
    transform: scale(1);
    color: var(--color-border);
  }
}

/* Enhanced dropdown styling for security link */
.nav-item.dropdown .dropdown-menu .security-dashboard-link {
  margin: 5px 0;
  border-radius: 6px !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  border-left: 3px solid var(--color-border) !important;
}

/* Mobile responsive for security link */
@media (max-width: 768px) {
  .security-dashboard-link {
    padding: 12px 16px !important;
    font-size: 0.9rem !important;
  }

  .security-dashboard-link .fas.fa-shield-alt {
    font-size: 1rem;
    margin-right: 6px;
  }
}