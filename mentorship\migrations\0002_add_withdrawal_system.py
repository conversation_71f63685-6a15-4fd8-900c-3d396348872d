# Generated manually for withdrawal system

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('mentorship', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='mentorprofile',
            name='withdrawn_earnings',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.CreateModel(
            name='WithdrawalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Amount to withdraw (minimum $10)', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('10.00'))])),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', help_text='Current status of withdrawal', max_length=20)),
                ('bank_account_last4', models.CharField(blank=True, help_text='Last 4 digits of bank account', max_length=4)),
                ('stripe_transfer_id', models.CharField(blank=True, help_text='Stripe transfer ID', max_length=255, null=True)),
                ('processing_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Processing fee charged', max_digits=6)),
                ('net_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Net amount transferred', max_digits=10)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes for admin')),
                ('failure_reason', models.TextField(blank=True, help_text='Reason for failure if applicable')),
                ('mentor', models.ForeignKey(help_text='Mentor requesting withdrawal', on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-requested_at'],
            },
        ),
        migrations.AddIndex(
            model_name='withdrawalrequest',
            index=models.Index(fields=['mentor', 'status'], name='mentorship_withdrawalrequest_mentor_status_idx'),
        ),
        migrations.AddIndex(
            model_name='withdrawalrequest',
            index=models.Index(fields=['status', 'requested_at'], name='mentorship_withdrawalrequest_status_requested_idx'),
        ),
    ]
