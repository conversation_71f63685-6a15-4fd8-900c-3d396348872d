{% extends 'base.html' %}
{% load static %}

{% block title %}Payment Failed - ForgeX{% endblock %}

{% block content %}
<div class="payment-failed-page">
    <div class="container">
        <div class="failed-container">
            <div class="failed-icon">
                <i class="fas fa-times-circle"></i>
            </div>
            
            <h1>Payment Failed</h1>
            <p class="failed-message">We couldn't process your payment. Your session has not been booked.</p>
            
            <div class="session-details-card">
                <h3>Session Details</h3>
                
                <div class="mentor-info">
                    <div class="mentor-avatar">
                        {% if session.mentor.profile and session.mentor.profile.profile_picture %}
                            <img src="{{ session.mentor.profile.profile_picture.url }}"
                                 alt="{{ session.mentor.get_full_name }}"
                                 class="profile-image">
                        {% else %}
                            <div class="avatar-initials">
                                {{ session.mentor.first_name|first|upper }}{{ session.mentor.last_name|first|upper }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="mentor-details">
                        <h4>{{ session.mentor.get_full_name|default:session.mentor.username }}</h4>
                        <p>Mentor</p>
                    </div>
                </div>
                
                <div class="session-info">
                    <div class="info-row">
                        <span class="label">Date & Time:</span>
                        <span class="value">{{ session.scheduled_time|date:"M d, Y" }} at {{ session.scheduled_time|time:"g:i A" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Duration:</span>
                        <span class="value">{{ session.get_duration_hours }} hour{{ session.get_duration_hours|pluralize }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Amount:</span>
                        <span class="value">${{ session.total_amount }}</span>
                    </div>
                </div>
            </div>
            
            <div class="troubleshooting">
                <h3>What went wrong?</h3>
                <div class="reasons-list">
                    <div class="reason">
                        <i class="fas fa-credit-card"></i>
                        <span>Your card was declined</span>
                    </div>
                    <div class="reason">
                        <i class="fas fa-shield-alt"></i>
                        <span>Insufficient funds</span>
                    </div>
                    <div class="reason">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Card information was incorrect</span>
                    </div>
                    <div class="reason">
                        <i class="fas fa-ban"></i>
                        <span>Payment was blocked by your bank</span>
                    </div>
                </div>
                
                <div class="suggestions">
                    <h4>Try these solutions:</h4>
                    <ul>
                        <li>Check your card details and try again</li>
                        <li>Use a different payment method</li>
                        <li>Contact your bank to authorize the payment</li>
                        <li>Ensure you have sufficient funds</li>
                    </ul>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="{% url 'mentorship:payment_page' session.id %}" class="btn btn-primary">
                    <i class="fas fa-redo"></i>
                    Try Payment Again
                </a>
                <a href="{% url 'mentorship:mentor_detail' session.mentor.mentor_profile.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Mentor Profile
                </a>
                <a href="{% url 'mentorship:marketplace' %}" class="btn btn-secondary">
                    <i class="fas fa-search"></i>
                    Find Another Mentor
                </a>
            </div>
            
            <div class="support-info">
                <p>Need help? <a href="mailto:<EMAIL>">Contact our support team</a></p>
            </div>
        </div>
    </div>
</div>

<style>
.payment-failed-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 3rem 0;
    display: flex;
    align-items: center;
}

.failed-container {
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.failed-icon {
    font-size: 4rem;
    color: #ff6b6b;
    margin-bottom: 2rem;
}

.failed-container h1 {
    font-size: 2.5rem;
    color: #ff6b6b;
    margin-bottom: 1rem;
}

.failed-message {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 3rem;
    line-height: 1.6;
}

.session-details-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 107, 107, 0.2);
    margin-bottom: 3rem;
    text-align: left;
}

.session-details-card h3 {
    color: #ff6b6b;
    margin-bottom: 1.5rem;
    text-align: center;
}

.mentor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mentor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #C0ff6b;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    overflow: hidden;
}

.mentor-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mentor-details h4 {
    margin: 0 0 0.25rem 0;
    color: #ffffff;
}

.mentor-details p {
    margin: 0;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.session-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-row .label {
    color: rgba(255,255,255,0.7);
    font-weight: 500;
}

.info-row .value {
    font-weight: 600;
    color: #ffffff;
}

.troubleshooting {
    margin-bottom: 3rem;
    text-align: left;
}

.troubleshooting h3 {
    color: #ff6b6b;
    margin-bottom: 1.5rem;
    text-align: center;
}

.reasons-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.reason {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(255, 107, 107, 0.2);
}

.reason i {
    color: #ff6b6b;
    font-size: 1.2rem;
}

.suggestions {
    background: rgba(255,255,255,0.05);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.1);
}

.suggestions h4 {
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.suggestions ul {
    margin: 0;
    padding-left: 1.5rem;
}

.suggestions li {
    margin-bottom: 0.5rem;
    color: rgba(255,255,255,0.8);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.btn-primary {
    background: #C0ff6b;
    color: #1a1a1a;
}

.btn-primary:hover {
    background: #a0e066;
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.support-info {
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.support-info p {
    color: rgba(255,255,255,0.7);
    margin: 0;
}

.support-info a {
    color: #C0ff6b;
    text-decoration: none;
}

.support-info a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .failed-container h1 {
        font-size: 2rem;
    }
    
    .failed-message {
        font-size: 1.1rem;
    }
    
    .reasons-list {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .mentor-info {
        flex-direction: column;
        text-align: center;
    }
    
    .info-row {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
}
</style>
{% endblock %}
