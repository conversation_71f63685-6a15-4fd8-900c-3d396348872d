# Generated by Django 5.2.1 on 2025-05-28 16:35

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("mentorship", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("chat", "Chat Message"),
                            ("system", "System Message"),
                            ("user_joined", "User Joined"),
                            ("user_left", "User Left"),
                        ],
                        default="chat",
                        help_text="Type of message",
                        max_length=20,
                    ),
                ),
                ("content", models.TextField(help_text="Message content")),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "sender",
                    models.ForeignKey(
                        blank=True,
                        help_text="User who sent the message (null for system messages)",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sent_chat_messages",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        help_text="The mentorship session this message belongs to",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_messages",
                        to="mentorship.mentorshipsession",
                    ),
                ),
            ],
            options={
                "ordering": ["timestamp"],
                "indexes": [
                    models.Index(
                        fields=["session", "timestamp"],
                        name="mentorship__session_3dd2b9_idx",
                    ),
                    models.Index(
                        fields=["sender", "timestamp"],
                        name="mentorship__sender__85ceaf_idx",
                    ),
                ],
            },
        ),
    ]
