"""
User Ranking Utility - ForgeX

This module provides functionality to rank users based on how well their skills
match a project's requirements, with special emphasis on critical skills.
"""

from django.contrib.auth import get_user_model
from collaborate.models import UserSkill
from collaborate.utils.skill_matching import calculate_total_matching_score
from collaborate.models import TeamRejectionLog
from collaborate.utils.ai_helpers import is_similar_project

def get_penalized_score(user, project, base_score):
    rejections = TeamRejectionLog.objects.filter(user=user)
    penalty = 0
    for rej in rejections:
        if is_similar_project(rej.project, project):
            penalty += 10
            if rej.availability_mismatch:
                penalty += 5
            if rej.timezone_difference > 4:
                penalty += 5
    return max(base_score - penalty, 0)

def rank_users_for_project(project):
    """
    Ranks all users based on how well they match the project's skills.
    Takes into account both required skills and critical skills.
    
    Args:
        project: Project model instance
        
    Returns:
        list: Sorted list of (user, matching_score) tuples, highest score first
    """
    # Get project skills as lists of IDs for efficient comparison
    project_required_skills = list(project.required_skills.values_list('id', flat=True))
    project_critical_skills = list(project.critical_skills.values_list('id', flat=True))
    
    # Get all users except the project owner
    User = get_user_model()
    all_users = User.objects.exclude(id=project.owner.id).filter(is_active=True)
    
    ranked_users = []
    
    for user in all_users:
        # Get user's skills from UserSkill model (which includes proficiency)
        user_skills = list(UserSkill.objects.filter(user=user).values_list('skill_id', flat=True))
        
        # Calculate matching score
        score = calculate_total_matching_score(user_skills, project_required_skills, project_critical_skills)
        
        # Add proficiency bonus (average proficiency of matching skills)
        matching_user_skills = UserSkill.objects.filter(
            user=user,
            skill_id__in=project_required_skills
        )
        
        if matching_user_skills.exists():
            avg_proficiency = sum(us.proficiency for us in matching_user_skills) / matching_user_skills.count()
            # Add small bonus based on proficiency (0.01 to 0.05 bonus)
            proficiency_bonus = (avg_proficiency / 100)
            score += proficiency_bonus
        
        # Apply penalty for similar project rejections
        penalized_score = get_penalized_score(user, project, round(score, 3))
        print(f"{user.username} match score adjusted from {round(score, 3)} to {penalized_score}")
        ranked_users.append((user, penalized_score))
    
    # Sort users descending by matching score
    ranked_users.sort(key=lambda x: x[1], reverse=True)
    
    return ranked_users

def get_top_candidates(project, limit=5):
    """
    Get top N candidates for a project based on skill matching.
    
    Args:
        project: Project model instance
        limit: Maximum number of candidates to return (default: 5)
        
    Returns:
        list: Top N (user, score) tuples
    """
    ranked_users = rank_users_for_project(project)
    return ranked_users[:limit]

def get_recommended_team(project):
    """
    Get a recommended team for a project, sized according to project.team_size.
    
    Args:
        project: Project model instance
        
    Returns:
        list: List of (user, score) tuples representing the recommended team
    """
    # Team size should include the owner, so we subtract 1
    team_size_needed = max(1, project.team_size - 1)
    ranked_users = rank_users_for_project(project)
    return ranked_users[:team_size_needed]

def get_team_skill_coverage(project, team_members):
    """
    Calculate what percentage of required project skills are covered by the team.
    
    Args:
        project: Project model instance
        team_members: List of User objects
        
    Returns:
        dict: Coverage statistics including percentage and missing skills
    """
    project_skills = set(project.required_skills.values_list('id', flat=True))
    
    # Skip calculation if project has no required skills
    if not project_skills:
        return {
            'coverage_percent': 100,
            'missing_skills': [],
            'covered_skills': [],
            'is_complete_coverage': True
        }
    
    # Get all skills from team members
    team_skills = set()
    for user in team_members:
        user_skills = UserSkill.objects.filter(user=user).values_list('skill_id', flat=True)
        team_skills.update(user_skills)
    
    # Calculate overlap with project skills
    covered_skills = project_skills.intersection(team_skills)
    missing_skills = project_skills - team_skills
    
    # Calculate coverage percentage
    coverage_percent = (len(covered_skills) / len(project_skills)) * 100 if project_skills else 100
    
    return {
        'coverage_percent': round(coverage_percent, 1),
        'missing_skills': list(missing_skills),
        'covered_skills': list(covered_skills),
        'is_complete_coverage': len(missing_skills) == 0
    }