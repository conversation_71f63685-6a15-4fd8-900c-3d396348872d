import pytz
import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from .models import UserSkill, ProjectMembership, Notification
from accounts.models import UserProfile
from django.contrib.auth import get_user_model
from .utils.team_analysis import analyze_team_balance, identify_skill_gaps

# Set up logging
logger = logging.getLogger(__name__)

def get_proficiency_name(proficiency_level):
    """
    Convert numeric proficiency level to a descriptive name.

    Args:
        proficiency_level: Integer proficiency level (1-5)

    Returns:
        str: Descriptive name for the proficiency level
    """
    proficiency_names = {
        1: "Beginner",
        2: "Intermediate",
        3: "Advanced",
        4: "Expert",
        5: "Master"
    }
    return proficiency_names.get(proficiency_level, "Unknown")

def calculate_timezone_difference(timezone1, timezone2):
    """
    Calculate the hour difference between two timezones.

    Args:
        timezone1: First timezone string (e.g., 'America/New_York')
        timezone2: Second timezone string (e.g., 'Europe/London')

    Returns:
        int: Absolute hour difference between the two timezones
    """
    if not timezone1 or not timezone2:
        return 12  # Default to maximum difference if either timezone is missing

    try:
        tz1 = pytz.timezone(timezone1)
        tz2 = pytz.timezone(timezone2)

        # Get current UTC time
        now_utc = datetime.now(pytz.UTC)

        # Convert to both timezones
        now_tz1 = now_utc.astimezone(tz1)
        now_tz2 = now_utc.astimezone(tz2)

        # Calculate hour difference
        hour_diff = abs(now_tz1.hour - now_tz2.hour)

        # Handle cases where the difference crosses day boundary
        if hour_diff > 12:
            hour_diff = 24 - hour_diff

        return hour_diff

    except Exception:
        return 12  # Default to maximum difference if there's an error

def is_user_available_now(user):
    """
    Check if a user is available right now based on their availability window.

    Args:
        user: User to check

    Returns:
        bool: True if user is available now, False otherwise
    """
    try:
        profile = UserProfile.objects.get(user=user)

        # If no availability set, assume not available
        if not profile.availability_start or not profile.availability_end:
            return False

        # Get current time in the user's timezone
        user_tz = pytz.timezone(profile.timezone)
        now = timezone.now().astimezone(user_tz).time()

        start = profile.availability_start
        end = profile.availability_end

        # Handle wrap-around availability (e.g., 22:00 - 06:00)
        if end < start:
            return now >= start or now <= end
        else:
            return start <= now <= end

    except UserProfile.DoesNotExist:
        return False

def score_user_for_project(user, project, project_owner):
    """
    Calculate a score for how well a user matches a project.

    Args:
        user: User to evaluate
        project: Project to match against
        project_owner: Owner of the project

    Returns:
        tuple: (score, matching_skills, missing_skills)
    """
    # Initialize score and skill lists
    score = 0
    matching_skills = []
    missing_skills = []

    # Get user's skills with proficiency levels
    user_skills_data = UserSkill.objects.filter(user=user).select_related('skill')
    user_skill_ids = [us.skill.id for us in user_skills_data]

    # Create a dictionary mapping skill IDs to proficiency levels
    skill_proficiency = {us.skill.id: us.proficiency for us in user_skills_data}

    # Get project's required skills
    project_required_skills = list(project.required_skills.all())
    project_skill_ids = [skill.id for skill in project_required_skills]

    # Get project's critical skills
    critical_skill_ids = list(project.critical_skills.values_list('id', flat=True))

    # Calculate matching skills
    for skill in project_required_skills:
        if skill.id in user_skill_ids:
            # Add points based on proficiency (1-5 scale)
            proficiency = skill_proficiency.get(skill.id, 1)

            # Base points for having the skill
            points = 10

            # Bonus points for higher proficiency
            if proficiency >= 4:  # Expert/Master
                points += 5
            elif proficiency >= 2:  # Intermediate/Advanced
                points += 2

            # Extra points for critical skills
            if skill.id in critical_skill_ids:
                points *= 1.5  # 50% bonus for critical skills

            score += points

            # Add to matching skills list with proficiency info
            proficiency_name = get_proficiency_name(proficiency)
            matching_skills.append({
                'id': skill.id,
                'name': skill.name,
                'proficiency': proficiency,
                'proficiency_name': proficiency_name,
                'is_critical': skill.id in critical_skill_ids
            })
        else:
            # Penalize for missing skills
            penalty = -5

            # Higher penalty for missing critical skills
            if skill.id in critical_skill_ids:
                penalty = -10

            score += penalty

            # Add to missing skills list
            missing_skills.append({
                'id': skill.id,
                'name': skill.name,
                'is_critical': skill.id in critical_skill_ids
            })

    # Add small bonus for extra skills that weren't required (+0.5 points each)
    extra_skills = set(user_skill_ids) - set(project_skill_ids)
    if extra_skills:
        score += len(extra_skills) * 0.5

    # Get user and project owner timezones
    try:
        user_profile = UserProfile.objects.get(user=user)
        owner_profile = UserProfile.objects.get(user=project_owner)

        # Calculate timezone compatibility
        timezone_diff = calculate_timezone_difference(
            user_profile.timezone,
            owner_profile.timezone
        )

        # Award more points for closer timezone matches
        if timezone_diff <= 1:
            score += 10  # +10 points if timezone difference <= 1 hour
        elif timezone_diff <= 3:
            score += 5   # +5 points if timezone difference <= 3 hours
        elif timezone_diff <= 6:
            score += 2   # +2 points if timezone difference <= 6 hours

        # Consider country for potential cultural compatibility
        if hasattr(user_profile, 'country') and hasattr(owner_profile, 'country'):
            if user_profile.country and owner_profile.country and user_profile.country == owner_profile.country:
                score += 3  # Small bonus for same country

    except UserProfile.DoesNotExist:
        # If profile doesn't exist, don't add any timezone or availability points
        pass

    return (score, matching_skills, missing_skills)

def calculate_availability_overlap(user1_start, user1_end, user2_start, user2_end):
    """
    Calculate overlap between two users' availability time ranges.

    Args:
        user1_start (time): Start time for user1
        user1_end (time): End time for user1
        user2_start (time): Start time for user2
        user2_end (time): End time for user2

    Returns:
        float: Overlap in hours, or 0 if no overlap
    """
    # Handle None values
    if not all([user1_start, user1_end, user2_start, user2_end]):
        return 0

    # Convert times to minutes for easier calculation
    def time_to_minutes(t):
        return t.hour * 60 + t.minute

    start1 = time_to_minutes(user1_start)
    end1 = time_to_minutes(user1_end)
    start2 = time_to_minutes(user2_start)
    end2 = time_to_minutes(user2_end)

    # Handle wrap-around cases (e.g., 22:00 - 06:00)
    if end1 < start1:
        end1 += 24 * 60  # Add 24 hours
    if end2 < start2:
        end2 += 24 * 60

    # Calculate overlap
    overlap_start = max(start1, start2)
    overlap_end = min(end1, end2)

    if overlap_end <= overlap_start:
        return 0

    # Convert overlap from minutes to hours
    overlap_hours = (overlap_end - overlap_start) / 60
    return overlap_hours

def score_user_for_project(user, project, project_owner):
    """
    Calculate a score for how well a user matches a project.

    Args:
        user: User to evaluate
        project: Project to match against
        project_owner: Owner of the project

    Returns:
        tuple: (score, matching_skills, missing_skills)
    """
    score = 0
    matching_skills = []
    missing_skills = []

    # Get user's skills with proficiency levels
    user_skills_data = UserSkill.objects.filter(user=user).select_related('skill')
    user_skill_ids = [us.skill.id for us in user_skills_data]

    # Create a dictionary mapping skill IDs to proficiency levels
    skill_proficiency = {us.skill.id: us.proficiency for us in user_skills_data}

    # Proficiency level weights for scoring
    LEVEL_WEIGHTS = {
        1: 5,    # Beginner: +5 points
        2: 8,    # Intermediate: +8 points
        3: 12,   # Advanced: +12 points
        4: 16,   # Expert: +16 points
        5: 20    # Master: +20 points
    }

    # Get project's required skills
    project_required_skills = list(project.required_skills.all())
    project_skill_ids = [skill.id for skill in project_required_skills]

    # Get project's critical skills
    critical_skill_ids = list(project.critical_skills.values_list('id', flat=True))

    # Calculate skill matching score with proficiency level weighting and critical skills importance
    for skill in project_required_skills:
        if skill.id in user_skill_ids:
            # Get the proficiency level (default to 1 if not found)
            proficiency = skill_proficiency.get(skill.id, 1)

            # Add points based on proficiency level
            points = LEVEL_WEIGHTS.get(proficiency, 5)

            # Add bonus points if this is a critical skill (+2 multiplier)
            if skill.id in critical_skill_ids:
                points *= 2
                is_critical = True
            else:
                is_critical = False

            score += points

            # Store the skill name, proficiency, and whether it's critical for display
            matching_skills.append({
                'name': skill.name,
                'proficiency': proficiency,
                'proficiency_name': dict(UserSkill.PROFICIENCY_CHOICES).get(proficiency, 'Beginner'),
                'is_critical': is_critical
            })
        else:
            # Penalize more for missing critical skills
            if skill.id in critical_skill_ids:
                score -= 10  # -10 points for each missing critical skill
                missing_skills.append({'name': skill.name, 'is_critical': True})
            else:
                score -= 5  # -5 points for each missing regular required skill
                missing_skills.append({'name': skill.name, 'is_critical': False})

    # Add small bonus for extra skills that weren't required (+0.5 points each)
    extra_skills = set(user_skill_ids) - set(project_skill_ids)
    if extra_skills:
        score += len(extra_skills) * 0.5

    # Get user and project owner timezones
    try:
        user_profile = UserProfile.objects.get(user=user)
        owner_profile = UserProfile.objects.get(user=project_owner)

        # Calculate timezone compatibility
        timezone_diff = calculate_timezone_difference(
            user_profile.timezone,
            owner_profile.timezone
        )

        # Award more points for closer timezone matches
        if timezone_diff <= 1:
            score += 10  # +10 points if timezone difference <= 1 hour
        elif timezone_diff <= 3:
            score += 5   # +5 points if timezone difference <= 3 hours

        # Calculate availability overlap if both users have availability set
        if (user_profile.availability_start and user_profile.availability_end and
            owner_profile.availability_start and owner_profile.availability_end):

            overlap_hours = calculate_availability_overlap(
                user_profile.availability_start,
                user_profile.availability_end,
                owner_profile.availability_start,
                owner_profile.availability_end
            )

            if overlap_hours >= 4:
                score += 10  # +10 points if availability overlaps by 4+ hours
            elif overlap_hours >= 2:
                score += 5   # +5 points if availability overlaps by 2+ hours

        # Consider availability type compatibility
        if hasattr(user_profile, 'availability_type') and hasattr(owner_profile, 'availability_type'):
            # If project owner wants Full Time and user offers Full Time
            if owner_profile.availability_type == 'Full Time' and user_profile.availability_type == 'Full Time':
                score += 10
            # If project owner wants Full Time but user offers Part Time
            elif owner_profile.availability_type == 'Full Time' and user_profile.availability_type == 'Part Time':
                score += 5
            # If both users have matching availability types
            elif user_profile.availability_type == owner_profile.availability_type:
                score += 8
            # If user is Flexible (always a good match)
            elif user_profile.availability_type == 'Flexible':
                score += 7

        # Consider country for potential cultural compatibility
        if hasattr(user_profile, 'country') and hasattr(owner_profile, 'country'):
            if user_profile.country and owner_profile.country and user_profile.country == owner_profile.country:
                score += 3  # Small bonus for same country

    except UserProfile.DoesNotExist:
        # If profile doesn't exist, don't add any timezone or availability points
        pass

    return (score, matching_skills, missing_skills)

def find_best_team(project, available_users):
    """
    Given a project and available users, return the best matched team.

    Args:
        project: Project object
        available_users: List of User objects

    Returns:
        dict: {
            "team_members": [user1, user2, user3, ...],
            "missing_skills": [... list of missing skills if any ...],
        }
    """
    # Validate team size
    team_size = min(max(project.team_size, 1), 6)  # Ensure team size is between 1 and 6

    # Ensure project owner is not in available_users
    available_users = [user for user in available_users if user != project.owner]

    # Calculate scores for each user
    user_scores = []
    for user in available_users:
        score, matching_skills, missing_skills = score_user_for_project(user, project, project.owner)
        user_scores.append((user, score, matching_skills, missing_skills))

    # Sort users by score (highest first)
    user_scores.sort(key=lambda x: x[1], reverse=True)

    # Select top users based on team size
    selected_users = user_scores[:team_size]

    # Extract team members and their matching skills
    team_members = [user_tuple[0] for user_tuple in selected_users]

    # Calculate missing skills across the entire team
    # Extract only the skill names from the matching_skills dictionaries
    all_matched_skill_names = set()
    for _, _, matching_skills, _ in selected_users:
        skill_names = [skill['name'] for skill in matching_skills]
        all_matched_skill_names.update(skill_names)

    # Get the required skills that are not matched by any team member
    required_skill_names = set(project.required_skills.values_list('name', flat=True))
    team_missing_skills = required_skill_names - all_matched_skill_names

    return {
        "team_members": team_members,
        "missing_skills": list(team_missing_skills),
    }

def is_user_available_now(user):
    """
    Check if a user is available right now based on their availability window.

    Args:
        user: User to check

    Returns:
        bool: True if user is available now, False otherwise
    """
    try:
        profile = UserProfile.objects.get(user=user)

        # If no availability set, assume not available
        if not profile.availability_start or not profile.availability_end:
            return False

        # Get current time in the user's timezone
        user_tz = pytz.timezone(profile.timezone)
        now = timezone.now().astimezone(user_tz).time()

        start = profile.availability_start
        end = profile.availability_end

        # Handle wrap-around availability (e.g., 22:00 - 06:00)
        if end < start:
            return now >= start or now <= end
        else:
            return start <= now <= end

    except UserProfile.DoesNotExist:
        return False

def find_best_team_with_availability_priority(project, available_users):
    """
    Enhanced version of find_best_team that prioritizes currently available users.

    Args:
        project: Project object
        available_users: List of User objects

    Returns:
        dict: Team formation result
    """
    # Try to get cached results first
    cache_key = f"team_match_{project.id}_{','.join(str(user.id) for user in available_users)}"
    cached_result = cache.get(cache_key)
    if cached_result:
        logger.info(f"Using cached match results for project {project.id}")
        return cached_result

    logger.info(f"Calculating new match for project {project.id} with {len(available_users)} available users")

    # Validate team size
    team_size = min(max(project.team_size, 1), 6)

    # Ensure project owner is not in available_users
    available_users = [user for user in available_users if user != project.owner]

    # Split users into currently available and not available
    available_now = []
    not_available_now = []

    for user in available_users:
        if is_user_available_now(user):
            available_now.append(user)
        else:
            not_available_now.append(user)

    # Calculate scores for each group
    available_scores = []
    for user in available_now:
        score, matching_skills, missing_skills = score_user_for_project(user, project, project.owner)
        # Add a bonus for being available now
        score += 10
        available_scores.append((user, score, matching_skills, missing_skills))

    not_available_scores = []
    for user in not_available_now:
        score, matching_skills, missing_skills = score_user_for_project(user, project, project.owner)
        not_available_scores.append((user, score, matching_skills, missing_skills))

    # Sort both groups by score
    available_scores.sort(key=lambda x: x[1], reverse=True)
    not_available_scores.sort(key=lambda x: x[1], reverse=True)

    # Combine the lists, with available users first
    combined_scores = available_scores + not_available_scores

    # Select top users based on team size
    selected_users = combined_scores[:team_size]

    # Extract team members and their matching skills
    team_members = [user_tuple[0] for user_tuple in selected_users]

    # Calculate matched skills across the entire team
    # Extract only the skill names from the matching_skills dictionaries
    all_matched_skill_names = set()
    for _, _, matching_skills, _ in selected_users:
        skill_names = [skill['name'] for skill in matching_skills]
        all_matched_skill_names.update(skill_names)

    # Get the required skills that are not matched by any team member
    required_skill_names = set(project.required_skills.values_list('name', flat=True))
    team_missing_skills = required_skill_names - all_matched_skill_names

    # Calculate skill coverage percentage
    if required_skill_names:
        coverage_percent = (len(all_matched_skill_names) / len(required_skill_names)) * 100
    else:
        coverage_percent = 100  # If no required skills, consider 100% coverage

    # Analyze team balance
    team_balance = analyze_team_balance(team_members, project)

    # Identify skill gaps
    skill_gaps = identify_skill_gaps(team_members, project)

    # Create result dictionary
    result = {
        "team_members": team_members,
        "missing_skills": list(team_missing_skills),
        "skill_coverage_percent": coverage_percent,
        "is_optimal_match": coverage_percent >= 80,  # Consider it optimal if 80% or more skills are covered
        "team_balance": team_balance,
        "skill_gaps": skill_gaps,
        "balance_score": team_balance.get('balance_score', 0),
        "has_critical_gaps": skill_gaps.get('has_critical_gaps', False)
    }

    # Log the match for analysis
    try:
        # Import both log models
        from .models import TeamMatchLog, TeamMatchAnalysisLog

        # Create analysis log
        match_analysis_log = TeamMatchAnalysisLog.objects.create(
            project=project,
            match_data={
                "skill_coverage_percent": coverage_percent,
                "is_optimal_match": coverage_percent >= 80,
                "balance_score": team_balance.get('balance_score', 0),
                "has_critical_gaps": skill_gaps.get('has_critical_gaps', False),
                "team_size": len(team_members),
                "available_now_count": len(available_now),
                "total_available_users": len(available_users)
            }
        )
        match_analysis_log.team_members.set(team_members)

        # Also log individual matches
        for user in team_members:
            score, matching_skills, _ = score_user_for_project(user, project, project.owner)
            TeamMatchLog.objects.create(
                project=project,
                user=user,
                match_score=score,
                overlap_skills=[skill['name'] for skill in matching_skills],
                missing_skills=[]
            )

        logger.info(f"Created match logs for project {project.id}")
    except Exception as e:
        logger.error(f"Error creating match log: {str(e)}")

    # Cache the result for 10 minutes
    cache.set(cache_key, result, 600)

    return result

def find_best_backup_member(project, _available_users):
    """
    Find the best backup teammate when a previously selected user declines an invitation.

    Args:
        project: Project that needs a backup teammate
        available_users: QuerySet of User objects who are available to be selected

    Returns:
        User: Best matching user for the project, or None if no suitable user found
    """
    best_candidate = None
    highest_match_score = 0

    # Get all users who aren't already members or invited
    User = get_user_model()
    already_members = ProjectMembership.objects.filter(project=project).values_list('user_id', flat=True)
    already_invited = Notification.objects.filter(
        project=project,
        is_accepted__isnull=True
    ).values_list('user_id', flat=True)

    # Combine users to exclude
    excluded_users = list(already_members) + list(already_invited) + [project.owner.id]

    # Get eligible users
    eligible_users = User.objects.filter(is_active=True).exclude(id__in=excluded_users)

    # If no eligible users, return None
    if not eligible_users.exists():
        return None

    # Find the best match
    for user in eligible_users:
        # Calculate user score using the scoring function
        score, _, _ = score_user_for_project(user, project, project.owner)

        # Add availability bonus
        if is_user_available_now(user):
            score += 10

        if score > highest_match_score:
            highest_match_score = score
            best_candidate = user

    return best_candidate