from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from mentorship.models import MentorshipSession, ChatMessage


class Command(BaseCommand):
    help = 'Test chat message functionality'

    def handle(self, *args, **options):
        # Get the first session
        session = MentorshipSession.objects.first()
        if not session:
            self.stdout.write(self.style.ERROR('No mentorship sessions found'))
            return

        # Get the first user
        user = User.objects.first()
        if not user:
            self.stdout.write(self.style.ERROR('No users found'))
            return

        # Create a test message
        message = ChatMessage.objects.create(
            session=session,
            sender=user,
            content="Test message from management command",
            message_type='chat'
        )

        self.stdout.write(
            self.style.SUCCESS(f'Created test message: {message.id}')
        )

        # List all messages for this session
        messages = ChatMessage.objects.filter(session=session)
        self.stdout.write(f'Total messages for session {session.id}: {messages.count()}')
        
        for msg in messages:
            self.stdout.write(f'- {msg.sender.username if msg.sender else "System"}: {msg.content}')
