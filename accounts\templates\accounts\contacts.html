{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Us - Forge X{% endblock %}

{% block content %}
<style>
/* Contact Page Specific Styles */
.contact-container {
  padding: 40px 0;
  min-height: 80vh;
}

.contact-hero {
  text-align: center;
  margin-bottom: 60px;
  padding: 40px 0;
}

.contact-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-hero p {
  font-size: 1.2rem;
  color: #d5d5d5;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Contact Info Cards */
.contact-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.contact-card {
  background: linear-gradient(135deg, rgba(28,28,28,0.9) 0%, rgba(45,45,45,0.9) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #C0ff6b 0%, #ffffff 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.contact-card:hover::before {
  opacity: 1;
}

.contact-card:hover {
  transform: translateY(-10px);
  border-color: rgba(192, 255, 107, 0.4);
  box-shadow: 0 20px 40px rgba(192, 255, 107, 0.1);
}

.contact-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #C0ff6b;
  display: block;
}

.contact-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 15px;
}

.contact-card p {
  color: #d5d5d5;
  line-height: 1.6;
  margin: 0;
}

/* Contact Form */
.contact-form-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 50px;
  backdrop-filter: blur(10px);
  max-width: 600px;
  margin: 0 auto;
}

.contact-form-section h2 {
  text-align: center;
  font-size: 2rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  font-weight: 500;
  color: #C0ff6b;
  margin-bottom: 8px;
  font-size: 1rem;
}

.form-control {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid rgba(192, 255, 107, 0.2);
  border-radius: 12px;
  background: rgba(28, 28, 28, 0.8);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-control:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 20px rgba(192, 255, 107, 0.2);
  background: rgba(28, 28, 28, 0.9);
}

.form-control::placeholder {
  color: #888;
}

textarea.form-control {
  height: 120px;
  resize: vertical;
  min-height: 120px;
}

.submit-btn {
  width: 100%;
  padding: 15px 30px;
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #000000;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(192, 255, 107, 0.3);
  background: linear-gradient(135deg, #a8e055 0%, #C0ff6b 100%);
}

.submit-btn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-hero h1 {
    font-size: 2.5rem;
  }

  .contact-hero p {
    font-size: 1rem;
    padding: 0 20px;
  }

  .contact-info-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .contact-card {
    padding: 30px 20px;
  }

  .contact-form-section {
    padding: 30px 20px;
    margin: 0 20px;
  }

  .contact-container {
    padding: 20px 0;
  }
}

@media (max-width: 480px) {
  .contact-hero h1 {
    font-size: 2rem;
  }

  .contact-card {
    padding: 25px 15px;
  }

  .contact-form-section {
    padding: 25px 15px;
  }
}
</style>

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<div class="contact-container">
  <div class="tile-wrap">
    <!-- Hero Section -->
    <div class="contact-hero fade-in">
      <h1>Get in Touch</h1>
      <p>Have questions about our platform? Want to collaborate or need support? We'd love to hear from you. Reach out to our team and let's build something amazing together.</p>
    </div>

    <!-- Contact Information -->
    <div class="contact-info-grid">
      <div class="contact-card fade-in" data-delay="0">
        <i class="fas fa-map-marker-alt contact-icon"></i>
        <h3>Location</h3>
        <p>P84M+70G, Maiti Devi Marg,<br>Kathmandu 44600, Nepal</p>
      </div>

      <div class="contact-card fade-in" data-delay="100">
        <i class="fas fa-envelope contact-icon"></i>
        <h3>Email</h3>
        <p><EMAIL><br><EMAIL></p>
      </div>

      <div class="contact-card fade-in" data-delay="200">
        <i class="fas fa-phone contact-icon"></i>
        <h3>Phone</h3>
        <p>+977-01-4524567<br>Available 24/7</p>
      </div>

      <div class="contact-card fade-in" data-delay="300">
        <i class="fas fa-clock contact-icon"></i>
        <h3>Working Hours</h3>
        <p>Monday to Saturday<br>9:00 AM to 5:00 PM NPT</p>
      </div>
    </div>

    <!-- Contact Form -->
    <div class="contact-form-section fade-in" data-delay="400">
      <h2>Send us a Message</h2>
      <form class="contact-form" method="post" action="{% url 'accounts:contact' %}">
        {% csrf_token %}
        <div class="form-group">
          <label for="name">Full Name</label>
          <input type="text" id="name" name="name" class="form-control" placeholder="Enter your full name" required>
        </div>

        <div class="form-group">
          <label for="email">Email Address</label>
          <input type="email" id="email" name="email" class="form-control" placeholder="Enter your email address" required>
        </div>

        <div class="form-group">
          <label for="phone">Phone Number</label>
          <input type="tel" id="phone" name="phone" class="form-control" placeholder="Enter your phone number">
        </div>

        <div class="form-group">
          <label for="subject">Subject</label>
          <input type="text" id="subject" name="subject" class="form-control" placeholder="What is this about?" required>
        </div>

        <div class="form-group">
          <label for="message">Message</label>
          <textarea id="message" name="message" class="form-control" placeholder="Tell us more about your inquiry..." required></textarea>
        </div>

        <button type="submit" class="submit-btn">
          <i class="fas fa-paper-plane"></i> Send Message
        </button>
      </form>
    </div>
  </div>
</div>

<!-- JavaScript for animations and particles -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": "#C0ff6b"
        },
        "shape": {
          "type": "circle",
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": false,
          "anim": {
            "enable": false,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": false,
            "speed": 40,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 6,
          "direction": "none",
          "random": false,
          "straight": false,
          "out_mode": "out",
          "bounce": false,
          "attract": {
            "enable": false,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "repulse"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 400,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 400,
            "size": 40,
            "duration": 2,
            "opacity": 8,
            "speed": 3
          },
          "repulse": {
            "distance": 200,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
    });

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        const delay = parseInt(element.getAttribute('data-delay')) || 0;

        if (elementTop < windowHeight - elementVisible) {
          setTimeout(() => {
            element.classList.add('visible');
          }, delay);
        }
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up scroll event listener
    window.addEventListener('scroll', revealOnScroll);

    // Form submission handling
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
      contactForm.addEventListener('submit', function(e) {
        // Don't prevent default - let the form submit normally
        const submitBtn = this.querySelector('.submit-btn');
        const originalText = submitBtn.innerHTML;

        // Show loading state
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        submitBtn.disabled = true;

        // Re-enable button after a short delay in case of errors
        setTimeout(() => {
          if (submitBtn.disabled) {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
          }
        }, 5000);
      });
    }
  });
</script>
{% endblock %}