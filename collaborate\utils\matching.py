"""
Matching Utility - ForgeX

This module provides functions for matching users with projects based on skills,
availability, and other factors.
"""

import pytz
from datetime import datetime
from django.utils import timezone
from accounts.models import UserProfile
from collaborate.models import UserSkill

def score_user_for_project(user, project, project_owner):
    """
    Calculate a score for how well a user matches a project.

    Args:
        user: User to evaluate
        project: Project to match against
        project_owner: Owner of the project

    Returns:
        tuple: (score, matching_skills, missing_skills)
    """
    # Initialize score and skill lists
    score = 0
    matching_skills = []
    missing_skills = []

    # Get user's skills with proficiency levels
    user_skills_data = UserSkill.objects.filter(user=user).select_related('skill')
    user_skill_ids = [us.skill.id for us in user_skills_data]
    user_skill_names = [us.skill.name for us in user_skills_data]

    # Create a dictionary mapping skill IDs to proficiency levels
    skill_proficiency = {us.skill.id: us.proficiency for us in user_skills_data}

    # Get project's required skills
    project_required_skills = list(project.required_skills.all())
    project_skill_ids = [skill.id for skill in project_required_skills]
    project_skill_names = [skill.name for skill in project_required_skills]

    # Get project's critical skills
    critical_skill_ids = list(project.critical_skills.values_list('id', flat=True))

    # Calculate matching skills
    for skill in project_required_skills:
        if skill.id in user_skill_ids:
            # Add points based on proficiency (1-5 scale)
            proficiency = skill_proficiency.get(skill.id, 1)
            
            # Base points for having the skill
            points = 10
            
            # Bonus points for higher proficiency
            if proficiency >= 4:  # Expert/Master
                points += 5
            elif proficiency >= 2:  # Intermediate/Advanced
                points += 2
                
            # Extra points for critical skills
            if skill.id in critical_skill_ids:
                points *= 1.5  # 50% bonus for critical skills
                
            score += points
            
            # Add to matching skills list with proficiency info
            proficiency_name = get_proficiency_name(proficiency)
            matching_skills.append({
                'id': skill.id,
                'name': skill.name,
                'proficiency': proficiency,
                'proficiency_name': proficiency_name,
                'is_critical': skill.id in critical_skill_ids
            })
        else:
            # Penalize for missing skills
            penalty = -5
            
            # Higher penalty for missing critical skills
            if skill.id in critical_skill_ids:
                penalty = -10
                
            score += penalty
            
            # Add to missing skills list
            missing_skills.append({
                'id': skill.id,
                'name': skill.name,
                'is_critical': skill.id in critical_skill_ids
            })

    # Add small bonus for extra skills that weren't required (+0.5 points each)
    extra_skills = set(user_skill_ids) - set(project_skill_ids)
    if extra_skills:
        score += len(extra_skills) * 0.5

    # Get user and project owner timezones
    try:
        user_profile = UserProfile.objects.get(user=user)
        owner_profile = UserProfile.objects.get(user=project_owner)

        # Calculate timezone compatibility
        timezone_diff = calculate_timezone_difference(
            user_profile.timezone,
            owner_profile.timezone
        )

        # Award more points for closer timezone matches
        if timezone_diff <= 1:
            score += 10  # +10 points if timezone difference <= 1 hour
        elif timezone_diff <= 3:
            score += 5   # +5 points if timezone difference <= 3 hours
        elif timezone_diff <= 6:
            score += 2   # +2 points if timezone difference <= 6 hours

        # Consider country for potential cultural compatibility
        if hasattr(user_profile, 'country') and hasattr(owner_profile, 'country'):
            if user_profile.country and owner_profile.country and user_profile.country == owner_profile.country:
                score += 3  # Small bonus for same country

    except UserProfile.DoesNotExist:
        # If profile doesn't exist, don't add any timezone or availability points
        pass

    return (score, matching_skills, missing_skills)

def is_user_available_now(user):
    """
    Check if a user is available right now based on their availability window.

    Args:
        user: User to check

    Returns:
        bool: True if user is available now, False otherwise
    """
    try:
        profile = UserProfile.objects.get(user=user)

        # If no availability set, assume not available
        if not profile.availability_start or not profile.availability_end:
            return False

        # Get current time in the user's timezone
        user_tz = pytz.timezone(profile.timezone)
        now = timezone.now().astimezone(user_tz).time()

        start = profile.availability_start
        end = profile.availability_end

        # Handle wrap-around availability (e.g., 22:00 - 06:00)
        if end < start:
            return now >= start or now <= end
        else:
            return start <= now <= end

    except UserProfile.DoesNotExist:
        return False

def calculate_timezone_difference(timezone1, timezone2):
    """
    Calculate the hour difference between two timezones.

    Args:
        timezone1: First timezone string (e.g., 'America/New_York')
        timezone2: Second timezone string (e.g., 'Europe/London')

    Returns:
        int: Absolute hour difference between the two timezones
    """
    if not timezone1 or not timezone2:
        return 12  # Default to maximum difference if either timezone is missing
        
    try:
        tz1 = pytz.timezone(timezone1)
        tz2 = pytz.timezone(timezone2)
        
        # Get current UTC time
        now_utc = datetime.now(pytz.UTC)
        
        # Convert to both timezones
        now_tz1 = now_utc.astimezone(tz1)
        now_tz2 = now_utc.astimezone(tz2)
        
        # Calculate hour difference
        hour_diff = abs(now_tz1.hour - now_tz2.hour)
        
        # Handle cases where the difference crosses day boundary
        if hour_diff > 12:
            hour_diff = 24 - hour_diff
            
        return hour_diff
        
    except Exception:
        return 12  # Default to maximum difference if there's an error

def get_proficiency_name(proficiency_level):
    """
    Convert numeric proficiency level to a descriptive name.

    Args:
        proficiency_level: Integer proficiency level (1-5)

    Returns:
        str: Descriptive name for the proficiency level
    """
    proficiency_names = {
        1: "Beginner",
        2: "Intermediate",
        3: "Advanced",
        4: "Expert",
        5: "Master"
    }
    return proficiency_names.get(proficiency_level, "Unknown")
