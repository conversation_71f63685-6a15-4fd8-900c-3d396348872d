{% extends 'base.html' %}
{% load collaborate_extras %}

{% block content %}
<div class="container mt-5">
  <h2>Testing Template Tag</h2>
  
  <div class="card">
    <div class="card-header">
      <h5>Test Data</h5>
    </div>
    <div class="card-body">
      <p>Original Dictionary: {{ test_dict }}</p>
      
      <h6>Using get_item filter:</h6>
      <ul>
        {% for key in test_keys %}
          <li>{{ key }}: {{ test_dict|get_item:key }}</li>
        {% endfor %}
      </ul>
    </div>
  </div>
</div>
{% endblock %}
