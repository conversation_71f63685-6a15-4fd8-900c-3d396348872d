{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Notification System Demo{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/account.css' %}">
<link rel="stylesheet" href="{% static 'css/notifications.css' %}">
{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="account-page">
    <div class="account-card fade-in expanded-profile">
        <h2>🚀 Enhanced Notification System Demo</h2>
        <p style="color: #ffffff; margin-bottom: 30px;">
            Experience the new notification system with perfect animations, 5-second timer, and glowing border effects!
        </p>

        <!-- Quick Demo Section -->
        <div class="demo-notification-section">
            <h3>✨ Quick Demo - Try Different Notification Types</h3>
            <p style="color: #cccccc; margin-bottom: 15px;">
                Click any button below to see the enhanced notification with:
                <br>• Right-center positioning • 5-second auto-dismiss timer • Glowing border animation • Smooth entrance/exit effects
            </p>

            <div class="demo-buttons">
                <button class="demo-btn info" onclick="showDemoNotification('info')">
                    <i class="fas fa-info-circle"></i> Info Notification
                </button>
                <button class="demo-btn success" onclick="showDemoNotification('success')">
                    <i class="fas fa-check-circle"></i> Success Notification
                </button>
                <button class="demo-btn warning" onclick="showDemoNotification('warning')">
                    <i class="fas fa-exclamation-triangle"></i> Warning Notification
                </button>
                <button class="demo-btn error" onclick="showDemoNotification('error')">
                    <i class="fas fa-times-circle"></i> Error Notification
                </button>
                <button class="demo-btn system" onclick="showDemoNotification('system')">
                    <i class="fas fa-bullhorn"></i> System Notification
                </button>
                <button class="demo-btn invitation" onclick="showDemoNotification('invitation')">
                    <i class="fas fa-user-plus"></i> Invitation Notification
                </button>
            </div>
        </div>

        <!-- Custom Notification Form -->
        <div class="profile-section">
            <h3>🎛️ Custom Notification Builder</h3>
            <p style="color: #cccccc; margin-bottom: 20px;">Create your own custom notification to test the system.</p>

            <form id="test-notification-form" class="account-form">
                <div class="form-group">
                    <label for="notification-title">Notification Title</label>
                    <input type="text" class="form-control" id="notification-title" value="Custom Test Notification" placeholder="Enter notification title">
                </div>

                <div class="form-group">
                    <label for="notification-message">Message Content</label>
                    <textarea class="form-control" id="notification-message" rows="3" placeholder="Enter your notification message">This is a custom test notification with enhanced animations and perfect timing!</textarea>
                </div>

                <div class="form-group">
                    <label for="notification-type">Notification Type</label>
                    <select class="form-control" id="notification-type">
                        <option value="info">📘 Info</option>
                        <option value="success">✅ Success</option>
                        <option value="warning">⚠️ Warning</option>
                        <option value="error">❌ Error</option>
                        <option value="system">📢 System</option>
                        <option value="invitation">👥 Invitation</option>
                    </select>
                </div>

                <button type="submit" class="account-btn">
                    <i class="fas fa-bell"></i> Send Custom Notification
                </button>
            </form>
        </div>

        <!-- Connection Status Section -->
        <div class="profile-section">
            <h3>🔗 WebSocket Connection Status</h3>
            <p style="color: #cccccc; margin-bottom: 15px;">
                Monitor your real-time notification connection status.
            </p>
            <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                <span style="color: #ffffff;">Status:</span>
                <span id="ws-status" class="badge bg-secondary">Checking...</span>
            </div>
            <button id="reconnect-btn" class="account-btn">
                <i class="fas fa-sync"></i> Reconnect WebSocket
            </button>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<!-- Particles.js for background -->
<script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

<script>
    // Initialize particles background
    particlesJS('particles-js', {
        particles: {
            number: { value: 80, density: { enable: true, value_area: 800 } },
            color: { value: "#c0ff6b" },
            shape: { type: "circle" },
            opacity: { value: 0.5, random: false },
            size: { value: 3, random: true },
            line_linked: { enable: true, distance: 150, color: "#c0ff6b", opacity: 0.4, width: 1 },
            move: { enable: true, speed: 6, direction: "none", random: false, straight: false, out_mode: "out", bounce: false }
        },
        interactivity: {
            detect_on: "canvas",
            events: { onhover: { enable: true, mode: "repulse" }, onclick: { enable: true, mode: "push" }, resize: true },
            modes: { grab: { distance: 400, line_linked: { opacity: 1 } }, bubble: { distance: 400, size: 40, duration: 2, opacity: 8, speed: 3 }, repulse: { distance: 200, duration: 0.4 }, push: { particles_nb: 4 }, remove: { particles_nb: 2 } }
        },
        retina_detect: true
    });

    // Demo notification messages
    const demoMessages = {
        info: {
            title: "📘 Information",
            message: "This is an informational notification with enhanced animations and perfect timing!"
        },
        success: {
            title: "✅ Success!",
            message: "Operation completed successfully! Notice the smooth glowing border animation."
        },
        warning: {
            title: "⚠️ Warning",
            message: "This is a warning notification. Watch the 5-second countdown timer!"
        },
        error: {
            title: "❌ Error Occurred",
            message: "An error has occurred. The notification will auto-dismiss after 5 seconds."
        },
        system: {
            title: "📢 System Update",
            message: "System notification with enhanced positioning and beautiful animations."
        },
        invitation: {
            title: "👥 New Invitation",
            message: "You have received a new invitation! Check out the right-center positioning."
        }
    };

    // Function to show demo notifications
    function showDemoNotification(type) {
        const demo = demoMessages[type];
        if (demo && window.showNotification) {
            showNotification(demo.title, demo.message, type);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('test-notification-form');
        const statusEl = document.getElementById('ws-status');
        const reconnectBtn = document.getElementById('reconnect-btn');

        // Add fade-in animation to elements
        setTimeout(() => {
            document.querySelector('.account-card').classList.add('visible');
        }, 100);

        // Update WebSocket status
        function updateStatus() {
            if (window.notificationSocket) {
                switch(window.notificationSocket.readyState) {
                    case WebSocket.CONNECTING:
                        statusEl.textContent = 'Connecting...';
                        statusEl.className = 'badge bg-warning';
                        break;
                    case WebSocket.OPEN:
                        statusEl.textContent = '✅ Connected';
                        statusEl.className = 'badge bg-success';
                        break;
                    case WebSocket.CLOSING:
                        statusEl.textContent = 'Closing...';
                        statusEl.className = 'badge bg-warning';
                        break;
                    case WebSocket.CLOSED:
                        statusEl.textContent = '❌ Disconnected';
                        statusEl.className = 'badge bg-danger';
                        break;
                }
            } else {
                statusEl.textContent = '⚪ Not Initialized';
                statusEl.className = 'badge bg-secondary';
            }
        }

        // Check status every second
        setInterval(updateStatus, 1000);
        updateStatus();

        // Handle form submission
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const title = document.getElementById('notification-title').value;
            const message = document.getElementById('notification-message').value;
            const type = document.getElementById('notification-type').value;

            // Show notification using our notification.js function
            if (window.showNotification) {
                showNotification(title, message, type);
            } else {
                alert('Notification system not loaded. Please refresh the page.');
            }
        });

        // Handle reconnect button
        reconnectBtn.addEventListener('click', function() {
            if (window.connectNotificationSocket) {
                connectNotificationSocket();
                updateStatus();
            } else {
                alert('Notification system not loaded. Please refresh the page.');
            }
        });

        // Show welcome notification after page loads
        setTimeout(() => {
            if (window.showNotification) {
                showNotification(
                    "🚀 Welcome to Enhanced Notifications!",
                    "Try the demo buttons to see the new notification system in action. Notice the right-center positioning, 5-second timer, and glowing border animation!",
                    "system"
                );
            }
        }, 1500);
    });
</script>
{% endblock %}
