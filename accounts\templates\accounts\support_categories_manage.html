{% extends 'base.html' %}
{% load static %}

{% block title %}Manage Support Categories - Forge X{% endblock %}

{% block content %}
<style>
/* Categories Management Styles */
.categories-container {
  padding: 40px 0;
  min-height: 80vh;
}

.categories-header {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.categories-title {
  color: #C0ff6b;
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #C0ff6b 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-section {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.section-title {
  color: #C0ff6b;
  font-size: 1.8rem;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  color: #C0ff6b;
  font-weight: 600;
  margin-bottom: 8px;
}

.form-control {
  width: 100%;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 12px 15px;
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 0 3px rgba(192, 255, 107, 0.2);
}

.form-check {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #ffffff;
}

.btn {
  background: linear-gradient(135deg, #C0ff6b 0%, #a8e055 100%);
  color: #1a1a1a;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.categories-list {
  background: linear-gradient(135deg, rgba(28,28,28,0.95) 0%, rgba(45,45,45,0.95) 100%);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
}

.category-item {
  background: rgba(192, 255, 107, 0.05);
  border: 1px solid rgba(192, 255, 107, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.3);
}

.category-name {
  color: #C0ff6b;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.category-description {
  color: #d5d5d5;
  margin-bottom: 10px;
}

.category-status {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
}

.status-active {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-inactive {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.back-link {
  display: inline-block;
  color: #C0ff6b;
  text-decoration: none;
  margin-bottom: 20px;
  transition: color 0.3s ease;
}

.back-link:hover {
  color: #ffffff;
  text-decoration: none;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .categories-container {
    padding: 20px 10px;
  }

  .categories-header, .form-section, .categories-list {
    margin: 0 10px 20px 10px;
    padding: 20px;
  }

  .categories-title {
    font-size: 2rem;
  }
}
</style>

<div class="categories-container">
  <div class="tile-wrap">
    <!-- Header -->
    <div class="categories-header">
      <a href="{% url 'accounts:support_admin_dashboard' %}" class="back-link">
        <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
      </a>
      
      <h1 class="categories-title">Manage Support Categories</h1>
      <p style="color: #d5d5d5; margin: 0;">Create and manage categories for organizing support tickets</p>
    </div>

    <!-- Create Category Form -->
    <div class="form-section">
      <h2 class="section-title">Create New Category</h2>
      
      <form method="post">
        {% csrf_token %}
        
        <div class="form-group">
          <label for="{{ form.name.id_for_label }}" class="form-label">
            {{ form.name.label }}
          </label>
          {{ form.name }}
          {{ form.name.errors }}
        </div>
        
        <div class="form-group">
          <label for="{{ form.description.id_for_label }}" class="form-label">
            {{ form.description.label }}
          </label>
          {{ form.description }}
          {{ form.description.errors }}
        </div>
        
        <div class="form-group">
          <div class="form-check">
            {{ form.is_active }}
            <label for="{{ form.is_active.id_for_label }}">{{ form.is_active.label }}</label>
          </div>
          {{ form.is_active.errors }}
        </div>
        
        <button type="submit" class="btn">
          <i class="fas fa-plus"></i> Create Category
        </button>
      </form>
    </div>

    <!-- Existing Categories -->
    <div class="categories-list">
      <h2 class="section-title">Existing Categories</h2>
      
      {% if categories %}
        {% for category in categories %}
        <div class="category-item">
          <div class="category-name">{{ category.name }}</div>
          <div class="category-description">
            {{ category.description|default:"No description provided" }}
          </div>
          <div style="margin-top: 10px;">
            <span class="category-status {% if category.is_active %}status-active{% else %}status-inactive{% endif %}">
              {% if category.is_active %}Active{% else %}Inactive{% endif %}
            </span>
            <span style="color: #999; margin-left: 15px; font-size: 0.9rem;">
              Created: {{ category.created_at|date:"M d, Y" }}
            </span>
          </div>
        </div>
        {% endfor %}
      {% else %}
        <div class="empty-state">
          <i class="fas fa-tags" style="font-size: 3rem; margin-bottom: 20px; display: block;"></i>
          No categories found. Create your first category above!
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
