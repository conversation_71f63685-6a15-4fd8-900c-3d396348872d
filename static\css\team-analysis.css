/* Team Analysis Pages - Consistent Styling */

/* Ensure CSS variables are available */
:root {
  --color-border: #C0ff6b;
  --color-bg: #000000;
  --color-container: #656565;
  --color-secondary: #d5d5d5;
}

/* Analysis Sections */
.analysis-section {
  margin-bottom: 60px !important;
  padding: 30px !important;
  background: rgba(40, 40, 40, 0.4) !important;
  border-radius: 20px !important;
  border: 1px solid rgba(192, 255, 107, 0.2) !important;
}

.section-header {
  margin-bottom: 35px !important;
  padding-bottom: 15px !important;
  border-bottom: 2px solid rgba(192, 255, 107, 0.3) !important;
}

.section-header h3 {
  color: #C0ff6b !important;
  font-size: 28px !important;
  font-weight: 600 !important;
  margin: 0 !important;
  text-align: center !important;
}

/* Stats Grid */
.stats-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
  gap: 30px !important;
  margin-bottom: 40px !important;
}

.stat-card {
  background: rgba(40, 40, 40, 0.8) !important;
  border: 1px solid rgba(192, 255, 107, 0.3) !important;
  border-radius: 20px !important;
  padding: 35px 25px !important;
  display: flex !important;
  align-items: center !important;
  gap: 20px !important;
  transition: all 0.3s ease !important;
  min-height: 120px !important;
}

.stat-card:hover {
  transform: translateY(-8px) !important;
  border-color: rgba(192, 255, 107, 0.6) !important;
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.25) !important;
}

.stat-icon {
  font-size: 40px !important;
  color: #C0ff6b !important;
  width: 60px !important;
  text-align: center !important;
  flex-shrink: 0 !important;
}

.stat-content {
  flex: 1 !important;
}

.stat-value {
  font-size: 32px !important;
  font-weight: 700 !important;
  color: #C0ff6b !important;
  margin-bottom: 8px !important;
  line-height: 1 !important;
}

.stat-label {
  color: #ffffff !important;
  font-size: 16px !important;
  opacity: 0.9 !important;
  font-weight: 500 !important;
}

/* Progress Bars */
.progress-section {
  margin: 35px 0;
  padding: 25px;
  background: rgba(60, 60, 60, 0.3);
  border-radius: 15px;
}

.progress-bar-container {
  margin-bottom: 20px;
}

.progress-label {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 16px;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  height: 16px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-fill {
  background: linear-gradient(90deg, var(--color-border), #a0e066);
  height: 100%;
  border-radius: 12px;
  transition: width 0.5s ease;
  box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3);
}

.progress-text {
  color: #ffffff;
  font-size: 15px;
  margin-top: 8px;
  text-align: right;
  font-weight: 500;
}

/* Status Alerts */
.status-alert {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px 30px;
  border-radius: 15px;
  margin: 30px 0;
  border-left: 5px solid;
}

.status-alert.success {
  background: rgba(40, 167, 69, 0.15);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-left-color: #28a745;
}

.status-alert.warning {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-left-color: #ffc107;
}

.status-alert.danger {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-left-color: #dc3545;
}

.status-alert.info {
  background: rgba(13, 202, 240, 0.15);
  border: 1px solid rgba(13, 202, 240, 0.3);
  border-left-color: #0dcaf0;
}

.status-alert i {
  font-size: 28px;
  margin-top: 3px;
  flex-shrink: 0;
}

.status-alert.success i { color: #28a745; }
.status-alert.warning i { color: #ffc107; }
.status-alert.danger i { color: #dc3545; }
.status-alert.info i { color: #0dcaf0; }

.status-alert div h4,
.status-alert div strong {
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 18px;
  display: block;
}

.status-alert div p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

/* Experience Cards */
.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.experience-card {
  background: rgba(40, 40, 40, 0.8);
  border-radius: 20px;
  padding: 35px 25px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.experience-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.experience-card.beginner {
  border-color: rgba(220, 53, 69, 0.5);
  background: rgba(220, 53, 69, 0.05);
}

.experience-card.intermediate {
  border-color: rgba(255, 193, 7, 0.5);
  background: rgba(255, 193, 7, 0.05);
}

.experience-card.expert {
  border-color: rgba(40, 167, 69, 0.5);
  background: rgba(40, 167, 69, 0.05);
}

.experience-icon {
  font-size: 40px;
  margin-bottom: 20px;
}

.experience-card.beginner .experience-icon { color: #dc3545; }
.experience-card.intermediate .experience-icon { color: #ffc107; }
.experience-card.expert .experience-icon { color: #28a745; }

.experience-count {
  font-size: 42px;
  font-weight: 700;
  color: var(--color-border);
  margin-bottom: 12px;
  line-height: 1;
}

.experience-label {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
}

.experience-desc {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* Info Boxes */
.info-box {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: rgba(40, 40, 40, 0.6);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px 30px;
  margin: 30px 0;
  border-left: 4px solid var(--color-border);
}

.info-box i {
  font-size: 28px;
  color: var(--color-border);
  margin-top: 3px;
  flex-shrink: 0;
}

.info-box div strong {
  color: var(--color-border);
  display: block;
  margin-bottom: 12px;
  font-size: 18px;
}

.info-box div p,
.info-box div ul {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

.info-box div ul {
  margin-top: 15px;
  padding-left: 25px;
}

.info-box div li {
  margin-bottom: 8px;
}

/* Level Badges */
.level-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.level-badge.beginner {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.4);
}

.level-badge.intermediate {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.4);
}

.level-badge.expert {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.4);
}

/* Skills Grid */
.skills-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 30px;
}

.skills-column {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.skills-column-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.skills-column-title.covered {
  color: #28a745;
}

.skills-column-title.missing {
  color: #dc3545;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.skill-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-height: 50px;
}

.skill-item:hover {
  transform: translateX(5px);
}

.skill-item.covered {
  background: rgba(40, 167, 69, 0.15);
  border: 1px solid rgba(40, 167, 69, 0.4);
}

.skill-item.missing {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.4);
}

.skill-item.critical {
  border-width: 2px;
  box-shadow: 0 2px 8px rgba(255, 193, 7, 0.2);
}

.skill-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 15px;
}

.critical-icon {
  color: #ffc107;
  margin-left: 10px;
  font-size: 16px;
}

.skill-count,
.skill-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.no-skills {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 30px 20px;
  font-size: 16px;
}

/* Coverage Summary */
.skill-coverage-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 40px;
  align-items: center;
  margin-bottom: 35px;
  padding: 25px;
  background: rgba(60, 60, 60, 0.3);
  border-radius: 15px;
}

.coverage-summary {
  display: flex;
  gap: 40px;
}

.coverage-stat {
  text-align: center;
  padding: 20px;
  background: rgba(40, 40, 40, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  min-width: 120px;
}

.coverage-number {
  font-size: 36px;
  font-weight: 700;
  color: var(--color-border);
  display: block;
  line-height: 1;
}

.coverage-label {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 8px;
  font-weight: 500;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 50px;
  padding: 30px;
  flex-wrap: wrap;
  background: rgba(40, 40, 40, 0.3);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

/* Members Grid */
.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.member-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
}

.member-card:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.6);
  box-shadow: 0 10px 30px rgba(192, 255, 107, 0.2);
}

.member-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.member-avatar {
  width: 50px;
  height: 50px;
  background: rgba(192, 255, 107, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--color-border);
}

.member-info {
  flex: 1;
}

.member-name {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.member-stats {
  margin-bottom: 20px;
}

.member-stat {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.member-stat i {
  color: var(--color-border);
  width: 20px;
}

.member-stat .stat-label {
  color: rgba(255, 255, 255, 0.8);
  flex: 1;
}

.member-stat .stat-value {
  color: var(--color-border);
  font-weight: 600;
}

.member-skills h5 {
  color: var(--color-border);
  font-size: 16px;
  margin-bottom: 12px;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.skill-tag {
  background: rgba(192, 255, 107, 0.2);
  color: var(--color-border);
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

/* Feedback Form Styles */
.feedback-form-section {
  margin-top: 30px;
}

.feedback-form {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 30px;
}

.form-section {
  margin-bottom: 35px;
}

.form-section h3 {
  color: var(--color-border);
  font-size: 20px;
  margin-bottom: 10px;
}

.section-desc {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
}

/* Star Rating */
.star-rating-container {
  text-align: center;
  margin: 25px 0;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
  flex-direction: row-reverse;
}

.rating-stars input {
  display: none;
}

.rating-stars label {
  font-size: 40px;
  color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.rating-stars label:hover,
.rating-stars label:hover ~ label,
.rating-stars input:checked ~ label {
  color: var(--color-border);
  transform: scale(1.1);
}

.rating-text {
  color: var(--color-border);
  font-weight: 600;
  font-size: 16px;
}

/* Aspect Rating */
.aspects-grid {
  display: grid;
  gap: 25px;
}

.aspect-item {
  background: rgba(60, 60, 60, 0.6);
  border-radius: 12px;
  padding: 20px;
}

.aspect-label {
  display: block;
  color: var(--color-border);
  font-weight: 600;
  margin-bottom: 15px;
}

.aspect-desc {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: normal;
  margin-top: 5px;
}

.aspect-slider {
  width: 100%;
  height: 8px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  margin: 15px 0;
  cursor: pointer;
}

.aspect-slider::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-border);
  cursor: pointer;
}

.aspect-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-border);
  cursor: pointer;
  border: none;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Feedback Textarea */
.feedback-textarea {
  width: 100%;
  background: rgba(60, 60, 60, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 10px;
  padding: 15px;
  color: #ffffff;
  font-size: 14px;
  resize: vertical;
  min-height: 120px;
}

.feedback-textarea:focus {
  outline: none;
  border-color: var(--color-border);
  box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
}

.feedback-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-help {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-top: 8px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

/* Rating Display */
.rating-display {
  text-align: center;
  margin: 25px 0;
}

.star-rating-large {
  font-size: 32px;
  color: var(--color-border);
}

.star-rating-large .empty {
  color: rgba(255, 255, 255, 0.3);
}

/* Rating Distribution */
.rating-distribution {
  margin: 30px 0;
  padding: 25px;
  background: rgba(60, 60, 60, 0.3);
  border-radius: 15px;
}

.rating-bars {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 12px 0;
}

.rating-label {
  color: #ffffff;
  font-weight: 500;
  min-width: 100px;
  font-size: 15px;
}

.bar-container {
  flex: 1;
  height: 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-border), #a0e066);
  border-radius: 8px;
  transition: width 0.5s ease;
  box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3);
}

.rating-count {
  color: var(--color-border);
  font-weight: 600;
  min-width: 50px;
  text-align: right;
  font-size: 16px;
}

/* Aspect Performance */
.aspects-performance {
  display: grid;
  gap: 25px;
  margin-top: 25px;
}

.aspect-performance-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px 30px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
  min-height: 70px;
}

.aspect-performance-item:hover {
  transform: translateY(-3px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.15);
}

.aspect-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.aspect-info i {
  color: var(--color-border);
  font-size: 24px;
  width: 30px;
  text-align: center;
}

.aspect-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 16px;
}

.aspect-rating {
  color: var(--color-border);
  font-weight: 600;
  font-size: 20px;
  padding: 8px 15px;
  background: rgba(192, 255, 107, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

/* Feedback Grid */
.feedback-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.feedback-card-item {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
  min-height: 150px;
}

.feedback-card-item:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 10px 30px rgba(192, 255, 107, 0.15);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.feedback-user {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--color-border);
  font-weight: 600;
  font-size: 16px;
}

.feedback-user i {
  font-size: 18px;
}

.feedback-meta {
  text-align: right;
}

.feedback-date {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  margin-bottom: 8px;
}

.feedback-stars {
  color: var(--color-border);
  font-size: 16px;
}

.feedback-stars .empty {
  color: rgba(255, 255, 255, 0.3);
}

.feedback-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 15px;
}

/* Suggestions */
.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.suggestion-card {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px;
  border-left: 5px solid;
  transition: all 0.3s ease;
  min-height: 160px;
}

.suggestion-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.suggestion-card.high {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.suggestion-card.medium {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.suggestion-card.low {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-header i {
  font-size: 24px;
  flex-shrink: 0;
}

.suggestion-card.high .suggestion-header i { color: #dc3545; }
.suggestion-card.medium .suggestion-header i { color: #ffc107; }
.suggestion-card.low .suggestion-header i { color: #28a745; }

.suggestion-title {
  color: var(--color-border);
  font-weight: 600;
  font-size: 18px;
}

.suggestion-rating {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  margin-bottom: 15px;
  font-weight: 500;
}

.suggestion-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-size: 15px;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 60px 30px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(40, 40, 40, 0.3);
  border-radius: 15px;
  border: 2px dashed rgba(192, 255, 107, 0.3);
  margin: 30px 0;
}

.no-data i {
  font-size: 64px;
  color: rgba(192, 255, 107, 0.5);
  margin-bottom: 20px;
  display: block;
}

.no-data strong {
  display: block;
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 20px;
}

.no-data p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .experience-grid {
    grid-template-columns: 1fr;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .skill-coverage-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .coverage-summary {
    justify-content: center;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .feedback-grid {
    grid-template-columns: 1fr;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .btn {
    width: 100%;
    max-width: 300px;
  }

  .rating-stars {
    gap: 5px;
  }

  .rating-stars label {
    font-size: 32px;
  }

  .feedback-header {
    flex-direction: column;
    gap: 10px;
  }

  .feedback-meta {
    text-align: left;
  }
}
