"""
Django management command to build secure JavaScript assets
Usage: python manage.py build_secure_assets [--production] [--admin]
"""

import os
import subprocess
import shutil
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.contrib.auth.models import User
import json

class Command(BaseCommand):
    help = 'Build secure JavaScript assets with obfuscation and minification'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--production',
            action='store_true',
            help='Build for production with full security measures',
        )
        parser.add_argument(
            '--admin',
            action='store_true',
            help='Build admin version with debug capabilities',
        )
        parser.add_argument(
            '--clean',
            action='store_true',
            help='Clean build directory before building',
        )
        parser.add_argument(
            '--watch',
            action='store_true',
            help='Watch for changes and rebuild automatically',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🔨 Building ForgeX Secure Assets...'))
        
        # Check if Node.js and npm are available
        if not self.check_dependencies():
            raise CommandError('Node.js and npm are required to build assets')
        
        # Clean build directory if requested
        if options['clean']:
            self.clean_build_directory()
        
        # Install npm dependencies
        self.install_dependencies()
        
        # Build assets based on options
        if options['production']:
            self.build_production_assets()
        elif options['admin']:
            self.build_admin_assets()
        else:
            self.build_development_assets()
        
        # Watch for changes if requested
        if options['watch']:
            self.watch_assets()
        
        self.stdout.write(self.style.SUCCESS('✅ Asset build completed successfully'))
    
    def check_dependencies(self):
        """Check if required build tools are available"""
        try:
            subprocess.run(['node', '--version'], check=True, capture_output=True, shell=True)
            subprocess.run(['npm', '--version'], check=True, capture_output=True, shell=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.stdout.write(
                self.style.ERROR('❌ Node.js and npm are required but not found')
            )
            self.stdout.write('Please install Node.js from https://nodejs.org/')
            return False
    
    def clean_build_directory(self):
        """Clean the build directory"""
        self.stdout.write('🧹 Cleaning build directory...')
        
        build_dir = os.path.join(settings.BASE_DIR, 'static', 'dist')
        if os.path.exists(build_dir):
            shutil.rmtree(build_dir)
            self.stdout.write(f'   Removed {build_dir}')
        
        # Also clean node_modules if it exists
        node_modules = os.path.join(settings.BASE_DIR, 'node_modules')
        if os.path.exists(node_modules):
            self.stdout.write('   Cleaning node_modules...')
            # Don't remove completely, just clean cache
            try:
                subprocess.run(['npm', 'cache', 'clean', '--force'], 
                             cwd=settings.BASE_DIR, check=True)
            except subprocess.CalledProcessError:
                pass
    
    def install_dependencies(self):
        """Install npm dependencies"""
        self.stdout.write('📦 Installing npm dependencies...')
        
        try:
            result = subprocess.run(
                ['npm', 'install'],
                cwd=settings.BASE_DIR,
                check=True,
                capture_output=True,
                text=True,
                shell=True
            )
            self.stdout.write('   Dependencies installed successfully')
        except subprocess.CalledProcessError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to install dependencies: {e.stderr}')
            )
            raise CommandError('npm install failed')
    
    def build_development_assets(self):
        """Build assets for development"""
        self.stdout.write('🔧 Building development assets...')
        
        try:
            subprocess.run(
                ['npm', 'run', 'build-dev'],
                cwd=settings.BASE_DIR,
                check=True,
                shell=True
            )
            self.stdout.write('   Development build completed')
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Development build failed: {e}')
    
    def build_production_assets(self):
        """Build assets for production with full security"""
        self.stdout.write('🔒 Building production assets with security measures...')
        
        try:
            # Build with security obfuscation
            subprocess.run(
                ['npm', 'run', 'build-secure'],
                cwd=settings.BASE_DIR,
                check=True,
                shell=True,
                env={**os.environ, 'NODE_ENV': 'production'}
            )
            self.stdout.write('   Production build with obfuscation completed')
            
            # Generate integrity hashes
            self.generate_integrity_hashes()
            
            # Create security manifest
            self.create_security_manifest()
            
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Production build failed: {e}')
    
    def build_admin_assets(self):
        """Build assets for admin users with debug capabilities"""
        self.stdout.write('👑 Building admin assets with debug capabilities...')
        
        try:
            subprocess.run(
                ['npm', 'run', 'build-admin'],
                cwd=settings.BASE_DIR,
                check=True,
                shell=True,
                env={**os.environ, 'NODE_ENV': 'development'}
            )
            self.stdout.write('   Admin build completed')
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Admin build failed: {e}')
    
    def watch_assets(self):
        """Watch for changes and rebuild automatically"""
        self.stdout.write('👀 Watching for changes... (Press Ctrl+C to stop)')
        
        try:
            subprocess.run(
                ['npm', 'run', 'watch'],
                cwd=settings.BASE_DIR,
                shell=True
            )
        except KeyboardInterrupt:
            self.stdout.write('\n🛑 Stopped watching')
        except subprocess.CalledProcessError as e:
            raise CommandError(f'Watch mode failed: {e}')
    
    def generate_integrity_hashes(self):
        """Generate SRI hashes for built assets"""
        self.stdout.write('🔐 Generating integrity hashes...')
        
        import hashlib
        import base64
        
        dist_dir = os.path.join(settings.BASE_DIR, 'static', 'dist')
        hashes = {}
        
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                if file.endswith('.js') or file.endswith('.css'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, dist_dir)
                    
                    with open(file_path, 'rb') as f:
                        content = f.read()
                        hash_value = hashlib.sha384(content).digest()
                        sri_hash = f"sha384-{base64.b64encode(hash_value).decode()}"
                        hashes[relative_path] = sri_hash
        
        # Save hashes to a JSON file
        hashes_file = os.path.join(dist_dir, 'integrity-hashes.json')
        with open(hashes_file, 'w') as f:
            json.dump(hashes, f, indent=2)
        
        self.stdout.write(f'   Generated {len(hashes)} integrity hashes')
    
    def create_security_manifest(self):
        """Create a security manifest file"""
        self.stdout.write('📋 Creating security manifest...')
        
        manifest = {
            'version': '1.0.0',
            'build_time': self.get_current_timestamp(),
            'security_features': {
                'obfuscation': True,
                'minification': True,
                'integrity_hashes': True,
                'source_maps': False
            },
            'bundles': self.get_bundle_info(),
            'environment': 'production'
        }
        
        manifest_file = os.path.join(
            settings.BASE_DIR, 'static', 'dist', 'security-manifest.json'
        )
        with open(manifest_file, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        self.stdout.write('   Security manifest created')
    
    def get_bundle_info(self):
        """Get information about built bundles"""
        dist_dir = os.path.join(settings.BASE_DIR, 'static', 'dist')
        bundles = {}
        
        for file in os.listdir(dist_dir):
            if file.endswith('.js'):
                file_path = os.path.join(dist_dir, file)
                size = os.path.getsize(file_path)
                bundles[file] = {
                    'size': size,
                    'size_human': self.format_bytes(size)
                }
        
        return bundles
    
    def get_current_timestamp(self):
        """Get current timestamp in ISO format"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def format_bytes(self, bytes_value):
        """Format bytes in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def create_build_report(self):
        """Create a detailed build report"""
        self.stdout.write('📊 Creating build report...')
        
        dist_dir = os.path.join(settings.BASE_DIR, 'static', 'dist')
        report = {
            'build_summary': {
                'total_files': 0,
                'total_size': 0,
                'js_files': 0,
                'css_files': 0
            },
            'files': []
        }
        
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = os.path.join(root, file)
                relative_path = os.path.relpath(file_path, dist_dir)
                size = os.path.getsize(file_path)
                
                report['files'].append({
                    'name': relative_path,
                    'size': size,
                    'size_human': self.format_bytes(size)
                })
                
                report['build_summary']['total_files'] += 1
                report['build_summary']['total_size'] += size
                
                if file.endswith('.js'):
                    report['build_summary']['js_files'] += 1
                elif file.endswith('.css'):
                    report['build_summary']['css_files'] += 1
        
        report['build_summary']['total_size_human'] = self.format_bytes(
            report['build_summary']['total_size']
        )
        
        # Save report
        report_file = os.path.join(dist_dir, 'build-report.json')
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Display summary
        summary = report['build_summary']
        self.stdout.write(f"   📁 Total files: {summary['total_files']}")
        self.stdout.write(f"   📄 JavaScript files: {summary['js_files']}")
        self.stdout.write(f"   🎨 CSS files: {summary['css_files']}")
        self.stdout.write(f"   📏 Total size: {summary['total_size_human']}")
    
    def verify_build(self):
        """Verify that the build was successful"""
        self.stdout.write('✅ Verifying build...')
        
        dist_dir = os.path.join(settings.BASE_DIR, 'static', 'dist')
        required_files = [
            'main.bundle.js',
            'security.min.js',
            'api-client.bundle.js'
        ]
        
        missing_files = []
        for file in required_files:
            file_path = os.path.join(dist_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
        
        if missing_files:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Missing files: {", ".join(missing_files)}')
            )
        else:
            self.stdout.write('   All required files present')
        
        return len(missing_files) == 0
