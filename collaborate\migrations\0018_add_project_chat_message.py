# Generated by Django 5.2 on 2025-05-30 07:13

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0017_rename_team_match_log"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ProjectChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField()),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("chat", "Chat Message"),
                            ("system", "System Message"),
                            ("user_joined", "User Joined"),
                            ("user_left", "User Left"),
                        ],
                        default="chat",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="chat_messages",
                        to="collaborate.project",
                    ),
                ),
                (
                    "sender",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
                "indexes": [
                    models.Index(
                        fields=["project", "created_at"],
                        name="collaborate_project_90c646_idx",
                    ),
                    models.Index(
                        fields=["sender", "created_at"],
                        name="collaborate_sender__d35491_idx",
                    ),
                ],
            },
        ),
    ]
