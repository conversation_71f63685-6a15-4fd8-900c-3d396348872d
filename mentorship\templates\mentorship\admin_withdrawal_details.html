{% extends 'base.html' %}
{% load static %}

{% block title %}Withdrawal Details - Admin - ForgeX{% endblock %}

{% block content %}
<div class="admin-withdrawal-details">
    {% csrf_token %}
    <!-- Header -->
    <section class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="page-title">
                    <h1><i class="fas fa-file-invoice-dollar"></i> Withdrawal Details</h1>
                    <p>Detailed information about withdrawal request #{{ withdrawal.id }}</p>
                </div>
                <div class="header-actions">
                    <a href="{% url 'mentorship:admin_withdrawal_dashboard' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Content -->
    <section class="page-content">
        <div class="container">
            <div class="content-grid">
                <!-- Withdrawal Information -->
                <div class="details-section withdrawal-info-section">
                    <div class="section-header">
                        <h2><i class="fas fa-info-circle"></i> Withdrawal Information</h2>
                        <span class="status-badge status-{{ withdrawal.status }}">
                            {{ withdrawal.get_status_display }}
                        </span>
                    </div>

                    <div class="info-grid">
                        <div class="info-item">
                            <label>Withdrawal ID</label>
                            <value>#{{ withdrawal.id }}</value>
                        </div>

                        <div class="info-item">
                            <label>Requested Amount</label>
                            <value class="amount">${{ withdrawal.amount }}</value>
                        </div>

                        <div class="info-item">
                            <label>Processing Fee</label>
                            <value class="fee">${{ withdrawal.processing_fee }}</value>
                        </div>

                        <div class="info-item">
                            <label>Net Amount</label>
                            <value class="net-amount">${{ withdrawal.net_amount }}</value>
                        </div>

                        <div class="info-item">
                            <label>Requested Date</label>
                            <value>{{ withdrawal.requested_at|date:"F d, Y g:i A" }}</value>
                        </div>

                        {% if withdrawal.processed_at %}
                        <div class="info-item">
                            <label>Processed Date</label>
                            <value>{{ withdrawal.processed_at|date:"F d, Y g:i A" }}</value>
                        </div>
                        {% endif %}

                        {% if withdrawal.completed_at %}
                        <div class="info-item">
                            <label>Completed Date</label>
                            <value>{{ withdrawal.completed_at|date:"F d, Y g:i A" }}</value>
                        </div>
                        {% endif %}

                        {% if withdrawal.failure_reason %}
                        <div class="info-item full-width">
                            <label>Failure Reason</label>
                            <value class="failure-reason">{{ withdrawal.failure_reason }}</value>
                        </div>
                        {% endif %}

                        {% if withdrawal.admin_notes %}
                        <div class="info-item full-width">
                            <label>Admin Notes</label>
                            <value class="admin-notes">{{ withdrawal.admin_notes }}</value>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Action Buttons -->
                    {% if withdrawal.status in 'pending,processing' %}
                    <div class="action-section">
                        <div class="action-buttons">
                            <button class="btn btn-success" onclick="approveWithdrawal({{ withdrawal.id }})">
                                <i class="fas fa-check"></i>
                                {% if withdrawal.status == 'pending' %}Approve{% else %}Complete{% endif %}
                            </button>
                            <button class="btn btn-danger" onclick="openRejectModal({{ withdrawal.id }})">
                                <i class="fas fa-times"></i>
                                Reject
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Mentor Information -->
                <div class="details-section mentor-info-section">
                    <div class="section-header">
                        <h2><i class="fas fa-user"></i> Mentor Information</h2>
                    </div>

                    <div class="mentor-details">
                        <div class="mentor-basic">
                            <div class="mentor-avatar">
                                {% if withdrawal.mentor.mentor_profile.profile_picture %}
                                    <img src="{{ withdrawal.mentor.mentor_profile.profile_picture.url }}"
                                         alt="{{ withdrawal.mentor.get_full_name }}">
                                {% else %}
                                    <div class="avatar-placeholder">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="mentor-info">
                                <h3>{{ withdrawal.mentor.get_full_name|default:withdrawal.mentor.username }}</h3>
                                <p>{{ withdrawal.mentor.email }}</p>
                                <div class="mentor-stats">
                                    <span class="stat">
                                        <i class="fas fa-star"></i>
                                        {{ withdrawal.mentor.mentor_profile.average_rating|default:"N/A" }}
                                    </span>
                                    <span class="stat">
                                        <i class="fas fa-calendar-check"></i>
                                        {{ withdrawal.mentor.mentor_profile.total_sessions }} sessions
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="mentor-earnings">
                            <div class="earnings-item">
                                <label>Total Earnings</label>
                                <value>${{ withdrawal.mentor.mentor_profile.total_earnings }}</value>
                            </div>
                            <div class="earnings-item">
                                <label>Total Withdrawn</label>
                                <value>${{ withdrawal.mentor.mentor_profile.withdrawn_earnings }}</value>
                            </div>
                            <div class="earnings-item">
                                <label>Available Balance</label>
                                <value class="highlight">${{ withdrawal.mentor.mentor_profile.get_available_balance }}</value>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mentor Statistics -->
                <div class="details-section stats-section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-bar"></i> Withdrawal Statistics</h2>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">{{ mentor_stats.total_withdrawals }}</span>
                                <span class="stat-label">Total Requests</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">{{ mentor_stats.completed_withdrawals }}</span>
                                <span class="stat-label">Completed</span>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stat-info">
                                <span class="stat-number">${{ mentor_stats.total_withdrawn }}</span>
                                <span class="stat-label">Total Withdrawn</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Withdrawal History -->
                <div class="details-section history-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> Recent Withdrawal History</h2>
                    </div>

                    {% if mentor_withdrawals %}
                        <div class="history-list">
                            {% for w in mentor_withdrawals %}
                                <div class="history-item {% if w.id == withdrawal.id %}current{% endif %}">
                                    <div class="history-info">
                                        <div class="history-amount">${{ w.amount }}</div>
                                        <div class="history-date">{{ w.requested_at|date:"M d, Y" }}</div>
                                    </div>
                                    <div class="history-status">
                                        <span class="status-badge status-{{ w.status }}">
                                            {{ w.get_status_display }}
                                        </span>
                                        {% if w.id == withdrawal.id %}
                                            <span class="current-label">Current</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <p>No withdrawal history available.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Rejection Modal -->
<div id="rejectModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Reject Withdrawal Request</h3>
            <span class="close" onclick="closeRejectModal()">&times;</span>
        </div>
        <div class="modal-body">
            <p>Please provide a reason for rejecting this withdrawal request:</p>

            <div class="form-group">
                <label for="rejectionReason">Rejection Reason *</label>
                <textarea id="rejectionReason" rows="4"
                          placeholder="Enter the reason for rejection..." required></textarea>
            </div>

            <div class="rejection-info">
                <i class="fas fa-info-circle"></i>
                <p>The mentor will be notified of the rejection and the amount will be restored to their available balance.</p>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeRejectModal()">Cancel</button>
            <button class="btn btn-danger" onclick="confirmRejectWithdrawal()">
                <i class="fas fa-times"></i>
                Reject Withdrawal
            </button>
        </div>
    </div>
</div>

<style>
.admin-withdrawal-details {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.page-header {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: #ffffff;
    padding: 3rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.page-title h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.page-title p {
    font-size: 1.1rem;
    opacity: 0.8;
}

.page-content {
    padding: 4rem 0;
}

.content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.withdrawal-info-section {
    grid-column: 1;
    grid-row: 1 / 3;
}

.mentor-info-section {
    grid-column: 2;
    grid-row: 1;
}

.stats-section {
    grid-column: 2;
    grid-row: 2;
}

.history-section {
    grid-column: 1 / -1;
    grid-row: 3;
}

.details-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.section-header h2 {
    color: #C0ff6b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.3rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: rgba(255,193,7,0.2); color: #ffc107; }
.status-processing { background: rgba(0,123,255,0.2); color: #007bff; }
.status-completed { background: rgba(40,167,69,0.2); color: #28a745; }
.status-failed { background: rgba(220,53,69,0.2); color: #dc3545; }
.status-cancelled { background: rgba(108,117,125,0.2); color: #6c757d; }

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-item.full-width {
    grid-column: 1 / -1;
}

.info-item label {
    font-size: 0.9rem;
    color: #C0ff6b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-item value {
    font-size: 1.1rem;
    font-weight: 500;
}

.amount {
    color: #C0ff6b;
    font-weight: 700;
    font-size: 1.3rem;
}

.fee {
    color: #ffc107;
    font-weight: 600;
}

.net-amount {
    color: #28a745;
    font-weight: 700;
    font-size: 1.3rem;
}

.failure-reason,
.admin-notes {
    background: rgba(255,255,255,0.05);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.admin-notes {
    border-left-color: #007bff;
}

.action-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(192,255,107,0.2);
}

.action-buttons {
    display: flex;
    gap: 1rem;
}

.mentor-details {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.mentor-basic {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.mentor-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #C0ff6b;
}

.mentor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-placeholder {
    width: 100%;
    height: 100%;
    background: rgba(192,255,107,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #C0ff6b;
    font-size: 2rem;
}

.mentor-info h3 {
    margin: 0 0 0.5rem 0;
    color: #C0ff6b;
}

.mentor-info p {
    margin: 0 0 1rem 0;
    opacity: 0.8;
}

.mentor-stats {
    display: flex;
    gap: 1rem;
}

.mentor-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.mentor-earnings {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.earnings-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(255,255,255,0.02);
    border-radius: 8px;
}

.earnings-item label {
    color: #C0ff6b;
    font-weight: 600;
}

.earnings-item value.highlight {
    color: #C0ff6b;
    font-weight: 700;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-card {
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border: 1px solid rgba(192,255,107,0.1);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: rgba(192,255,107,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #C0ff6b;
    font-size: 1.2rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #C0ff6b;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.8;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(192,255,107,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-item.current {
    border-color: #C0ff6b;
    background: rgba(192,255,107,0.05);
}

.history-amount {
    font-weight: 600;
    color: #C0ff6b;
    font-size: 1.1rem;
}

.history-date {
    font-size: 0.9rem;
    opacity: 0.8;
}

.history-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-label {
    background: #C0ff6b;
    color: #1a1a1a;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-success {
    background: #28a745;
    color: #ffffff;
}

.btn-danger {
    background: #dc3545;
    color: #ffffff;
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
    border: 1px solid rgba(192,255,107,0.2);
}

.btn:hover {
    transform: translateY(-2px);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
}

.modal-content {
    background: #2d2d2d;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    color: #ffffff;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(192,255,107,0.2);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(192,255,107,0.2);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    resize: vertical;
}

.rejection-info {
    background: rgba(255,193,7,0.1);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-top: 1rem;
}

.rejection-info i {
    color: #ffc107;
    margin-top: 0.25rem;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #C0ff6b;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    opacity: 0.6;
}

.empty-state i {
    font-size: 2rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .withdrawal-info-section,
    .mentor-info-section,
    .stats-section,
    .history-section {
        grid-column: 1;
        grid-row: auto;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }

    .mentor-basic {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script>
let currentWithdrawalId = {{ withdrawal.id }};

// Approve withdrawal
async function approveWithdrawal(withdrawalId) {
    if (!confirm('Are you sure you want to approve this withdrawal request?')) {
        return;
    }

    try {
        const response = await fetch(`{% url 'mentorship:admin_approve_withdrawal' 0 %}`.replace('0', withdrawalId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}

// Open reject modal
function openRejectModal(withdrawalId) {
    currentWithdrawalId = withdrawalId;
    document.getElementById('rejectionReason').value = '';
    document.getElementById('rejectModal').style.display = 'block';
}

// Close reject modal
function closeRejectModal() {
    document.getElementById('rejectModal').style.display = 'none';
}

// Confirm rejection
async function confirmRejectWithdrawal() {
    const reason = document.getElementById('rejectionReason').value.trim();

    if (!reason) {
        alert('Please provide a rejection reason.');
        return;
    }

    try {
        const response = await fetch(`{% url 'mentorship:admin_reject_withdrawal' 0 %}`.replace('0', currentWithdrawalId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ reason: reason })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            closeRejectModal();
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('rejectModal');
    if (event.target == modal) {
        closeRejectModal();
    }
}
</script>
{% endblock %}
