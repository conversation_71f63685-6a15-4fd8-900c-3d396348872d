from django.urls import path
from . import views
from . import payment_views
from . import withdrawal_views

app_name = 'mentorship'

urlpatterns = [
    # Test URL
    path('test/', views.test_template, name='test_template'),

    # Marketplace and mentor discovery
    path('', views.mentor_marketplace, name='marketplace'),
    path('mentor/<int:mentor_id>/', views.mentor_detail, name='mentor_detail'),
    path('mentor/<int:mentor_id>/book/', views.book_session, name='book_session'),

    # Session management
    path('session/<uuid:room_id>/', views.session_room, name='session_room'),
    path('session/<uuid:room_id>/end/', views.end_session, name='end_session'),
    path('my-sessions/', views.my_sessions, name='my_sessions'),
    path('session/<int:session_id>/details/', views.session_details, name='session_details'),
    path('session/<int:session_id>/feedback/', views.session_feedback, name='session_feedback'),
    path('session/<int:session_id>/feedback/submit/', views.submit_feedback, name='submit_feedback'),

    # <PERSON>tor features
    path('become-mentor/', views.become_mentor, name='become_mentor'),
    path('mentor/dashboard/', views.mentor_dashboard, name='mentor_dashboard'),

    # Payment URLs
    path('payment/<int:session_id>/', payment_views.payment_page, name='payment_page'),
    path('payment/<int:session_id>/create-intent/', payment_views.create_payment_intent, name='create_payment_intent'),
    path('payment/<int:session_id>/confirm/', payment_views.confirm_payment, name='confirm_payment'),
    path('payment/<int:session_id>/success/', payment_views.payment_success, name='payment_success'),
    path('payment/<int:session_id>/failed/', payment_views.payment_failed, name='payment_failed'),
    path('webhooks/stripe/', payment_views.stripe_webhook, name='stripe_webhook'),

    # Withdrawal URLs
    path('withdrawal/', withdrawal_views.withdrawal_dashboard, name='withdrawal_dashboard'),
    path('withdrawal/request/', withdrawal_views.request_withdrawal, name='request_withdrawal'),
    path('withdrawal/history/', withdrawal_views.withdrawal_history, name='withdrawal_history'),
    path('withdrawal/<int:withdrawal_id>/cancel/', withdrawal_views.cancel_withdrawal, name='cancel_withdrawal'),

    # Admin Withdrawal URLs
    path('admin/withdrawal/', withdrawal_views.admin_withdrawal_dashboard, name='admin_withdrawal_dashboard'),
    path('admin/withdrawal/<int:withdrawal_id>/', withdrawal_views.admin_withdrawal_details, name='admin_withdrawal_details'),
    path('admin/withdrawal/<int:withdrawal_id>/approve/', withdrawal_views.admin_approve_withdrawal, name='admin_approve_withdrawal'),
    path('admin/withdrawal/<int:withdrawal_id>/reject/', withdrawal_views.admin_reject_withdrawal, name='admin_reject_withdrawal'),

    # API endpoints for AJAX calls
    path('api/mentors/', views.mentor_marketplace, name='api_mentors'),
    path('api/chat-history/<uuid:room_id>/', views.get_chat_history, name='get_chat_history'),
]
