import google.generativeai as genai
import json
import re
from typing import Dict, List, Optional
from enum import Enum


# --- Enum Definitions ---
class CodeLanguage(str, Enum):
    python = "python"
    javascript = "javascript"
    html = "html"
    css = "css"
    java = "java"
    cpp = "cpp"
    csharp = "csharp"
    php = "php"
    ruby = "ruby"
    go = "go"
    rust = "rust"
    typescript = "typescript"
    other = "other"

class QueryType(str, Enum):
    debug = "debug"
    generate = "generate"
    explain = "explain"
    optimize = "optimize"
    review = "review"

# --- Code Analysis Functions ---
def detect_language(code: str, file_name: str = None) -> CodeLanguage:
    """Detect programming language from code content and filename"""
    if file_name:
        extension = file_name.split('.')[-1].lower()
        extension_map = {
            'py': CodeLanguage.python,
            'js': CodeLanguage.javascript,
            'html': CodeLanguage.html,
            'css': CodeLanguage.css,
            'java': CodeLanguage.java,
            'cpp': CodeLanguage.cpp,
            'c': CodeLanguage.cpp,
            'cs': CodeLanguage.csharp,
            'php': CodeLanguage.php,
            'rb': CodeLanguage.ruby,
            'go': CodeLanguage.go,
            'rs': CodeLanguage.rust,
            'ts': CodeLanguage.typescript,
        }
        if extension in extension_map:
            return extension_map[extension]

    # Fallback to content-based detection
    if 'def ' in code or 'import ' in code or 'print(' in code:
        return CodeLanguage.python
    elif 'function ' in code or 'const ' in code or 'let ' in code:
        return CodeLanguage.javascript
    elif '<html' in code or '<div' in code:
        return CodeLanguage.html
    elif 'public class' in code or 'System.out' in code:
        return CodeLanguage.java

    return CodeLanguage.other

def analyze_code_errors(code: str, language: CodeLanguage) -> List[str]:
    """Basic code error detection"""
    errors = []

    if language == CodeLanguage.python:
        # Check for common Python errors
        if code.count('(') != code.count(')'):
            errors.append("Mismatched parentheses")
        if code.count('[') != code.count(']'):
            errors.append("Mismatched square brackets")
        if code.count('{') != code.count('}'):
            errors.append("Mismatched curly braces")

        # Check for indentation issues (basic)
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                if i > 0 and lines[i-1].strip().endswith(':'):
                    errors.append(f"Line {i+1}: Missing indentation after colon")

    elif language == CodeLanguage.javascript:
        # Check for common JavaScript errors
        if code.count('(') != code.count(')'):
            errors.append("Mismatched parentheses")
        if code.count('{') != code.count('}'):
            errors.append("Mismatched curly braces")
        if 'var ' in code:
            errors.append("Consider using 'let' or 'const' instead of 'var'")

    return errors

# --- Configuration ---
GOOGLE_API_KEY = "AIzaSyD2JrW72oo2aRwOIhDNNgoQ7FSNfv67lRQ"

# Initialize the generative model
try:
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel('gemini-2.5-flash-preview-04-17')
except Exception as e:
    print(f"Error configuring Google API: {e}")
    model = None

# --- Code Assistant Class ---
class CodeAssistant:
    def __init__(self):
        self.model = model
        self.conversation_history = []

    def get_system_prompt(self, code_context: Optional[str] = None, language: CodeLanguage = CodeLanguage.python) -> str:
        """Generate system prompt for code assistance"""
        base_prompt = """You are a code assistant for programming help ONLY. Be concise and direct like ChatGPT.

**STRICT RULES:**
- ONLY answer programming and coding questions
- If asked about anything else (history, science, general knowledge, etc.), politely decline
- Say: "I'm a code assistant. Please ask me about programming, debugging, or code-related topics."

**Response Style:**
- Keep answers SHORT and to the point
- Only give detailed explanations when specifically asked
- For code improvements: list 2-3 key points, then show improved code
- Use code blocks for examples
- Be friendly but not overly verbose

**I can help with:**
- Writing code in any programming language
- Debugging and fixing errors
- Code improvements and optimization
- Explaining programming concepts
- Code reviews and best practices

**Current Context:**"""

        if code_context:
            base_prompt += f"""
**Code being analyzed:**
```{language.value}
{code_context}
```

Please analyze this code and help the user with their request."""
        else:
            base_prompt += "\nNo code context provided. Ready to help with any programming questions or tasks."

        return base_prompt

    def analyze_code(self, code: str, file_name: str = None) -> Dict:
        """Analyze code for errors and suggestions"""
        language = detect_language(code, file_name)
        errors = analyze_code_errors(code, language)

        analysis = {
            "language": language.value,
            "errors": errors,
            "suggestions": [],
            "complexity": "unknown"
        }

        # Add basic complexity analysis
        lines = len([line for line in code.split('\n') if line.strip()])
        if lines < 10:
            analysis["complexity"] = "simple"
        elif lines < 50:
            analysis["complexity"] = "moderate"
        else:
            analysis["complexity"] = "complex"

        return analysis

    def get_response(self, user_message: str, code_context: Optional[str] = None,
                    language: CodeLanguage = CodeLanguage.python) -> str:
        """Get AI response for user query"""
        if not self.model:
            return "Sorry, the AI model is not available at the moment. Please check the API configuration."

        try:
            # Build the prompt
            system_prompt = self.get_system_prompt(code_context, language)

            # Include recent conversation history
            conversation_context = ""
            if self.conversation_history:
                conversation_context = "\n\n**Recent conversation:**\n"
                for msg in self.conversation_history[-4:]:  # Last 4 messages
                    role = "User" if msg["role"] == "user" else "Assistant"
                    conversation_context += f"{role}: {msg['content']}\n"

            full_prompt = f"{system_prompt}{conversation_context}\n\n**Current question:** {user_message}"

            # Get AI response
            response = self.model.generate_content(full_prompt)
            assistant_response = response.text

            # Update conversation history
            self.conversation_history.append({"role": "user", "content": user_message})
            self.conversation_history.append({"role": "assistant", "content": assistant_response})

            # Keep only recent history (last 10 messages)
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]

            return assistant_response

        except Exception as e:
            return f"Sorry, I encountered an error while processing your request: {str(e)}"

    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []

# Global instance
code_assistant = CodeAssistant()

# --- Helper Functions for Django Integration ---
def get_chatbot_response(user_message: str, code_context: str = None, file_name: str = None) -> Dict:
    """
    Main function to get chatbot response for Django integration

    Args:
        user_message: User's question or request
        code_context: Optional code content to analyze
        file_name: Optional filename for language detection

    Returns:
        Dict with response and metadata
    """
    try:
        # Detect language if code context is provided
        language = CodeLanguage.python
        if code_context and file_name:
            language = detect_language(code_context, file_name)
        elif code_context:
            language = detect_language(code_context)

        # Get AI response
        response = code_assistant.get_response(
            user_message=user_message,
            code_context=code_context,
            language=language
        )

        # Analyze code if provided
        analysis = None
        if code_context:
            analysis = code_assistant.analyze_code(code_context, file_name)

        return {
            "success": True,
            "response": response,
            "analysis": analysis,
            "language": language.value if code_context else None
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response": "Sorry, I encountered an error while processing your request."
        }

def analyze_current_code(code: str, file_name: str = None) -> Dict:
    """
    Analyze code for errors and suggestions

    Args:
        code: Code content to analyze
        file_name: Optional filename

    Returns:
        Dict with analysis results
    """
    try:
        analysis = code_assistant.analyze_code(code, file_name)

        # Generate suggestions based on analysis
        suggestions = []
        if analysis["errors"]:
            suggestions.append("Fix the identified syntax errors")

        if analysis["complexity"] == "complex":
            suggestions.append("Consider breaking down complex functions into smaller ones")

        language = analysis["language"]
        if language == "python":
            suggestions.append("Follow PEP 8 style guidelines")
        elif language == "javascript":
            suggestions.append("Use modern ES6+ features where appropriate")

        analysis["suggestions"] = suggestions

        return {
            "success": True,
            "analysis": analysis
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

def clear_chatbot_history():
    """Clear the chatbot conversation history"""
    code_assistant.clear_history()
    return {"success": True, "message": "Chat history cleared"}

# --- Example usage functions for testing ---
if __name__ == "__main__":
    # Test the chatbot functionality
    print("🤖 Code Assistant Test")
    print("=" * 50)

    # Test basic question
    result = get_chatbot_response("How do I create a Python function?")
    print("Q: How do I create a Python function?")
    print(f"A: {result['response']}")
    print()

    # Test code analysis
    test_code = """
def hello_world():
    print("Hello, World!"
    return "Hello"
"""

    result = get_chatbot_response(
        "What's wrong with this code?",
        code_context=test_code,
        file_name="test.py"
    )
    print("Q: What's wrong with this code?")
    print(f"A: {result['response']}")
    if result['analysis']:
        print(f"Analysis: {result['analysis']}")
    print()

    # Test code analysis function
    analysis_result = analyze_current_code(test_code, "test.py")
    print("Code Analysis:")
    print(f"Result: {analysis_result}")