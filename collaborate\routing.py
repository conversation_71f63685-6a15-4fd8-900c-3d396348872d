from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    re_path(r'ws/collaborate/(?P<project_hash>\w+)/$', consumers.EditorConsumer.as_asgi()),
    re_path(r'ws/notifications/$', consumers.NotificationConsumer.as_asgi()),
    re_path(r'ws/collab/(?P<room_id>\d+)/$', consumers.CollabConsumer.as_asgi()),
    re_path(r'ws/account/notifications/$', consumers.NotificationConsumer.as_asgi()),
    re_path(r'ws/voice/(?P<project_hash>\w+)/$', consumers.VoiceConsumer.as_asgi()),
    re_path(r'ws/project/(?P<project_hash>\w+)/file-changes/$', consumers.FileChangeConsumer.as_asgi()),

    # Terminal WebSocket routes - DISABLED for Windows compatibility
    # re_path(r'ws/terminal/(?P<project_hash>\w+)/(?P<terminal_id>\w+)/$', consumers.TerminalConsumer.as_asgi()),

    # Yjs WebSocket routes - make sure these are prioritized correctly
    re_path(r'ws/yjs-room/(?P<room_name>\w+)/$', consumers.YjsConsumer.as_asgi()),
    re_path(r'ws/yjs-room/$', consumers.YjsConsumer.as_asgi()),

    re_path(r'ws/chat/(?P<room_name>\w+)/$', consumers.ChatConsumer.as_asgi()),
]