# Generated by Django 5.2.1 on 2025-05-20 10:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0014_remove_project_matching_approach"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name="teamrejectionlog",
            name="timestamp",
        ),
        migrations.CreateModel(
            name="SkillAlias",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("alias", models.CharField(max_length=100, unique=True)),
                (
                    "skill",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="aliases",
                        to="collaborate.skill",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SkillRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "relationship_type",
                    models.CharField(
                        choices=[
                            ("parent", "Parent"),
                            ("child", "Child"),
                            ("related", "Related"),
                        ],
                        max_length=20,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "from_skill",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relationships_from",
                        to="collaborate.skill",
                    ),
                ),
                (
                    "to_skill",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="relationships_to",
                        to="collaborate.skill",
                    ),
                ),
            ],
            options={
                "unique_together": {("from_skill", "to_skill", "relationship_type")},
            },
        ),
        migrations.CreateModel(
            name="TeamMatchFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "rating",
                    models.IntegerField(
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Fair"),
                            (3, "3 - Good"),
                            (4, "4 - Very Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating from 1-5 on match quality",
                    ),
                ),
                ("feedback_text", models.TextField(blank=True, null=True)),
                (
                    "match_aspects",
                    models.JSONField(
                        default=dict,
                        help_text="Ratings for specific aspects of the match (e.g., skill_match, timezone_compatibility)",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="collaborate.project",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("project", "user")},
            },
        ),
    ]
