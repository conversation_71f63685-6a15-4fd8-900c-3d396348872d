from django.contrib import admin
from .models import Course, Chapter, Lesson, LessonContent

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('name', 'get_creator_name', 'difficulty', 'status', 'is_approved', 'created_at', 'id')
    list_filter = ('difficulty', 'status', 'is_approved', 'created_at', 'created_by')
    search_fields = ('name', 'description', 'created_by__username', 'created_by__first_name', 'created_by__last_name')
    ordering = ('-created_at', 'name')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('status', 'is_approved')

    def get_creator_name(self, obj):
        return obj.get_creator_name()
    get_creator_name.short_description = 'Created By'

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'image', 'description')
        }),
        ('Course Details', {
            'fields': ('difficulty', 'estimated_duration', 'prerequisites', 'learning_objectives')
        }),
        ('Creator and Status', {
            'fields': ('created_by', 'status', 'is_approved'),
            'description': 'Course creator and approval status'
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        # If creating a new course and no creator is set, set the current user
        if not change and not obj.created_by:
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(Chapter)
class ChapterAdmin(admin.ModelAdmin):
    list_display = ('name', 'course', 'order', 'id')
    list_filter = ('course',)
    search_fields = ('name', 'description')
    ordering = ('course', 'order')
    list_editable = ('order',)

@admin.register(Lesson)
class LessonAdmin(admin.ModelAdmin):
    list_display = ('name', 'chapter', 'lesson_type', 'estimated_time', 'order', 'id')
    list_filter = ('lesson_type', 'chapter__course')
    search_fields = ('name',)
    ordering = ('chapter', 'order')
    list_editable = ('order', 'lesson_type', 'estimated_time')

@admin.register(LessonContent)
class LessonContentAdmin(admin.ModelAdmin):
    list_display = ('get_lesson_name', 'content_type', 'title', 'order', 'id')
    list_filter = ('content_type', 'lesson__chapter__course')
    search_fields = ('title', 'content')
    ordering = ('lesson', 'order')
    list_editable = ('order',)

    def get_lesson_name(self, obj):
        return obj.lesson.name
    get_lesson_name.short_description = 'Lesson'

    fieldsets = (
        ('Content Information', {
            'fields': ('lesson', 'content_type', 'title', 'order')
        }),
        ('Content Body', {
            'fields': ('content',)
        }),
        ('Code Settings', {
            'fields': ('code_language',),
            'description': 'Only required for code examples'
        }),
    )
