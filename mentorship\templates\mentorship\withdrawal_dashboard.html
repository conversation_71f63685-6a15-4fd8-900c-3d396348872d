{% extends 'base.html' %}
{% load static %}

{% block title %}Withdrawal Dashboard - ForgeX{% endblock %}

{% block content %}
<div class="withdrawal-dashboard">
    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="header-content">
                <div class="withdrawal-welcome">
                    <h1>Withdrawal Dashboard</h1>
                    <p>Manage your earnings withdrawals</p>
                </div>

                <div class="balance-card">
                    <div class="balance-info">
                        <span class="balance-label">Available Balance</span>
                        <span class="balance-amount">${{ available_balance }}</span>
                    </div>
                    <div class="balance-actions">
                        {% if available_balance >= min_withdrawal %}
                            <button class="btn btn-primary" onclick="openWithdrawalModal()">
                                <i class="fas fa-money-bill-wave"></i>
                                Withdraw Money
                            </button>
                        {% else %}
                            <button class="btn btn-disabled" disabled>
                                <i class="fas fa-money-bill-wave"></i>
                                Minimum ${{ min_withdrawal }}
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="dashboard-content">
        <div class="container">
            <div class="dashboard-grid">
                <!-- Withdrawal Form -->
                <div class="dashboard-section withdrawal-form-section">
                    <div class="section-header">
                        <h2><i class="fas fa-credit-card"></i> Quick Withdrawal</h2>
                    </div>

                    <div class="withdrawal-form-content">
                        <form id="withdrawalForm" class="withdrawal-form">
                            {% csrf_token %}

                            <div class="form-group">
                                <label for="withdrawalAmount">Withdrawal Amount (USD)</label>
                                <input type="number" id="withdrawalAmount" name="amount"
                                       min="{{ min_withdrawal }}" max="{{ available_balance }}"
                                       step="0.01" placeholder="Enter amount" required>
                                <small>Minimum withdrawal: ${{ min_withdrawal }}</small>
                            </div>

                            <div class="fee-breakdown">
                                <div class="fee-item">
                                    <span>Withdrawal Amount:</span>
                                    <span id="withdrawalAmountDisplay">$0.00</span>
                                </div>
                                <div class="fee-item">
                                    <span>Processing Fee (2.5%, min $0.25):</span>
                                    <span id="processingFeeDisplay">$0.00</span>
                                </div>
                                <div class="fee-item total">
                                    <span>You'll Receive:</span>
                                    <span id="netAmountDisplay">$0.00</span>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary btn-large">
                                    <i class="fas fa-paper-plane"></i>
                                    Submit Withdrawal Request
                                </button>
                            </div>
                        </form>

                        <div class="withdrawal-info">
                            <h4>Withdrawal Information</h4>
                            <ul>
                                <li><i class="fas fa-clock"></i> Processing time: 1-3 business days</li>
                                <li><i class="fas fa-shield-alt"></i> Secure bank transfer via Stripe</li>
                                <li><i class="fas fa-dollar-sign"></i> Minimum withdrawal: ${{ min_withdrawal }}</li>
                                <li><i class="fas fa-percentage"></i> Processing fee: 2.5% (minimum $0.25)</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Recent Withdrawals -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> Recent Withdrawals</h2>
                        <a href="{% url 'mentorship:withdrawal_history' %}" class="view-all-link">View All</a>
                    </div>

                    {% if withdrawal_requests %}
                        <div class="withdrawals-list">
                            {% for withdrawal in withdrawal_requests %}
                                <div class="withdrawal-item status-{{ withdrawal.status }}">
                                    <div class="withdrawal-info">
                                        <div class="withdrawal-amount">${{ withdrawal.amount }}</div>
                                        <div class="withdrawal-details">
                                            <span class="withdrawal-date">
                                                <i class="fas fa-calendar"></i>
                                                {{ withdrawal.requested_at|date:"M d, Y" }}
                                            </span>
                                            <span class="withdrawal-status status-{{ withdrawal.status }}">
                                                {{ withdrawal.get_status_display }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="withdrawal-actions">
                                        {% if withdrawal.can_cancel %}
                                            <button class="btn btn-sm btn-outline"
                                                    onclick="cancelWithdrawal({{ withdrawal.id }})">
                                                Cancel
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-money-bill-wave"></i>
                            <p>No withdrawal requests yet</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Earnings Summary -->
                <div class="dashboard-section earnings-summary-section">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-pie"></i> Earnings Summary</h2>
                    </div>

                    <div class="earnings-summary">
                        <div class="earnings-item">
                            <span class="earnings-label">Total Earnings</span>
                            <span class="earnings-value">${{ mentor_profile.total_earnings }}</span>
                        </div>
                        <div class="earnings-item">
                            <span class="earnings-label">Total Withdrawn</span>
                            <span class="earnings-value">${{ mentor_profile.withdrawn_earnings }}</span>
                        </div>
                        <div class="earnings-item">
                            <span class="earnings-label">Available Balance</span>
                            <span class="earnings-value highlight">${{ available_balance }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Withdrawal Modal -->
<div id="withdrawalModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Withdraw Money</h3>
            <span class="close" onclick="closeWithdrawalModal()">&times;</span>
        </div>
        <div class="modal-body">
            <p>Available Balance: <strong>${{ available_balance }}</strong></p>
            <p>Enter the amount you'd like to withdraw. Funds will be transferred to your bank account within 1-3 business days.</p>

            <div class="form-group">
                <label for="modalWithdrawalAmount">Amount (USD)</label>
                <input type="number" id="modalWithdrawalAmount"
                       min="{{ min_withdrawal }}" max="{{ available_balance }}"
                       step="0.01" placeholder="Enter amount">
            </div>

            <div class="modal-fee-breakdown">
                <div class="fee-item">
                    <span>Amount:</span>
                    <span id="modalAmountDisplay">$0.00</span>
                </div>
                <div class="fee-item">
                    <span>Fee:</span>
                    <span id="modalFeeDisplay">$0.00</span>
                </div>
                <div class="fee-item total">
                    <span>You'll receive:</span>
                    <span id="modalNetDisplay">$0.00</span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeWithdrawalModal()">Cancel</button>
            <button class="btn btn-primary" onclick="submitWithdrawal()">Submit Request</button>
        </div>
    </div>
</div>

<style>
.withdrawal-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.dashboard-header {
    background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.withdrawal-welcome h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.balance-card {
    background: rgba(26,26,26,0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.balance-amount {
    font-size: 3rem;
    font-weight: 900;
    color: #1a1a1a;
    display: block;
}

.balance-label {
    font-size: 1.1rem;
    opacity: 0.8;
    display: block;
    margin-bottom: 0.5rem;
}

.dashboard-content {
    padding: 4rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.section-header h2 {
    color: #C0ff6b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.3rem;
}

.withdrawal-form-section {
    grid-row: 1 / 3;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 15px rgba(192,255,107,0.4);
    background: rgba(255,255,255,0.15);
}

.fee-breakdown {
    background: rgba(255,255,255,0.02);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.fee-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.fee-item.total {
    border-top: 1px solid rgba(192,255,107,0.2);
    padding-top: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.withdrawal-info {
    background: rgba(192,255,107,0.1);
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.withdrawal-info ul {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0 0;
}

.withdrawal-info li {
    padding: 0.25rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.withdrawals-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.withdrawal-item {
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid rgba(192,255,107,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.withdrawal-amount {
    font-size: 1.2rem;
    font-weight: 600;
    color: #C0ff6b;
}

.withdrawal-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-pending { background: rgba(255,193,7,0.2); color: #ffc107; }
.status-processing { background: rgba(0,123,255,0.2); color: #007bff; }
.status-completed { background: rgba(40,167,69,0.2); color: #28a745; }
.status-failed { background: rgba(220,53,69,0.2); color: #dc3545; }
.status-cancelled { background: rgba(108,117,125,0.2); color: #6c757d; }

.earnings-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.earnings-item {
    display: flex;
    justify-content: space-between;
    padding: 1rem;
    background: rgba(255,255,255,0.02);
    border-radius: 8px;
}

.earnings-value.highlight {
    color: #C0ff6b;
    font-weight: 600;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-outline {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-outline:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-disabled {
    background: linear-gradient(135deg, #666, #555);
    color: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

.btn:hover:not(.btn-disabled) {
    transform: translateY(-2px);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
}

.modal-content {
    background: #2d2d2d;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    color: #ffffff;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(192,255,107,0.2);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #C0ff6b;
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    opacity: 0.6;
}

.empty-state i {
    font-size: 3rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Calculate fees in real-time
function calculateFees(amount) {
    const feePercentage = amount * 0.025;
    const processingFee = Math.max(feePercentage, 0.25);
    const netAmount = amount - processingFee;

    return {
        amount: amount,
        fee: processingFee,
        net: netAmount
    };
}

// Update fee display
function updateFeeDisplay(inputId, amountDisplayId, feeDisplayId, netDisplayId) {
    const input = document.getElementById(inputId);
    const amount = parseFloat(input.value) || 0;
    const fees = calculateFees(amount);

    document.getElementById(amountDisplayId).textContent = `$${fees.amount.toFixed(2)}`;
    document.getElementById(feeDisplayId).textContent = `$${fees.fee.toFixed(2)}`;
    document.getElementById(netDisplayId).textContent = `$${fees.net.toFixed(2)}`;
}

// Event listeners
document.getElementById('withdrawalAmount').addEventListener('input', function() {
    updateFeeDisplay('withdrawalAmount', 'withdrawalAmountDisplay', 'processingFeeDisplay', 'netAmountDisplay');
});

document.getElementById('modalWithdrawalAmount').addEventListener('input', function() {
    updateFeeDisplay('modalWithdrawalAmount', 'modalAmountDisplay', 'modalFeeDisplay', 'modalNetDisplay');
});

// Modal functions
function openWithdrawalModal() {
    document.getElementById('withdrawalModal').style.display = 'block';
}

function closeWithdrawalModal() {
    document.getElementById('withdrawalModal').style.display = 'none';
}

// Form submission
document.getElementById('withdrawalForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const amount = document.getElementById('withdrawalAmount').value;

    if (!amount || parseFloat(amount) < {{ min_withdrawal }}) {
        alert('Please enter a valid amount (minimum ${{ min_withdrawal }})');
        return;
    }

    try {
        const response = await fetch('{% url "mentorship:request_withdrawal" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ amount: parseFloat(amount) })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
});

// Submit withdrawal from modal
async function submitWithdrawal() {
    const amount = document.getElementById('modalWithdrawalAmount').value;

    if (!amount || parseFloat(amount) < {{ min_withdrawal }}) {
        alert('Please enter a valid amount (minimum ${{ min_withdrawal }})');
        return;
    }

    try {
        const response = await fetch('{% url "mentorship:request_withdrawal" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({ amount: parseFloat(amount) })
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            closeWithdrawalModal();
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}

// Cancel withdrawal
async function cancelWithdrawal(withdrawalId) {
    if (!confirm('Are you sure you want to cancel this withdrawal request?')) {
        return;
    }

    try {
        const response = await fetch(`/mentorship/withdrawal/${withdrawalId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });

        const result = await response.json();

        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('withdrawalModal');
    if (event.target == modal) {
        closeWithdrawalModal();
    }
}
</script>
{% endblock %}
