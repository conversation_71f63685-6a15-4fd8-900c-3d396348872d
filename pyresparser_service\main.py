from fastapi import FastAPI, UploadFile
from pyresparser import ResumeParser
import os

app = FastAPI()

UPLOAD_DIR = "/app/user_cvs"
os.makedirs(UPLOAD_DIR, exist_ok=True)

@app.get("/")
def root():
    return {"message": "Pyresparser service is running"}

@app.post("/process-cv/")
async def process_cv(file: UploadFile):
    file_path = os.path.join(UPLOAD_DIR, file.filename)
    with open(file_path, "wb") as f:
        f.write(await file.read())

    # Process the CV using pyresparser
    data = ResumeParser(file_path).get_extracted_data()
    return {"extracted_data": data}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8005, reload=True)