"""
API Views for Accounts App
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
import json
import logging

# Set up logging for security events
security_logger = logging.getLogger('security')

@csrf_exempt
@require_http_methods(["POST"])
def log_security_event(request):
    """
    Log security events from client-side
    """
    try:
        data = json.loads(request.body)
        
        # Extract security event data
        event_type = data.get('event_type', 'unknown')
        details = data.get('details', '')
        user_agent = data.get('user_agent', '')
        timestamp = data.get('timestamp', '')
        url = data.get('url', '')
        
        # Get user info
        user_id = request.user.id if request.user.is_authenticated else 'anonymous'
        username = request.user.username if request.user.is_authenticated else 'anonymous'
        
        # Log the security event
        security_logger.warning(
            f"Security Event: {event_type} | "
            f"User: {username} ({user_id}) | "
            f"Details: {details} | "
            f"URL: {url} | "
            f"User-Agent: {user_agent} | "
            f"Timestamp: {timestamp}"
        )
        
        # You could also save to database here if needed
        # SecurityEvent.objects.create(
        #     user=request.user if request.user.is_authenticated else None,
        #     event_type=event_type,
        #     details=details,
        #     user_agent=user_agent,
        #     url=url,
        #     ip_address=get_client_ip(request)
        # )
        
        return JsonResponse({'status': 'logged'})
        
    except Exception as e:
        security_logger.error(f"Failed to log security event: {str(e)}")
        return JsonResponse({'status': 'error'}, status=500)

@login_required
def get_user_role(request):
    """
    Get user role and permissions for security system
    """
    user = request.user
    
    return JsonResponse({
        'role': 'admin' if user.is_superuser else ('staff' if user.is_staff else 'user'),
        'is_superuser': user.is_superuser,
        'is_staff': user.is_staff,
        'security_level': 'high' if user.is_superuser else ('medium' if user.is_staff else 'standard'),
        'username': user.username,
        'user_id': user.id
    })

def get_client_ip(request):
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip
