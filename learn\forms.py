from .models import Course, Chapter, Lesson, LessonContent
from django import forms

class CourseForm(forms.ModelForm):
    class Meta:
        model = Course
        fields = ['name', 'image', 'description', 'difficulty', 'estimated_duration', 'prerequisites', 'learning_objectives', 'status']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'course-title-form'}),
            'image': forms.ClearableFileInput(attrs={'class': 'course-image-form'}),
            'description': forms.Textarea(attrs={'class': 'course-description-form', 'rows': 4}),
            'difficulty': forms.Select(attrs={'class': 'course-difficulty-form'}),
            'estimated_duration': forms.TextInput(attrs={'class': 'course-duration-form'}),
            'prerequisites': forms.Textarea(attrs={'class': 'course-prerequisites-form', 'rows': 3}),
            'learning_objectives': forms.Textarea(attrs={'class': 'course-objectives-form', 'rows': 3}),
            'status': forms.Select(attrs={'class': 'course-status-form'}),
        }
        labels = {
            'name': 'Course Title',
            'description': 'Course Description',
            'image': 'Course Image',
            'difficulty': 'Difficulty Level',
            'estimated_duration': 'Estimated Duration',
            'prerequisites': 'Prerequisites',
            'learning_objectives': 'Learning Objectives',
            'status': 'Course Status',
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # If user is not superuser, limit status choices and add help text
        if user and not user.is_superuser:
            self.fields['status'].choices = [
                ('draft', 'Draft'),
                ('published', 'Published'),
            ]
            self.fields['status'].help_text = "📝 Draft: Only visible to you | 🚀 Published: Will be reviewed by admins before going live"
            self.fields['status'].initial = 'draft'  # Default to draft for mentors
        else:
            # For superusers, show all options with different help text
            self.fields['status'].help_text = "📝 Draft: Only visible to creator | 🚀 Published: Visible to all students | 📦 Archived: Hidden from students"

class ChapterForm(forms.ModelForm):
    class Meta:
        model = Chapter
        fields = ['name', 'description', 'order']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'chapter-title-form'}),
            'description': forms.Textarea(attrs={'class': 'chapter-description-form', 'rows': 3}),
            'order': forms.NumberInput(attrs={'class': 'chapter-order-form'}),
        }
        labels = {
            'name': 'Chapter Title',
            'description': 'Chapter Description',
            'order': 'Chapter Order',
        }

class LessonForm(forms.ModelForm):
    class Meta:
        model = Lesson
        fields = ['name', 'lesson_type', 'estimated_time', 'order']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'lesson-title-form'}),
            'lesson_type': forms.Select(attrs={'class': 'lesson-type-form'}),
            'estimated_time': forms.TextInput(attrs={'class': 'lesson-time-form'}),
            'order': forms.NumberInput(attrs={'class': 'lesson-order-form'}),
        }
        labels = {
            'name': 'Lesson Title',
            'lesson_type': 'Lesson Type',
            'estimated_time': 'Estimated Time',
            'order': 'Lesson Order',
        }

class LessonContentForm(forms.ModelForm):
    class Meta:
        model = LessonContent
        fields = ['content_type', 'title', 'content', 'code_language', 'order']
        widgets = {
            'content_type': forms.Select(attrs={'class': 'content-type-form'}),
            'title': forms.TextInput(attrs={'class': 'content-title-form'}),
            'content': forms.Textarea(attrs={'class': 'lesson-content-form', 'rows': 8}),
            'code_language': forms.TextInput(attrs={'class': 'code-language-form'}),
            'order': forms.NumberInput(attrs={'class': 'content-order-form'}),
        }
        labels = {
            'content_type': 'Content Type',
            'title': 'Content Title',
            'content': 'Content',
            'code_language': 'Code Language (for code examples)',
            'order': 'Content Order',
        }