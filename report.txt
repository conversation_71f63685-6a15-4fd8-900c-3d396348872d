ForgeX: AI-Powered Developer Collaboration Platform
Real-Time Collaborative Development Environment
with Intelligent Pairing System

Bachelor of Science with Honours Computer and Data Science, Sunway College Kathmandu Birmingham City

University, BCU Kathmandu, Nepal

Abstract —heart disease remains a leading cause of mortality worldwide, with heart attacks being a significant contributor. Early
prediction and prevention are crucial in reducing the impact of this condition. This project presents a web-based machine learning model
designed to predict an individual's risk of having a heart attack based on common, easily understandable parameters. By utilizing
everyday factors such as general health, smoking habits, alcohol consumption, stress levels, and BMI, our system provides an accessible
tool for preliminary risk assessment without relying on complex medical jargon or invasive tests.
The system combines a machine learning model trained on relevant health data with a user-friendly web interface developed using
Django. This integration allows users to input their information easily and receive instant risk predictions. By leveraging machine
learning algorithms and web technologies, our solution aims to empower individuals with knowledge about their heart health,
potentially encouraging early intervention and lifestyle changes to mitigate risks.

Keywords — Minimum viable Product, Heart Disease Prediction, Heart disease, Machine learning prediction, Aorta Ai, Logistic
Regression, Random Forest, python programming

I. INTRODUCTION
A. Exploring the domain of health and Cardio-Vascular Disease
The domain of health, particularly cardiovascular disease (CVD), is critical due to its significant impact on global health. The
global burden of CVDs has nearly doubled from 1990 to 2019, with significant increases in deaths and disability-adjusted life
years (<PERSON> et al., 2020). Cardiovascular diseases include coronary artery disease, heart failure, arrhythmias, and stroke. Ischemic
heart disease (IHD) and stroke are the principal contributors to CVD mortality and disability.
Advancements in medical technology, data analytics, and AI have opened new ways to understand and manage CVDs. By using
these tools, researchers can gain insights into risk factors, disease progression, and treatment. Modifiable risk factors, such as high
blood pressure and smoking, high blood pressure, unhealthy diet, high fasting plasma glucose, and high LDL cholesterol continue
to drive the global increase in CVD burden (Jagannathan et al., 2019). Public health initiatives promoting healthy lifestyles and
improving healthcare access are essential.
B. Problem Statement
As said earlier, heart disease remains one of the leading causes of death worldwide. Health is a basic need for everyone nowadays,
for everyone but still in various areas of the world there is no availability of health services and tools to treat them. In the case of
heart attacks, early prediction and intervention can significantly reduce the risk of fatal heart attacks. But it is expensive just to
check and not available in all areas. Also, any kind of prediction system uses traditional methods of heart attack prediction often
involve complex medical tests and jargon that can be understood by the public. So, a common question arises, what if there would

Sanket Shrestha
<EMAIL>

Riya Shrestha
<EMAIL>

Nishan Shrestha
<EMAIL>

Ashlesha Shrestha
<EMAIL>.

uk

Ankit Shrestha
<EMAIL>

be a system that is available to all, user-friendly and could provide basic testing before getting treatment from a doctor?
Thus, this project aims to create an accessible, user-friendly tool that leverages machine learning to predict heart attack risk using
easily understandable health parameters.
C. Introducing Aorta-Ai
Addressing the problem faced by the modern prediction app and manual testing system, Aorta-Ai is a web-based machine
learning to predict heart disease probability. Its is designed specifically designed to address current problem of availability of
heath service and cost-effective early detection system by hosting this system in web for everyone to use and providing an early
detection system that can predict the probability of a person’s proneness to heart disease regardless of their financial status or
location. It mainly has two features currently.
The first and major feature of Aorta-Ai is to predict one’s proneness to heart disease which is carried out using logistic regression
and random forest algorithms to predict the data. This is done by taking data from websites and then processing those data with
these algorithms to generate a prediction which is then displayed on the website.
The second feature is follow-up emailing, we take data of users and personally ask them about their health, have they booked doctor
or provide them recommendations. This establishes trust between user and the company which will ultimately be beneficial for us.
This can be done by taking their email for their data then providing them with the required information by manually sending them
emails.

D. Objective of this Project
The objectives of this project are:
• To develop an accurate machine learning model for heart attack prediction.
• To create a user-friendly web interface that allows users to input health parameters and receive predictions.
• To provide personalized health recommendations based on the prediction results.
• To plan for future features such as follow-up emails and doctor appointment bookings through the website.

II. LITERATURE REVIEW
A. Background Research on use of AI in Cardiovascular disease
The methodology involved an extensive literature review on AI in cardiovascular health care, focusing on applications like disease
detection, diagnosis, individualized treatment planning, and decision-making. The review aims to showcase how AI and machine
learning technologies can enhance patient outcomes, reduce costs, and revolutionize cardiovascular healthcare (Khawar Hussain et
al., 2023).

B. Current Trend
AI integration in cardiovascular health care addresses challenges in disease detection, diagnosis, and treatment. AI algorithms,
such as Convolutional Neural Networks (CNNs) and Support Vector Machines (SVMs), help automate the detection and diagnosis
of cardiovascular diseases, improving accuracy and efficiency (Khawar Hussain et al., 2023). Machine learning techniques, like
Decision Tree classifiers, predict heart disease with fewer variables, aiding medical practitioners in decision-making by analyzing
complex medical data (Wankhade, 2022).

C. Future Trends
Future trends include developing sophisticated algorithms for larger, diverse datasets and integrating AI with IoT and wearable
devices for real-time health insights. Collaboration among healthcare professionals, researchers, and industry partners is essential to
drive innovation and address current limitations in AI applications for cardiovascular diseases (Khawar Hussain et al., 2023;
Wankhade, 2022; Dey et al., 2021).

D. Research Gap in the Research
While significant advancements have been made, gaps remain in the reliability and efficiency of current diagnostic methods.
Traditional image interpretation is time-consuming and subjective, necessitating improved AI algorithms for more accurate and
timely analysis. Additionally, existing predictive models often lack the capability to uncover hidden linkages and trends in complex
medical data, highlighting the need for more robust machine learning approaches (Wankhade, 2022).

E. Critical Evaluation
AI's potential in transforming cardiovascular health care is significant, but challenges like ensuring algorithm reliability and
accuracy in diverse clinical settings remain. Ethical considerations, including data privacy and algorithmic biases, need careful
management. Efficient analysis methods are necessary to uncover hidden linkages and trends in heart disease data (Khawar Hussain
et al., 2023; Wankhade, 2022; Dey et al., 2021).

III. MINIMUM VIABLE PRODUCT

A. Significance of MVP during production:
A Minimum Viable Product (MVP) is a development technique in which a new product or website is developed with sufficient
features to satisfy early adopters. The final, complete set of features is only designed and developed after considering feedback from
the product's initial users.
Importance of MVP for Aorta-Ai is crucial for these reasons:
• Core Functionality: It includes only the essential features that address the primary problem or need of the target
audience. So that we can omit non-essential features in the initial version.
• User Feedback: It helps us to gather feedback from real users. The feedback is used to improve and refine the product in
subsequent iterations.
• Iterative Process: developing MVP is iterative process itself. After making the 1st MVP we can improve our original
product based on the constructive feedback we get.
• Minimize Financial Risk: focusing on the minimum set of features, and it reduces development costs and minimizes
financial risk.
• Learn About Users: it helps us to understand how users interact with the product and what they truly need or value and
gauge the market demand for the product and identify potential target audiences

B. .MVP Canvas

C. Hardware and Software Required:
1) Hardware Required:
i. Laptop: as there are no servers to handle load balancing and host our website, in our MVP we will be using laptop to host
our website and extract data from it.
2) Software Required:
The MVP version of the Aorta-Ai using the following software tools and technologies for its initial development:
i. Programming Languages:
a. Python (for backend and machine learning)
b. JavaScript (for frontend interactivity)
c. HTML/CSS (for frontend structure and styling)
ii. Web Framework:
a. Django (Python-based web framework)
iii. Database:
a. SQLite (for development)
iv. Machine Learning Libraries:
a. Scikit-learn (for model development)
b. Pandas (for data manipulation)
c. NumPy (for numerical operations)
d. Matplotlib and Seaborn (for data visualization)

Fig.1. MVP Canvas of Aorta AI

IV. CLIENT-SERVER ARCHITECTURE

At its core, the proposed system aims to predict the risk of heart attacks based on user-input health parameters. To achieve this, a
client-server architecture has been implemented that enables users to input their health data and receive risk assessments. The server
can be as simple as a cloud-based instance running the server code and hosting the machine learning model. The server is responsible
for receiving user data, processing it through the prediction model, and returning the risk assessment results to the client.
The client is essentially a web-based interface with form inputs, accessible through any modern web browser, requiring minimal
processing capability on the user's end. In a practical scenario, the client-side script runs in the user's web browser, collecting input
data through a user-friendly form. The server-side script, hosting the machine learning model, runs on a cloud platform. Both client
and server communicate over HTTPS, ensuring secure data transmission.
The client sends the user's health data to the server via API calls. The server then processes this data through the pre-trained machine
learning model to generate a heart attack risk prediction. This prediction is then sent back to the client, where it's displayed to the
user in an easy-to-understand format.

Fig.2. Client server architecture

V. PRODUCT PROTYPE

The prototype of Aorta-AI helped us to clearly see and improve the system's functionalities and test the user interface effectively.
We used Django for the backend and MySQL to store patient health data and user details. This setup allowed us to refine the
prediction algorithms and ensure that essential features, such as heart disease prediction, and follow-up emails worked smoothly.
Users can directly access the home page of the website, where they can use the features to book appointments and access other
services without the need to log in or sign up currently.

VI. FEATURES FUNCTIONALITY

Aorta-AI's basic version helps predict heart disease using age, gender, stress, BMI, etc. It quickly assesses the risk of heart disease
based on this information. It also gives general tips on improving heart health, like diet and exercise suggestions to the user. Along
with emails remind users about health check-ups and medications, helping them stay on track with their health goals. The interface
is easy to use, showing risk assessments and tips clearly.

Fig.3. Aorta AI Landing Page

VII. WORKING MECHANISM

Users provide information such as age, gender, cholesterol levels, and blood pressure through interactive forms on the website.
Once submitted, this data is securely transmitted to the backend system of Aorta-AI. Here, the system processes the data by
encoding categorical variables like gender and normalizing numerical features such as age and blood pressure.

Fig.4. Aorta AI Dashboard

Fig.5. Aorta AI Flowchart

Here, the system processes the data by encoding categorical variables like gender and normalizing numerical features such as age
and blood pressure. Utilizing machine learning models—like Logistic Regression or Random Forest Classifier—the system
calculates the likelihood of the user having heart disease based on their input. The results are then presented back to the user
through the website, offering clear feedback such as a risk score or categorized risk level (low, medium, high).
Additionally, the website could provide personalized recommendations for improving heart health and send automated reminders
for health check-ups and lifestyle adjustments.

VIII.MODEL TRAINING PROCESS

A. Data loading
The code loads the dataset heart.csv into a pandas Data Frame heart_data, which contains the data required for training and testing
machine learning models.

B. Data Preprocessing
This section preprocesses the dataset by encoding categorical variables using LabelEncoder and normalizing numerical variables
using StandardScaler. These steps are essential for preparing the data for machine learning model training.

C. Model Training and Evaluation
This part splits the preprocessed data into training and testing sets (X_train, X_test, y_train, y_test). It defines two models (Logistic
Regression and Random Forest), trains each model on the training data, and evaluates their performance using metrics like accuracy,
classification report, and confusion matrix.
file_path = '/content/heart.csv'
heart_data = pd.read_csv(file_path)

# Encode categorical variables
categorical_columns = heart_data.select_dtypes(include=['object']).columns
label_encoders = {col: LabelEncoder() for col in categorical_columns}
for col in categorical_columns:
heart_data[col] = label_encoders[col].fit_transform(heart_data[col])
# Normalize numerical variables, excluding the target variable 'HeartDisease'
numerical_columns = heart_data.select_dtypes(include=['float64', 'int64']).columns
numerical_columns = numerical_columns.drop('HeartDisease')
scaler = StandardScaler()
heart_data[numerical_columns] = scaler.fit_transform(heart_data[numerical_columns])

# Split the data into training and testing sets
X = heart_data.drop(columns=['HeartDisease'])
y = heart_data['HeartDisease']
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
# Define models to train
models = {
"Logistic Regression": LogisticRegression(max_iter=1000),
"Random Forest": RandomForestClassifier()
}
# Train and evaluate models
results = {}
for model_name, model in models.items():
print(f"Training {model_name}...")
model.fit(X_train, y_train)
y_pred = model.predict(X_test)
accuracy = accuracy_score(y_test, y_pred)
report = classification_report(y_test, y_pred, output_dict=True)
confusion = confusion_matrix(y_test, y_pred)
results[model_name] = {
"accuracy": accuracy,
"classification_report": report,
"confusion_matrix": confusion
}

D. Model Selection and Prediction on New Data
After evaluating the models, the code identifies the best-performing model (Random Forest in this case) based on accuracy. It then
prepares new data (new_data) with specific features and predicts the likelihood of heart disease using the selected model
(best_model). The predictions (prediction) and associated probabilities (prediction_prob) are printed to assess the model's
performance on new, unseen data.

# Predict using the best model (assuming it's Random Forest in this case)
best_model_name = max(results, key=lambda name: results[name]['accuracy'])
best_model = models[best_model_name]
new_data = pd.DataFrame({
'Age': [65],
'Sex': ['Male'],
'ChestPainType': ['Non-anginal pain'],
'RestingBloodPressure': [140],
'Cholesterol': [260],
'FastingBloodSugar': [1],
'RestingECG': [0],
'MaxHeartRate': [140],
'ExerciseInducedAngina': [0],
'STDepression': [3.0],
'STSlope': [2],
'NumMajorVessels': [0],
'Thalassemia': ['Normal'],
})
# Encoding categorical variables in new_data
for col in categorical_columns:
new_data[col] = label_encoders[col].transform(new_data[col])
# Normalizing numerical variables in new_data
new_data[numerical_columns] = scaler.transform(new_data[numerical_columns])
# Making predictions
prediction = best_model.predict(new_data)
prediction_prob = best_model.predict_proba(new_data)[:, 1]
print(f"Prediction: {prediction}")
print(f"Probability of Heart Disease: {prediction_prob}")

IX. CHALLENGES FACED

During Aorta-AI's development, we faced several challenges. Poor backend connections hindered data transfer between models
and the user interface, so we implemented a more robust API and improved server infrastructure. Insufficient training data was
addressed by partnering with hospitals to obtain a larger, diverse dataset. To help doctors understand the model's predictions, we
used explainable AI techniques and SHAP values. Performance issues with handling large patient data were resolved by refining
algorithms and utilizing cloud computing.

Fig.6. Aorta AI Challenges

X. FUTURE IMPLEMENTATION

Future enhancements for Aorta-AI include several exciting developments. One planned improvement is the integration of a doctor
and consultant booking system. This feature will allow patients to easily book appointments with healthcare professionals directly
through the Aorta-AI platform, ensuring timely and convenient access to medical care.
Another enhancement is the implementation of personalized recommendations based on user data. By analyzing individual health
records and lifestyle information, Aorta-AI will be able to provide tailored advice and treatment plans that cater specifically to each
patient's needs.
Additionally, Aorta-AI aims to improve its accuracy and predictive capabilities by utilizing advanced data analysis techniques and
other innovative methods. These techniques will help generate more precise predictions and offer clear visualizations of a patient's
heart health, ultimately leading to better medical outcomes. These future enhancements will not only enhance the user experience
but also significantly contribute to the early detection and prevention of heart disease.

XI. CONCLUSION

In conclusion, Aorta-AI represents a significant step forward in utilizing machine learning for early heart disease prediction. By
focusing on easily understandable health parameters and offering a user-friendly web interface, our system aims to empower
individuals with accessible tools for assessing their heart health. The integration of advanced data analysis techniques and
personalized recommendations will further enhance the system's capabilities, potentially leading to improved medical outcomes
through early detection and preventive measures. As we continue to evolve Aorta-AI, our goal remains steadfast: to make heart
disease prediction more accessible, effective, and beneficial to individuals worldwide.
Fig.7. Aorta AI Future Plans

XII. REFERENCES

Dey, M., Mondal, A. and Sarkar, D. (2021) ‘Heart disease prediction—an online consultation software’, Algorithms for Intelligent
Systems, pp. 791–800. doi:10.1007/978-981-33-4604-8_61.
Jagannathan, R. et al. (2019) ‘Global updates on cardiovascular disease mortality trends and attribution of traditional risk factors’,
Current Diabetes Reports, 19(7). doi:10.1007/s11892-019-1161-2.
Khawar Hussain, H., Tariq, A. and Yousaf Gill, A. (2023) ‘Role of artificial intelligence in cardiovascular health care’, Journal of
World Science, 2(4), pp. 583–591. doi:10.58344/jws.v2i4.284.
Roth, G.A. et al. (2020) ‘Global burden of cardiovascular diseases and risk factors, 1990–2019’, Journal of the American College
of Cardiology, 76(25), pp. 2982–3021. doi:10.1016/j.jacc.2020.11.010.
Wankhade, Sunil.B. (2022) ‘Heart disease prediction’, INTERANTIONAL JOURNAL OF SCIENTIFIC RESEARCH IN
ENGINEERING AND MANAGEMENT, 06(05). doi:10.55041/ijsrem12754.