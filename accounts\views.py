from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .forms import SignUpForm, UserProfileForm, EmailAuthenticationForm, SkillWithLevelForm, SkillLevelFormSet, EventForm, CustomPasswordResetForm, CustomSetPasswordForm, UserAccountForm, ExtendedUserProfileForm
from .models import UserProfile, Skill, timezone_choices, Event
from .session_utils import get_session_info, get_user_sessions
import os
import logging
# Removed unused pyresparser import as it is now handled in the Dockerized service
# from pyresparser import ResumeParser
# Removed nltk import as it is no longer needed in the main environment
# from nltk import download
# Removed spacy-related code as it is no longer needed
import requests
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes, force_str
from django.template.loader import render_to_string
from django.http import HttpResponse, JsonResponse
from django.contrib.sites.shortcuts import get_current_site
from django.urls import reverse
from django.utils.timezone import now
from datetime import timedelta
from django.contrib.auth.models import User
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from collaborate.models import Notification, ProjectMembership  # Import Notification and ProjectMembership models
from collaborate.management.commands.skill_heatmap import get_skill_heatmap
from collaborate.utils.ai_helpers import get_skills_to_learn
from allauth.socialaccount.models import SocialAccount, SocialApp
from allauth.socialaccount import app_settings

logger = logging.getLogger(__name__)

# Custom ResumeParser to bypass config issues
# class CustomResumeParser:
#     def __init__(self, resume):
#         self.resume = resume
#         self.nlp = spacy.load("en_core_web_sm")

#     def get_extracted_data(self):
#         return ResumeParser(self.resume).get_extracted_data()

@csrf_exempt
def check_verification_status(request):
    if request.user.is_authenticated and request.user.is_active:
        return JsonResponse({'verified': True})
    return JsonResponse({'verified': False})

def verification_pending(request):
    return render(request, 'accounts/verification_pending.html')

def signup(request):
    if request.user.is_authenticated:
        return redirect('accounts:profile')

    if request.method == 'POST':
        form = SignUpForm(request.POST)
        if form.is_valid():
            user = form.save(commit=False)
            user.is_active = False  # Deactivate account until email is verified
            user.save()

            # Generate email verification token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            current_site = get_current_site(request)
            verification_link = request.build_absolute_uri(
                reverse('accounts:verify_email', kwargs={'uidb64': uid, 'token': token})
            )

            # Send verification email
            subject = 'Verify your email address'
            html_message = render_to_string('accounts/email_verification.html', {
                'user': user,
                'verification_link': verification_link,
                'domain': current_site.domain,
            })
            # Create plain text version for fallback
            plain_message = f"""
Hi {user.first_name or user.username},

Thank you for signing up for ForgeX. Please verify your email address by clicking the link below:

{verification_link}

If you did not sign up, please ignore this email.

Best regards,
The ForgeX Team
            """.strip()

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message
            )

            messages.success(request, 'A verification email has been sent to your email address.')
            return redirect('accounts:verification_pending')
    else:
        form = SignUpForm()
    return render(request, 'accounts/signup.html', {'form': form})

def verify_email(request, uidb64, token):
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user is not None and default_token_generator.check_token(user, token):
        user.is_active = True
        user.save()
        # Log the user in automatically after verification with explicit backend
        # Use Django's default backend for login
        user.backend = 'django.contrib.auth.backends.ModelBackend'
        login(request, user)
        messages.success(request, 'Your email has been verified. Please complete your profile setup.')
        return redirect('accounts:profile_setup')
    else:
        messages.error(request, 'The verification link is invalid or has expired.')
        return render(request, 'accounts/email_verification_failed.html')

def resend_verification_email(request):
    if request.method == 'POST':
        email = request.POST.get('email')
        try:
            user = User.objects.get(email=email, is_active=False)
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            current_site = get_current_site(request)
            verification_link = request.build_absolute_uri(
                reverse('accounts:verify_email', kwargs={'uidb64': uid, 'token': token})
            )

            # Send verification email
            subject = 'Resend: Verify your email address'
            html_message = render_to_string('accounts/email_verification.html', {
                'user': user,
                'verification_link': verification_link,
                'domain': current_site.domain,
            })
            # Create plain text version for fallback
            plain_message = f"""
Hi {user.first_name or user.username},

This is a resend of your email verification for ForgeX. Please verify your email address by clicking the link below:

{verification_link}

If you did not sign up, please ignore this email.

Best regards,
The ForgeX Team
            """.strip()

            send_mail(
                subject=subject,
                message=plain_message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                html_message=html_message
            )

            messages.success(request, 'A new verification email has been sent.')
        except User.DoesNotExist:
            messages.error(request, 'No inactive account found with this email address.')

    return render(request, 'accounts/resend_verification_email.html')

def generate_leaderboard():
    """Generate leaderboard data for top developers"""
    from django.contrib.auth.models import User
    from collaborate.models import Project, UserSkill, ProjectMembership
    from mentorship.models import MentorProfile, MentorshipSession
    from django.db.models import Q, Count, Avg, Sum
    from decimal import Decimal

    # Get all active users with their stats
    users_data = []

    for user in User.objects.filter(is_active=True).select_related('profile'):
        # Skip superusers from leaderboard
        if user.is_superuser:
            continue

        # Calculate project count (owned + member)
        project_count = Project.objects.filter(
            Q(owner=user) | Q(memberships__user=user)
        ).distinct().count()

        # Calculate skills count
        skills_count = UserSkill.objects.filter(user=user).count()

        # Check if user is a mentor and get mentor stats
        mentor_sessions = 0
        mentor_rating = 0.0
        mentor_earnings = Decimal('0.00')
        is_mentor = False

        try:
            mentor_profile = MentorProfile.objects.get(user=user)
            is_mentor = True
            mentor_sessions = mentor_profile.total_sessions
            mentor_rating = float(mentor_profile.average_rating)
            mentor_earnings = mentor_profile.total_earnings
        except MentorProfile.DoesNotExist:
            pass

        # Calculate overall score for ranking
        # Projects: 10 points each, Skills: 2 points each, Mentor sessions: 5 points each
        # Mentor rating: 20 points for 5-star rating
        score = (project_count * 10) + (skills_count * 2) + (mentor_sessions * 5) + (mentor_rating * 20)

        # Only include users with some activity
        if project_count > 0 or skills_count > 0 or mentor_sessions > 0:
            users_data.append({
                'user': user,
                'project_count': project_count,
                'skills_count': skills_count,
                'mentor_sessions': mentor_sessions,
                'mentor_rating': mentor_rating,
                'mentor_earnings': mentor_earnings,
                'is_mentor': is_mentor,
                'score': score,
                'profile_picture': user.profile.profile_picture if hasattr(user, 'profile') and user.profile.profile_picture else user.profile.social_profile_picture_url if hasattr(user, 'profile') and user.profile.social_profile_picture_url else None,
            })

    # Sort by score (highest first) and limit to top 10
    users_data.sort(key=lambda x: x['score'], reverse=True)
    top_developers = users_data[:10]

    # Create separate rankings for different categories
    top_by_projects = sorted(users_data, key=lambda x: x['project_count'], reverse=True)[:5]
    top_by_skills = sorted(users_data, key=lambda x: x['skills_count'], reverse=True)[:5]
    top_mentors = sorted([u for u in users_data if u['is_mentor']], key=lambda x: x['mentor_rating'], reverse=True)[:5]

    return {
        'top_developers': top_developers,
        'top_by_projects': top_by_projects,
        'top_by_skills': top_by_skills,
        'top_mentors': top_mentors,
    }

def login_view(request):
    # Check if user is already authenticated
    if request.user.is_authenticated:
        return redirect('accounts:profile')

    if request.method == 'POST':
        # Use the custom EmailAuthenticationForm to allow login with email or username
        form = EmailAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            user = form.get_user()
            remember_me = form.cleaned_data.get('remember_me', False)

            # Set session expiry based on remember me choice
            if remember_me:
                # Keep logged in for 30 days
                request.session.set_expiry(60 * 60 * 24 * 30)
            else:
                # Session expires when browser closes
                request.session.set_expiry(0)

            login(request, user)

            # Add success message
            messages.success(request, f'Welcome back, {user.username}! You are now logged in.')

            return redirect('accounts:profile')  # Redirect to profile page instead of home
    else:
        form = EmailAuthenticationForm(request)
    return render(request, 'accounts/login.html', {'form': form})

def logout_view(request):
    if request.method == 'POST':
        logout(request)
        return redirect('home_view')
    return render(request, 'accounts/logout.html')

@login_required
def dashboard(request):
    """Dashboard view for authenticated users"""
    from learn.models import Course
    from collaborate.models import Project, UserSkill, ProjectMembership
    from mentorship.models import MentorProfile, MentorshipSession, SessionFeedback
    from django.db.models import Q, Sum, Avg, Count, F, Case, When, IntegerField
    from django.utils import timezone
    from datetime import timedelta
    from decimal import Decimal

    # Handle welcome messages
    if request.GET.get('welcome') == 'google':
        if request.GET.get('new') == 'true':
            messages.success(request, f'🎉 Welcome to Forge X, {request.user.username}! You\'ve successfully signed in with Google. Let\'s get you started!')
        else:
            messages.info(request, f'Welcome back, {request.user.username}! 👋')

    # Get counts for dashboard stats
    course_count = Course.objects.count()

    # Get projects where user is either the owner or a member
    user_projects = Project.objects.filter(
        Q(owner=request.user) | Q(memberships__user=request.user)
    ).distinct()

    user_skills_count = UserSkill.objects.filter(user=request.user).count()

    # Get recent projects for display (limit to 3 most recent)
    recent_projects = user_projects.order_by('-created_at')[:3]

    # Mentorship data
    mentorship_data = {}

    # Check if user is a mentor
    try:
        mentor_profile = MentorProfile.objects.get(user=request.user)
        mentorship_data['is_mentor'] = True
        mentorship_data['mentor_profile'] = mentor_profile

        # Get mentor's upcoming sessions
        mentor_upcoming_sessions = MentorshipSession.objects.filter(
            mentor=request.user,
            status__in=['scheduled', 'active'],
            scheduled_time__gte=timezone.now()
        ).select_related('learner').order_by('scheduled_time')[:3]

        # Get mentor's monthly earnings
        current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        # Query for sessions completed this month (regardless of when payment was made)
        monthly_earnings_query = MentorshipSession.objects.filter(
            mentor=request.user,
            status='completed',
            ended_at__gte=current_month,
            is_paid=True
        )
        monthly_earnings = monthly_earnings_query.aggregate(total=Sum('mentor_earnings'))['total'] or Decimal('0.00')

        # Debug: Get all completed sessions for this mentor to understand the data
        all_completed_sessions = MentorshipSession.objects.filter(
            mentor=request.user,
            status='completed'
        ).order_by('-ended_at')

        print(f"DEBUG - User: {request.user.username}, Is Mentor: True")
        print(f"DEBUG - Current month: {current_month}")
        print(f"DEBUG - Monthly earnings query count: {monthly_earnings_query.count()}")
        print(f"DEBUG - Monthly earnings: {monthly_earnings}")
        print(f"DEBUG - All completed sessions count: {all_completed_sessions.count()}")

        # Debug: Print details of recent completed sessions
        for i, session in enumerate(all_completed_sessions[:5]):
            print(f"DEBUG - Session {i+1}: ended_at={session.ended_at}, is_paid={session.is_paid}, earnings={session.mentor_earnings}, learner={session.learner.username}")

        # Alternative calculation: Count all paid completed sessions regardless of month
        total_earnings_query = MentorshipSession.objects.filter(
            mentor=request.user,
            status='completed',
            is_paid=True
        )
        total_earnings = total_earnings_query.aggregate(total=Sum('mentor_earnings'))['total'] or Decimal('0.00')
        print(f"DEBUG - Total lifetime earnings: {total_earnings}")

        # Get recent feedback
        recent_feedback = SessionFeedback.objects.filter(
            session__mentor=request.user,
            session__status='completed'
        ).select_related('session__learner').order_by('-created_at')[:2]

        mentorship_data.update({
            'mentor_upcoming_sessions': mentor_upcoming_sessions,
            'monthly_earnings': monthly_earnings,
            'recent_feedback': recent_feedback,
        })

    except MentorProfile.DoesNotExist:
        mentorship_data['is_mentor'] = False
        print(f"DEBUG - User: {request.user.username}, Is Mentor: False")

    # Get learner's upcoming sessions
    learner_upcoming_sessions = MentorshipSession.objects.filter(
        learner=request.user,
        status__in=['scheduled', 'active'],
        scheduled_time__gte=timezone.now()
    ).select_related('mentor').order_by('scheduled_time')[:3]

    # Get learner's recent completed sessions
    learner_recent_sessions = MentorshipSession.objects.filter(
        learner=request.user,
        status='completed'
    ).select_related('mentor').order_by('-ended_at')[:2]

    # General mentorship stats
    total_mentors = MentorProfile.objects.filter(is_active=True, profile_completed=True).count()
    today = timezone.now().date()
    sessions_today = MentorshipSession.objects.filter(
        scheduled_time__date=today,
        status__in=['scheduled', 'active']
    ).count()

    # Debug: Print general stats
    print(f"DEBUG - Total mentors: {total_mentors}, Sessions today: {sessions_today}")

    mentorship_data.update({
        'learner_upcoming_sessions': learner_upcoming_sessions,
        'learner_recent_sessions': learner_recent_sessions,
        'total_mentors': total_mentors,
        'sessions_today': sessions_today,
    })

    # Generate Leaderboard Data
    leaderboard_data = generate_leaderboard()

    context = {
        'course_count': course_count,
        'project_count': user_projects.count(),
        'user_skills_count': user_skills_count,
        'recent_projects': recent_projects,
        'mentorship_data': mentorship_data,
        'leaderboard_data': leaderboard_data,
    }
    return render(request, 'accounts/dashboard.html', context)

@login_required
def profile(request):
    try:
        user_profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(user=request.user)

    heatmap = get_skill_heatmap()

    # Get user skills from the user_profile instead of directly from user
    from collaborate.models import UserSkill
    user_skills = UserSkill.objects.filter(user=request.user).select_related('skill')

    # Get skill names for suggested skills calculation
    user_skill_names = set(skill.skill.name.lower() for skill in user_skills)

    # Calculate suggested skills with better categorization
    from collaborate.models import Skill as CollaborateSkill

    # Get all skills and categorize them
    all_skills = CollaborateSkill.objects.all()

    # Define skill categories
    skill_categories = {
        'Programming Languages': ['python', 'javascript', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin'],
        'Web Development': ['html', 'css', 'react', 'angular', 'vue', 'node.js', 'express', 'django', 'flask', 'laravel'],
        'Data & Analytics': ['sql', 'mongodb', 'postgresql', 'mysql', 'power bi', 'tableau', 'excel', 'r', 'pandas', 'numpy'],
        'Cloud & DevOps': ['aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'terraform', 'ansible'],
        'Mobile Development': ['android', 'ios', 'react native', 'flutter', 'xamarin', 'swift', 'kotlin'],
        'AI & Machine Learning': ['machine learning', 'deep learning', 'tensorflow', 'pytorch', 'nlp', 'computer vision'],
        'Design & UX': ['ui/ux', 'figma', 'adobe', 'photoshop', 'illustrator', 'sketch', 'prototyping'],
        'Other Technologies': []
    }

    # Categorize suggested skills
    categorized_suggestions = {}
    high_demand_skills = []

    for skill, stats in heatmap.items():
        if stats['demand'] > stats['supply'] and skill.lower() not in user_skill_names:
            # Find category for this skill
            skill_category = 'Other Technologies'
            for category, keywords in skill_categories.items():
                if any(keyword in skill.lower() for keyword in keywords):
                    skill_category = category
                    break

            if skill_category not in categorized_suggestions:
                categorized_suggestions[skill_category] = []

            categorized_suggestions[skill_category].append({
                'name': skill,
                'demand': stats['demand'],
                'supply': stats['supply'],
                'demand_ratio': stats['demand'] / max(stats['supply'], 1)
            })

            # Track high demand skills (demand > 3 and demand_ratio > 2)
            if stats['demand'] > 3 and stats['demand'] / max(stats['supply'], 1) > 2:
                high_demand_skills.append(skill)

    # Sort skills within each category by demand ratio
    for category in categorized_suggestions:
        categorized_suggestions[category].sort(key=lambda x: x['demand_ratio'], reverse=True)
        # Limit to top 8 skills per category to avoid overwhelming display
        categorized_suggestions[category] = categorized_suggestions[category][:8]

    # Remove empty categories
    categorized_suggestions = {k: v for k, v in categorized_suggestions.items() if v}

    return render(request, 'accounts/profile.html', {
        'user_profile': user_profile,
        'skills': user_profile.skills.all(),
        'categorized_suggestions': categorized_suggestions,
        'high_demand_skills': high_demand_skills[:10],  # Top 10 high demand skills
        'total_suggestions': sum(len(skills) for skills in categorized_suggestions.values())
    })

@login_required
def profile_setup_view(request):
    try:
        user_profile = UserProfile.objects.get(user=request.user)
    except UserProfile.DoesNotExist:
        user_profile = UserProfile.objects.create(user=request.user)

    # Get existing user skills to pre-populate
    from collaborate.models import UserSkill, Skill as CollaborateSkill
    from django.forms import formset_factory
    from django.db import transaction

    # Create formset for skill levels
    SkillLevelFormSetFactory = formset_factory(
        SkillWithLevelForm,
        formset=SkillLevelFormSet,
        extra=1,  # Start with at least one empty form
        max_num=15,  # Limit to 15 skills at a time
        can_delete=True  # Allow deletion of skills
    )

    # Initialize with existing skills data if available
    initial_skills = []
    existing_skills = UserSkill.objects.filter(user=request.user).select_related('skill')
    for user_skill in existing_skills:
        initial_skills.append({
            'skill': user_skill.skill.name,
            'level': user_skill.proficiency
        })

    # Handle form submission
    if request.method == 'POST':
        # Initialize both forms
        user_form = UserAccountForm(request.POST, instance=request.user)
        profile_form = ExtendedUserProfileForm(request.POST, request.FILES, instance=user_profile)
        skill_formset = SkillLevelFormSetFactory(request.POST, initial=initial_skills, prefix='skills')

        # Separate validation for forms and formset to make skills optional
        user_form_valid = user_form.is_valid()
        profile_form_valid = profile_form.is_valid()

        # Debug logging
        logger.info(f"User form valid: {user_form_valid}")
        logger.info(f"Profile form valid: {profile_form_valid}")
        if not user_form_valid:
            logger.error(f"User form errors: {user_form.errors}")
        if not profile_form_valid:
            logger.error(f"Profile form errors: {profile_form.errors}")

        # Check if there are any non-empty skill forms before validation
        has_skills_to_validate = False
        for form_data in request.POST:
            if form_data.startswith('skills-') and form_data.endswith('-skill'):
                skill_value = request.POST.get(form_data, '').strip()
                if skill_value:
                    has_skills_to_validate = True
                    break

        # Only validate the formset if there are skills to validate
        if has_skills_to_validate:
            formset_valid = skill_formset.is_valid()
        else:
            # If no skills to validate, consider the formset valid
            formset_valid = True

        # If both main forms are valid, we can proceed
        if user_form_valid and profile_form_valid:
            try:
                with transaction.atomic():
                    # Save user account information
                    user = user_form.save()
                    logger.info(f"User saved: {user.username}, {user.first_name}, {user.last_name}")

                    # Save profile information
                    profile = profile_form.save()
                    logger.info(f"Profile saved: {profile.bio[:50] if profile.bio else 'No bio'}, phone: {profile.phone_number}")

                    # Mark profile as completed
                    profile.profile_completed = True
                    profile.save()
                    logger.info(f"Profile marked as completed")

                    # Process skill formset data
                    # First clear existing skills to avoid duplicates
                    UserSkill.objects.filter(user=request.user).delete()

                    # If the formset is valid and has skills, process them
                    if formset_valid and has_skills_to_validate:
                        # Add each skill with its level
                        for skill_form in skill_formset:
                            if skill_form.cleaned_data and skill_form.cleaned_data.get('skill'):
                                skill_name = skill_form.cleaned_data.get('skill').strip()
                                if skill_name:  # Only process non-empty skills
                                    level = int(skill_form.cleaned_data.get('level', 1))
                                    # Add skill to profile using our helper method
                                    profile.add_skill_with_level(skill_name, level)
                    elif not formset_valid:
                        # Log the formset errors for debugging
                        logger.error(f"Skill formset validation errors: {skill_formset.errors}")
                        if skill_formset.non_form_errors():
                            logger.error(f"Non-form errors: {skill_formset.non_form_errors()}")

                        # Show detailed error messages to the user
                        for i, form_errors in enumerate(skill_formset.errors):
                            if form_errors:
                                for field, errors in form_errors.items():
                                    for error in errors:
                                        messages.error(request, f"Skill {i+1}, {field}: {error}")

                    messages.success(request, 'Your profile has been updated successfully!')
                    return redirect('accounts:profile')
            except Exception as e:
                logger.error(f"Error processing profile: {str(e)}")
                messages.error(request, f"Error processing profile: {str(e)}")
        else:
            # Collect all form errors
            errors = []
            if not user_form_valid:
                errors.extend([f"Account: {error}" for field_errors in user_form.errors.values() for error in field_errors])
            if not profile_form_valid:
                errors.extend([f"Profile: {error}" for field_errors in profile_form.errors.values() for error in field_errors])

            if errors:
                for error in errors:
                    messages.error(request, error)

        # Process CV after successful save (outside transaction) - only if forms were valid
        if user_form_valid and profile_form_valid:
            # Get the updated profile instance
            try:
                updated_profile = UserProfile.objects.get(user=request.user)
                if updated_profile.cv and os.path.exists(updated_profile.cv.path):
                    cv_path = updated_profile.cv.path
                    try:
                        logger.info(f"Sending CV to FastAPI service: {cv_path}")
                        with open(cv_path, 'rb') as cv_file:
                            response = requests.post(
                                f"{settings.PYRESPARSER_SERVICE_URL}/process-cv/",
                                files={"file": cv_file}
                            )
                            response.raise_for_status()
                            data = response.json().get("extracted_data", {})

                        logger.info(f"Extracted data: {data}")

                        # Extract and store skills
                        if 'skills' in data and data['skills']:
                            for skill_name in data['skills']:
                                # Add skill with default beginner level (1)
                                updated_profile.add_skill_with_level(skill_name, 1)

                        # Extract and store other useful information
                        updated_profile.bio = data.get('summary', updated_profile.bio)
                        updated_profile.save()

                    except Exception as e:
                        logger.error(f"Error processing CV: {str(e)}")
                        messages.error(request, f"Error processing CV: {str(e)}")
            except Exception as e:
                logger.error(f"Error getting updated profile: {str(e)}")
                messages.error(request, f"Error processing profile: {str(e)}")

            # Show appropriate messages based on what was saved
            if has_skills_to_validate and not formset_valid:
                messages.success(request, "Profile updated successfully, but some skills could not be saved due to validation errors.")
            elif has_skills_to_validate and formset_valid:
                messages.success(request, "Profile and skills updated successfully!")
            else:
                # No skills were submitted or all skills were empty
                messages.success(request, "Profile updated successfully! No skills were added.")

            return redirect('accounts:profile')
    else:
        user_form = UserAccountForm(instance=request.user)
        profile_form = ExtendedUserProfileForm(instance=user_profile)
        skill_formset = SkillLevelFormSetFactory(initial=initial_skills, prefix='skills')

    # Get all available skills for the datalist
    all_skills = CollaborateSkill.objects.all().values_list('name', flat=True)

    return render(request, 'accounts/profile_setup.html', {
        'user_form': user_form,
        'profile_form': profile_form,
        'skill_formset': skill_formset,
        'all_skills': all_skills,
        'skills': Skill.objects.all(),
        'timezone_choices': timezone_choices
    })

def test_service_communication(request):
    try:
        fastapi_response = requests.get('http://localhost:8002')
        pyresparser_response = requests.get('http://localhost:8003')
        return JsonResponse({
            'fastapi_status': fastapi_response.status_code,
            'fastapi_response': fastapi_response.text,
            'pyresparser_status': pyresparser_response.status_code,
            'pyresparser_response': pyresparser_response.text,
        })
    except Exception as e:
        return JsonResponse({'error': str(e)})

@login_required
def notification_list(request):
    # Get system notifications (where project is None)
    system_unread_notifications = Notification.objects.filter(
        user=request.user,
        is_read=False,
        project=None
    ).order_by('-created_at')

    system_read_notifications = Notification.objects.filter(
        user=request.user,
        is_read=True,
        project=None
    ).order_by('-created_at')

    # Get project invite notifications (where project is not None)
    project_unread_notifications = Notification.objects.filter(
        user=request.user,
        is_read=False,
        project__isnull=False
    ).order_by('-created_at')

    project_read_notifications = Notification.objects.filter(
        user=request.user,
        is_read=True,
        project__isnull=False
    ).order_by('-created_at')

    context = {
        'system_unread_notifications': system_unread_notifications,
        'system_read_notifications': system_read_notifications,
        'project_unread_notifications': project_unread_notifications,
        'project_read_notifications': project_read_notifications,
    }
    return render(request, 'accounts/notification_list.html', context)

@login_required
def mark_notification_read(request, notification_id):
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.is_read = True
    notification.save()
    return JsonResponse({'status': 'success'})

@login_required
def unread_notification_count(request):
    count = Notification.objects.filter(user=request.user, is_read=False).count()
    return JsonResponse({'count': count})

@login_required
def mark_all_notifications_read(request):
    notification_type = request.GET.get('type')

    if notification_type == 'system':
        Notification.objects.filter(user=request.user, is_read=False, project=None).update(is_read=True)
    elif notification_type == 'project':
        Notification.objects.filter(user=request.user, is_read=False, project__isnull=False).update(is_read=True)
    else:
        # If no type specified, mark all as read (maintain backward compatibility)
        Notification.objects.filter(user=request.user, is_read=False).update(is_read=True)

    return JsonResponse({'status': 'success'})

@login_required
def respond_invite(request, invite_id):
    """
    Respond to a project invitation notification by accepting or rejecting it
    """
    # Get the notification for the current user with the given id
    notification = get_object_or_404(Notification, id=invite_id, user=request.user)

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'accept':
            notification.is_accepted = True
            notification.is_read = True
            notification.save()

            # If there's a project associated with this notification, add the user as a member
            if notification.project:
                # Check if the user is already a member of this project
                membership_exists = ProjectMembership.objects.filter(
                    user=request.user,
                    project=notification.project
                ).exists()

                # If not already a member, create a new membership
                if not membership_exists:
                    ProjectMembership.objects.create(
                        user=request.user,
                        project=notification.project,
                        role='member'
                    )

                messages.success(request, f'You have joined the project: {notification.project.title}')

        elif action == 'reject':
            notification.is_accepted = False
            notification.is_read = True
            notification.save()
            messages.info(request, 'You have declined the invitation')

    # Redirect to the notifications list page
    return redirect('accounts:notification_list')


def contact_view(request):
    """Contact us page view"""
    if request.method == 'POST':
        # Handle contact form submission
        name = request.POST.get('name')
        email = request.POST.get('email')
        phone = request.POST.get('phone')
        subject = request.POST.get('subject')
        message = request.POST.get('message')

        try:
            # Create email subject
            email_subject = f"Contact Form: {subject}"

            # Create email message
            email_message = f"""
New contact form submission from ForgeX website:

Name: {name}
Email: {email}
Phone: {phone or 'Not provided'}
Subject: {subject}

Message:
{message}

---
This message was sent from the ForgeX contact form.
            """.strip()

            # Send <NAME_EMAIL> from user's email
            send_mail(
                subject=email_subject,
                message=email_message,
                from_email=email,  # Use user's email as sender
                recipient_list=['<EMAIL>'],
                fail_silently=False
            )

            messages.success(request, f'Thank you {name}! Your message has been sent successfully. We will get back to you soon.')

        except Exception as e:
            # Log the error and show user-friendly message
            messages.error(request, 'Sorry, there was an error sending your message. Please try again later or contact us <NAME_EMAIL>')

        return redirect('accounts:contact')

    return render(request, 'accounts/contacts.html')


def documentation_view(request):
    """Documentation page view"""
    return render(request, 'accounts/documentation.html')


def forms_view(request):
    """Forms page view - central hub for all forms in the application"""
    return render(request, 'accounts/forms.html')


def help_center_view(request):
    """Help Center main page"""
    from .models import SupportCategory, SupportTicket
    from .forms import SupportTicketForm

    # Get active categories
    categories = SupportCategory.objects.filter(is_active=True)

    # Get user's recent tickets if authenticated
    user_tickets = []
    if request.user.is_authenticated:
        user_tickets = SupportTicket.objects.filter(user=request.user)[:5]

    # Initialize form
    form = SupportTicketForm() if request.user.is_authenticated else None

    context = {
        'categories': categories,
        'user_tickets': user_tickets,
        'form': form,
    }

    return render(request, 'accounts/help_center.html', context)


@login_required
def create_support_ticket(request):
    """Create a new support ticket"""
    from .models import SupportTicket, SupportCategory
    from .forms import SupportTicketForm

    # Check if categories exist
    if not SupportCategory.objects.filter(is_active=True).exists():
        messages.warning(request, 'No support categories are available. Please contact an administrator to set up categories first.')
        if request.user.is_staff or request.user.is_superuser:
            messages.info(request, 'As an admin, you can create default categories from the Help Center.')
        return redirect('accounts:help_center')

    if request.method == 'POST':
        form = SupportTicketForm(request.POST)
        if form.is_valid():
            ticket = form.save(commit=False)
            ticket.user = request.user
            ticket.save()

            messages.success(request, f'Support ticket #{ticket.ticket_id} has been created successfully!')
            return redirect('accounts:support_ticket_detail', ticket_id=ticket.ticket_id)
    else:
        form = SupportTicketForm()

    context = {
        'form': form,
        'title': 'Create Support Ticket'
    }

    return render(request, 'accounts/create_support_ticket.html', context)


@login_required
def support_ticket_detail(request, ticket_id):
    """View support ticket details and chat"""
    from .models import SupportTicket, SupportMessage
    from .forms import SupportMessageForm

    # Get ticket and check access
    ticket = get_object_or_404(SupportTicket, ticket_id=ticket_id)

    # Check if user has access to this ticket
    if not (ticket.user == request.user or request.user.is_staff or
            request.user.is_superuser or ticket.assigned_to == request.user):
        messages.error(request, 'You do not have permission to view this ticket.')
        return redirect('accounts:help_center')

    # Get messages
    messages_list = SupportMessage.objects.filter(ticket=ticket)
    if not (request.user.is_staff or request.user.is_superuser):
        # Regular users can't see internal messages
        messages_list = messages_list.filter(is_internal=False)

    # Handle message form
    message_form = SupportMessageForm()
    if request.method == 'POST' and 'send_message' in request.POST:
        message_form = SupportMessageForm(request.POST)
        if message_form.is_valid():
            message = message_form.save(commit=False)
            message.ticket = ticket
            message.sender = request.user
            message.is_internal = request.POST.get('is_internal') == 'on' and (request.user.is_staff or request.user.is_superuser)
            message.save()

            # Update ticket status if needed
            if ticket.status == 'waiting_user' and ticket.user == request.user:
                ticket.status = 'in_progress'
                ticket.save()

            messages.success(request, 'Message sent successfully!')
            return redirect('accounts:support_ticket_detail', ticket_id=ticket.ticket_id)

    context = {
        'ticket': ticket,
        'messages': messages_list,
        'message_form': message_form,
        'can_see_internal': request.user.is_staff or request.user.is_superuser,
        'is_staff': request.user.is_staff or request.user.is_superuser,
    }

    return render(request, 'accounts/support_ticket_detail.html', context)


@login_required
def my_support_tickets(request):
    """View user's support tickets"""
    from .models import SupportTicket

    tickets = SupportTicket.objects.filter(user=request.user).order_by('-created_at')

    context = {
        'tickets': tickets,
        'title': 'My Support Tickets'
    }

    return render(request, 'accounts/my_support_tickets.html', context)


@login_required
def support_admin_dashboard(request):
    """Admin dashboard for managing support tickets"""
    if not (request.user.is_staff or request.user.is_superuser):
        messages.error(request, 'You do not have permission to access the support admin dashboard.')
        return redirect('accounts:help_center')

    from .models import SupportTicket, SupportCategory
    from django.db.models import Count, Q
    from django.utils import timezone
    from datetime import timedelta

    # Get ticket statistics
    total_tickets = SupportTicket.objects.count()
    open_tickets = SupportTicket.objects.filter(status='open').count()
    in_progress_tickets = SupportTicket.objects.filter(status='in_progress').count()
    resolved_tickets = SupportTicket.objects.filter(status='resolved').count()

    # Get recent tickets
    recent_tickets = SupportTicket.objects.select_related('user', 'category', 'assigned_to').order_by('-created_at')[:10]

    # Get unassigned tickets
    unassigned_tickets = SupportTicket.objects.filter(
        assigned_to__isnull=True,
        status__in=['open', 'in_progress']
    ).select_related('user', 'category')[:5]

    # Get tickets assigned to current user
    my_tickets = SupportTicket.objects.filter(
        assigned_to=request.user,
        status__in=['open', 'in_progress', 'waiting_user']
    ).select_related('user', 'category')[:5]

    # Get priority distribution
    priority_stats = SupportTicket.objects.values('priority').annotate(count=Count('id'))

    context = {
        'total_tickets': total_tickets,
        'open_tickets': open_tickets,
        'in_progress_tickets': in_progress_tickets,
        'resolved_tickets': resolved_tickets,
        'recent_tickets': recent_tickets,
        'unassigned_tickets': unassigned_tickets,
        'my_tickets': my_tickets,
        'priority_stats': priority_stats,
    }

    return render(request, 'accounts/support_admin_dashboard.html', context)


@login_required
def support_ticket_update(request, ticket_id):
    """Update support ticket (admin only)"""
    if not (request.user.is_staff or request.user.is_superuser):
        messages.error(request, 'You do not have permission to update tickets.')
        return redirect('accounts:help_center')

    from .models import SupportTicket
    from .forms import SupportTicketUpdateForm

    ticket = get_object_or_404(SupportTicket, ticket_id=ticket_id)

    if request.method == 'POST':
        form = SupportTicketUpdateForm(request.POST, instance=ticket)
        if form.is_valid():
            updated_ticket = form.save()

            # Update resolved_at if status changed to resolved
            if updated_ticket.status == 'resolved' and not updated_ticket.resolved_at:
                from django.utils import timezone
                updated_ticket.resolved_at = timezone.now()
                updated_ticket.save()

            messages.success(request, f'Ticket #{ticket.ticket_id} has been updated successfully!')
            return redirect('accounts:support_ticket_detail', ticket_id=ticket.ticket_id)
    else:
        form = SupportTicketUpdateForm(instance=ticket)

    context = {
        'form': form,
        'ticket': ticket,
        'title': f'Update Ticket #{ticket.ticket_id}'
    }

    return render(request, 'accounts/support_ticket_update.html', context)


@login_required
def support_categories_manage(request):
    """Manage support categories (admin only)"""
    if not (request.user.is_staff or request.user.is_superuser):
        messages.error(request, 'You do not have permission to manage categories.')
        return redirect('accounts:help_center')

    from .models import SupportCategory
    from .forms import SupportCategoryForm

    categories = SupportCategory.objects.all().order_by('name')

    if request.method == 'POST':
        form = SupportCategoryForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Category created successfully!')
            return redirect('accounts:support_categories_manage')
    else:
        form = SupportCategoryForm()

    context = {
        'categories': categories,
        'form': form,
        'title': 'Manage Support Categories'
    }

    return render(request, 'accounts/support_categories_manage.html', context)


def create_default_categories(request):
    """Create default support categories (admin only)"""
    if not (request.user.is_staff or request.user.is_superuser):
        messages.error(request, 'You do not have permission to create categories.')
        return redirect('accounts:help_center')

    from .models import SupportCategory

    categories = [
        {
            'name': 'Technical Issues',
            'description': 'Bug reports, performance issues, and technical problems'
        },
        {
            'name': 'Account & Profile',
            'description': 'Account settings, profile management, and login issues'
        },
        {
            'name': 'Collaboration',
            'description': 'Project creation, team management, and collaboration features'
        },
        {
            'name': 'Mentorship',
            'description': 'Mentor booking, session issues, and mentorship platform support'
        },
        {
            'name': 'Learning',
            'description': 'Course access, learning materials, and educational content'
        },
        {
            'name': 'Billing & Payments',
            'description': 'Payment issues, subscription problems, and billing inquiries'
        },
        {
            'name': 'Feature Requests',
            'description': 'Suggestions for new features and platform improvements'
        },
        {
            'name': 'General Support',
            'description': 'General questions and support requests'
        }
    ]

    created_count = 0
    for category_data in categories:
        category, created = SupportCategory.objects.get_or_create(
            name=category_data['name'],
            defaults={'description': category_data['description']}
        )
        if created:
            created_count += 1

    if created_count > 0:
        messages.success(request, f'Successfully created {created_count} support categories!')
    else:
        messages.info(request, 'All default categories already exist.')

    return redirect('accounts:help_center')


def status_view(request):
    """System status page view"""
    # In a real implementation, you would fetch actual system metrics here
    # For now, we'll use the template with simulated data
    try:
        return render(request, 'accounts/status.html')
    except Exception as e:
        # Fallback to a simple response if template fails
        from django.http import HttpResponse
        return HttpResponse(f"Status page template error: {str(e)}")


def events_view(request):
    """Events page view - shows all active events"""
    from django.utils import timezone

    # Get all active events, separated by upcoming and past
    now = timezone.now()
    upcoming_events = Event.objects.filter(
        is_active=True,
        event_date__gte=now
    ).order_by('event_date')

    past_events = Event.objects.filter(
        is_active=True,
        event_date__lt=now
    ).order_by('-event_date')[:10]  # Show only last 10 past events

    context = {
        'upcoming_events': upcoming_events,
        'past_events': past_events,
    }
    return render(request, 'accounts/events.html', context)


@login_required
def create_event_view(request):
    """Create event view - only accessible by superusers"""
    if not request.user.is_superuser:
        messages.error(request, 'You need superuser privileges to create events.')
        return redirect('accounts:events')

    if request.method == 'POST':
        form = EventForm(request.POST)
        if form.is_valid():
            event = form.save(commit=False)
            event.created_by = request.user
            event.save()

            # Send system notification to all users about the new event
            from collaborate.notification_utils import send_system_notification
            send_system_notification(
                title=f"New Event: {event.title}",
                message=f"A new event '{event.title}' has been scheduled for {event.event_date.strftime('%B %d, %Y at %I:%M %p')}. Check the events page for more details!",
                notification_type="system"
            )

            messages.success(request, f'Event "{event.title}" has been created successfully and notifications sent to all users!')
            return redirect('accounts:events')
    else:
        form = EventForm()

    return render(request, 'accounts/create_event.html', {'form': form})


@login_required
def edit_event_view(request, event_id):
    """Edit event view - only accessible by superusers"""
    if not request.user.is_superuser:
        messages.error(request, 'You need superuser privileges to edit events.')
        return redirect('accounts:events')

    event = get_object_or_404(Event, id=event_id)

    if request.method == 'POST':
        form = EventForm(request.POST, instance=event)
        if form.is_valid():
            form.save()
            messages.success(request, f'Event "{event.title}" has been updated successfully!')
            return redirect('accounts:events')
    else:
        form = EventForm(instance=event)

    return render(request, 'accounts/edit_event.html', {'form': form, 'event': event})


@login_required
def delete_event_view(request, event_id):
    """Delete event view - only accessible by superusers"""
    if not request.user.is_superuser:
        messages.error(request, 'You need superuser privileges to delete events.')
        return redirect('accounts:events')

    event = get_object_or_404(Event, id=event_id)

    if request.method == 'POST':
        event_title = event.title
        event.delete()
        messages.success(request, f'Event "{event_title}" has been deleted successfully!')
        return redirect('accounts:events')

    return render(request, 'accounts/delete_event.html', {'event': event})


@login_required
def social_accounts_management(request):
    """
    Professional social account management view
    """
    from allauth.socialaccount.models import SocialAccount

    # Get user's connected social accounts
    social_accounts = SocialAccount.objects.filter(user=request.user)

    # Get available social providers
    available_providers = []
    for provider_id, provider_config in app_settings.PROVIDERS.items():
        provider_name = provider_config.get('name', provider_id.title())
        is_connected = social_accounts.filter(provider=provider_id).exists()

        available_providers.append({
            'id': provider_id,
            'name': provider_name,
            'is_connected': is_connected,
            'connect_url': f'/accounts/{provider_id}/login/',
        })

    context = {
        'social_accounts': social_accounts,
        'available_providers': available_providers,
    }

    return render(request, 'accounts/social_accounts.html', context)


@login_required
@require_POST
def disconnect_social_account(request, account_id):
    """
    Disconnect a social account with proper validation
    """
    from allauth.socialaccount.models import SocialAccount

    try:
        social_account = SocialAccount.objects.get(id=account_id, user=request.user)

        # Check if user has other login methods before disconnecting
        has_password = request.user.has_usable_password()
        other_social_accounts = SocialAccount.objects.filter(user=request.user).exclude(id=account_id).exists()

        if not has_password and not other_social_accounts:
            messages.error(
                request,
                "Cannot disconnect this account as it's your only login method. "
                "Please set a password or connect another social account first."
            )
            return redirect('accounts:social_accounts_management')

        provider_name = social_account.provider.title()
        social_account.delete()

        # Log the disconnection
        logger.info(f"User {request.user.username} disconnected {provider_name} account")

        messages.success(request, f"Successfully disconnected your {provider_name} account.")

    except SocialAccount.DoesNotExist:
        messages.error(request, "Social account not found or you don't have permission to disconnect it.")
    except Exception as e:
        logger.error(f"Error disconnecting social account: {str(e)}")
        messages.error(request, "An error occurred while disconnecting the account. Please try again.")

    return redirect('accounts:social_accounts_management')


@login_required
def account_security(request):
    """
    Account security overview with professional features
    """
    from allauth.socialaccount.models import SocialAccount
    from django.contrib.auth.models import User
    from django.utils import timezone
    from datetime import timedelta

    # Get security information
    has_password = request.user.has_usable_password()
    social_accounts = SocialAccount.objects.filter(user=request.user)

    # Get recent login activity (this would require custom logging)
    # For now, we'll show basic account info
    account_age = timezone.now() - request.user.date_joined

    # Security recommendations
    recommendations = []

    if not has_password:
        recommendations.append({
            'type': 'warning',
            'title': 'Set a Password',
            'description': 'Add a password to your account for additional security.',
            'action_url': '/accounts/password/set/',
            'action_text': 'Set Password'
        })

    if not social_accounts.exists():
        recommendations.append({
            'type': 'info',
            'title': 'Connect Social Accounts',
            'description': 'Link your Google account for easier sign-in.',
            'action_url': reverse('accounts:social_accounts_management'),
            'action_text': 'Manage Social Accounts'
        })

    if not request.user.email:
        recommendations.append({
            'type': 'danger',
            'title': 'Add Email Address',
            'description': 'An email address is required for account recovery.',
            'action_url': reverse('accounts:profile'),
            'action_text': 'Update Profile'
        })

    context = {
        'has_password': has_password,
        'social_accounts': social_accounts,
        'account_age': account_age,
        'recommendations': recommendations,
        'last_login': request.user.last_login,
    }

    return render(request, 'accounts/account_security.html', context)


def password_reset_request(request):
    """Handle password reset requests with email verification"""
    if request.user.is_authenticated:
        return redirect('accounts:profile')

    if request.method == 'POST':
        form = CustomPasswordResetForm(request.POST)
        if form.is_valid():
            email = form.cleaned_data['email']
            try:
                user = User.objects.get(email=email, is_active=True)

                # Generate password reset token
                token = default_token_generator.make_token(user)
                uid = urlsafe_base64_encode(force_bytes(user.pk))
                current_site = get_current_site(request)
                reset_link = request.build_absolute_uri(
                    reverse('accounts:password_reset_confirm', kwargs={'uidb64': uid, 'token': token})
                )

                # Send password reset email
                subject = 'Reset your ForgeX password'
                html_message = render_to_string('accounts/password_reset_email.html', {
                    'user': user,
                    'reset_link': reset_link,
                    'domain': current_site.domain,
                })

                # Create plain text version for fallback
                plain_message = f"""
Hi {user.first_name or user.username},

You requested a password reset for your ForgeX account. Click the link below to reset your password:

{reset_link}

This link will expire in 24 hours for security reasons.

If you did not request this password reset, please ignore this email.

Best regards,
The ForgeX Team
                """.strip()

                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_message
                )

                messages.success(request, 'Password reset email has been sent to your email address.')
                return redirect('accounts:password_reset_done')

            except User.DoesNotExist:
                # Don't reveal that the email doesn't exist for security
                messages.success(request, 'If an account with this email exists, a password reset link has been sent.')
                return redirect('accounts:password_reset_done')
    else:
        form = CustomPasswordResetForm()

    return render(request, 'accounts/password_reset_form.html', {'form': form})


def password_reset_done(request):
    """Show confirmation that password reset email was sent"""
    return render(request, 'accounts/password_reset_done.html')


def password_reset_confirm(request, uidb64, token):
    """Handle password reset confirmation with new password setting"""
    try:
        uid = force_str(urlsafe_base64_decode(uidb64))
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, OverflowError, User.DoesNotExist):
        user = None

    if user is not None and default_token_generator.check_token(user, token):
        if request.method == 'POST':
            form = CustomSetPasswordForm(user, request.POST)
            if form.is_valid():
                form.save()
                messages.success(request, 'Your password has been reset successfully. You can now log in with your new password.')
                return redirect('accounts:login')
        else:
            form = CustomSetPasswordForm(user)

        return render(request, 'accounts/password_reset_confirm.html', {
            'form': form,
            'validlink': True,
            'user': user
        })
    else:
        messages.error(request, 'The password reset link is invalid or has expired.')
        return render(request, 'accounts/password_reset_confirm.html', {
            'validlink': False
        })


@login_required
def session_debug(request):
    """Debug view to show session information (for development/debugging)"""
    session_info = get_session_info(request)
    user_sessions = get_user_sessions(request.user)

    context = {
        'session_info': session_info,
        'user_sessions': user_sessions,
        'session_data': dict(request.session.items()),
    }

    return render(request, 'accounts/session_debug.html', context)

@login_required
def security_demo(request):
    """Security system demonstration page"""
    context = {
        'user': request.user,
        'is_admin': request.user.is_superuser,
        'is_staff': request.user.is_staff,
    }

    # Add security profile information if available
    if hasattr(request.user, 'security_profile'):
        context['security_profile'] = request.user.security_profile

    return render(request, 'accounts/security_demo.html', context)
