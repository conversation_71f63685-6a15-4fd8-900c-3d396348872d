from collaborate.models import Notification

def notification_processor(request):
    """
    Context processor to add notification counts to all templates
    """
    context = {
        'unread_notification_count': 0
    }
    
    if request.user.is_authenticated:
        context['unread_notification_count'] = Notification.objects.filter(
            user=request.user, 
            is_read=False
        ).count()
        
    return context