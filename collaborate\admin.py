from django.contrib import admin
from .models import (
    Project, ProjectMembership, ProjectApplication, ProjectAsset,
    Skill, UserSkill, ProjectRequiredSkill, UserPairing,
    ProjectCommit, ProjectFileVersion, ProjectBranch
)

@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'created_at')
    list_filter = ('category',)
    search_fields = ('name', 'description')

@admin.register(UserSkill)
class UserSkillAdmin(admin.ModelAdmin):
    list_display = ('user', 'skill', 'proficiency', 'years_experience')
    list_filter = ('proficiency',)
    search_fields = ('user__username', 'skill__name')
    raw_id_fields = ('user', 'skill')

@admin.register(ProjectRequiredSkill)
class ProjectRequiredSkillAdmin(admin.ModelAdmin):
    list_display = ('project', 'skill', 'importance')
    list_filter = ('importance',)
    search_fields = ('project__title', 'skill__name')
    raw_id_fields = ('project', 'skill')

@admin.register(UserPairing)
class UserPairingAdmin(admin.ModelAdmin):
    list_display = ('project', 'user1', 'user2', 'status', 'successful', 'created_at')
    list_filter = ('status', 'successful')
    search_fields = ('project__title', 'user1__username', 'user2__username')
    raw_id_fields = ('project', 'user1', 'user2')
    fieldsets = (
        (None, {
            'fields': ('project', 'user1', 'user2', 'status')
        }),
        ('ML Training Data', {
            'fields': ('successful', 'feedback'),
            'description': 'Data used for AI model training'
        })
    )
    
    def get_readonly_fields(self, request, obj=None):
        # Once created, don't allow changing the users or project
        if obj:
            return ('user1', 'user2', 'project')
        return super().get_readonly_fields(request, obj)

@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ('title', 'owner', 'type', 'created_at')  # Removed 'status'
    list_filter = ('type',)  # Removed 'matching_approach'
    search_fields = ('title', 'description', 'owner__username')
    filter_horizontal = ('required_skills',)
    fieldsets = (
        (None, {
            'fields': ('title', 'description', 'owner', 'type')  # Removed 'status'
        }),
        ('Team Requirements', {
            'fields': ('required_skills', 'required_languages', 'team_size')
        })
    )

@admin.register(ProjectMembership)
class ProjectMembershipAdmin(admin.ModelAdmin):
    list_display = ('user', 'project', 'role', 'joined_at')
    list_filter = ('role',)
    search_fields = ('user__username', 'project__title')

@admin.register(ProjectApplication)
class ProjectApplicationAdmin(admin.ModelAdmin):
    list_display = ('user', 'project', 'status', 'created_at')
    list_filter = ('status',)
    search_fields = ('user__username', 'project__title')

@admin.register(ProjectAsset)
class ProjectAssetAdmin(admin.ModelAdmin):
    list_display = ('name', 'project', 'path', 'is_folder')
    list_filter = ('is_folder',)
    search_fields = ('name', 'path', 'project__title')

@admin.register(ProjectCommit)
class ProjectCommitAdmin(admin.ModelAdmin):
    list_display = ('commit_hash_short', 'project', 'author', 'message_short', 'branch_name', 'created_at', 'files_changed')
    list_filter = ('branch_name', 'created_at', 'author')
    search_fields = ('commit_hash', 'message', 'project__title', 'author__username')
    readonly_fields = ('commit_hash', 'created_at')

    def commit_hash_short(self, obj):
        return obj.commit_hash[:8]
    commit_hash_short.short_description = 'Hash'

    def message_short(self, obj):
        return obj.message[:50] + '...' if len(obj.message) > 50 else obj.message
    message_short.short_description = 'Message'

@admin.register(ProjectFileVersion)
class ProjectFileVersionAdmin(admin.ModelAdmin):
    list_display = ('file_path', 'commit_hash_short', 'file_size', 'is_deleted', 'created_at')
    list_filter = ('is_deleted', 'created_at')
    search_fields = ('file_path', 'commit__commit_hash', 'commit__project__title')
    readonly_fields = ('content_hash', 'file_size', 'created_at')

    def commit_hash_short(self, obj):
        return obj.commit.commit_hash[:8]
    commit_hash_short.short_description = 'Commit'

@admin.register(ProjectBranch)
class ProjectBranchAdmin(admin.ModelAdmin):
    list_display = ('name', 'project', 'head_commit_short', 'created_by', 'is_default', 'created_at')
    list_filter = ('is_default', 'created_at')
    search_fields = ('name', 'project__title', 'created_by__username')
    readonly_fields = ('created_at',)

    def head_commit_short(self, obj):
        return obj.head_commit.commit_hash[:8] if obj.head_commit else 'None'
    head_commit_short.short_description = 'Head Commit'