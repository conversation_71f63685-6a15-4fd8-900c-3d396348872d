{% extends 'base.html' %}

{% block content %}
<div class="debug">
  <div class="tile-wrap">
    <div class="container mt-5">
  <h2>🧠 AI Team Pairing Debug Info</h2>
  <p class="lead mb-4">Project: {{ project.title }}</p>

  <!-- Team Analysis Logs -->
  <div class="card mb-4">
    <div class="card-header bg-primary text-white">
      <h5 class="mb-0">Team Analysis Logs</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>Algorithm</th>
              <th>Skill Coverage</th>
              <th>Team Size</th>
              <th>Team Members</th>
            </tr>
          </thead>
          <tbody>
            {% for log in analysis_logs %}
              <tr>
                <td>{{ log.timestamp|date:"M d, Y H:i" }}</td>
                <td>{{ log.algorithm_used }}</td>
                <td>
                  <div class="progress" style="height: 20px;">
                    <div class="progress-bar {% if log.match_data.is_optimal_match %}bg-success{% else %}bg-warning{% endif %}"
                         role="progressbar"
                         style="width: {{ log.match_data.skill_coverage_percent }}%;"
                         aria-valuenow="{{ log.match_data.skill_coverage_percent }}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                      {{ log.match_data.skill_coverage_percent|floatformat:1 }}%
                    </div>
                  </div>
                </td>
                <td>{{ log.match_data.team_size }}</td>
                <td>
                  {% for member in log.team_members.all %}
                    <span class="badge bg-info me-1">{{ member.username }}</span>
                  {% empty %}
                    <span class="text-muted">None</span>
                  {% endfor %}
                </td>
              </tr>
            {% empty %}
              <tr>
                <td colspan="5" class="text-center">No team analysis logs found. Run an AI pairing to generate logs.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Individual Match Logs -->
  <div class="card mb-4">
    <div class="card-header bg-info text-white">
      <h5 class="mb-0">Individual Match Logs</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>User</th>
              <th>Match Score</th>
              <th>Overlap Skills</th>
              <th>Missing Skills</th>
              <th>Is Backup</th>
              <th>Timestamp</th>
            </tr>
          </thead>
          <tbody>
            {% for log in logs %}
              <tr {% if log.is_backup %}class="table-secondary"{% endif %}>
                <td>{{ log.user.username }}</td>
                <td>
                  <span class="badge {% if log.match_score > 60 %}bg-success{% elif log.match_score > 30 %}bg-warning{% else %}bg-danger{% endif %}">
                    {{ log.match_score|floatformat:2 }}
                  </span>
                </td>
                <td>
                  {% for skill in log.overlap_skills %}
                    <span class="badge bg-primary me-1">{{ skill }}</span>
                  {% empty %}
                    <span class="text-muted">None</span>
                  {% endfor %}
                </td>
                <td>
                  {% for skill in log.missing_skills %}
                    <span class="badge bg-secondary me-1">{{ skill }}</span>
                  {% empty %}
                    <span class="text-success">None missing!</span>
                  {% endfor %}
                </td>
                <td>
                  {% if log.is_backup %}
                    <span class="badge bg-warning">Backup</span>
                  {% else %}
                    <span class="badge bg-success">Primary</span>
                  {% endif %}
                </td>
                <td>{{ log.timestamp|date:"M d, Y H:i" }}</td>
              </tr>
            {% empty %}
              <tr>
                <td colspan="6" class="text-center">No individual match logs found. Run an AI pairing to generate logs.</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between mt-4 mb-5">
    <a href="{% url 'collaborate:project_detail' project.id %}" class="btn btn-secondary">
      <i class="fas fa-arrow-left"></i> Back to Project
    </a>
    <a href="{% url 'collaborate:match_users' project.id %}" class="btn btn-primary">
      <i class="fas fa-users"></i> View Matched Users
    </a>
  </div>
</div>
  </div>
</div>
{% endblock %}