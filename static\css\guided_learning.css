/* Guided Learning Section Styles */
.guided-learning {
  padding: 100px 100px;
  background: linear-gradient(to bottom, #2d2d2d, #1c1c1c);
  position: relative;
  overflow: visible; /* Changed from hidden to visible to prevent content clipping */
  padding-bottom: 150px; /* Add extra padding to ensure content has room */
}

.guided-learning::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
  animation: pulse 8s infinite;
}

.guided-learning-container {
  display: flex;
  gap: 60px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.guided-learning-content {
  flex: 1;
  padding: 30px;
  background: rgba(40, 40, 40, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.guided-learning-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.guided-learning-content p {
  color: #ffffff;
  line-height: 1.6;
  font-size: 16px;
  margin-bottom: 25px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.guided-learning-image {
  flex: 1;
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.guided-learning-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(192, 255, 107, 0.3);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  position: relative;
  z-index: 2;
}

.guided-learning-image img:hover {
  transform: scale(1.05);
  border-color: rgba(192, 255, 107, 0.7);
  box-shadow: 0 0 20px rgba(192, 255, 107, 0.3);
}

.learning-path-list {
  margin-top: 30px;
}

.learning-path-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background: rgba(28, 28, 28, 0.7);
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.learning-path-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  background: rgba(40, 40, 40, 0.9);
}

.learning-path-icon {
  width: 40px;
  height: 40px;
  margin-right: 15px;
  background: rgba(192, 255, 107, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.learning-path-icon i {
  color: #C0ff6b;
  font-size: 20px;
}

.learning-path-text {
  flex: 1;
}

.learning-path-text h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #ffffff;
}

.learning-path-text p {
  font-size: 14px;
  margin-bottom: 0;
  color: #cccccc;
}

.explore-button {
  display: inline-block;
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #C0ff6b;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  text-decoration: none;
}

.explore-button:hover {
  background-color: #C0ff6b;
  color: #000000 !important;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .guided-learning {
    padding: 80px 50px;
  }

  .guided-learning-container {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .guided-learning {
    padding: 60px 30px;
  }
}

/* Mentorship Section Styles */
.mentorship-section {
  padding: 100px 100px;
  background: linear-gradient(to bottom, #1c1c1c, #2d2d2d);
  position: relative;
  overflow: visible;
  padding-bottom: 150px;
}

.mentorship-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.08) 0%, transparent 70%);
  z-index: 0;
  animation: pulse 10s infinite;
}

.mentorship-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.mentorship-content {
  padding: 40px;
  background: rgba(40, 40, 40, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(192, 255, 107, 0.2);
  text-align: center;
}

.mentorship-content h2 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.mentorship-content p {
  color: #ffffff;
  line-height: 1.7;
  font-size: 18px;
  margin-bottom: 40px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.mentorship-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin: 40px 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  padding: 25px;
  background: rgba(28, 28, 28, 0.8);
  border-radius: 15px;
  transition: all 0.4s ease;
  border: 1px solid rgba(192, 255, 107, 0.1);
  text-align: left;
}

.feature-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  background: rgba(40, 40, 40, 0.95);
  border-color: rgba(192, 255, 107, 0.3);
}

.feature-icon {
  width: 50px;
  height: 50px;
  margin-right: 20px;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.3), rgba(192, 255, 107, 0.1));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.4);
  flex-shrink: 0;
}

.feature-icon i {
  color: #C0ff6b;
  font-size: 24px;
}

.feature-text {
  flex: 1;
}

.feature-text h4 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #ffffff;
}

.feature-text p {
  font-size: 15px;
  margin-bottom: 0;
  color: #cccccc;
  line-height: 1.5;
  text-shadow: none;
}

.mentorship-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
  flex-wrap: wrap;
}

.explore-button.primary {
  background-color: #C0ff6b;
  color: #000000;
  border: 2px solid #C0ff6b;
  font-weight: 700;
}

.explore-button.primary:hover {
  background-color: transparent;
  color: #C0ff6b !important;
  box-shadow: 0 0 20px rgba(192, 255, 107, 0.6);
}

.explore-button.secondary {
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #ffffff;
}

.explore-button.secondary:hover {
  background-color: #ffffff;
  color: #000000 !important;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* Animated bar for mentorship section */
.mentorship-content .animated-bar {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #C0ff6b, #a0e066);
  margin: 0 auto 30px auto;
  border-radius: 2px;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(192, 255, 107, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(192, 255, 107, 0.8);
  }
}

/* Responsive styles for mentorship section */
@media (max-width: 1024px) {
  .mentorship-section {
    padding: 80px 50px;
  }

  .mentorship-features {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .mentorship-section {
    padding: 60px 30px;
  }

  .mentorship-content {
    padding: 30px 20px;
  }

  .mentorship-content h2 {
    font-size: 32px;
  }

  .mentorship-content p {
    font-size: 16px;
  }

  .mentorship-features {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .mentorship-actions {
    flex-direction: column;
    align-items: center;
  }

  .explore-button {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }
}
