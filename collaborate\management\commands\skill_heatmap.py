from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from collaborate.models import Project, Skill
from collections import defaultdict

class Command(BaseCommand):
    help = 'Generate skill heatmap data'

    def handle(self, *args, **kwargs):
        skill_demand = defaultdict(int)
        skill_supply = defaultdict(int)

        # Demand: count required skills in all projects
        for project in Project.objects.all():
            for skill in project.required_skills.all():
                skill_demand[skill.name.lower()] += 1

        # Supply: count user skills
        User = get_user_model()
        for user in User.objects.all():
            # If using UserProfile or user.skills as ManyToMany
            if hasattr(user, 'skills'):
                for skill in user.skills.all():
                    skill_supply[skill.name.lower()] += 1
            # If using UserSkill model, you could also aggregate from there

        all_skills = set(skill_demand) | set(skill_supply)

        self.stdout.write("Skill Heatmap:\n")
        for skill in sorted(all_skills):
            demand = skill_demand.get(skill, 0)
            supply = skill_supply.get(skill, 0)
            self.stdout.write(f"{skill}: Demand = {demand}, Supply = {supply}")

def get_skill_heatmap():
    from django.contrib.auth import get_user_model
    from collaborate.models import Project
    from collections import defaultdict

    demand = defaultdict(int)
    supply = defaultdict(int)

    for project in Project.objects.all():
        for skill in project.required_skills.all():
            demand[skill.name.lower()] += 1

    User = get_user_model()
    for user in User.objects.all():
        if hasattr(user, 'skills'):
            for skill in user.skills.all():
                supply[skill.name.lower()] += 1

    return {
        skill: {"demand": demand[skill], "supply": supply[skill]}
        for skill in set(demand) | set(supply)
    }