<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Denied - ForgeX</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .blocked-container {
            background: white;
            border-radius: 15px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            margin: 2rem;
        }
        
        .blocked-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .blocked-title {
            color: #dc3545;
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .blocked-subtitle {
            color: #6c757d;
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .info-card {
            background: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 1.5rem;
            margin: 1.5rem 0;
            text-align: left;
            border-radius: 0 8px 8px 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #495057;
            display: inline-block;
            min-width: 120px;
        }
        
        .info-value {
            color: #6c757d;
        }
        
        .appeal-section {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .appeal-title {
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .contact-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .countdown {
            font-size: 1.1rem;
            font-weight: bold;
            color: #dc3545;
            margin-top: 1rem;
        }
        
        .security-tips {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 1.5rem;
            text-align: left;
        }
        
        .tips-title {
            color: #0c5460;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .tips-list {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .tips-list li {
            margin-bottom: 0.5rem;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="blocked-container">
        <div class="blocked-icon">
            <i class="fas fa-ban"></i>
        </div>
        
        <h1 class="blocked-title">Access Denied</h1>
        <p class="blocked-subtitle">Your IP address has been blocked due to security violations</p>
        
        <div class="info-card">
            <div class="mb-2">
                <span class="info-label">IP Address:</span>
                <span class="info-value"><code>{{ ip_address }}</code></span>
            </div>
            <div class="mb-2">
                <span class="info-label">Block Type:</span>
                <span class="info-value">{{ block_type }}</span>
            </div>
            <div class="mb-2">
                <span class="info-label">Reason:</span>
                <span class="info-value">{{ block_reason }}</span>
            </div>
            {% if custom_reason %}
            <div class="mb-2">
                <span class="info-label">Details:</span>
                <span class="info-value">{{ custom_reason }}</span>
            </div>
            {% endif %}
            {% if expires_at %}
            <div class="mb-2">
                <span class="info-label">Expires:</span>
                <span class="info-value">{{ expires_at|date:"M d, Y H:i:s" }} UTC</span>
            </div>
            {% endif %}
        </div>
        
        {% if expires_at %}
        <div class="countdown" id="countdown">
            <i class="fas fa-clock"></i> Calculating time remaining...
        </div>
        {% else %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Permanent Block:</strong> This IP address has been permanently blocked.
        </div>
        {% endif %}
        
        <div class="security-tips">
            <div class="tips-title">
                <i class="fas fa-lightbulb"></i> Security Guidelines
            </div>
            <ul class="tips-list">
                <li>Do not attempt to access browser developer tools (F12, Ctrl+Shift+I)</li>
                <li>Avoid using browser console or attempting to modify page content</li>
                <li>Do not try to bypass security measures or access restricted areas</li>
                <li>Follow all platform terms of service and security policies</li>
                <li>Report any security concerns through proper channels</li>
            </ul>
        </div>
        
        <div class="appeal-section">
            <div class="appeal-title">
                <i class="fas fa-gavel"></i> Appeal Process
            </div>
            <p>If you believe this block was issued in error, you can appeal by contacting our security team:</p>
            
            <div class="contact-info">
                <div><strong>Email:</strong> {{ admin_contact|default:"<EMAIL>" }}</div>
                <div><strong>Subject:</strong> IP Block Appeal - {{ ip_address }}</div>
                <div><strong>Include:</strong> Your username, timestamp of block, and explanation</div>
            </div>
            
            <p class="mt-3 mb-0">
                <small class="text-muted">
                    Appeals are typically reviewed within 24-48 hours. Repeated violations may result in permanent restrictions.
                </small>
            </p>
        </div>
        
        <div class="mt-4">
            <a href="/" class="btn btn-outline-primary">
                <i class="fas fa-home"></i> Return to Homepage
            </a>
        </div>
        
        <div class="mt-3">
            <small class="text-muted">
                Block ID: {{ ip_address|slugify }}-{{ expires_at|date:"YmdHis"|default:"permanent" }}
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    {% if expires_at %}
    <script>
        // Countdown timer for temporary blocks
        function updateCountdown() {
            const expiresAt = new Date('{{ expires_at|date:"c" }}');
            const now = new Date();
            const timeLeft = expiresAt - now;
            
            if (timeLeft <= 0) {
                document.getElementById('countdown').innerHTML = 
                    '<i class="fas fa-check-circle text-success"></i> Block has expired. You may try accessing the site again.';
                
                // Show refresh button
                const refreshBtn = document.createElement('button');
                refreshBtn.className = 'btn btn-success mt-2';
                refreshBtn.innerHTML = '<i class="fas fa-refresh"></i> Refresh Page';
                refreshBtn.onclick = () => location.reload();
                document.getElementById('countdown').appendChild(refreshBtn);
                
                return;
            }
            
            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
            
            let countdownText = '<i class="fas fa-clock"></i> Time remaining: ';
            
            if (days > 0) {
                countdownText += `${days}d `;
            }
            if (hours > 0) {
                countdownText += `${hours}h `;
            }
            if (minutes > 0) {
                countdownText += `${minutes}m `;
            }
            countdownText += `${seconds}s`;
            
            document.getElementById('countdown').innerHTML = countdownText;
        }
        
        // Update countdown every second
        updateCountdown();
        setInterval(updateCountdown, 1000);
    </script>
    {% endif %}
</body>
</html>
