from django.contrib.auth import get_user_model
from .models import Notification, Project
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import json
import logging

logger = logging.getLogger(__name__)
User = get_user_model()
channel_layer = get_channel_layer()

def send_notification(user, title, message, project=None, require_action=False, notification_type="info"):
    """
    Send a notification to a user.

    Args:
        user: The user to send the notification to
        title: The notification title
        message: The notification message
        project: Optional Project object related to the notification
        require_action: If True, sets is_accepted to None, indicating user needs to take action
        notification_type: Type of notification (info, success, warning, error, system)

    Returns:
        Notification: The created notification object
    """
    is_accepted = None if require_action else False

    # Save to database
    notification = Notification.objects.create(
        user=user,
        title=title,  # Make sure to save the title
        message=message,
        project=project,
        is_accepted=is_accepted,
        notification_type=notification_type
    )

    # Send via WebSocket
    if channel_layer:
        group_name = f"user_{user.id}"
        try:
            # Send WebSocket notification
            async_to_sync(channel_layer.group_send)(
                group_name,
                {
                    "type": "send_notification",
                    "title": title,
                    "message": message,
                    "notification_type": notification_type,
                    "notification_id": notification.id
                }
            )
        except Exception as e:
            # If WebSocket fails, we still have the database notification
            logger.error(f"WebSocket notification failed: {e}")

    return notification

def send_project_invitation(user, project, message=None):
    """
    Send a project invitation notification that requires user acceptance.

    Args:
        user: The user to invite
        project: The project to invite them to
        message: Optional custom message

    Returns:
        Notification: The created notification object
    """
    if message is None:
        message = f"You've been invited to join project '{project.title}'"

    return send_notification(
        user=user,
        title="Project Invitation",
        message=message,
        project=project,
        require_action=True
    )

def send_bulk_notification(users, message, project=None):
    """
    Send the same notification to multiple users.

    Args:
        users: List of User objects or QuerySet
        message: The notification message
        project: Optional Project object

    Returns:
        list: List of created notification objects
    """
    notifications = []

    for user in users:
        notification = send_notification(user, "Bulk Notification", message, project)
        notifications.append(notification)

    return notifications

def mark_notifications_as_read(user):
    """
    Mark all unread notifications for a user as read.

    Args:
        user: The user whose notifications should be marked as read

    Returns:
        int: Number of notifications marked as read
    """
    return Notification.objects.filter(user=user, is_read=False).update(is_read=True)

def get_unread_count(user):
    """
    Get the count of unread notifications for a user.

    Args:
        user: The user to count notifications for

    Returns:
        int: Count of unread notifications
    """
    return Notification.objects.filter(user=user, is_read=False).count()

def send_system_notification(title, message, notification_type="system"):
    """
    Send a system notification to all users.

    Args:
        title: The notification title
        message: The notification message
        notification_type: Type of notification (default: system)

    Returns:
        list: List of created notification objects
    """
    notifications = []
    users = User.objects.filter(is_active=True)

    # Create notifications for all users
    for user in users:
        notification = send_notification(
            user=user,
            title=title,
            message=message,
            notification_type=notification_type
        )
        notifications.append(notification)

    # Also broadcast to system channel for real-time updates
    if channel_layer:
        try:
            async_to_sync(channel_layer.group_send)(
                "system_notifications",
                {
                    "type": "system_notification",
                    "title": title,
                    "message": message,
                    "notification_type": notification_type
                }
            )
        except Exception as e:
            logger.error(f"System WebSocket notification failed: {e}")

    return notifications