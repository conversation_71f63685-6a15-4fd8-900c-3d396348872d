from django import template

register = template.Library()

@register.filter
def modulo(value, arg):
    """
    Returns the remainder of value divided by arg

    Example:
        {{ value|modulo:2 }}
    """
    try:
        value = int(value)
        arg = int(arg)
        return value % arg
    except (ValueError, TypeError):
        return 0


@register.filter
def split(value, arg):
    """
    Splits a string by the given separator

    Example:
        {{ "apple,banana,cherry"|split:"," }}
    """
    if value:
        return value.split(arg)
    return []


@register.filter
def trim(value):
    """
    Removes leading and trailing whitespace

    Example:
        {{ " hello world "|trim }}
    """
    if value:
        return value.strip()
    return value


@register.filter
def filter_by_status(queryset, status):
    """
    Filters a queryset by status

    Example:
        {{ applications|filter_by_status:"pending" }}
    """
    if hasattr(queryset, 'filter'):
        return queryset.filter(status=status)
    return [item for item in queryset if getattr(item, 'status', None) == status]
