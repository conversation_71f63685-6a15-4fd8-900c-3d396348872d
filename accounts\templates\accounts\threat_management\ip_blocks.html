{% extends 'base.html' %}
{% load static %}

{% block title %}IP Blocks - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Page Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🚫 IP Blocks</h1>
      <p class="welcome-subtitle">Manage blocked IP addresses and access restrictions</p>
    </div>

    <!-- Filters -->
    <div class="dashboard-section">
      <h2>🔍 Filter IP Blocks</h2>
      <form method="get" class="filter-form">
        <div class="filter-grid">
          <div class="filter-item">
            <label for="block_type">Block Type:</label>
            <select name="block_type" id="block_type" class="form-control">
              <option value="">All Types</option>
              <option value="temporary" {% if request.GET.block_type == 'temporary' %}selected{% endif %}>Temporary</option>
              <option value="permanent" {% if request.GET.block_type == 'permanent' %}selected{% endif %}>Permanent</option>
            </select>
          </div>
          <div class="filter-item">
            <label for="is_active">Status:</label>
            <select name="is_active" id="is_active" class="form-control">
              <option value="">All Statuses</option>
              <option value="true" {% if request.GET.is_active == 'true' %}selected{% endif %}>Active</option>
              <option value="false" {% if request.GET.is_active == 'false' %}selected{% endif %}>Inactive</option>
            </select>
          </div>
          <div class="filter-item">
            <button type="submit" class="btn btn-primary">Apply Filters</button>
            <a href="{% url 'accounts:ip_blocks_list' %}" class="btn btn-secondary">Clear</a>
          </div>
        </div>
      </form>
    </div>

    <!-- IP Blocks List -->
    <div class="dashboard-section">
      <h2>📋 IP Blocks ({{ page_obj.object_list|length }} found)</h2>
      
      {% if page_obj.object_list %}
      <div class="blocks-table-container">
        <table class="blocks-table">
          <thead>
            <tr>
              <th>IP Address</th>
              <th>Type</th>
              <th>Status</th>
              <th>Reason</th>
              <th>Admin</th>
              <th>Created</th>
              <th>Expires</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for block in page_obj.object_list %}
            <tr class="block-row {% if block.is_active %}active{% else %}inactive{% endif %}">
              <td class="block-ip">
                <strong>{{ block.ip_address }}</strong>
                {% if block.user %}
                  <br><small>User: {{ block.user.username }}</small>
                {% endif %}
              </td>
              <td class="block-type">
                <span class="type-badge type-{{ block.block_type }}">{{ block.get_block_type_display }}</span>
              </td>
              <td class="block-status">
                {% if block.is_active %}
                  <span class="status-badge status-active">Active</span>
                {% else %}
                  <span class="status-badge status-inactive">Inactive</span>
                {% endif %}
              </td>
              <td class="block-reason">
                {{ block.get_reason_display }}
                {% if block.custom_reason %}
                  <br><small>{{ block.custom_reason|truncatewords:10 }}</small>
                {% endif %}
              </td>
              <td class="block-admin">{{ block.admin.username }}</td>
              <td class="block-created">{{ block.created_at|date:"M d, Y H:i" }}</td>
              <td class="block-expires">
                {% if block.expires_at %}
                  {{ block.expires_at|date:"M d, Y H:i" }}
                {% else %}
                  <em>Never</em>
                {% endif %}
              </td>
              <td class="block-actions">
                {% if block.is_active %}
                <button class="btn btn-sm btn-warning" onclick="unblockIP({{ block.id }})">
                  <i class="fas fa-unlock"></i> Unblock
                </button>
                {% endif %}
                {% if block.user %}
                <a href="{% url 'accounts:user_security_profile' block.user.id %}" class="btn btn-sm btn-info">
                  <i class="fas fa-user"></i> Profile
                </a>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {% if is_paginated %}
      <div class="pagination-container">
        <div class="pagination">
          {% if page_obj.has_previous %}
            <a href="?page=1{% if request.GET.block_type %}&block_type={{ request.GET.block_type }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" class="page-link">&laquo; First</a>
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.block_type %}&block_type={{ request.GET.block_type }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" class="page-link">Previous</a>
          {% endif %}
          
          <span class="page-info">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
          </span>
          
          {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.block_type %}&block_type={{ request.GET.block_type }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" class="page-link">Next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.block_type %}&block_type={{ request.GET.block_type }}{% endif %}{% if request.GET.is_active %}&is_active={{ request.GET.is_active }}{% endif %}" class="page-link">Last &raquo;</a>
          {% endif %}
        </div>
      </div>
      {% endif %}

      {% else %}
      <div class="no-blocks-message">
        <div class="no-blocks-icon">🛡️</div>
        <h3>No IP Blocks Found</h3>
        <p>No IP blocks match your current filters. Try adjusting your search criteria.</p>
      </div>
      {% endif %}
    </div>

    <!-- Back to Dashboard -->
    <div class="dashboard-section">
      <div class="back-to-dashboard">
        <a href="{% url 'accounts:threat_management_dashboard' %}" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Security Dashboard
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Section Styling - Match Main Dashboard */
    .dashboard-section {
      margin-bottom: 40px;
    }

    .dashboard-section h2 {
      color: #ffffff;
      margin-bottom: 30px;
      font-size: 1.8rem;
      font-weight: 600;
    }

    /* Filter Form Styling */
    .filter-form {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      margin-bottom: 30px;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      align-items: end;
    }

    .filter-item label {
      display: block;
      color: #ffffff;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-control {
      background-color: rgba(60, 60, 60, 0.8);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      padding: 10px 15px;
      color: #ffffff;
      font-size: 0.9rem;
    }

    .form-control:focus {
      border-color: var(--color-border);
      box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
      outline: none;
    }

    /* Blocks Table Styling */
    .blocks-table-container {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .blocks-table {
      width: 100%;
      border-collapse: collapse;
    }

    .blocks-table th {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      padding: 15px;
      text-align: left;
      font-weight: 600;
      border-bottom: 1px solid rgba(192, 255, 107, 0.3);
    }

    .blocks-table td {
      padding: 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    .block-row:hover {
      background-color: rgba(192, 255, 107, 0.05);
    }

    .block-row.active {
      border-left: 3px solid #dc3545;
    }

    .block-row.inactive {
      opacity: 0.6;
    }

    .type-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .type-temporary {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .type-permanent {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-active {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .status-inactive {
      background: rgba(108, 117, 125, 0.2);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .block-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 8px 16px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-block;
      border: none;
      cursor: pointer;
      font-size: 0.9rem;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.8rem;
    }

    .btn-primary {
      background-color: var(--color-border);
      color: #000;
    }

    .btn-secondary {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
    }

    .btn-warning {
      background-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .btn-info {
      background-color: rgba(23, 162, 184, 0.2);
      color: #17a2b8;
      border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* No Blocks Message */
    .no-blocks-message {
      text-align: center;
      padding: 60px 20px;
      background-color: rgba(40, 40, 40, 0.3);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .no-blocks-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    .no-blocks-message h3 {
      color: var(--color-border);
      margin-bottom: 10px;
    }

    .no-blocks-message p {
      color: #b0b0b0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .filter-grid {
        grid-template-columns: 1fr;
      }

      .blocks-table-container {
        overflow-x: auto;
      }

      .blocks-table {
        min-width: 900px;
      }

      .block-actions {
        flex-direction: column;
        gap: 5px;
      }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<script>
function unblockIP(blockId) {
    if (!confirm('Are you sure you want to unblock this IP address?')) {
        return;
    }

    fetch(`{% url 'accounts:api_unblock_ip' 0 %}`.replace('0', blockId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('IP address unblocked successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error unblocking IP: ' + error.message);
    });
}
</script>
{% endblock %}
