# Generated by Django 5.2.1 on 2025-05-21 18:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0016_alter_notification_options_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="TeamMatchAnalysisLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("match_data", models.J<PERSON>NField(default=dict)),
                (
                    "algorithm_used",
                    models.CharField(
                        default="find_best_team_with_availability_priority",
                        max_length=100,
                    ),
                ),
                (
                    "project",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="collaborate.project",
                    ),
                ),
                (
                    "team_members",
                    models.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(
                        related_name="match_analysis_logs", to=settings.AUTH_USER_MODEL
                    ),
                ),
            ],
        ),
    ]
