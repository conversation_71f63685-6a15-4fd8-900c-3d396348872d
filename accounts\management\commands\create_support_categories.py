from django.core.management.base import BaseCommand
from accounts.models import SupportCategory


class Command(BaseCommand):
    help = 'Create default support categories'

    def handle(self, *args, **options):
        categories = [
            {
                'name': 'Technical Issues',
                'description': 'Bug reports, performance issues, and technical problems'
            },
            {
                'name': 'Account & Profile',
                'description': 'Account settings, profile management, and login issues'
            },
            {
                'name': 'Collaboration',
                'description': 'Project creation, team management, and collaboration features'
            },
            {
                'name': 'Mentorship',
                'description': 'Mentor booking, session issues, and mentorship platform support'
            },
            {
                'name': 'Learning',
                'description': 'Course access, learning materials, and educational content'
            },
            {
                'name': 'Billing & Payments',
                'description': 'Payment issues, subscription problems, and billing inquiries'
            },
            {
                'name': 'Feature Requests',
                'description': 'Suggestions for new features and platform improvements'
            },
            {
                'name': 'General Support',
                'description': 'General questions and support requests'
            }
        ]

        created_count = 0
        for category_data in categories:
            category, created = SupportCategory.objects.get_or_create(
                name=category_data['name'],
                defaults={'description': category_data['description']}
            )
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'Created category: {category.name}')
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f'Category already exists: {category.name}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} new categories')
        )
