"""
Context processors for the learn app to provide global template variables
"""

def learn_permissions(request):
    """
    Add learn-related permissions to all template contexts
    """
    context = {}
    
    if request.user.is_authenticated:
        # Check if user is a verified mentor
        is_verified_mentor = False
        if hasattr(request.user, 'mentor_profile'):
            is_verified_mentor = request.user.mentor_profile.verified
        
        # Check if user can create courses
        can_create_courses = request.user.is_superuser or is_verified_mentor
        
        context.update({
            'is_verified_mentor': is_verified_mentor,
            'can_create_courses': can_create_courses,
        })
    else:
        context.update({
            'is_verified_mentor': False,
            'can_create_courses': False,
        })
    
    return context
