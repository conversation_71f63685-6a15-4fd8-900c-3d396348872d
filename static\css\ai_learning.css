/* AI Learning Enhancement Styles */

/* AI Badge */
.ai-badge {
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
  color: #000;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 10px;
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(192, 255, 107, 0.5); }
  50% { box-shadow: 0 0 15px rgba(192, 255, 107, 0.8); }
}

/* AI Learning Dashboard */
.ai-learning-dashboard {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.dashboard-header h2 {
  color: #C0ff6b;
  margin: 0;
  font-size: 24px;
}

.learning-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
  background: rgba(28, 28, 28, 0.6);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  min-width: 80px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.8;
}

/* Progress Badge */
.progress-badge {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 10px;
}

.progress-completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.progress-in_progress {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid #FFC107;
}

.progress-mastered {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
}

.progress-not_started {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid #9E9E9E;
}

/* AI Assistant Panel */
.ai-assistant-panel, .code-analysis-panel {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  margin-bottom: 30px;
  overflow: hidden;
}

.assistant-header, .analysis-header {
  background: rgba(28, 28, 28, 0.8);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
}

.assistant-header h3, .analysis-header h3 {
  color: #C0ff6b;
  margin: 0;
  font-size: 18px;
}

.toggle-assistant, .toggle-analysis {
  background: transparent;
  border: 1px solid #C0ff6b;
  color: #C0ff6b;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-assistant:hover, .toggle-analysis:hover {
  background: #C0ff6b;
  color: #000;
}

.assistant-content, .analysis-content {
  padding: 20px;
}

/* Chat Interface */
.chat-container {
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 20px;
  padding: 10px;
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
}

.message {
  margin-bottom: 15px;
  animation: fadeInUp 0.3s ease;
}

.user-message {
  text-align: right;
}

.user-message .message-content {
  background: #C0ff6b;
  color: #000;
  padding: 10px 15px;
  border-radius: 15px 15px 5px 15px;
  display: inline-block;
  max-width: 80%;
  word-wrap: break-word;
}

.ai-message .message-content {
  background: rgba(40, 40, 40, 0.8);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 15px 15px 15px 5px;
  display: inline-block;
  max-width: 80%;
  word-wrap: break-word;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.message-time {
  font-size: 11px;
  color: #999;
  margin-top: 5px;
}

.chat-input-container {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

#chat-input {
  flex: 1;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 10px;
  padding: 10px;
  color: #ffffff;
  resize: vertical;
  min-height: 60px;
}

#chat-input:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.send-btn {
  background: #C0ff6b;
  color: #000;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.send-btn:hover {
  background: #a0e066;
  transform: translateY(-2px);
}

/* Code Analysis */
.code-input-section {
  margin-bottom: 20px;
}

.input-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

#code-language, #analysis-type {
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  color: #ffffff;
  min-width: 120px;
}

.analyze-btn {
  background: #C0ff6b;
  color: #000;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.analyze-btn:hover {
  background: #a0e066;
  transform: translateY(-1px);
}

#code-input {
  width: 100%;
  background: rgba(28, 28, 28, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 10px;
  padding: 15px;
  color: #ffffff;
  font-family: 'Courier New', monospace;
  resize: vertical;
  min-height: 200px;
}

.analysis-results {
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
  padding: 20px;
  margin-top: 20px;
}

.score-display {
  text-align: center;
  margin-bottom: 20px;
}

.score-circle {
  display: inline-block;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: conic-gradient(#C0ff6b 0deg, #C0ff6b var(--score-angle, 0deg), rgba(192, 255, 107, 0.2) var(--score-angle, 0deg));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
}

.score-label {
  display: block;
  color: #ffffff;
  font-size: 14px;
}

/* Code Actions */
.code-actions {
  display: flex;
  gap: 10px;
}

.analyze-code-btn {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.analyze-code-btn:hover {
  background: #C0ff6b;
  color: #000;
}

/* AI Recommendations */
.ai-recommendations {
  background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(28, 28, 28, 0.8));
  border: 2px solid rgba(192, 255, 107, 0.4);
  border-radius: 20px;
  padding: 35px;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 0 25px rgba(192, 255, 107, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.ai-recommendations::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #C0ff6b, #a0e066, #80c055, #a0e066, #C0ff6b);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.ai-recommendations h3 {
  color: #C0ff6b;
  margin-bottom: 25px;
  font-size: 24px;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
  position: relative;
}

.ai-recommendations h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #C0ff6b, transparent);
  border-radius: 2px;
}

.recommendations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
  margin-top: 25px;
}

.recommendation-card {
  background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(28, 28, 28, 0.9));
  border: 2px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(192, 255, 107, 0.1);
}

.recommendation-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #C0ff6b, #a0e066, #80c055);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.recommendation-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 15px 40px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(192, 255, 107, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.recommendation-card:hover::before {
  opacity: 1;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 10px;
}

.rec-type {
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.2), rgba(160, 224, 102, 0.15));
  color: #C0ff6b;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  border: 1px solid rgba(192, 255, 107, 0.3);
  box-shadow: 0 2px 8px rgba(192, 255, 107, 0.2);
}

.rec-confidence {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  opacity: 0.8;
}

.recommendation-card h3 {
  color: #ffffff;
  margin: 15px 0 12px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
  transition: color 0.3s ease;
}

.recommendation-card:hover h3 {
  color: #C0ff6b;
}

.recommendation-card p {
  color: #cccccc;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 20px;
  opacity: 0.9;
}

.rec-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
}

.rec-action-btn {
  color: #000;
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
  text-decoration: none;
  font-weight: 600;
  font-size: 13px;
  padding: 10px 18px;
  border-radius: 20px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.rec-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(192, 255, 107, 0.4);
  background: linear-gradient(135deg, #a0e066, #C0ff6b);
  color: #000;
  text-decoration: none;
}

.rec-action-btn.primary {
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
}

.rec-action-btn.secondary {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
}

.rec-action-btn.secondary:hover {
  background: #C0ff6b;
  color: #000;
}

.rec-action:hover {
  text-decoration: underline;
}

/* Progress Tracking */
.progress-tracking {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 30px;
}

.progress-tracking h3 {
  color: #C0ff6b;
  margin-bottom: 15px;
}

.progress-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.time-tracker {
  background: rgba(28, 28, 28, 0.6);
  padding: 8px 15px;
  border-radius: 20px;
  color: #ffffff;
  font-size: 14px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    text-align: center;
  }
  
  .learning-stats {
    justify-content: center;
  }
  
  .input-controls {
    flex-direction: column;
  }
  
  .progress-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .recommendations-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .recommendation-card {
    padding: 20px;
  }

  .rec-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .rec-actions {
    flex-direction: column;
    gap: 8px;
  }

  .rec-action-btn {
    text-align: center;
    padding: 12px 20px;
  }
}

/* Learning Dashboard Specific Styles */
.dashboard-hero {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(160, 224, 102, 0.05));
  border-radius: 15px;
  margin-bottom: 30px;
}

.dashboard-hero h1 {
  color: #C0ff6b;
  font-size: 36px;
  margin-bottom: 15px;
}

.dashboard-hero p {
  color: #ffffff;
  font-size: 18px;
  opacity: 0.9;
}

.setup-notice {
  background: rgba(255, 193, 7, 0.2);
  border: 1px solid #FFC107;
  border-radius: 10px;
  padding: 15px;
  margin-top: 20px;
}

.setup-notice p {
  color: #FFC107;
  margin: 0;
  font-weight: 600;
}

.analytics-overview {
  margin-bottom: 40px;
}

.analytics-overview h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 24px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.analytics-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.analytics-icon {
  font-size: 32px;
  width: 50px;
  text-align: center;
}

.analytics-content {
  flex: 1;
}

.analytics-value {
  font-size: 28px;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.analytics-label {
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
}

.learning-profile-section {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
}

.learning-profile-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.profile-item {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-label {
  color: #ffffff;
  opacity: 0.8;
}

.profile-value {
  color: #C0ff6b;
  font-weight: 600;
}

.profile-goals h4 {
  color: #C0ff6b;
  margin-bottom: 10px;
}

.profile-goals p {
  color: #ffffff;
  line-height: 1.6;
}

.ai-recommendations-section {
  margin-bottom: 40px;
}

.ai-recommendations-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.recent-activity-section {
  margin-bottom: 40px;
}

.recent-activity-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.activity-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.activity-lessons, .activity-conversations {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
}

.activity-lessons h3, .activity-conversations h3 {
  color: #C0ff6b;
  margin-bottom: 15px;
  font-size: 18px;
}

.lessons-list, .conversations-list {
  max-height: 300px;
  overflow-y: auto;
}

.lesson-item, .conversation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.lesson-item:last-child, .conversation-item:last-child {
  border-bottom: none;
}

.lesson-title a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
}

.lesson-title a:hover {
  color: #C0ff6b;
}

.lesson-meta {
  color: #cccccc;
  font-size: 12px;
  margin-top: 5px;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-status {
  font-size: 18px;
}

.progress-score {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.conversation-type {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  margin-bottom: 5px;
}

.conversation-preview {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 5px;
}

.conversation-time {
  color: #cccccc;
  font-size: 11px;
}

.no-activity {
  color: #cccccc;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.quick-actions-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  text-align: center;
}

.action-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.action-title {
  color: #C0ff6b;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.action-description {
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
}

/* Modal Styles */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: rgba(40, 40, 40, 0.95);
  margin: 5% auto;
  padding: 0;
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  background: rgba(28, 28, 28, 0.8);
  padding: 20px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  color: #C0ff6b;
  margin: 0;
}

.close {
  color: #ffffff;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
}

.close:hover {
  color: #C0ff6b;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* Responsive Design for Dashboard */
@media (max-width: 768px) {
  .dashboard-hero h1 {
    font-size: 28px;
  }

  .analytics-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .profile-content {
    grid-template-columns: 1fr;
  }

  .activity-content {
    grid-template-columns: 1fr;
  }

  .actions-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }
}

/* Integration Styles for Lesson Details */
.ai-learning-section {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 30px;
  margin: 40px 0;
  backdrop-filter: blur(10px);
}

.ai-section-title {
  color: #C0ff6b;
  font-size: 28px;
  margin-bottom: 30px;
  text-align: center;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.ai-progress-card {
  background: rgba(28, 28, 28, 0.6);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
  gap: 15px;
}

.progress-header h3 {
  color: #C0ff6b;
  margin: 0;
  font-size: 20px;
}

.progress-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.progress-status {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 600;
}

.status-completed {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.status-in_progress {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
  border: 1px solid #FFC107;
}

.status-mastered {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
}

.status-not_started {
  background: rgba(158, 158, 158, 0.2);
  color: #9E9E9E;
  border: 1px solid #9E9E9E;
}

.progress-score, .study-time {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
}

.progress-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.progress-actions .btn {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-success {
  background: #4CAF50;
  color: white;
}

.btn-success:hover {
  background: #45a049;
  transform: translateY(-2px);
}

.btn-secondary {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
}

.btn-secondary:hover {
  background: #C0ff6b;
  color: #000;
}

.ai-setup-notice {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #FFC107;
  border-radius: 15px;
  padding: 30px;
  margin: 40px 0;
  text-align: center;
}

.ai-setup-notice h3 {
  color: #FFC107;
  margin-bottom: 15px;
}

.ai-setup-notice p {
  color: #ffffff;
  margin-bottom: 15px;
}

.ai-setup-notice code {
  background: rgba(28, 28, 28, 0.8);
  color: #C0ff6b;
  padding: 10px 15px;
  border-radius: 5px;
  display: inline-block;
  font-family: 'Courier New', monospace;
}

/* Code Actions Enhancement */
.code-actions {
  display: flex;
  gap: 10px;
}

.copy-code-btn, .analyze-code-btn {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.copy-code-btn:hover, .analyze-code-btn:hover {
  background: #C0ff6b;
  color: #000;
}

/* Responsive adjustments for AI features */
@media (max-width: 768px) {
  .ai-learning-section {
    padding: 20px;
    margin: 20px 0;
  }

  .progress-header {
    flex-direction: column;
    text-align: center;
  }

  .progress-stats {
    justify-content: center;
  }

  .progress-actions {
    justify-content: center;
  }

  .code-actions {
    flex-direction: column;
    gap: 5px;
  }
}

/* AI Dashboard Widget for Course List */
.ai-dashboard-widget {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin: 30px 0;
  backdrop-filter: blur(10px);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.ai-dashboard-widget h3 {
  color: #C0ff6b;
  margin: 0;
  font-size: 22px;
}

.dashboard-btn {
  background: linear-gradient(135deg, #C0ff6b, #a0e066);
  color: #000;
  padding: 8px 16px;
  border-radius: 20px;
  text-decoration: none;
  font-weight: 600;
  font-size: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(192, 255, 107, 0.3);
  border: none;
  cursor: pointer;
}

.dashboard-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.5);
  color: #000;
  text-decoration: none;
}

.dashboard-btn.primary {
  padding: 12px 24px;
  font-size: 14px;
  border-radius: 25px;
}

.ai-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.ai-stat {
  text-align: center;
  background: rgba(28, 28, 28, 0.6);
  padding: 15px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  min-width: 100px;
  flex: 1;
}

.ai-stat .stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.ai-stat .stat-label {
  color: #ffffff;
  font-size: 12px;
  opacity: 0.8;
}

.ai-quick-recommendations {
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
  padding: 20px;
}

.ai-quick-recommendations h4 {
  color: #C0ff6b;
  margin-bottom: 15px;
  font-size: 16px;
}

.quick-rec {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-rec:last-child {
  border-bottom: none;
}

.rec-title {
  color: #ffffff;
  font-size: 14px;
  flex: 1;
}

.rec-link {
  color: #C0ff6b;
  text-decoration: none;
  font-weight: 600;
  font-size: 12px;
  padding: 4px 8px;
  border: 1px solid #C0ff6b;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.rec-link:hover {
  background: #C0ff6b;
  color: #000;
}

.ai-setup-widget {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid #FFC107;
  border-radius: 15px;
  padding: 25px;
  margin: 30px 0;
  text-align: center;
}

.ai-setup-widget h3 {
  color: #FFC107;
  margin-bottom: 10px;
}

.ai-setup-widget p {
  color: #ffffff;
  margin: 0 0 15px 0;
}

.dashboard-access-widget {
  background: rgba(40, 40, 40, 0.9);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin: 30px 0;
  text-align: center;
  backdrop-filter: blur(10px);
}

.dashboard-access-widget h3 {
  color: #C0ff6b;
  margin-bottom: 15px;
}

.dashboard-access-widget p {
  color: #ffffff;
  margin-bottom: 20px;
}

/* Responsive AI Widget */
@media (max-width: 768px) {
  .ai-stats {
    flex-direction: column;
    align-items: center;
  }

  .ai-stat {
    width: 100%;
    max-width: 200px;
  }

  .quick-rec {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
