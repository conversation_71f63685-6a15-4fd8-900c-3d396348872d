{% extends 'base.html' %}
{% load static %}
{% block content %}

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<!-- Main content wrapper -->
<div class="about-page-wrapper" style="position: relative; z-index: 5; padding: 20px;">

<style>
/* About Us Page Styles with Parallax Effects */
/* Fix for container visibility */
.about-container, .project-hero-content {
  background-color: rgba(40, 40, 40, 0.8);
  border-radius: 15px;
  padding: 40px;
  margin: 30px auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  z-index: 10;
  position: relative;
  max-width: 1300px;
  width: 90%;
  overflow: hidden;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

/* Ensure all content is visible */
main {
  position: relative;
  z-index: 5;
  min-height: 100vh;
}

/* Fix for particles container */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

/* Base styles for about us page */
.about-section {
  position: relative;
  padding: 80px 0;
  overflow: hidden;
  z-index: 1;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 50px 20px;
  position: relative;
  z-index: 2;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #ffffff;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: #C0ff6b;
  box-shadow: 0 0 10px #C0ff6b;
}

/* Hero section with project logo */
.project-hero {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 50px 0;
}

.project-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(28, 28, 28, 0.5) 0%, rgba(28, 28, 28, 0.9) 100%);
  z-index: 0;
}

.project-hero-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  padding: 50px 20px;
  position: relative;
  z-index: 2;
}

.project-text-container {
  width: 60%;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.project-text-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.project-title-head {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.project-para {
  font-size: 24px;
  color: #C0ff6b !important;
  margin-bottom: 30px;
  text-shadow: 0 0 10px #C0ff6b, 0 0 20px #C0ff6b;
}

.project-image-container {
  width: 300px;
  height: 300px;
  position: relative;
  animation: float 6s ease-in-out infinite;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.project-image-container.visible {
  opacity: 1;
  transform: translateY(0);
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(192, 255, 107, 0.5);
  transition: all 0.5s ease;
}

.project-image:hover {
  transform: scale(1.05);
  box-shadow: 0 0 40px rgba(192, 255, 107, 0.8);
}

/* Introduction section with GitHub link and video */
.project-intro {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.intro-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 40px;
  padding: 50px 0;
}

.intro-text {
  flex: 1;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 1s ease, transform 1s ease;
  min-height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  word-wrap: break-word;
  overflow: hidden;
}

.intro-text.visible {
  opacity: 1;
  transform: translateX(0);
}

.intro-text:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 15px rgba(192, 255, 107, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
  transform: translateY(-5px);
}

.intro-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  position: relative;
}

.intro-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 50px;
  height: 3px;
  background: #C0ff6b;
  box-shadow: 0 0 10px #C0ff6b;
}

.intro-para {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 30px;
}

.github-link {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  color: #C0ff6b !important;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border: 2px solid #C0ff6b;
  border-radius: 30px;
}

.github-link:hover {
  background-color: #C0ff6b;
  color: #000000 !important;
  box-shadow: 0 0 15px #C0ff6b;
}

.github-link i {
  font-size: 20px;
}

.project-demo {
  flex: 1;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  display: flex;
  flex-direction: column;
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 1s ease, transform 1s ease;
}

.project-demo.visible {
  opacity: 1;
  transform: translateX(0);
}

.project-demo:hover {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 15px rgba(192, 255, 107, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
  transform: translateY(-5px);
}

.video-container {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: #1c1c1c;
  border-radius: 10px;
  margin: 20px 0;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  border: none;
  transition: all 0.3s ease;
}

.video-container:hover {
  box-shadow: 0 0 30px rgba(192, 255, 107, 0.3);
  border-color: rgba(192, 255, 107, 0.3);
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #ffffff;
}

.video-placeholder i {
  font-size: 48px;
  color: #C0ff6b;
  margin-bottom: 15px;
}

/* UN Sustainable Goals section */
.sdg-section {
  padding: 100px 0;
  position: relative;
  background: rgba(40, 40, 40, 0.8);
  overflow: hidden;
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.sdg-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('https://www.un.org/sustainabledevelopment/wp-content/uploads/2019/01/SDG-Wheel_WEB.png') no-repeat center;
  background-size: contain;
  opacity: 0.05;
  z-index: 0;
  filter: brightness(1.5);
  animation: rotate 120s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.sdg-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-top: 50px;
}

.sdg-card {
  width: 300px;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
  min-height: auto;
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
  overflow: visible;
  position: relative;
  margin-bottom: 30px;
}

.sdg-card::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: rgba(192, 255, 107, 0.1);
  border-radius: 50%;
  z-index: 0;
}

.sdg-card.visible {
  opacity: 1;
  transform: translateY(0);
  overflow: hidden
}

.sdg-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.sdg-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  flex-wrap: nowrap;
}

.sdg-number {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #C0ff6b;
  color: #000000;
  font-size: 24px;
  font-weight: 700;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
  margin-right: 10px;
  flex-shrink: 0;
}

.sdg-card:hover .sdg-number {
  transform: scale(1.1);
  box-shadow: 0 0 20px rgba(192, 255, 107, 0.8);
}

.sdg-title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  flex: 1;
  word-wrap: break-word;
}

.sdg-subtitle {
  color: #C0ff6b;
  font-size: 16px;
  margin-bottom: 20px;
}

.sdg-content {
  margin-top: 20px;
}

.sdg-content-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 10px;
}

.sdg-content-text {
  color: #ffffff;
  line-height: 1.6;
}

/* Project sections layout */
.hero-section {
  position: relative;
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Project hero section */
.project-hero {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  padding: 50px 0;
}

/* Project intro section */
.project-intro {
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}
</style>

<!-- Hero Section with Parallax -->
<section class="project-hero parallax-section">
  <div class="about-container">
    <div class="project-hero-content">
      <div class="project-text-container fade-in">
        <h1 class="project-title-head">Forge X</h1>
        <p class="project-para">
          AI Powered Developer Collaboration Platform
        </p>
      </div>
      <div class="project-image-container floating fade-in">
        <img
          src="{% static 'img/ForgeX-Logo(Transparant).png' %}"
          alt="Project Image"
          class="project-image parallax-scroll"
          data-speed="0.05"
        />
      </div>
    </div>
  </div>
</section>

<!-- Introduction Section with Parallax -->
<section class="project-intro parallax-section">
  <div class="about-container">
    <div class="intro-container">
      <div class="intro-text slide-right">
        <h3 class="intro-title">Project Introduction</h3>
        <p class="intro-para">
          Forge X is a project designed to solve a specific problem in the field
          of Education and IT. The project aims to provide students a learning
          platform and developers a collaborative platform. With the increasing
          demand for developers in Nepal most of the fresh graduated students
          often lack industry level skills, our solution provides an efficient
          and user-friendly way to tackle these challenges.
        </p>
        <a
          href="https://github.com/BhuwanJoshi-01/ForgeX"
          class="github-link"
          target="_blank"
        >
          <i class="fab fa-github"></i> View Our GitHub
        </a>
      </div>
      <div class="project-demo slide-left">
        <h3 class="intro-title">Project Demo</h3>
        <div class="video-container">
          <iframe
            src="https://www.youtube.com/embed/2t1jSBg086o?si=0hpZOQIAUW141ybl"
            title="ForgeX Demo Video"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            referrerpolicy="strict-origin-when-cross-origin">
          </iframe>
        </div>
        <p class="intro-para">
          Watch how Forge X transforms developer collaboration using AI.
        </p>
      </div>
    </div>
  </div>
</section>

<!-- UN Sustainable Goals Section with Parallax -->
<section class="sdg-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">UN Sustainable Development Goals</h2>
    <div class="sdg-cards">
      <div class="sdg-card fade-in" data-delay="0">
        <div class="sdg-header">
          <div class="sdg-number">4</div>
          <h3 class="sdg-title">Quality Education</h3>
        </div>
        <p class="sdg-subtitle">
          Promoting inclusive and equitable quality education.
        </p>
        <div class="sdg-content">
          <p class="sdg-content-title">ForgeX for Education</p>
          <p class="sdg-content-text">
            ForgeX provides a structured and accessible learning platform for
            students, especially in underserved areas, supporting continuous
            education and skill development aligned with industry standards.
          </p>
        </div>
      </div>

      <div class="sdg-card fade-in" data-delay="200">
        <div class="sdg-header">
          <div class="sdg-number">8</div>
          <h3 class="sdg-title">Decent Work and Economic Growth</h3>
        </div>
        <p class="sdg-subtitle">
          Promoting productive employment and entrepreneurship.
        </p>
        <div class="sdg-content">
          <p class="sdg-content-title">Empowering Developers</p>
          <p class="sdg-content-text">
            ForgeX enables aspiring developers to gain practical experience and
            showcase their work, boosting employability and entrepreneurial
            opportunities through a collaborative platform.
          </p>
        </div>
      </div>

      <div class="sdg-card fade-in" data-delay="400">
        <div class="sdg-header">
          <div class="sdg-number">9</div>
          <h3 class="sdg-title">Industry, Innovation, and Infrastructure</h3>
        </div>
        <p class="sdg-subtitle">
          Fostering innovation through infrastructure and collaboration.
        </p>
        <div class="sdg-content">
          <p class="sdg-content-title">Driving Innovation</p>
          <p class="sdg-content-text">
            By connecting learners and professionals, ForgeX builds digital
            infrastructure that accelerates innovation and supports the local
            tech ecosystem.
          </p>
        </div>
      </div>

      <div class="sdg-card fade-in" data-delay="600">
        <div class="sdg-header">
          <div class="sdg-number">17</div>
          <h3 class="sdg-title">Partnerships for the Goals</h3>
        </div>
        <p class="sdg-subtitle">
          Strengthening collaboration to achieve development goals.
        </p>
        <div class="sdg-content">
          <p class="sdg-content-title">Collaborative Growth</p>
          <p class="sdg-content-text">
            ForgeX fosters partnerships between students, mentors, institutions,
            and developers to collectively achieve goals through knowledge
            sharing and co-creation.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Design Section with Parallax -->
<section class="design-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Design & Solution</h2>
    <div class="design-container">
      <div class="challenge-card parallax-scroll" data-speed="0.05">
        <div class="challenge-content fade-in">
          <h3 class="challenge-title">Challenges Addressed</h3>
          <div class="animated-bar"></div>
          <p class="challenge-description">
            There are many IT students and self-taught developers without adequate
            exposure to real-world projects or teamwork experiences, which leaves
            them unprepared for industry demands.
          </p>
          <ul class="challenge-list">
            <li class="challenge-item">
              <i class="fas fa-exclamation-circle"></i>
              Limited industry exposure for students
            </li>
            <li class="challenge-item">
              <i class="fas fa-exclamation-circle"></i>
              Lack of real-time coding platforms
            </li>
            <li class="challenge-item">
              <i class="fas fa-exclamation-circle"></i>
              Difficulty finding mentors and peer collaborators
            </li>
            <li class="challenge-item">
              <i class="fas fa-exclamation-circle"></i>
              Lack of access to collaborative development environments
            </li>
            <li class="challenge-item">
              <i class="fas fa-exclamation-circle"></i>
              No centralized platform to build developer portfolios
            </li>
          </ul>
          <p class="challenge-conclusion">
            ForgeX aims to remove these barriers by integrating learning,
            real-time collaboration, and matchmaking on a single platform.
          </p>
        </div>
      </div>

      <div class="solution-card parallax-scroll" data-speed="0.05">
        <div class="solution-content fade-in">
          <h3 class="solution-title">Our Solution</h3>
          <div class="animated-bar"></div>
          <p class="solution-description">
            ForgeX combines learning modules, team-based project work, and real-time
            collaboration to bridge the gap between theoretical knowledge and
            practical skills.
          </p>
          <p class="solution-description">
            Its system recommends teammates, mentors, and projects based on users'
            skill sets and learning goals, ensuring a personalized and productive
            experience.
          </p>
          <div class="use-case-container">
            <h4 class="use-case-title">
              <i class="fas fa-user-check"></i> Use Case: From Learner to Team Lead
            </h4>
            <p class="use-case-description">
              A user joins ForgeX as a student, completes a few learning paths,
              collaborates on small projects, and eventually leads a team to build a
              capstone project—all within the same platform.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Design Section with Parallax */
.design-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: rgba(40, 40, 40, 0.8);
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.design-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.design-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
  margin-top: 50px;
  padding: 0 20px;
}

.challenge-card, .solution-card {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.5s ease;
  word-wrap: break-word;
  overflow: hidden;
  min-height: auto;
  display: flex;
  flex-direction: column;

  margin-top: 130px;
}

.challenge-card:hover, .solution-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.challenge-title, .solution-title {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  margin-top: 0;
  padding-top: 0;
  line-height: 1.2;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.challenge-description, .solution-description {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 20px;
}

.challenge-list {
  list-style-type: none;
  padding: 0;
  margin: 20px 0;
}

.challenge-item {
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.challenge-item i {
  color: #C0ff6b;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  background-color: rgba(192, 255, 107, 0.1);
  border-radius: 50%;
  flex-shrink: 0;
}

.challenge-conclusion {
  color: #ffffff;
  line-height: 1.6;
  margin-top: 20px;
  font-weight: 600;
}

.use-case-container {
  margin-top: 30px;
  padding: 20px;
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
  border-left: 3px solid #C0ff6b;
}

.use-case-title {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.use-case-title i {
  color: #C0ff6b;
}

.use-case-description {
  color: #ffffff;
  line-height: 1.6;
}

.animated-bar {
  width: 50px;
  height: 3px;
  background: #C0ff6b;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.animated-bar::before {
  content: '';
  position: absolute;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% { left: 100%; }
}

.challenge-content, .solution-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
</style>

<!-- Prototype Section with Parallax -->
<section class="prototype-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Prototype Development</h2>
    <div class="prototype-container">
      <div class="prototype-description fade-in">
        <h3 class="prototype-title">Our Development Journey</h3>
        <div class="animated-bar"></div>
        <p class="prototype-text">
          Our prototype represents the core functionality of ForgeX, focusing on the most critical features
          that address the primary challenges faced by developers and students. We prioritized real-time
          collaboration, AI-powered matchmaking, and an intuitive learning interface.
        </p>
        <div class="prototype-features">
          <h4 class="features-title">Key Prototype Features:</h4>
          <ul class="features-list">
            <li class="feature-item">
              <i class="fas fa-code"></i>
              <span>Real-time collaborative code editor with Monaco Editor</span>
            </li>
            <li class="feature-item">
              <i class="fas fa-users"></i>
              <span>AI-powered team matching based on skills and goals</span>
            </li>
            <li class="feature-item">
              <i class="fas fa-graduation-cap"></i>
              <span>Interactive learning modules with progress tracking</span>
            </li>
            <li class="feature-item">
              <i class="fas fa-project-diagram"></i>
              <span>Project management dashboard with task allocation</span>
            </li>
            <li class="feature-item">
              <i class="fas fa-comments"></i>
              <span>Integrated communication system for seamless collaboration</span>
            </li>
          </ul>
        </div>
        <p class="prototype-rationale">
          These features were selected as they form the foundation of effective developer collaboration
          and learning. The prototype validates our core hypothesis that combining education with
          practical project experience accelerates skill development.
        </p>
      </div>
      <div class="prototype-images fade-in">
        <h3 class="prototype-title">Prototype Visuals</h3>
        <div class="prototype-gallery">
          <div class="prototype-image-card">
            <img src="{% static 'img/initial_wireframe.png' %}" alt="Initial Wireframes" class="prototype-img" />
            <div class="image-caption">Initial Wireframes & User Flow</div>
          </div>
          <div class="prototype-image-card">
            <img src="{% static 'img/prototype-dashboard.png' %}" alt="Dashboard Prototype" class="prototype-img" />
            <div class="image-caption">Dashboard Prototype Design</div>
          </div>
          <div class="prototype-image-card">
            <img src="{% static 'img/prototype-editor.png' %}" alt="Code Editor Prototype" class="prototype-img" />
            <div class="image-caption">Collaborative Editor Interface</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Prototype Section Styles */
.prototype-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: rgba(40, 40, 40, 0.8);
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.prototype-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.prototype-container {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  margin-top: 50px;
  padding: 0 20px;
}

.prototype-description, .prototype-images {
  flex: 1;
  min-width: 300px;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.5s ease;
}

.prototype-description:hover, .prototype-images:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.prototype-title {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
}

.prototype-text, .prototype-rationale {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 20px;
}

.prototype-features {
  margin: 30px 0;
}

.features-title {
  font-size: 20px;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 15px;
}

.features-list {
  list-style: none;
  padding: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  color: #ffffff;
}

.feature-item i {
  color: #C0ff6b;
  font-size: 18px;
  width: 20px;
  text-align: center;
}

.prototype-gallery {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.prototype-image-card {
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(192, 255, 107, 0.1);
  transition: all 0.3s ease;
}

.prototype-image-card:hover {
  border-color: rgba(192, 255, 107, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.prototype-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.prototype-image-card:hover .prototype-img {
  transform: scale(1.05);
}

.image-caption {
  padding: 15px;
  color: #ffffff;
  font-weight: 600;
  text-align: center;
  background: rgba(40, 40, 40, 0.8);
}
</style>

<!-- Tech Stack Section with Parallax -->
<section class="tech-stack-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Technologies Used</h2>
    <div class="tech-stack-container">
      <div class="tech-stack-marquee">
        <div class="tech-stack-item">
          <i class="fab fa-python"></i>
          <span>Python</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-python"></i>
          <span>Django</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-html5"></i>
          <span>HTML5</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-css3-alt"></i>
          <span>CSS3</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-code"></i>
          <span>Monaco Editor</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-js"></i>
          <span>JavaScript</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-database"></i>
          <span>PostgreSQL</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-docker"></i>
          <span>Docker</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-bolt"></i>
          <span>Fast API</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-file-code"></i>
          <span>Jinja</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-project-diagram"></i>
          <span>Yjs</span>
        </div>
      </div>
      <div class="tech-stack-marquee" aria-hidden="true">
        <div class="tech-stack-item">
          <i class="fab fa-python"></i>
          <span>Python</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-python"></i>
          <span>Django</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-html5"></i>
          <span>HTML5</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-css3-alt"></i>
          <span>CSS3</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-code"></i>
          <span>Monaco Editor</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-js"></i>
          <span>JavaScript</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-database"></i>
          <span>PostgreSQL</span>
        </div>
        <div class="tech-stack-item">
          <i class="fab fa-docker"></i>
          <span>Docker</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-bolt"></i>
          <span>Fast API</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-file-code"></i>
          <span>Jinja</span>
        </div>
        <div class="tech-stack-item">
          <i class="fas fa-project-diagram"></i>
          <span>Yjs</span>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Tech Stack Section with Parallax */
.tech-stack-section {
  padding: 80px 0;
  position: relative;
  background: rgba(40, 40, 40, 0.8);
  overflow: hidden;
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.tech-stack-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.tech-stack-container {
  display: flex;
  overflow: hidden;
  margin: 50px auto;
  background: rgba(28, 28, 28, 0.5);
  border-radius: 15px;
  padding: 40px 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(192, 255, 107, 0.1);
  position: relative;
  max-width: 90%;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.tech-stack-container::before,
.tech-stack-container::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100px;
  height: 100%;
  z-index: 2;
}

.tech-stack-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(28, 28, 28, 1), transparent);
}

.tech-stack-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(28, 28, 28, 1), transparent);
}

.tech-stack-marquee {
  display: flex;
  gap: 40px;
  animation: scroll 30s linear infinite;
  padding: 0 20px;
}

@keyframes scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-100%); }
}

.tech-stack-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  min-width: 100px;
  transition: all 0.3s ease;
  justify-content: center;
  text-align: center;
}

.tech-stack-item:hover {
  transform: translateY(-10px);
}

.tech-stack-item i {
  font-size: 36px;
  color: #C0ff6b;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.tech-stack-item span {
  color: #ffffff;
  font-weight: 600;
}

.tech-stack-container:hover .tech-stack-marquee {
  animation-play-state: paused;
}
</style>

<!-- Market Impact Section with Parallax -->
<section class="market-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Market Impact & Analysis</h2>
    <div class="market-container">
      <div class="market-research fade-in">
        <h3 class="market-title">Current Market Landscape</h3>
        <div class="animated-bar"></div>
        <p class="market-text">
          The developer education and collaboration market is rapidly expanding, with the global
          e-learning market projected to reach $1 trillion by 2027. However, existing solutions
          often focus on either education OR collaboration, not both.
        </p>
        <div class="market-stats">
          <div class="stat-item">
            <div class="stat-number">73%</div>
            <div class="stat-label">of developers are self-taught</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">65%</div>
            <div class="stat-label">struggle with real-world project experience</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">89%</div>
            <div class="stat-label">prefer collaborative learning environments</div>
          </div>
        </div>
      </div>

      <div class="competitive-analysis fade-in">
        <h3 class="market-title">Competitive Advantage</h3>
        <div class="animated-bar"></div>
        <div class="competitors-grid">
          <div class="competitor-card">
            <h4 class="competitor-name">Traditional Platforms</h4>
            <p class="competitor-desc">GitHub, GitLab - Focus on code hosting</p>
            <div class="advantage-point">
              <i class="fas fa-check"></i>
              <span>ForgeX adds AI-powered learning and mentorship</span>
            </div>
          </div>

          <div class="competitor-card">
            <h4 class="competitor-name">Learning Platforms</h4>
            <p class="competitor-desc">Coursera, Udemy - Individual learning focus</p>
            <div class="advantage-point">
              <i class="fas fa-check"></i>
              <span>ForgeX emphasizes collaborative project-based learning</span>
            </div>
          </div>

          <div class="competitor-card">
            <h4 class="competitor-name">Coding Platforms</h4>
            <p class="competitor-desc">LeetCode, HackerRank - Algorithm practice</p>
            <div class="advantage-point">
              <i class="fas fa-check"></i>
              <span>ForgeX focuses on real-world project development</span>
            </div>
          </div>
        </div>

        <div class="unique-value">
          <h4 class="value-title">Our Unique Value Proposition</h4>
          <p class="value-text">
            ForgeX is the first platform to seamlessly integrate AI-powered learning,
            real-time collaboration, and industry-standard project development in a
            single ecosystem, specifically designed for the Nepali and South Asian market.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Market Impact Section Styles */
.market-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: rgba(40, 40, 40, 0.8);
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.market-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.market-container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 50px;
  padding: 0 20px;
}

.market-research, .competitive-analysis {
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.5s ease;
}

.market-research:hover, .competitive-analysis:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.market-title {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
}

.market-text {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 30px;
}

.market-stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 30px;
}

.stat-item {
  text-align: center;
  background: rgba(28, 28, 28, 0.5);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  min-width: 150px;
}

.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 10px;
}

.stat-label {
  color: #ffffff;
  font-size: 14px;
}

.competitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.competitor-card {
  background: rgba(28, 28, 28, 0.5);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  transition: all 0.3s ease;
}

.competitor-card:hover {
  border-color: rgba(192, 255, 107, 0.3);
  transform: translateY(-5px);
}

.competitor-name {
  font-size: 18px;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 10px;
}

.competitor-desc {
  color: #ffffff;
  margin-bottom: 15px;
  font-size: 14px;
}

.advantage-point {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #ffffff;
}

.advantage-point i {
  color: #C0ff6b;
  font-size: 16px;
}

.unique-value {
  margin-top: 30px;
  padding: 25px;
  background: rgba(28, 28, 28, 0.5);
  border-radius: 10px;
  border-left: 4px solid #C0ff6b;
}

.value-title {
  font-size: 20px;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 15px;
}

.value-text {
  color: #ffffff;
  line-height: 1.6;
}
</style>

<!-- Final Product Section with Parallax -->
<section class="final-product-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Final Product & Development Process</h2>

    <!-- Development Process -->
    <div class="process-timeline fade-in">
      <h3 class="process-title">Our Development Journey</h3>
      <div class="animated-bar"></div>
      <div class="timeline-container">
        <div class="timeline-item">
          <div class="timeline-marker">1</div>
          <div class="timeline-content">
            <h4 class="timeline-title">Research & Planning</h4>
            <p class="timeline-desc">Market research, user interviews, and technical architecture planning using Django and React frameworks.</p>
          </div>
        </div>

        <div class="timeline-item">
          <div class="timeline-marker">2</div>
          <div class="timeline-content">
            <h4 class="timeline-title">Prototype Development</h4>
            <p class="timeline-desc">Built core features including real-time collaboration using Yjs, Monaco Editor integration, and basic AI matching algorithms.</p>
          </div>
        </div>

        <div class="timeline-item">
          <div class="timeline-marker">3</div>
          <div class="timeline-content">
            <h4 class="timeline-title">Testing & Iteration</h4>
            <p class="timeline-desc">User testing with local developer communities, feedback integration, and performance optimization using Docker containerization.</p>
          </div>
        </div>

        <div class="timeline-item">
          <div class="timeline-marker">4</div>
          <div class="timeline-content">
            <h4 class="timeline-title">Final Implementation</h4>
            <p class="timeline-desc">Full-stack implementation with PostgreSQL database, FastAPI microservices, and responsive frontend with advanced AI features.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Final Product Images -->
    <div class="product-showcase fade-in">
      <h3 class="showcase-title">Final Product Screenshots</h3>
      <div class="animated-bar"></div>
      <div class="product-gallery">
        <div class="product-image-card main-feature">
          <img src="{% static 'img/final-dashboard.png' %}" alt="ForgeX Dashboard" class="product-img" />
          <div class="image-overlay">
            <h4 class="overlay-title">Main Dashboard</h4>
            <p class="overlay-desc">Comprehensive project overview with AI-powered recommendations</p>
          </div>
        </div>

        <div class="product-images-grid">
          <div class="product-image-card">
            <img src="{% static 'img/final-editor.png' %}" alt="Collaborative Editor" class="product-img" />
            <div class="image-overlay">
              <h4 class="overlay-title">Real-time Editor</h4>
              <p class="overlay-desc">Monaco-powered collaborative coding environment</p>
            </div>
          </div>

          <div class="product-image-card">
            <img src="{% static 'img/final-matching.png' %}" alt="AI Matching System" class="product-img" />
            <div class="image-overlay">
              <h4 class="overlay-title">AI Matching</h4>
              <p class="overlay-desc">Smart team formation based on skills and goals</p>
            </div>
          </div>

          <div class="product-image-card">
            <img src="{% static 'img/final-learning.png' %}" alt="Learning Modules" class="product-img" />
            <div class="image-overlay">
              <h4 class="overlay-title">Learning Paths</h4>
              <p class="overlay-desc">Interactive tutorials with progress tracking</p>
            </div>
          </div>

          <div class="product-image-card">
            <img src="{% static 'img/final-projects.png' %}" alt="Project Management" class="product-img" />
            <div class="image-overlay">
              <h4 class="overlay-title">Project Hub</h4>
              <p class="overlay-desc">Comprehensive project management and collaboration tools</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Final Product Section Styles */
.final-product-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: rgba(40, 40, 40, 0.8);
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.final-product-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.process-timeline {
  margin: 50px 0;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.process-title, .showcase-title {
  font-size: 26px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 20px;
  text-align: center;
}

.timeline-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  margin-top: 40px;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 22px;
  top: 50px;
  width: 2px;
  height: 60px;
  background: linear-gradient(to bottom, #C0ff6b, rgba(192, 255, 107, 0.3));
}

.timeline-marker {
  width: 45px;
  height: 45px;
  background: #C0ff6b;
  color: #000000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 18px;
  flex-shrink: 0;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
}

.timeline-content {
  flex: 1;
  background: rgba(28, 28, 28, 0.5);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.1);
  transition: all 0.3s ease;
}

.timeline-content:hover {
  border-color: rgba(192, 255, 107, 0.3);
  transform: translateX(10px);
}

.timeline-title {
  font-size: 20px;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 10px;
}

.timeline-desc {
  color: #ffffff;
  line-height: 1.6;
}

.product-showcase {
  margin: 50px 0;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.product-gallery {
  margin-top: 40px;
}

.main-feature {
  margin-bottom: 30px;
}

.product-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.product-image-card {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  background: rgba(28, 28, 28, 0.5);
  border: 1px solid rgba(192, 255, 107, 0.1);
  transition: all 0.3s ease;
}

.product-image-card:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.3);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.product-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: all 0.3s ease;
}

.main-feature .product-img {
  height: 300px;
}

.product-image-card:hover .product-img {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 20px;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.product-image-card:hover .image-overlay {
  transform: translateY(0);
}

.overlay-title {
  font-size: 18px;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.overlay-desc {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.4;
}
</style>

<!-- Team Section with Parallax -->
<section class="team-section parallax-section">
  <div class="about-container">
    <h2 class="section-title fade-in">Our Team</h2>
    <div class="team-grid">
      <div class="team-card fade-in" data-delay="0">
        <div class="team-image-container">
          <img src="{% static 'img/bhuwan.jpg' %}" alt="Bhuwan Chakra Joshi" class="team-image" />
          <div class="team-image-overlay">
            <a href="https://www.linkedin.com/in/bhuwanjoshi-np/" target="_blank" class="team-social-link">
              <i class="fab fa-linkedin"></i>
            </a>
          </div>
        </div>
        <div class="team-info">
          <h3 class="team-name">Bhuwan Chakra Joshi</h3>
          <p class="team-role">Backend Developer</p>
          <p class="team-id">ID: 25123787</p>
        </div>
      </div>

      <div class="team-card fade-in" data-delay="200">
        <div class="team-image-container">
          <img src="{% static 'img/lokesh.jpg' %}" alt="Lokesh Shrestha" class="team-image" />
          <div class="team-image-overlay">
            <a href="http://www.linkedin.com/in/lokeshshrestha" target="_blank" class="team-social-link">
              <i class="fab fa-linkedin"></i>
            </a>
          </div>
        </div>
        <div class="team-info">
          <h3 class="team-name">Lokesh Shrestha</h3>
          <p class="team-role">Backend Developer</p>
          <p class="team-id">ID: 25123810</p>
        </div>
      </div>

      <div class="team-card fade-in" data-delay="400">
        <div class="team-image-container">
          <img src="{% static 'img/kiran.jpg' %}" alt="Kiran Basnet" class="team-image" />
          <div class="team-image-overlay">
            <a href="https://www.linkedin.com/in/basnet-kiran" target="_blank" class="team-social-link">
              <i class="fab fa-linkedin"></i>
            </a>
          </div>
        </div>
        <div class="team-info">
          <h3 class="team-name">Kiran Basnet</h3>
          <p class="team-role">Frontend Developer</p>
          <p class="team-id">ID: 25123805</p>
        </div>
      </div>

      <div class="team-card fade-in" data-delay="600">
        <div class="team-image-container">
          <img src="{% static 'img/saurav.jpg' %}" alt="Saurav G.C" class="team-image" />
          <div class="team-image-overlay">
            <a href="https://www.linkedin.com/in/iamsauravgc/" target="_blank" class="team-social-link">
              <i class="fab fa-linkedin"></i>
            </a>
          </div>
        </div>
        <div class="team-info">
          <h3 class="team-name">Saurav G.C</h3>
          <p class="team-role">Frontend Developer</p>
          <p class="team-id">ID: ********</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Call to Action Section with Parallax -->
<section class="cta-section parallax-section">
  <div class="about-container">
    <div class="cta-content fade-in">
      <h2>Ready to join our developer community?</h2>
      <p>Start building amazing projects and grow your skills with ForgeX.</p>
      <a href="{% url 'accounts:signup' %}" class="cta-button">Join Now</a>
    </div>
  </div>
</section>

<style>
/* Team Section with Parallax */
.team-section {
  padding: 100px 0;
  position: relative;
  overflow: hidden;
  background: rgba(40, 40, 40, 0.8);
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.team-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
}

.team-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
  margin-top: 50px;
}

.team-card {
  width: 250px;
  background: rgba(40, 40, 40, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.5s ease;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.team-card.visible {
  opacity: 1;
  transform: translateY(0);
}

.team-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.5);
}

.team-image-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.team-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s ease;
}

.team-card:hover .team-image {
  transform: scale(1.1);
  filter: brightness(0.7);
}

.team-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: all 0.5s ease;
}

.team-card:hover .team-image-overlay {
  opacity: 1;
}

.team-social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #C0ff6b;
  border-radius: 50%;
  color: #000000;
  font-size: 24px;
  transition: all 0.3s ease;
  transform: translateY(20px);
}

.team-card:hover .team-social-link {
  transform: translateY(0);
}

.team-social-link:hover {
  background: #ffffff;
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.team-info {
  padding: 20px;
  text-align: center;
}

.team-name {
  font-size: 20px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 10px;
}

.team-role {
  color: #C0ff6b;
  font-size: 16px;
  margin-bottom: 5px;
}

.team-id {
  color: #ffffff;
  font-size: 14px;
  opacity: 0.7;
}

/* Call to Action Section with Parallax */
.cta-section {
  padding: 100px 0;
  text-align: center;
  background: rgba(40, 40, 40, 0.8);
  position: relative;
  overflow: hidden;
  margin: 40px auto;
  border-radius: 15px;
  max-width: 1300px;
  width: 90%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.2);
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.1) 0%, transparent 70%);
  z-index: 0;
  animation: pulse 4s infinite;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.cta-content.visible {
  opacity: 1;
  transform: translateY(0);
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.cta-content p {
  color: #ffffff;
  margin-bottom: 30px;
  font-size: 18px;
}

.cta-button {
  display: inline-block;
  background-color: transparent;
  color: #ffffff;
  border: 2px solid #C0ff6b;
  padding: 12px 30px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  text-decoration: none;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.3), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover {
  background-color: #C0ff6b;
  color: #000000;
  box-shadow: 0 0 15px #C0ff6b, 0 0 30px #C0ff6b;
}

.cta-button:hover::before {
  left: 100%;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .about-container, .project-hero-content {
    max-width: 95%;
    padding: 30px;
  }

  .section-title {
    font-size: 32px;
  }

  .project-title-head {
    font-size: 40px;
  }
}

@media (max-width: 768px) {
  .about-page-wrapper {
    padding: 10px;
  }

  .about-container, .project-hero-content {
    padding: 20px;
    margin: 20px auto;
  }

  .project-hero-content {
    flex-direction: column;
    text-align: center;
    gap: 30px;
  }

  .project-text-container {
    width: 100%;
  }

  .project-title-head {
    font-size: 32px;
  }

  .project-para {
    font-size: 20px;
  }

  .intro-container {
    flex-direction: column;
    gap: 30px;
  }

  .design-container {
    flex-direction: column;
  }

  .challenge-card, .solution-card {
    margin-top: 0;
  }

  .prototype-container {
    flex-direction: column;
  }

  .market-container {
    padding: 0 10px;
  }

  .market-stats {
    flex-direction: column;
    align-items: center;
  }

  .competitors-grid {
    grid-template-columns: 1fr;
  }

  .timeline-item {
    flex-direction: column;
    text-align: center;
  }

  .timeline-item::after {
    display: none;
  }

  .product-images-grid {
    grid-template-columns: 1fr;
  }

  .team-grid {
    flex-direction: column;
    align-items: center;
  }

  .tech-stack-container {
    max-width: 95%;
  }

  .sdg-cards {
    flex-direction: column;
    align-items: center;
  }

  .sdg-card {
    width: 100%;
    max-width: 400px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 24px;
  }

  .project-title-head {
    font-size: 24px;
  }

  .project-para {
    font-size: 16px;
  }

  .intro-title, .challenge-title, .solution-title, .market-title, .process-title, .showcase-title {
    font-size: 20px;
  }

  .about-container, .project-hero-content {
    padding: 15px;
  }

  .tech-stack-item {
    min-width: 80px;
  }

  .tech-stack-item i {
    font-size: 24px;
  }

  .team-card {
    width: 100%;
    max-width: 300px;
  }

  .cta-content h2 {
    font-size: 24px;
  }

  .timeline-marker {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .stat-item {
    min-width: 120px;
  }

  .stat-number {
    font-size: 28px;
  }
}
</style>

<!-- Required JavaScript for animations and parallax -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Make sure particles.js is loaded
    if (typeof particlesJS !== 'undefined') {
      // Initialize particles.js for background effect
      particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 120,
          "density": {
            "enable": true,
            "value_area": 1000
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
        },
        "shape": {
          "type": ["circle", "triangle", "polygon"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          },
          "polygon": {
            "nb_sides": 5
          }
        },
        "opacity": {
          "value": 0.6,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.2,
            "sync": false
          }
        },
        "size": {
          "value": 4,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.5,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.5,
          "width": 1.2
        },
        "move": {
          "enable": true,
          "speed": 1.2,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "bounce",
          "bounce": true,
          "attract": {
            "enable": true,
            "rotateX": 600,
            "rotateY": 1200
          }
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "bubble"
          },
          "onclick": {
            "enable": true,
            "mode": "repulse"
          },
          "resize": true
        },
        "modes": {
          "grab": {
            "distance": 180,
            "line_linked": {
              "opacity": 1
            }
          },
          "bubble": {
            "distance": 200,
            "size": 6,
            "duration": 2,
            "opacity": 0.8,
            "speed": 3
          },
          "repulse": {
            "distance": 200,
            "duration": 0.4
          },
          "push": {
            "particles_nb": 4
          },
          "remove": {
            "particles_nb": 2
          }
        }
      },
      "retina_detect": true
      });
    } else {
      console.warn("particles.js not loaded. Falling back to simple background.");
      document.getElementById('particles-js').style.backgroundColor = "#1c1c1c";
    }

    // Scroll reveal functionality
    function revealOnScroll() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      const windowHeight = window.innerHeight;

      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        // Add visible class when element enters viewport
        if (elementTop < windowHeight - elementVisible) {
          element.classList.add('visible');
        }
      });
    }

    // Parallax scroll effect
    function parallaxScroll() {
      const scrolled = window.scrollY;

      // Apply parallax effect to elements with parallax-scroll class
      document.querySelectorAll('.parallax-scroll').forEach(element => {
        const speed = parseFloat(element.getAttribute('data-speed') || 0.2);
        let yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });
    }

    // Floating animation for elements with floating class
    function setupFloatingElements() {
      document.querySelectorAll('.floating').forEach((element, index) => {
        const delay = parseInt(element.getAttribute('data-delay') || 0);

        setInterval(() => {
          const floatOffset = Math.sin((Date.now() + delay) / 1000 + index) * 10;

          // Only apply transform if element doesn't have a hover state
          if (!element.matches(':hover')) {
            element.style.transform = `translateY(${floatOffset}px)`;
          }
        }, 50);
      });
    }

    // Run initial reveal
    revealOnScroll();

    // Set up event listeners
    window.addEventListener('scroll', function() {
      revealOnScroll();
      parallaxScroll();
    });

    // Set up floating elements
    setupFloatingElements();
  });
</script>

</div> <!-- End of about-page-wrapper -->

{% endblock %}