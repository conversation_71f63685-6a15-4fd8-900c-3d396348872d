{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">Session Debug Information</h2>
            <p class="text-center text-muted">This page shows session information for debugging purposes</p>
        </div>
    </div>

    <div class="row">
        <!-- Current Session Info -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Current Session</h5>
                </div>
                <div class="card-body">
                    {% if session_info %}
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Session Key:</strong></td>
                                <td><code>{{ session_info.session_key|slice:":20" }}...</code></td>
                            </tr>
                            <tr>
                                <td><strong>User:</strong></td>
                                <td>{{ session_info.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>Authenticated:</strong></td>
                                <td>
                                    {% if session_info.is_authenticated %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-danger">No</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Expires:</strong></td>
                                <td>{{ session_info.expire_date }}</td>
                            </tr>
                            <tr>
                                <td><strong>Time Until Expiry:</strong></td>
                                <td>{{ session_info.time_until_expiry }}</td>
                            </tr>
                            <tr>
                                <td><strong>Remember Me:</strong></td>
                                <td>
                                    {% if session_info.remember_me %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-warning">No</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Last Activity:</strong></td>
                                <td>{{ session_info.last_activity }}</td>
                            </tr>
                        </table>
                    {% else %}
                        <p class="text-muted">No session information available</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- All User Sessions -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">All Your Sessions</h5>
                </div>
                <div class="card-body">
                    {% if user_sessions %}
                        <p class="text-muted mb-3">You have {{ user_sessions|length }} active session(s)</p>
                        {% for session in user_sessions %}
                            <div class="border rounded p-2 mb-2">
                                <small>
                                    <strong>Session:</strong> {{ session.session_key|slice:":15" }}...<br>
                                    <strong>Expires:</strong> {{ session.expire_date }}<br>
                                    <strong>Remember Me:</strong> 
                                    {% if session.remember_me %}
                                        <span class="badge bg-success">Yes</span>
                                    {% else %}
                                        <span class="badge bg-warning">No</span>
                                    {% endif %}
                                </small>
                            </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No active sessions found</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Session Data -->
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Session Data</h5>
                </div>
                <div class="card-body">
                    {% if session_data %}
                        <pre class="bg-light p-3 rounded"><code>{% for key, value in session_data.items %}{{ key }}: {{ value }}
{% endfor %}</code></pre>
                    {% else %}
                        <p class="text-muted">No session data available</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 text-center">
            <a href="{% url 'accounts:profile' %}" class="btn btn-primary">Back to Profile</a>
            <a href="{% url 'accounts:logout' %}" class="btn btn-outline-danger">Logout</a>
        </div>
    </div>
</div>

<style>
.card {
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.table td {
    border: none;
    padding: 0.5rem;
}

.table tr:nth-child(even) {
    background-color: #f8f9fa;
}

pre code {
    font-size: 0.875rem;
    color: #333;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}
