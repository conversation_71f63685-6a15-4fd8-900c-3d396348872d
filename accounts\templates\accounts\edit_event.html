{% extends 'base.html' %}
{% load static %}
{% block title %}Edit Event - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Edit Event Header -->
    <div class="dashboard-welcome">
      <h1>✏️ Edit Event</h1>
      <p class="welcome-subtitle">Update event details for "{{ event.title }}"</p>
    </div>

    <!-- Navigation Actions -->
    <div class="quick-actions" style="margin-bottom: 30px;">
      <div class="action-grid" style="grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));">
        <a href="{% url 'accounts:events' %}" class="action-card">
          <div class="action-icon">⬅️</div>
          <h3>Back to Events</h3>
          <p>Return to events list</p>
        </a>
        <a href="{% url 'accounts:delete_event' event.id %}" class="action-card" style="border-color: rgba(244, 67, 54, 0.5);">
          <div class="action-icon" style="color: #f44336;">🗑️</div>
          <h3 style="color: #f44336;">Delete Event</h3>
          <p>Permanently remove this event</p>
        </a>
      </div>
    </div>

    <!-- Event Form Section -->
    <div class="dashboard-section">
      <h2>📝 Event Details</h2>
      <div class="form-container">
        <form method="post" class="event-form">
          {% csrf_token %}

          <div class="form-row">
            <div class="form-group">
              <label for="{{ form.title.id_for_label }}">
                <i class="fas fa-heading"></i> {{ form.title.label }}
              </label>
              {{ form.title }}
              {% if form.title.errors %}
                <div class="error-message">
                  {% for error in form.title.errors %}
                    <small>{{ error }}</small>
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="{{ form.description.id_for_label }}">
                <i class="fas fa-align-left"></i> {{ form.description.label }}
              </label>
              {{ form.description }}
              {% if form.description.errors %}
                <div class="error-message">
                  {% for error in form.description.errors %}
                    <small>{{ error }}</small>
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>

          <div class="form-row-split">
            <div class="form-group">
              <label for="{{ form.event_date.id_for_label }}">
                <i class="fas fa-calendar-alt"></i> {{ form.event_date.label }}
              </label>
              {{ form.event_date }}
              {% if form.event_date.errors %}
                <div class="error-message">
                  {% for error in form.event_date.errors %}
                    <small>{{ error }}</small>
                  {% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="form-group">
              <label for="{{ form.location.id_for_label }}">
                <i class="fas fa-map-marker-alt"></i> {{ form.location.label }}
              </label>
              {{ form.location }}
              {% if form.location.errors %}
                <div class="error-message">
                  {% for error in form.location.errors %}
                    <small>{{ error }}</small>
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>

          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-save"></i> Update Event
            </button>
            <a href="{% url 'accounts:events' %}" class="btn btn-secondary">
              <i class="fas fa-times"></i> Cancel
            </a>
          </div>
        </form>
      </div>
    </div>

    <!-- Event Information Section -->
    <div class="dashboard-section">
      <h2>📋 Event Information</h2>
      <div class="dashboard-stats">
        <div class="stat-card">
          <div class="stat-icon">👤</div>
          <div class="stat-content">
            <h3>{{ event.created_by.get_full_name|default:event.created_by.username }}</h3>
            <p>Created By</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📅</div>
          <div class="stat-content">
            <h3>{{ event.created_at|date:"M d, Y" }}</h3>
            <p>Created On</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🔄</div>
          <div class="stat-content">
            <h3>{{ event.updated_at|date:"M d, Y" }}</h3>
            <p>Last Updated</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            {% if event.is_upcoming %}🟢{% else %}🔴{% endif %}
          </div>
          <div class="stat-content">
            <h3>
              {% if event.is_upcoming %}
                Upcoming
              {% else %}
                Past
              {% endif %}
            </h3>
            <p>Status</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Live Preview Section -->
    <div class="dashboard-section">
      <h2>👁️ Live Preview</h2>
      <div class="preview-container">
        <div class="project-card" id="preview-card">
          <div class="project-header">
            <h3 id="preview-title">{{ event.title }}</h3>
          </div>

          <div class="project-body">
            <div class="event-meta">
              <p id="preview-date"><i class="fas fa-clock"></i> {{ event.event_date|date:"F d, Y g:i A" }}</p>
              <p id="preview-location" {% if not event.location %}style="display: none;"{% endif %}>
                <i class="fas fa-map-marker-alt"></i> <span id="preview-location-text">{{ event.location }}</span>
              </p>
            </div>
            <p id="preview-description">{{ event.description }}</p>
          </div>

          <div class="project-actions">
            <small style="color: #b0b0b0;">
              Created by {{ event.created_by.get_full_name|default:event.created_by.username }}
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.2,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .action-card, .project-card, .form-container, .stat-card');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });

    // Live preview functionality
    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
    const descriptionInput = document.getElementById('{{ form.description.id_for_label }}');
    const dateInput = document.getElementById('{{ form.event_date.id_for_label }}');
    const locationInput = document.getElementById('{{ form.location.id_for_label }}');

    const previewTitle = document.getElementById('preview-title');
    const previewDescription = document.getElementById('preview-description');
    const previewDate = document.getElementById('preview-date');
    const previewLocation = document.getElementById('preview-location');
    const previewLocationText = document.getElementById('preview-location-text');

    function updatePreview() {
        // Update title
        if (previewTitle && titleInput) {
            previewTitle.textContent = titleInput.value || 'Event Title';
        }

        // Update description
        if (previewDescription && descriptionInput) {
            previewDescription.textContent = descriptionInput.value || 'Event description will appear here...';
        }

        // Update date
        if (previewDate && dateInput) {
            if (dateInput.value) {
                const date = new Date(dateInput.value);
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true
                };
                previewDate.innerHTML = '<i class="fas fa-clock"></i> ' + date.toLocaleDateString('en-US', options);
            }
        }

        // Update location
        if (previewLocation && previewLocationText && locationInput) {
            if (locationInput.value.trim()) {
                previewLocationText.textContent = locationInput.value;
                previewLocation.style.display = 'block';
            } else {
                previewLocation.style.display = 'none';
            }
        }
    }

    // Add event listeners
    if (titleInput) titleInput.addEventListener('input', updatePreview);
    if (descriptionInput) descriptionInput.addEventListener('input', updatePreview);
    if (dateInput) dateInput.addEventListener('input', updatePreview);
    if (locationInput) locationInput.addEventListener('input', updatePreview);

    // Initial preview update
    updatePreview();
  });
</script>

<style>
/* Dashboard specific styles for edit event page */
.dashboard-welcome {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
}

.dashboard-welcome h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  color: #b0b0b0;
  font-size: 1.1rem;
  margin: 0;
}

.dashboard-section {
  margin-bottom: 40px;
}

.dashboard-section h2 {
  color: #ffffff;
  margin-bottom: 30px;
  font-size: 1.8rem;
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.stat-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.stat-content h3 {
  font-size: 1.5rem;
  color: var(--color-border);
  margin-bottom: 5px;
  word-break: break-word;
}

.stat-content p {
  color: #b0b0b0;
  margin: 0;
}

.quick-actions {
  margin-bottom: 40px;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
  text-decoration: none;
  color: inherit;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--color-border);
}

.action-card h3 {
  color: var(--color-border);
  margin-bottom: 10px;
  font-size: 1.3rem;
}

.action-card p {
  color: #b0b0b0;
  margin: 0;
  font-size: 0.9rem;
}

/* Form styling */
.form-container {
  background-color: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.form-row, .form-row-split {
  margin-bottom: 25px;
}

.form-row-split {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 16px;
}

.form-group label i {
  color: var(--color-border);
  margin-right: 8px;
  width: 16px;
}

.form-control {
  background-color: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 12px 15px;
  color: #ffffff;
  font-size: 16px;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--color-border);
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  outline: none;
}

.form-control:hover {
  border-color: rgba(192, 255, 107, 0.5);
}

.error-message {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: center;
}

/* Preview styling */
.preview-container {
  max-width: 600px;
  margin: 0 auto;
}

.event-meta p {
  margin-bottom: 8px;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.event-meta i {
  color: var(--color-border);
  margin-right: 8px;
  width: 16px;
}

/* Messages styling */
.messages-container {
  margin-bottom: 30px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 15px;
  position: relative;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.alert-success {
  background-color: rgba(76, 175, 80, 0.2);
  border: 1px solid rgba(76, 175, 80, 0.5);
  color: #4CAF50;
}

.alert-info {
  background-color: rgba(33, 150, 243, 0.2);
  border: 1px solid rgba(33, 150, 243, 0.5);
  color: #2196F3;
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.2);
  border: 1px solid rgba(255, 193, 7, 0.5);
  color: #FFC107;
}

.alert-error {
  background-color: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #f44336;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .form-row-split {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .stat-content h3 {
    font-size: 1.2rem;
  }
}
</style>

{% endblock %}
