{"version": 3, "names": ["_decorators", "require", "FEATURES", "exports", "Object", "freeze", "fields", "privateMethods", "decorators", "privateIn", "staticBlocks", "featuresSameLoose", "Map", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "looseLowPriorityKey", "canIgnoreLoose", "file", "feature", "get", "enableFeature", "loose", "hasFeature", "set", "setL<PERSON>e", "resolvedLoose", "mask", "name", "isLoose", "Error", "getBabelShowConfigForHint", "higherPriorityPluginName", "undefined", "console", "warn", "filename", "opts", "shouldTransform", "path", "decoratorPath", "publicFieldPath", "privateFieldPath", "privateMethodPath", "staticBlockPath", "hasOwnDecorators", "node", "el", "isClassProperty", "isClassPrivateProperty", "isClassPrivateMethod", "isStaticBlock", "buildCodeFrameError"], "sources": ["../src/features.ts"], "sourcesContent": ["import type { File, types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/core\";\nimport { hasOwnDecorators } from \"./decorators.ts\";\n\nexport const FEATURES = Object.freeze({\n  //classes: 1 << 0,\n  fields: 1 << 1,\n  privateMethods: 1 << 2,\n  decorators: 1 << 3,\n  privateIn: 1 << 4,\n  staticBlocks: 1 << 5,\n});\n\nconst featuresSameLoose = new Map([\n  [FEATURES.fields, \"@babel/plugin-transform-class-properties\"],\n  [FEATURES.privateMethods, \"@babel/plugin-transform-private-methods\"],\n  [FEATURES.privateIn, \"@babel/plugin-transform-private-property-in-object\"],\n]);\n\n// We can't use a symbol because this needs to always be the same, even if\n// this package isn't deduped by npm. e.g.\n//  - node_modules/\n//    - @babel/plugin-class-features\n//    - @babel/plugin-proposal-decorators\n//      - node_modules\n//        - @babel-plugin-class-features\nconst featuresKey = \"@babel/plugin-class-features/featuresKey\";\nconst looseKey = \"@babel/plugin-class-features/looseKey\";\n\nif (!process.env.BABEL_8_BREAKING) {\n  // See https://github.com/babel/babel/issues/11622.\n  // Since preset-env sets loose for the fields and private methods plugins, it can\n  // cause conflicts with the loose mode set by an explicit plugin in the config.\n  // To solve this problem, we ignore preset-env's loose mode if another plugin\n  // explicitly sets it\n  // The code to handle this logic doesn't check that \"low priority loose\" is always\n  // the same. However, it is only set by the preset and not directly by users:\n  // unless someone _wants_ to break it, it shouldn't be a problem.\n  // eslint-disable-next-line no-var\n  var looseLowPriorityKey =\n    \"@babel/plugin-class-features/looseLowPriorityKey/#__internal__@babel/preset-env__please-overwrite-loose-instead-of-throwing\";\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // eslint-disable-next-line no-var\n  var canIgnoreLoose = function (file: File, feature: number) {\n    return !!(file.get(looseLowPriorityKey) & feature);\n  };\n}\n\nexport function enableFeature(file: File, feature: number, loose: boolean) {\n  // We can't blindly enable the feature because, if it was already set,\n  // \"loose\" can't be changed, so that\n  //   @babel/plugin-class-properties { loose: true }\n  //   @babel/plugin-class-properties { loose: false }\n  // is transformed in loose mode.\n  // We only enabled the feature if it was previously disabled.\n  if (process.env.BABEL_8_BREAKING) {\n    if (!hasFeature(file, feature)) {\n      file.set(featuresKey, file.get(featuresKey) | feature);\n      setLoose(file, feature, loose);\n    }\n  } else if (!hasFeature(file, feature) || canIgnoreLoose(file, feature)) {\n    file.set(featuresKey, file.get(featuresKey) | feature);\n    if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, true);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, false);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else {\n      setLoose(file, feature, loose);\n    }\n  }\n\n  let resolvedLoose: boolean | undefined;\n  for (const [mask, name] of featuresSameLoose) {\n    if (!hasFeature(file, mask)) continue;\n    if (!process.env.BABEL_8_BREAKING) {\n      if (canIgnoreLoose(file, mask)) continue;\n    }\n\n    const loose = isLoose(file, mask);\n\n    if (resolvedLoose === !loose) {\n      throw new Error(\n        \"'loose' mode configuration must be the same for @babel/plugin-transform-class-properties, \" +\n          \"@babel/plugin-transform-private-methods and \" +\n          \"@babel/plugin-transform-private-property-in-object (when they are enabled).\" +\n          \"\\n\\n\" +\n          getBabelShowConfigForHint(file),\n      );\n    } else {\n      resolvedLoose = loose;\n\n      if (!process.env.BABEL_8_BREAKING) {\n        // eslint-disable-next-line no-var\n        var higherPriorityPluginName = name;\n      }\n    }\n  }\n\n  if (!process.env.BABEL_8_BREAKING && resolvedLoose !== undefined) {\n    for (const [mask, name] of featuresSameLoose) {\n      if (hasFeature(file, mask) && isLoose(file, mask) !== resolvedLoose) {\n        setLoose(file, mask, resolvedLoose);\n        console.warn(\n          `Though the \"loose\" option was set to \"${!resolvedLoose}\" in your @babel/preset-env ` +\n            `config, it will not be used for ${name} since the \"loose\" mode option was set to ` +\n            `\"${resolvedLoose}\" for ${higherPriorityPluginName}.\\nThe \"loose\" option must be the ` +\n            `same for @babel/plugin-transform-class-properties, @babel/plugin-transform-private-methods ` +\n            `and @babel/plugin-transform-private-property-in-object (when they are enabled): you can ` +\n            `silence this warning by explicitly adding\\n` +\n            `\\t[\"${name}\", { \"loose\": ${resolvedLoose} }]\\n` +\n            `to the \"plugins\" section of your Babel config.` +\n            \"\\n\\n\" +\n            getBabelShowConfigForHint(file),\n        );\n      }\n    }\n  }\n}\n\nfunction getBabelShowConfigForHint(file: File) {\n  let { filename } = file.opts;\n  if (!filename || filename === \"unknown\") {\n    filename = \"[name of the input file]\";\n  }\n  return `\\\nIf you already set the same 'loose' mode for these plugins in your config, it's possible that they \\\nare enabled multiple times with different options.\nYou can re-run Babel with the BABEL_SHOW_CONFIG_FOR environment variable to show the loaded \\\nconfiguration:\n\\tnpx cross-env BABEL_SHOW_CONFIG_FOR=${filename} <your build command>\nSee https://babeljs.io/docs/configuration#print-effective-configs for more info.`;\n}\n\nfunction hasFeature(file: File, feature: number) {\n  return !!(file.get(featuresKey) & feature);\n}\n\nexport function isLoose(file: File, feature: number) {\n  return !!(file.get(looseKey) & feature);\n}\n\nfunction setLoose(file: File, feature: number, loose: boolean) {\n  if (loose) file.set(looseKey, file.get(looseKey) | feature);\n  else file.set(looseKey, file.get(looseKey) & ~feature);\n\n  if (!process.env.BABEL_8_BREAKING) {\n    file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) & ~feature);\n  }\n}\n\nexport function shouldTransform(path: NodePath<t.Class>, file: File): boolean {\n  let decoratorPath: NodePath<t.Decorator> | null = null;\n  let publicFieldPath: NodePath<t.ClassProperty> | null = null;\n  let privateFieldPath: NodePath<t.ClassPrivateProperty> | null = null;\n  let privateMethodPath: NodePath<t.ClassPrivateMethod> | null = null;\n  let staticBlockPath: NodePath<t.StaticBlock> | null = null;\n\n  if (hasOwnDecorators(path.node)) {\n    decoratorPath = path.get(\"decorators.0\");\n  }\n  for (const el of path.get(\"body.body\")) {\n    if (!decoratorPath && hasOwnDecorators(el.node)) {\n      decoratorPath = el.get(\"decorators.0\");\n    }\n    if (!publicFieldPath && el.isClassProperty()) {\n      publicFieldPath = el;\n    }\n    if (!privateFieldPath && el.isClassPrivateProperty()) {\n      privateFieldPath = el;\n    }\n    // NOTE: path.isClassPrivateMethod() it isn't supported in <7.2.0\n    if (!privateMethodPath && el.isClassPrivateMethod?.()) {\n      privateMethodPath = el;\n    }\n    if (!staticBlockPath && el.isStaticBlock?.()) {\n      staticBlockPath = el;\n    }\n  }\n\n  if (decoratorPath && privateFieldPath) {\n    throw privateFieldPath.buildCodeFrameError(\n      \"Private fields in decorated classes are not supported yet.\",\n    );\n  }\n  if (decoratorPath && privateMethodPath) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Private methods in decorated classes are not supported yet.\",\n    );\n  }\n\n  if (decoratorPath && !hasFeature(file, FEATURES.decorators)) {\n    throw path.buildCodeFrameError(\n      \"Decorators are not enabled.\" +\n        \"\\nIf you are using \" +\n        '[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }], ' +\n        'make sure it comes *before* \"@babel/plugin-transform-class-properties\" ' +\n        \"and enable loose mode, like so:\\n\" +\n        '\\t[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }]\\n' +\n        '\\t[\"@babel/plugin-transform-class-properties\", { \"loose\": true }]',\n    );\n  }\n\n  if (privateMethodPath && !hasFeature(file, FEATURES.privateMethods)) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Class private methods are not enabled. \" +\n        \"Please add `@babel/plugin-transform-private-methods` to your configuration.\",\n    );\n  }\n\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    !hasFeature(file, FEATURES.fields) &&\n    // We want to allow enabling the private-methods plugin even without enabling\n    // the class-properties plugin. Class fields will still be compiled in classes\n    // that contain private methods.\n    // This is already allowed with the other various class features plugins, but\n    // it's because they can fallback to a transform separated from this helper.\n    !hasFeature(file, FEATURES.privateMethods)\n  ) {\n    throw path.buildCodeFrameError(\n      \"Class fields are not enabled. \" +\n        \"Please add `@babel/plugin-transform-class-properties` to your configuration.\",\n    );\n  }\n\n  if (staticBlockPath && !hasFeature(file, FEATURES.staticBlocks)) {\n    throw path.buildCodeFrameError(\n      \"Static class blocks are not enabled. \" +\n        \"Please add `@babel/plugin-transform-class-static-block` to your configuration.\",\n    );\n  }\n\n  if (decoratorPath || privateMethodPath || staticBlockPath) {\n    // If one of those feature is used we know that its transform is\n    // enabled, otherwise the previous checks throw.\n    return true;\n  }\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    hasFeature(file, FEATURES.fields)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;AAEA,IAAAA,WAAA,GAAAC,OAAA;AAEO,MAAMC,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAGE,MAAM,CAACC,MAAM,CAAC;EAEpCC,MAAM,EAAE,CAAC,IAAI,CAAC;EACdC,cAAc,EAAE,CAAC,IAAI,CAAC;EACtBC,UAAU,EAAE,CAAC,IAAI,CAAC;EAClBC,SAAS,EAAE,CAAC,IAAI,CAAC;EACjBC,YAAY,EAAE,CAAC,IAAI;AACrB,CAAC,CAAC;AAEF,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAChC,CAACV,QAAQ,CAACI,MAAM,EAAE,0CAA0C,CAAC,EAC7D,CAACJ,QAAQ,CAACK,cAAc,EAAE,yCAAyC,CAAC,EACpE,CAACL,QAAQ,CAACO,SAAS,EAAE,oDAAoD,CAAC,CAC3E,CAAC;AASF,MAAMI,WAAW,GAAG,0CAA0C;AAC9D,MAAMC,QAAQ,GAAG,uCAAuC;AAErB;EAUjC,IAAIC,mBAAmB,GACrB,6HAA6H;AACjI;AAEmC;EAEjC,IAAIC,cAAc,GAAG,SAAAA,CAAUC,IAAU,EAAEC,OAAe,EAAE;IAC1D,OAAO,CAAC,EAAED,IAAI,CAACE,GAAG,CAACJ,mBAAmB,CAAC,GAAGG,OAAO,CAAC;EACpD,CAAC;AACH;AAEO,SAASE,aAAaA,CAACH,IAAU,EAAEC,OAAe,EAAEG,KAAc,EAAE;EAYlE,IAAI,CAACC,UAAU,CAACL,IAAI,EAAEC,OAAO,CAAC,IAAIF,cAAc,CAACC,IAAI,EAAEC,OAAO,CAAC,EAAE;IACtED,IAAI,CAACM,GAAG,CAACV,WAAW,EAAEI,IAAI,CAACE,GAAG,CAACN,WAAW,CAAC,GAAGK,OAAO,CAAC;IACtD,IAEEG,KAAK,KACL,qFAAqF,EACrF;MACAG,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAE,IAAI,CAAC;MAC7BD,IAAI,CAACM,GAAG,CAACR,mBAAmB,EAAEE,IAAI,CAACE,GAAG,CAACJ,mBAAmB,CAAC,GAAGG,OAAO,CAAC;IACxE,CAAC,MAAM,IAELG,KAAK,KACL,qFAAqF,EACrF;MACAG,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAE,KAAK,CAAC;MAC9BD,IAAI,CAACM,GAAG,CAACR,mBAAmB,EAAEE,IAAI,CAACE,GAAG,CAACJ,mBAAmB,CAAC,GAAGG,OAAO,CAAC;IACxE,CAAC,MAAM;MACLM,QAAQ,CAACP,IAAI,EAAEC,OAAO,EAAEG,KAAK,CAAC;IAChC;EACF;EAEA,IAAII,aAAkC;EACtC,KAAK,MAAM,CAACC,IAAI,EAAEC,IAAI,CAAC,IAAIhB,iBAAiB,EAAE;IAC5C,IAAI,CAACW,UAAU,CAACL,IAAI,EAAES,IAAI,CAAC,EAAE;IACM;MACjC,IAAIV,cAAc,CAACC,IAAI,EAAES,IAAI,CAAC,EAAE;IAClC;IAEA,MAAML,KAAK,GAAGO,OAAO,CAACX,IAAI,EAAES,IAAI,CAAC;IAEjC,IAAID,aAAa,KAAK,CAACJ,KAAK,EAAE;MAC5B,MAAM,IAAIQ,KAAK,CACb,4FAA4F,GAC1F,8CAA8C,GAC9C,6EAA6E,GAC7E,MAAM,GACNC,yBAAyB,CAACb,IAAI,CAClC,CAAC;IACH,CAAC,MAAM;MACLQ,aAAa,GAAGJ,KAAK;MAEc;QAEjC,IAAIU,wBAAwB,GAAGJ,IAAI;MACrC;IACF;EACF;EAEA,IAAqCF,aAAa,KAAKO,SAAS,EAAE;IAChE,KAAK,MAAM,CAACN,IAAI,EAAEC,IAAI,CAAC,IAAIhB,iBAAiB,EAAE;MAC5C,IAAIW,UAAU,CAACL,IAAI,EAAES,IAAI,CAAC,IAAIE,OAAO,CAACX,IAAI,EAAES,IAAI,CAAC,KAAKD,aAAa,EAAE;QACnED,QAAQ,CAACP,IAAI,EAAES,IAAI,EAAED,aAAa,CAAC;QACnCQ,OAAO,CAACC,IAAI,CACV,yCAAyC,CAACT,aAAa,8BAA8B,GACnF,mCAAmCE,IAAI,4CAA4C,GACnF,IAAIF,aAAa,SAASM,wBAAwB,oCAAoC,GACtF,6FAA6F,GAC7F,0FAA0F,GAC1F,6CAA6C,GAC7C,OAAOJ,IAAI,iBAAiBF,aAAa,OAAO,GAChD,gDAAgD,GAChD,MAAM,GACNK,yBAAyB,CAACb,IAAI,CAClC,CAAC;MACH;IACF;EACF;AACF;AAEA,SAASa,yBAAyBA,CAACb,IAAU,EAAE;EAC7C,IAAI;IAAEkB;EAAS,CAAC,GAAGlB,IAAI,CAACmB,IAAI;EAC5B,IAAI,CAACD,QAAQ,IAAIA,QAAQ,KAAK,SAAS,EAAE;IACvCA,QAAQ,GAAG,0BAA0B;EACvC;EACA,OAAO;AACT;AACA;AACA;AACA;AACA,wCAAwCA,QAAQ;AAChD,iFAAiF;AACjF;AAEA,SAASb,UAAUA,CAACL,IAAU,EAAEC,OAAe,EAAE;EAC/C,OAAO,CAAC,EAAED,IAAI,CAACE,GAAG,CAACN,WAAW,CAAC,GAAGK,OAAO,CAAC;AAC5C;AAEO,SAASU,OAAOA,CAACX,IAAU,EAAEC,OAAe,EAAE;EACnD,OAAO,CAAC,EAAED,IAAI,CAACE,GAAG,CAACL,QAAQ,CAAC,GAAGI,OAAO,CAAC;AACzC;AAEA,SAASM,QAAQA,CAACP,IAAU,EAAEC,OAAe,EAAEG,KAAc,EAAE;EAC7D,IAAIA,KAAK,EAAEJ,IAAI,CAACM,GAAG,CAACT,QAAQ,EAAEG,IAAI,CAACE,GAAG,CAACL,QAAQ,CAAC,GAAGI,OAAO,CAAC,CAAC,KACvDD,IAAI,CAACM,GAAG,CAACT,QAAQ,EAAEG,IAAI,CAACE,GAAG,CAACL,QAAQ,CAAC,GAAG,CAACI,OAAO,CAAC;EAEnB;IACjCD,IAAI,CAACM,GAAG,CAACR,mBAAmB,EAAEE,IAAI,CAACE,GAAG,CAACJ,mBAAmB,CAAC,GAAG,CAACG,OAAO,CAAC;EACzE;AACF;AAEO,SAASmB,eAAeA,CAACC,IAAuB,EAAErB,IAAU,EAAW;EAC5E,IAAIsB,aAA2C,GAAG,IAAI;EACtD,IAAIC,eAAiD,GAAG,IAAI;EAC5D,IAAIC,gBAAyD,GAAG,IAAI;EACpE,IAAIC,iBAAwD,GAAG,IAAI;EACnE,IAAIC,eAA+C,GAAG,IAAI;EAE1D,IAAI,IAAAC,4BAAgB,EAACN,IAAI,CAACO,IAAI,CAAC,EAAE;IAC/BN,aAAa,GAAGD,IAAI,CAACnB,GAAG,CAAC,cAAc,CAAC;EAC1C;EACA,KAAK,MAAM2B,EAAE,IAAIR,IAAI,CAACnB,GAAG,CAAC,WAAW,CAAC,EAAE;IACtC,IAAI,CAACoB,aAAa,IAAI,IAAAK,4BAAgB,EAACE,EAAE,CAACD,IAAI,CAAC,EAAE;MAC/CN,aAAa,GAAGO,EAAE,CAAC3B,GAAG,CAAC,cAAc,CAAC;IACxC;IACA,IAAI,CAACqB,eAAe,IAAIM,EAAE,CAACC,eAAe,CAAC,CAAC,EAAE;MAC5CP,eAAe,GAAGM,EAAE;IACtB;IACA,IAAI,CAACL,gBAAgB,IAAIK,EAAE,CAACE,sBAAsB,CAAC,CAAC,EAAE;MACpDP,gBAAgB,GAAGK,EAAE;IACvB;IAEA,IAAI,CAACJ,iBAAiB,IAAII,EAAE,CAACG,oBAAoB,YAAvBH,EAAE,CAACG,oBAAoB,CAAG,CAAC,EAAE;MACrDP,iBAAiB,GAAGI,EAAE;IACxB;IACA,IAAI,CAACH,eAAe,IAAIG,EAAE,CAACI,aAAa,YAAhBJ,EAAE,CAACI,aAAa,CAAG,CAAC,EAAE;MAC5CP,eAAe,GAAGG,EAAE;IACtB;EACF;EAEA,IAAIP,aAAa,IAAIE,gBAAgB,EAAE;IACrC,MAAMA,gBAAgB,CAACU,mBAAmB,CACxC,4DACF,CAAC;EACH;EACA,IAAIZ,aAAa,IAAIG,iBAAiB,EAAE;IACtC,MAAMA,iBAAiB,CAACS,mBAAmB,CACzC,6DACF,CAAC;EACH;EAEA,IAAIZ,aAAa,IAAI,CAACjB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACM,UAAU,CAAC,EAAE;IAC3D,MAAM8B,IAAI,CAACa,mBAAmB,CAC5B,6BAA6B,GAC3B,qBAAqB,GACrB,kEAAkE,GAClE,yEAAyE,GACzE,mCAAmC,GACnC,oEAAoE,GACpE,mEACJ,CAAC;EACH;EAEA,IAAIT,iBAAiB,IAAI,CAACpB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACK,cAAc,CAAC,EAAE;IACnE,MAAMmC,iBAAiB,CAACS,mBAAmB,CACzC,yCAAyC,GACvC,6EACJ,CAAC;EACH;EAEA,IACE,CAACX,eAAe,IAAIC,gBAAgB,KACpC,CAACnB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACI,MAAM,CAAC,IAMlC,CAACgB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACK,cAAc,CAAC,EAC1C;IACA,MAAM+B,IAAI,CAACa,mBAAmB,CAC5B,gCAAgC,GAC9B,8EACJ,CAAC;EACH;EAEA,IAAIR,eAAe,IAAI,CAACrB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACQ,YAAY,CAAC,EAAE;IAC/D,MAAM4B,IAAI,CAACa,mBAAmB,CAC5B,uCAAuC,GACrC,gFACJ,CAAC;EACH;EAEA,IAAIZ,aAAa,IAAIG,iBAAiB,IAAIC,eAAe,EAAE;IAGzD,OAAO,IAAI;EACb;EACA,IACE,CAACH,eAAe,IAAIC,gBAAgB,KACpCnB,UAAU,CAACL,IAAI,EAAEf,QAAQ,CAACI,MAAM,CAAC,EACjC;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd", "ignoreList": []}