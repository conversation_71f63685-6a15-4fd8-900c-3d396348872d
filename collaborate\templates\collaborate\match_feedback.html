{% extends "base.html" %}
{% load static %}
{% block title %}Match Feedback - {{ project.title }}{% endblock %}

{% block extra_css %}
<style>
/* Match Feedback Page - Dashboard Style Internal CSS */

/* Dashboard Section Styling */
.dashboard-section {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 2rem;
  border: 1px solid rgba(192, 255, 107, 0.2);
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.dashboard-section h2 {
  color: #C0ff6b;
  margin-bottom: 25px;
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.dashboard-section h3 {
  color: #C0ff6b;
  margin-bottom: 20px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Stats Grid - Dashboard Style */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  min-height: 120px;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.6);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.25);
}

.stat-icon {
  font-size: 2.5rem;
  color: #C0ff6b;
  width: 60px;
  text-align: center;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label {
  color: #ffffff;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
}

/* Page Header Styling */
.collaborate-card h1 {
  color: #ffffff;
  font-size: 2.2rem;
  margin-bottom: 20px;
  text-align: center;
}

.collaborate-card .lead {
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin-bottom: 30px;
  font-size: 1.1rem;
}

/* Rating Display */
.rating-display {
  text-align: center;
  margin: 25px 0;
}

.star-rating-large {
  font-size: 32px;
  color: #C0ff6b;
  margin: 20px 0;
}

.star-rating-large .empty {
  color: rgba(255, 255, 255, 0.3);
}

/* Rating Distribution */
.rating-distribution {
  margin: 30px 0;
  padding: 25px;
  background: rgba(60, 60, 60, 0.3);
  border-radius: 15px;
}

.rating-bars {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.rating-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 12px 0;
}

.rating-label {
  color: #ffffff;
  font-weight: 500;
  min-width: 100px;
  font-size: 15px;
}

.bar-container {
  flex: 1;
  height: 16px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #C0ff6b, #a0e066);
  border-radius: 8px;
  transition: width 0.5s ease;
  box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3);
}

.rating-count {
  color: #C0ff6b;
  font-weight: 600;
  min-width: 50px;
  text-align: right;
  font-size: 16px;
}

/* Aspect Performance */
.aspects-performance {
  display: grid;
  gap: 25px;
  margin-top: 25px;
}

.aspect-performance-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px 30px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
  min-height: 70px;
}

.aspect-performance-item:hover {
  transform: translateY(-3px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.15);
}

.aspect-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.aspect-info i {
  color: #C0ff6b;
  font-size: 24px;
  width: 30px;
  text-align: center;
}

.aspect-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 16px;
}

.aspect-rating {
  color: #C0ff6b;
  font-weight: 600;
  font-size: 20px;
  padding: 8px 15px;
  background: rgba(192, 255, 107, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

/* Feedback Grid */
.feedback-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.feedback-card-item {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
  min-height: 150px;
}

.feedback-card-item:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 10px 30px rgba(192, 255, 107, 0.15);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.feedback-user {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #C0ff6b;
  font-weight: 600;
  font-size: 16px;
}

.feedback-user i {
  font-size: 18px;
}

.feedback-meta {
  text-align: right;
}

.feedback-date {
  color: rgba(255, 255, 255, 0.7);
  font-size: 13px;
  margin-bottom: 8px;
}

.feedback-stars {
  color: #C0ff6b;
  font-size: 16px;
}

.feedback-stars .empty {
  color: rgba(255, 255, 255, 0.3);
}

.feedback-content {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 15px;
}

/* Suggestions Grid */
.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.suggestion-card {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px;
  border-left: 5px solid;
  transition: all 0.3s ease;
  min-height: 160px;
}

.suggestion-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.suggestion-card.high {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

.suggestion-card.medium {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.suggestion-card.low {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-header i {
  font-size: 24px;
  flex-shrink: 0;
}

.suggestion-card.high .suggestion-header i { color: #dc3545; }
.suggestion-card.medium .suggestion-header i { color: #ffc107; }
.suggestion-card.low .suggestion-header i { color: #28a745; }

.suggestion-title {
  color: #C0ff6b;
  font-weight: 600;
  font-size: 18px;
}

.suggestion-rating {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  margin-bottom: 15px;
  font-weight: 500;
}

.suggestion-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-size: 15px;
}

/* Info Box */
.info-box {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: rgba(40, 40, 40, 0.6);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px 30px;
  margin: 30px 0;
  border-left: 4px solid #C0ff6b;
}

.info-box i {
  font-size: 28px;
  color: #C0ff6b;
  margin-top: 3px;
  flex-shrink: 0;
}

.info-box div strong {
  color: #C0ff6b;
  display: block;
  margin-bottom: 12px;
  font-size: 18px;
}

.info-box div p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

/* Status Alerts */
.status-alert {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 25px 30px;
  border-radius: 15px;
  margin: 30px 0;
  border-left: 5px solid;
}

.status-alert.success {
  background: rgba(40, 167, 69, 0.15);
  border: 1px solid rgba(40, 167, 69, 0.3);
  border-left-color: #28a745;
}

.status-alert.warning {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-left-color: #ffc107;
}

.status-alert.danger {
  background: rgba(220, 53, 69, 0.15);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-left-color: #dc3545;
}

.status-alert.info {
  background: rgba(13, 202, 240, 0.15);
  border: 1px solid rgba(13, 202, 240, 0.3);
  border-left-color: #0dcaf0;
}

.status-alert i {
  font-size: 28px;
  margin-top: 3px;
  flex-shrink: 0;
}

.status-alert.success i { color: #28a745; }
.status-alert.warning i { color: #ffc107; }
.status-alert.danger i { color: #dc3545; }
.status-alert.info i { color: #0dcaf0; }

.status-alert div strong {
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 18px;
  display: block;
}

.status-alert div p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 50px;
  padding: 30px;
  flex-wrap: wrap;
  background: rgba(40, 40, 40, 0.3);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 60px 30px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(40, 40, 40, 0.3);
  border-radius: 15px;
  border: 2px dashed rgba(192, 255, 107, 0.3);
  margin: 30px 0;
}

.no-data i {
  font-size: 64px;
  color: rgba(192, 255, 107, 0.5);
  margin-bottom: 20px;
  display: block;
}

.no-data strong {
  display: block;
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 20px;
}

.no-data p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

.no-data div {
  color: rgba(255, 255, 255, 0.6);
}

.no-data div strong {
  color: #ffffff;
  font-size: 20px;
  margin-bottom: 12px;
}

.no-data div p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  line-height: 1.5;
}

/* Feedback Form Styling */
.feedback-form-section {
  margin-bottom: 40px;
}

.feedback-form {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.form-section {
  margin-bottom: 35px;
  padding: 25px;
  background: rgba(60, 60, 60, 0.3);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.form-section h3 {
  color: #C0ff6b;
  margin-bottom: 15px;
  font-size: 1.3rem;
  font-weight: 600;
}

.section-desc {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
  font-size: 0.95rem;
}

/* Star Rating */
.star-rating-container {
  text-align: center;
  margin: 20px 0;
}

.rating-stars {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 15px;
}

.rating-stars input[type="radio"] {
  display: none;
}

.rating-stars label {
  font-size: 2rem;
  color: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.rating-stars label:hover,
.rating-stars input[type="radio"]:checked ~ label,
.rating-stars label:hover ~ label {
  color: #C0ff6b;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.rating-text {
  color: #C0ff6b;
  font-weight: 500;
  font-size: 1.1rem;
}

/* Aspect Sliders */
.aspects-grid {
  display: grid;
  gap: 25px;
}

.aspect-item {
  background: rgba(40, 40, 40, 0.5);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.aspect-label {
  display: block;
  color: #C0ff6b;
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 1rem;
}

.aspect-desc {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  font-weight: 400;
  margin-top: 5px;
}

.aspect-slider {
  width: 100%;
  height: 8px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  margin: 15px 0;
  cursor: pointer;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
}

/* Feedback Textarea */
.feedback-textarea {
  width: 100%;
  min-height: 120px;
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 10px;
  padding: 15px;
  color: #ffffff;
  font-size: 0.95rem;
  resize: vertical;
  transition: all 0.3s ease;
}

.feedback-textarea:focus {
  outline: none;
  border-color: #C0ff6b;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.form-help {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  margin-top: 10px;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .feedback-grid {
    grid-template-columns: 1fr;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .aspects-performance {
    gap: 15px;
  }

  .aspect-performance-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .rating-bar {
    flex-direction: column;
    gap: 10px;
  }

  .rating-label {
    min-width: auto;
  }

  .collaborate-card h1 {
    font-size: 1.8rem;
  }

  .rating-stars {
    gap: 5px;
  }

  .rating-stars label {
    font-size: 1.5rem;
  }
}
</style>
{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="tile-wrap">
    <div class="collaborate-card fade-in">

      {% if form %}
        <!-- Feedback Form Section -->
        <h1><i class="fas fa-comment-dots me-3"></i>Provide Team Match Feedback</h1>
        <p class="lead">Your feedback helps us improve our team matching algorithm for "{{ project.title }}"</p>

        <div class="feedback-form-section">
          <div class="info-box">
            <i class="fas fa-info-circle"></i>
            <div>
              <strong>Why Your Feedback Matters:</strong>
              <p>Your input helps us refine our matching algorithm to create more effective teams. All feedback is anonymous and used only for improving the system.</p>
            </div>
          </div>

        <form method="post" action="{% url 'collaborate:submit_match_feedback' project.hash %}" id="feedbackForm" class="feedback-form">
          {% csrf_token %}

          <!-- Overall Rating Section -->
          <div class="form-section">
            <h3><i class="fas fa-star me-2"></i>Overall Match Quality</h3>
            <p class="section-desc">How well did our algorithm match you with suitable teammates?</p>

            <div class="star-rating-container">
              <div class="rating-stars">
                <input type="radio" name="rating" id="star5" value="5" required>
                <label for="star5" title="Excellent"><i class="fas fa-star"></i></label>

                <input type="radio" name="rating" id="star4" value="4">
                <label for="star4" title="Very Good"><i class="fas fa-star"></i></label>

                <input type="radio" name="rating" id="star3" value="3">
                <label for="star3" title="Good"><i class="fas fa-star"></i></label>

                <input type="radio" name="rating" id="star2" value="2">
                <label for="star2" title="Fair"><i class="fas fa-star"></i></label>

                <input type="radio" name="rating" id="star1" value="1">
                <label for="star1" title="Poor"><i class="fas fa-star"></i></label>
              </div>
              <div class="rating-text">
                <span id="ratingText">Select a rating</span>
              </div>
            </div>
          </div>
          <!-- Specific Aspects Section -->
          <div class="form-section">
            <h3><i class="fas fa-sliders-h me-2"></i>Specific Aspects</h3>
            <p class="section-desc">Rate how well the matching performed in these areas:</p>

            <div class="aspects-grid">
              <div class="aspect-item">
                <label class="aspect-label">
                  <i class="fas fa-code me-2"></i>Skill Match
                  <span class="aspect-desc">How well did team members' skills complement each other?</span>
                </label>
                <input type="range" class="aspect-slider" min="1" max="5" value="3" name="match_aspects[skill_match]" id="skillMatchRange">
                <div class="slider-labels">
                  <span>Poor</span>
                  <span>Fair</span>
                  <span>Good</span>
                  <span>Very Good</span>
                  <span>Excellent</span>
                </div>
              </div>

              <div class="aspect-item">
                <label class="aspect-label">
                  <i class="fas fa-globe me-2"></i>Timezone Compatibility
                  <span class="aspect-desc">How well did team members' availability align?</span>
                </label>
                <input type="range" class="aspect-slider" min="1" max="5" value="3" name="match_aspects[timezone_compatibility]" id="timezoneRange">
                <div class="slider-labels">
                  <span>Poor</span>
                  <span>Fair</span>
                  <span>Good</span>
                  <span>Very Good</span>
                  <span>Excellent</span>
                </div>
              </div>

              <div class="aspect-item">
                <label class="aspect-label">
                  <i class="fas fa-balance-scale me-2"></i>Team Balance
                  <span class="aspect-desc">How well balanced were experience levels in the team?</span>
                </label>
                <input type="range" class="aspect-slider" min="1" max="5" value="3" name="match_aspects[team_balance]" id="balanceRange">
                <div class="slider-labels">
                  <span>Poor</span>
                  <span>Fair</span>
                  <span>Good</span>
                  <span>Very Good</span>
                  <span>Excellent</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Additional Feedback Section -->
          <div class="form-section">
            <h3><i class="fas fa-comment-alt me-2"></i>Additional Feedback</h3>
            <textarea class="feedback-textarea" id="feedbackText" name="feedback_text" rows="4"
                      placeholder="Please share any additional thoughts on the team matching process, what worked well, and what could be improved..."></textarea>
            <div class="form-help">
              <i class="fas fa-lightbulb me-1"></i>Your specific suggestions help us improve the matching algorithm.
            </div>
          </div>

          <!-- Submit Button -->
          <div class="form-actions">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-paper-plane me-2"></i>Submit Feedback
            </button>
          </div>
        </form>
      </div>

      {% else %}
        <h1><i class="fas fa-chart-bar me-3"></i>Team Match Feedback Summary</h1>
        <p class="lead">Insights from team members about the matching algorithm performance for "{{ project.title }}"</p>

        <!-- Overall Statistics Section -->
        <div class="dashboard-section">
          <h2><i class="fas fa-star me-2"></i>Overall Rating Summary</h2>

          <div class="dashboard-stats">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-star"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ feedback_summary.average_rating|floatformat:1 }}</div>
                <div class="stat-label">Average Rating</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-users"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ feedback_summary.feedback_count }}</div>
                <div class="stat-label">Total Feedback</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon">
                <i class="fas fa-thumbs-up"></i>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ feedback_summary.rating_counts.5|default:"0"|add:feedback_summary.rating_counts.4|default:"0" }}</div>
                <div class="stat-label">Positive Reviews</div>
              </div>
            </div>
          </div>

        <div class="rating-display">
          <div class="star-rating-large">
            {% for i in "12345"|make_list %}
              {% if forloop.counter <= feedback_summary.average_rating|floatformat:"0" %}
                <i class="fas fa-star"></i>
              {% else %}
                <i class="fas fa-star empty"></i>
              {% endif %}
            {% endfor %}
          </div>
        </div>

        {% if feedback_summary.average_rating >= 4 %}
          <div class="status-alert success">
            <i class="fas fa-thumbs-up"></i>
            <div>
              <strong>Excellent Feedback!</strong>
              <p>Team members are very satisfied with the matching algorithm performance.</p>
            </div>
          </div>
        {% elif feedback_summary.average_rating >= 3 %}
          <div class="status-alert info">
            <i class="fas fa-info-circle"></i>
            <div>
              <strong>Good Performance</strong>
              <p>Team members are generally satisfied with the matching results.</p>
            </div>
          </div>
        {% else %}
          <div class="status-alert warning">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
              <strong>Room for Improvement</strong>
              <p>The matching algorithm could be enhanced based on team feedback.</p>
            </div>
          </div>
          {% endif %}
        </div>

        <!-- Rating Distribution Section -->
        <div class="dashboard-section">
          <h2><i class="fas fa-chart-pie me-2"></i>Rating Distribution</h2>

          <div class="rating-distribution">
            <div class="rating-bars">
              {% for i in "54321"|make_list %}
                <div class="rating-bar">
                  <span class="rating-label">{{ i }} Star{{ i|pluralize }}</span>
                  <div class="bar-container">
                    <div class="bar-fill" style="width: {% if feedback_summary.feedback_count > 0 %}{% widthratio feedback_summary.rating_counts|default_if_none:0 feedback_summary.feedback_count 100 %}{% else %}0{% endif %}%"></div>
                  </div>
                  <span class="rating-count">{{ feedback_summary.rating_counts.i|default:"0" }}</span>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>

        <!-- Aspect Ratings Section -->
        <div class="dashboard-section">
          <h2><i class="fas fa-sliders-h me-2"></i>Aspect Performance</h2>

          <div class="aspects-performance">
            <div class="aspect-performance-item">
              <div class="aspect-info">
                <i class="fas fa-code"></i>
                <span class="aspect-name">Skill Match</span>
              </div>
              <div class="aspect-rating">{{ feedback_summary.aspect_ratings.skill_match|default:"0"|floatformat:1 }}/5</div>
            </div>

            <div class="aspect-performance-item">
              <div class="aspect-info">
                <i class="fas fa-globe"></i>
                <span class="aspect-name">Timezone Compatibility</span>
              </div>
              <div class="aspect-rating">{{ feedback_summary.aspect_ratings.timezone_compatibility|default:"0"|floatformat:1 }}/5</div>
            </div>

            <div class="aspect-performance-item">
              <div class="aspect-info">
                <i class="fas fa-balance-scale"></i>
                <span class="aspect-name">Team Balance</span>
              </div>
              <div class="aspect-rating">{{ feedback_summary.aspect_ratings.team_balance|default:"0"|floatformat:1 }}/5</div>
            </div>
          </div>
        </div>

        <!-- Recent Feedback Section -->
        <div class="dashboard-section">
          <h2><i class="fas fa-comments me-2"></i>Recent Feedback</h2>

          {% if feedback_summary.recent_feedback %}
            <div class="feedback-grid">
              {% for feedback in feedback_summary.recent_feedback|slice:":6" %}
                <div class="feedback-card-item">
                  <div class="feedback-header">
                    <div class="feedback-user">
                      <i class="fas fa-user-circle"></i>
                      <span>{{ feedback.user.username }}</span>
                    </div>
                    <div class="feedback-meta">
                      <span class="feedback-date">{{ feedback.created_at|date:"M d, Y" }}</span>
                      <div class="feedback-stars">
                        {% for i in "12345"|make_list %}
                          {% if forloop.counter <= feedback.rating %}
                            <i class="fas fa-star"></i>
                          {% else %}
                            <i class="fas fa-star empty"></i>
                          {% endif %}
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                  <div class="feedback-content">
                    {% if feedback.feedback_text %}
                      {{ feedback.feedback_text|truncatechars:150 }}
                    {% else %}
                      <em>No additional comments provided.</em>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="no-data">
              <i class="fas fa-info-circle"></i>
              <div>
                <strong>No feedback submitted yet</strong>
                <p>Be the first to provide feedback on this team match!</p>
              </div>
            </div>
          {% endif %}
        </div>

        <!-- Improvement Suggestions Section -->
        {% if feedback_summary.improvement_areas %}
          <div class="dashboard-section">
            <h2><i class="fas fa-lightbulb me-2"></i>Suggested Improvements</h2>

            <div class="info-box">
              <i class="fas fa-info-circle"></i>
              <div>
                <strong>Based on team feedback:</strong>
                <p>Here are areas where the matching algorithm could be enhanced.</p>
              </div>
            </div>

            <div class="suggestions-grid">
              {% for area in feedback_summary.improvement_areas|slice:":3" %}
                <div class="suggestion-card {% if area.rating < 2.5 %}high{% elif area.rating < 3.5 %}medium{% else %}low{% endif %}">
                  <div class="suggestion-header">
                    <i class="fas fa-{% if area.rating < 2.5 %}exclamation-triangle{% elif area.rating < 3.5 %}info-circle{% else %}check-circle{% endif %}"></i>
                    <span class="suggestion-title">{{ area.aspect }}</span>
                  </div>
                  <div class="suggestion-rating">
                    <span>Current Rating: {{ area.rating|floatformat:1 }}/5</span>
                  </div>
                  <p class="suggestion-text">{{ area.suggestion }}</p>
                </div>
              {% endfor %}
            </div>
          </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
          <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Project
          </a>
          {% if not user_has_feedback %}
            <a href="{% url 'collaborate:submit_match_feedback' project.hash %}" class="btn btn-primary">
              <i class="fas fa-comment-dots me-2"></i>Submit Your Feedback
            </a>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);

  // Star rating functionality for feedback form
  const ratingInputs = document.querySelectorAll('input[name="rating"]');
  const ratingText = document.getElementById('ratingText');

  if (ratingInputs.length > 0 && ratingText) {
    const ratingLabels = {
      '1': 'Poor - The match did not meet expectations',
      '2': 'Fair - The match had some issues',
      '3': 'Good - The match was acceptable',
      '4': 'Very Good - The match exceeded expectations',
      '5': 'Excellent - The match was perfect'
    };

    ratingInputs.forEach(input => {
      input.addEventListener('change', function() {
        ratingText.textContent = ratingLabels[this.value];
      });
    });
  }

  // Slider functionality for aspect ratings
  const sliders = document.querySelectorAll('.aspect-slider');
  sliders.forEach(slider => {
    slider.addEventListener('input', function() {
      // Visual feedback for sliders can be added here
      const value = this.value;
      const percentage = ((value - 1) / 4) * 100;
      this.style.background = `linear-gradient(to right, var(--color-border) ${percentage}%, rgba(255,255,255,0.2) ${percentage}%)`;
    });

    // Initialize slider appearance
    const event = new Event('input');
    slider.dispatchEvent(event);
  });
});
</script>
{% endblock %}
