from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.db.models import Q, Avg, Count, Sum
from django.db import models
from django.core.paginator import Paginator
from datetime import datetime, timedelta
import json
from decimal import Decimal

from .models import MentorProfile, MentorshipSession, SessionFeedback, MentorAvailability, ChatMessage
from collaborate.models import Skill

def test_template(request):
    """Test template rendering"""
    return render(request, 'mentorship_test.html')

def mentor_marketplace(request):
    """Display the mentor marketplace with filtering and search"""

    # Get all active mentors (exclude current user if they are a mentor)
    mentors = MentorProfile.objects.filter(
        is_active=True,
        profile_completed=True
    ).select_related('user', 'user__profile').prefetch_related('specializations')

    # Exclude current user from mentor list if they are authenticated
    if request.user.is_authenticated:
        mentors = mentors.exclude(user=request.user)

    # Apply filters
    skill_filter = request.GET.get('skill')
    if skill_filter:
        mentors = mentors.filter(specializations__name__icontains=skill_filter)

    min_rate = request.GET.get('min_rate')
    max_rate = request.GET.get('max_rate')
    if min_rate:
        mentors = mentors.filter(hourly_rate__gte=min_rate)
    if max_rate:
        mentors = mentors.filter(hourly_rate__lte=max_rate)

    rating_filter = request.GET.get('min_rating')
    if rating_filter:
        mentors = mentors.filter(average_rating__gte=rating_filter)

    # Search functionality
    search_query = request.GET.get('search')
    if search_query:
        mentors = mentors.filter(
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(user__username__icontains=search_query) |
            Q(bio__icontains=search_query) |
            Q(specializations__name__icontains=search_query)
        ).distinct()

    # Sorting
    sort_by = request.GET.get('sort', 'rating')
    if sort_by == 'rating':
        mentors = mentors.order_by('-average_rating', '-total_sessions')
    elif sort_by == 'price_low':
        mentors = mentors.order_by('hourly_rate')
    elif sort_by == 'price_high':
        mentors = mentors.order_by('-hourly_rate')
    elif sort_by == 'experience':
        mentors = mentors.order_by('-total_sessions')

    # Pagination
    paginator = Paginator(mentors, 12)  # Show 12 mentors per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all skills for filter dropdown
    all_skills = Skill.objects.all().order_by('name')

    context = {
        'page_obj': page_obj,
        'all_skills': all_skills,
        'current_filters': {
            'skill': skill_filter,
            'min_rate': min_rate,
            'max_rate': max_rate,
            'min_rating': rating_filter,
            'search': search_query,
            'sort': sort_by,
        }
    }

    return render(request, 'mentorship/marketplace.html', context)

def mentor_detail(request, mentor_id):
    """Display detailed mentor profile with booking option"""

    mentor_profile = get_object_or_404(
        MentorProfile.objects.select_related('user', 'user__profile').prefetch_related('specializations'),
        id=mentor_id,
        is_active=True,
        profile_completed=True
    )

    # Get recent feedback for this mentor
    recent_feedback = SessionFeedback.objects.filter(
        session__mentor=mentor_profile.user,
        session__status='completed'
    ).select_related('session__learner').order_by('-created_at')[:5]

    # Get mentor's availability for the next 30 days
    today = timezone.now().date()
    future_date = today + timedelta(days=30)

    available_slots = MentorAvailability.objects.filter(
        mentor=mentor_profile.user,
        date__gte=today,
        date__lte=future_date,
        is_booked=False
    ).order_by('date', 'start_time')

    context = {
        'mentor_profile': mentor_profile,
        'recent_feedback': recent_feedback,
        'available_slots': available_slots,
    }

    return render(request, 'mentorship/mentor_detail.html', context)

@login_required
@require_http_methods(["POST"])
def book_session(request, mentor_id):
    """Handle session booking"""

    mentor_profile = get_object_or_404(MentorProfile, id=mentor_id, is_active=True)

    # Prevent users from booking sessions with themselves
    if request.user == mentor_profile.user:
        return JsonResponse({
            'error': 'You cannot book a session with yourself. Please select a different mentor.'
        }, status=400)

    try:
        data = json.loads(request.body)
        availability_id = data.get('availability_id')
        duration_minutes = int(data.get('duration_minutes', 60))

        # Validate duration
        if duration_minutes not in [60, 120]:
            return JsonResponse({'error': 'Invalid duration'}, status=400)

        # Get the availability slot
        availability = get_object_or_404(
            MentorAvailability,
            id=availability_id,
            mentor=mentor_profile.user,
            is_booked=False
        )

        # Check if slot can accommodate the duration
        if not availability.is_available_for_duration(duration_minutes):
            return JsonResponse({'error': 'Slot cannot accommodate requested duration'}, status=400)

        # Create the session
        scheduled_datetime = datetime.combine(availability.date, availability.start_time)
        scheduled_datetime = timezone.make_aware(scheduled_datetime)

        session = MentorshipSession.objects.create(
            learner=request.user,
            mentor=mentor_profile.user,
            scheduled_time=scheduled_datetime,
            duration_minutes=duration_minutes,
            hourly_rate=mentor_profile.hourly_rate,
            status='scheduled'
        )

        # Mark availability as booked
        availability.is_booked = True
        availability.session = session
        availability.save()

        return JsonResponse({
            'success': True,
            'session_id': session.id,
            'room_id': str(session.room_id),
            'total_amount': float(session.total_amount),
            'redirect_url': f'/mentorship/payment/{session.id}/'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def session_room(request, room_id):
    """Display the mentorship session room with Monaco editor and voice chat"""

    session = get_object_or_404(
        MentorshipSession.objects.select_related('learner', 'mentor', 'learner__profile', 'mentor__profile'),
        room_id=room_id,
        status__in=['scheduled', 'active']
    )

    # Check if user is authorized to access this session
    if request.user not in [session.learner, session.mentor]:
        return HttpResponseForbidden("You are not authorized to access this session.")

    # Check if payment is required and completed
    if session.is_payment_required():
        messages.error(request, 'Payment is required before accessing this session.')
        return redirect('mentorship:payment_page', session_id=session.id)

    # Check if session can be started
    if session.status == 'scheduled' and session.can_start():
        # Auto-start the session if it's time
        session.status = 'active'
        session.started_at = timezone.now()
        session.save()

    # Determine user role
    user_role = 'mentor' if request.user == session.mentor else 'learner'

    # Calculate remaining time and session timing info
    remaining_seconds = session.get_remaining_time_seconds()
    session_started = session.status == 'active' and session.started_at is not None

    context = {
        'session': session,
        'user_role': user_role,
        'room_id': str(room_id),
        'can_edit': user_role == 'mentor',  # Only mentor can edit by default
        'session_duration_ms': session.duration_minutes * 60 * 1000,  # Convert to milliseconds for JS
        'remaining_time_seconds': remaining_seconds,
        'session_started': session_started,
        'session_start_timestamp': session.started_at.timestamp() * 1000 if session.started_at else None,  # JS timestamp
        'scheduled_start_timestamp': session.scheduled_time.timestamp() * 1000,  # JS timestamp
    }

    return render(request, 'mentorship/session_room.html', context)

@login_required
def my_sessions(request):
    """Display user's mentorship sessions (both as learner and mentor)"""

    # Get sessions where user is a learner
    learner_sessions = MentorshipSession.objects.filter(
        learner=request.user
    ).select_related('mentor').order_by('-scheduled_time')

    # Get sessions where user is a mentor
    mentor_sessions = MentorshipSession.objects.filter(
        mentor=request.user
    ).select_related('learner').order_by('-scheduled_time')

    # Filter by status if requested
    status_filter = request.GET.get('status')
    if status_filter:
        learner_sessions = learner_sessions.filter(status=status_filter)
        mentor_sessions = mentor_sessions.filter(status=status_filter)

    context = {
        'learner_sessions': learner_sessions,
        'mentor_sessions': mentor_sessions,
        'status_filter': status_filter,
    }

    return render(request, 'mentorship/my_sessions.html', context)

@login_required
def session_details(request, session_id):
    """Display detailed information about a mentorship session"""

    print(f"DEBUG: session_details called with session_id: {session_id}")
    print(f"DEBUG: User: {request.user.username}")
    print(f"DEBUG: Request method: {request.method}")
    print(f"DEBUG: Request path: {request.path}")

    try:
        # Get the session and ensure user has access to it
        session = get_object_or_404(
            MentorshipSession.objects.select_related('learner', 'mentor', 'learner__profile', 'mentor__profile'),
            id=session_id
        )

        print(f"DEBUG: Found session {session.id}, learner: {session.learner.username}, mentor: {session.mentor.username}")

        # Check if user is authorized to view this session
        if request.user not in [session.learner, session.mentor]:
            messages.error(request, 'You are not authorized to view this session.')
            return redirect('mentorship:my_sessions')

        # Determine user role
        user_role = 'learner' if request.user == session.learner else 'mentor'

        # Get mentor profile for additional details
        mentor_profile = None
        if hasattr(session.mentor, 'mentor_profile'):
            mentor_profile = session.mentor.mentor_profile

        # Calculate session timing information
        now = timezone.now()
        time_until_session = None
        time_since_session = None

        if session.scheduled_time > now:
            time_until_session = session.scheduled_time - now
        else:
            time_since_session = now - session.scheduled_time

        # Get feedback if it exists
        feedback = None
        if hasattr(session, 'detailed_feedback'):
            feedback = session.detailed_feedback

        context = {
            'session': session,
            'user_role': user_role,
            'mentor_profile': mentor_profile,
            'time_until_session': time_until_session,
            'time_since_session': time_since_session,
            'feedback': feedback,
            'can_join': session.can_start() if hasattr(session, 'can_start') else False,
            'is_payment_required': session.is_payment_required() if hasattr(session, 'is_payment_required') else False,
        }

        print(f"DEBUG: Rendering session_details.html with context keys: {list(context.keys())}")
        return render(request, 'mentorship/session_details.html', context)

    except Exception as e:
        print(f"ERROR in session_details view: {str(e)}")
        import traceback
        traceback.print_exc()
        messages.error(request, 'Session not found or not accessible.')
        return redirect('mentorship:my_sessions')

@login_required
def session_feedback(request, session_id):
    """Display feedback page for a completed session"""

    try:
        session = get_object_or_404(
            MentorshipSession,
            id=session_id,
            learner=request.user,
            status='completed'
        )

        # Check if feedback already exists
        if hasattr(session, 'detailed_feedback'):
            messages.info(request, 'You have already submitted feedback for this session.')
            return redirect('mentorship:my_sessions')

        context = {
            'session': session,
            'mentor': session.mentor,
        }

        return render(request, 'mentorship/session_feedback.html', context)

    except Exception as e:
        print(f"Error in session_feedback view: {str(e)}")
        messages.error(request, 'Session not found or not accessible.')
        return redirect('mentorship:my_sessions')

@login_required
@require_http_methods(["POST"])
def end_session(request, room_id):
    """End a mentorship session and update status"""

    try:
        print(f"DEBUG: Attempting to end session with room_id: {room_id}")
        print(f"DEBUG: User: {request.user.username} (ID: {request.user.id})")

        # First, let's find the session without status restriction to see what's happening
        try:
            session = MentorshipSession.objects.get(room_id=room_id)
            print(f"DEBUG: Found session {session.id} with status: {session.status}")
            print(f"DEBUG: Session learner: {session.learner.username} (ID: {session.learner.id})")
            print(f"DEBUG: Session mentor: {session.mentor.username} (ID: {session.mentor.id})")
        except MentorshipSession.DoesNotExist:
            print(f"DEBUG: No session found with room_id: {room_id}")
            return JsonResponse({'error': 'Session not found'}, status=404)

        # Check if session can be ended
        if session.status not in ['scheduled', 'active']:
            print(f"DEBUG: Session status '{session.status}' cannot be ended")
            return JsonResponse({'error': f'Session is already {session.status}'}, status=400)

        # Check if user is authorized to end this session
        if request.user not in [session.learner, session.mentor]:
            print(f"DEBUG: User {request.user.username} not authorized to end session")
            return JsonResponse({'error': 'Unauthorized'}, status=403)

        # Update session status
        old_status = session.status
        session.status = 'completed'
        session.ended_at = timezone.now()
        session.save()

        print(f"DEBUG: Session status updated from '{old_status}' to 'completed'")

        # Return different redirect URLs based on user role
        if request.user == session.learner:
            redirect_url = reverse('mentorship:session_feedback', args=[session.id])
            print(f"DEBUG: Learner redirect URL: {redirect_url}")
        else:
            redirect_url = reverse('mentorship:my_sessions')
            print(f"DEBUG: Mentor redirect URL: {redirect_url}")

        return JsonResponse({
            'success': True,
            'redirect_url': redirect_url,
            'message': 'Session ended successfully'
        })

    except Exception as e:
        # Log the error for debugging
        print(f"ERROR: Exception in end_session view: {str(e)}")
        import traceback
        traceback.print_exc()

        return JsonResponse({
            'error': f'An error occurred while ending the session: {str(e)}'
        }, status=500)

@login_required
@require_http_methods(["POST"])
def submit_feedback(request, session_id):
    """Submit feedback for a completed session"""

    session = get_object_or_404(
        MentorshipSession,
        id=session_id,
        learner=request.user,
        status='completed'
    )

    # Check if feedback already exists
    if hasattr(session, 'detailed_feedback'):
        return JsonResponse({'error': 'Feedback already submitted'}, status=400)

    try:
        data = json.loads(request.body)

        # Create detailed feedback
        feedback = SessionFeedback.objects.create(
            session=session,
            communication_rating=int(data.get('communication_rating')),
            knowledge_rating=int(data.get('knowledge_rating')),
            helpfulness_rating=int(data.get('helpfulness_rating')),
            would_recommend=data.get('would_recommend', False),
            what_went_well=data.get('what_went_well', ''),
            areas_for_improvement=data.get('areas_for_improvement', ''),
            additional_comments=data.get('additional_comments', '')
        )

        # Update session rating
        session.rating = feedback.get_average_rating()
        session.learner_feedback = data.get('general_feedback', '')
        session.save()

        # Update mentor's average rating
        try:
            mentor_profile = session.mentor.mentor_profile

            # Calculate average rating across all feedback categories
            feedback_data = SessionFeedback.objects.filter(
                session__mentor=session.mentor,
                session__status='completed'
            ).aggregate(
                avg_communication=Avg('communication_rating'),
                avg_knowledge=Avg('knowledge_rating'),
                avg_helpfulness=Avg('helpfulness_rating')
            )

            # Calculate overall average from the three category averages
            if all(value is not None for value in feedback_data.values()):
                overall_avg = (
                    feedback_data['avg_communication'] +
                    feedback_data['avg_knowledge'] +
                    feedback_data['avg_helpfulness']
                ) / 3
                mentor_profile.average_rating = round(overall_avg, 2)
                mentor_profile.save()
                print(f"Updated mentor {session.mentor.username} average rating to {mentor_profile.average_rating}")
            else:
                print("Some feedback data is None, skipping average rating update")
        except Exception as rating_error:
            print(f"Error updating mentor average rating: {str(rating_error)}")
            # Don't fail the entire feedback submission if rating update fails

        return JsonResponse({'success': True, 'message': 'Feedback submitted successfully'})

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def become_mentor(request):
    """Allow users to apply to become mentors"""

    # Check if user already has a mentor profile
    try:
        mentor_profile = request.user.mentor_profile
        return redirect('mentorship:mentor_dashboard')
    except MentorProfile.DoesNotExist:
        pass

    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            # Validate required fields
            bio = data.get('bio', '').strip()
            hourly_rate_str = data.get('hourly_rate', '25.00')

            if not bio:
                return JsonResponse({'error': 'Bio is required'}, status=400)

            try:
                hourly_rate = Decimal(hourly_rate_str)
                if hourly_rate < Decimal('5.00') or hourly_rate > Decimal('500.00'):
                    return JsonResponse({'error': 'Hourly rate must be between $5.00 and $500.00'}, status=400)
            except (ValueError, TypeError):
                return JsonResponse({'error': 'Invalid hourly rate format'}, status=400)

            # Create mentor profile
            mentor_profile = MentorProfile.objects.create(
                user=request.user,
                bio=bio,
                hourly_rate=hourly_rate,
                available_slots=data.get('available_slots', []),
                profile_completed=True
            )

            # Add specializations
            skill_ids = data.get('specializations', [])
            if skill_ids:
                try:
                    skills = Skill.objects.filter(id__in=skill_ids)
                    mentor_profile.specializations.set(skills)
                except Exception:
                    # Don't fail the entire operation for specialization errors
                    pass

            return JsonResponse({
                'success': True,
                'message': 'Mentor profile created successfully!',
                'redirect_url': '/mentorship/mentor/dashboard/'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    # GET request - show the form
    all_skills = Skill.objects.all().order_by('name')

    context = {
        'all_skills': all_skills,
    }

    return render(request, 'mentorship/become_mentor.html', context)

@login_required
def mentor_dashboard(request):
    """Dashboard for mentors to manage their profile and sessions"""

    try:
        mentor_profile = request.user.mentor_profile
    except MentorProfile.DoesNotExist:
        return redirect('mentorship:become_mentor')

    if request.method == 'POST':
        try:
            data = json.loads(request.body)

            # Handle draft save (auto-save)
            if data.get('draft'):
                mentor_profile.bio = data.get('bio', mentor_profile.bio)
                mentor_profile.hourly_rate = Decimal(str(data.get('hourly_rate', mentor_profile.hourly_rate)))
                mentor_profile.save()
                return JsonResponse({'success': True, 'message': 'Draft saved'})

            # Handle full profile update
            mentor_profile.bio = data.get('bio', '')
            mentor_profile.hourly_rate = Decimal(str(data.get('hourly_rate', 25.00)))
            mentor_profile.is_active = data.get('is_active', False)
            mentor_profile.profile_completed = data.get('profile_completed', False)

            # Update availability slots
            available_slots = data.get('available_slots', [])
            mentor_profile.available_slots = available_slots

            # Update specializations
            specialization_ids = data.get('specializations', [])
            if specialization_ids:
                skills = Skill.objects.filter(id__in=specialization_ids)
                mentor_profile.specializations.set(skills)

            mentor_profile.save()

            # Create specific availability slots for booking
            if available_slots:
                create_mentor_availability_slots(request.user, available_slots)

            return JsonResponse({
                'success': True,
                'message': 'Profile updated successfully',
                'redirect_url': reverse('mentorship:mentor_dashboard')
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    # GET request - display dashboard
    # Get upcoming sessions
    upcoming_sessions = MentorshipSession.objects.filter(
        mentor=request.user,
        status__in=['scheduled', 'active'],
        scheduled_time__gte=timezone.now()
    ).select_related('learner').order_by('scheduled_time')[:5]

    # Get recent completed sessions
    recent_sessions = MentorshipSession.objects.filter(
        mentor=request.user,
        status='completed'
    ).select_related('learner').order_by('-ended_at')[:5]

    # Calculate earnings for this month
    current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    monthly_earnings = MentorshipSession.objects.filter(
        mentor=request.user,
        status='completed',
        ended_at__gte=current_month,
        is_paid=True
    ).aggregate(total=Sum('mentor_earnings'))['total'] or Decimal('0.00')

    # Calculate sessions count for this month
    monthly_sessions_count = MentorshipSession.objects.filter(
        mentor=request.user,
        status='completed',
        ended_at__gte=current_month
    ).count()

    # Get all skills for the form
    all_skills = Skill.objects.all().order_by('name')

    # Calculate available balance for withdrawal
    available_balance = mentor_profile.get_available_balance()

    context = {
        'mentor_profile': mentor_profile,
        'upcoming_sessions': upcoming_sessions,
        'recent_sessions': recent_sessions,
        'monthly_earnings': monthly_earnings,
        'monthly_sessions_count': monthly_sessions_count,
        'available_balance': available_balance,
        'all_skills': all_skills,
    }

    return render(request, 'mentorship/mentor_dashboard.html', context)


def create_mentor_availability_slots(mentor_user, available_slots):
    """Create specific availability slots for the next 30 days based on weekly schedule"""

    # Clear existing future availability
    MentorAvailability.objects.filter(
        mentor=mentor_user,
        date__gte=timezone.now().date()
    ).delete()

    # Create slots for the next 30 days
    for day_offset in range(30):
        date = timezone.now().date() + timedelta(days=day_offset)
        day_name = date.strftime('%A')

        # Find matching availability slot for this day
        for slot in available_slots:
            if slot['day'] == day_name:
                # Create slots within the available time range
                start_time = datetime.strptime(slot['start'], '%H:%M').time()
                end_time = datetime.strptime(slot['end'], '%H:%M').time()

                # Calculate datetime objects for slot creation
                start_datetime = datetime.combine(date, start_time)
                end_datetime = datetime.combine(date, end_time)

                # Create slots that can accommodate both 1-hour and 2-hour sessions
                current_time = start_datetime

                while current_time < end_datetime:
                    # Calculate remaining time
                    remaining_time = (end_datetime - current_time).total_seconds() / 3600

                    # Create 2-hour slot if there's enough time (2+ hours remaining)
                    if remaining_time >= 2:
                        slot_end_2h = current_time + timedelta(hours=2)
                        MentorAvailability.objects.create(
                            mentor=mentor_user,
                            date=date,
                            start_time=current_time.time(),
                            end_time=slot_end_2h.time(),
                            is_booked=False
                        )
                        current_time += timedelta(hours=2)

                    # Create 1-hour slot if there's at least 1 hour remaining
                    elif remaining_time >= 1:
                        slot_end_1h = current_time + timedelta(hours=1)
                        MentorAvailability.objects.create(
                            mentor=mentor_user,
                            date=date,
                            start_time=current_time.time(),
                            end_time=slot_end_1h.time(),
                            is_booked=False
                        )
                        current_time += timedelta(hours=1)

                    else:
                        # Less than 1 hour remaining, break
                        break


@login_required
def get_chat_history(request, room_id):
    """API endpoint to get chat history for a session"""

    try:
        print(f"DEBUG: Looking for session with room_id: {room_id}")
        print(f"DEBUG: User: {request.user.username}")

        session = get_object_or_404(
            MentorshipSession,
            room_id=room_id
        )

        print(f"DEBUG: Found session {session.id}, learner: {session.learner.username}, mentor: {session.mentor.username}")

        # Check if user is authorized to access this session
        if request.user not in [session.learner, session.mentor]:
            print(f"DEBUG: User {request.user.username} not authorized for session")
            return JsonResponse({'error': 'Unauthorized'}, status=403)

        # Get chat messages for this session from both models
        # First try the new SessionChatMessage model
        new_messages = session.session_chat_messages.select_related('sender').order_by('created_at')
        # Also get legacy messages for backward compatibility
        legacy_messages = session.legacy_chat_messages.select_related('sender').order_by('timestamp')

        print(f"DEBUG: Found {new_messages.count()} new messages and {legacy_messages.count()} legacy messages")

        # Format messages for JSON response
        chat_history = []

        # Add new messages
        for message in new_messages:
            chat_history.append({
                'id': message.id,
                'content': message.message,  # Note: field is 'message' not 'content'
                'username': message.sender.username if message.sender else 'System',
                'user_id': message.sender.id if message.sender else None,
                'message_type': message.message_type,
                'timestamp': message.created_at.timestamp()  # Note: field is 'created_at' not 'timestamp'
            })

        # Add legacy messages
        for message in legacy_messages:
            chat_history.append({
                'id': f"legacy_{message.id}",
                'content': message.content,
                'username': message.sender.username if message.sender else 'System',
                'user_id': message.sender.id if message.sender else None,
                'message_type': message.message_type,
                'timestamp': message.timestamp.timestamp()
            })

        # Sort by timestamp
        chat_history.sort(key=lambda x: x['timestamp'])

        print(f"DEBUG: Returning {len(chat_history)} messages")
        return JsonResponse({
            'success': True,
            'messages': chat_history
        })

    except Exception as e:
        print(f"DEBUG: Error in get_chat_history: {str(e)}")
        import traceback
        traceback.print_exc()
        return JsonResponse({'error': str(e)}, status=500)
