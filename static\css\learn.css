/* Breadcrumb navigation styles */
.breadcrumb-nav {
  margin-bottom: 2em;
}
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0;
  background: none;
  margin-bottom: 0;
}
.breadcrumb-item {
  color: #007bff;
  text-decoration: none;
}
.breadcrumb-item.active {
  color: #ffffff;
  font-weight: bold;
}
.breadcrumb-item a {
  text-decoration: none;
  color: var(--color-border);
  transition: color 0.3s ease;
}
.breadcrumb-item a:hover {
  color: #ffffff;
}
.breadcrumb-separator {
  margin: 0 0.5em;
  color: white;
}

/* <PERSON><PERSON> Header Styles */
.lesson-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
}

.lesson-meta-info {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.lesson-type-badge {
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  color: #1c1c1c;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.lesson-duration {
  background: rgba(40, 40, 40, 0.8);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  font-size: 0.9rem;
}

.lesson-title {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: glow 2s ease-in-out infinite alternate;
}

.lesson-progress-info {
  margin-top: 15px;
}

.course-info {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

.course-name, .chapter-name {
  color: #b0b0b0;
  font-size: 1rem;
  padding: 5px 10px;
  background: rgba(40, 40, 40, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

/* Content Block Styles */
.lesson-content-container {
  max-width: 900px;
  margin: 0 auto;
}

.content-block {
  background: rgba(40, 40, 40, 0.5);
  border-radius: 15px;
  margin-bottom: 30px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.content-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

/* Content Type Specific Styling */
.content-type-tip {
  border-left: 5px solid #ffd700;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(40, 40, 40, 0.5));
}

.content-type-warning {
  border-left: 5px solid #ff6b6b;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(40, 40, 40, 0.5));
}

.content-type-code_example {
  border-left: 5px solid #4ecdc4;
  background: linear-gradient(135deg, rgba(78, 205, 196, 0.1), rgba(40, 40, 40, 0.5));
}

.content-type-exercise {
  border-left: 5px solid #ff9f43;
  background: linear-gradient(135deg, rgba(255, 159, 67, 0.1), rgba(40, 40, 40, 0.5));
}

.content-type-summary {
  border-left: 5px solid #a55eea;
  background: linear-gradient(135deg, rgba(165, 94, 234, 0.1), rgba(40, 40, 40, 0.5));
}

.content-header {
  padding: 20px 25px 0;
}

.content-title {
  color: var(--color-border);
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.content-body {
  padding: 0 25px 25px;
}

.text-content {
  color: #ffffff;
  line-height: 1.8;
  font-size: 1.1rem;
}

.text-content h1, .text-content h2, .text-content h3, .text-content h4 {
  color: var(--color-border);
  margin-top: 25px;
  margin-bottom: 15px;
}

.text-content ul, .text-content ol {
  margin-left: 25px;
  margin-bottom: 20px;
}

.text-content li {
  margin-bottom: 8px;
}

.text-content p {
  margin-bottom: 15px;
}

.text-content strong {
  color: var(--color-border);
}

.text-content em {
  color: #ffd700;
}

/* Code Example Styles */
.code-example-container {
  background: #1e1e1e;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid #333;
}

.code-header {
  background: #2d2d2d;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #333;
}

.code-language {
  color: var(--color-border);
  font-weight: 600;
  font-size: 0.9rem;
}

.copy-code-btn {
  background: var(--color-border);
  color: #1c1c1c;
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.copy-code-btn:hover {
  background: #a0e066;
  transform: scale(1.05);
}

.code-block {
  margin: 0;
  padding: 20px;
  background: #1e1e1e;
  color: #ffffff;
  font-family: 'Fira Code', 'Consolas', monospace;
  font-size: 0.95rem;
  line-height: 1.6;
  overflow-x: auto;
}

/* Chapter and lesson styles */
.chapter-list, .lesson-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.chapter-box {
  border: 2px solid var( --color-border);
  border-radius: 10px;
  padding: 1em;
  margin-bottom: 1.5em;
  background: rgba(40, 40, 40, 0.5);
  box-shadow: 0 2px 8px rgba(26, 199, 41, 0.08);
}
.chapter-title {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 0.5em;
  cursor: pointer;
  text-decoration: none;
  display: block;
}
.lesson-list {
  margin-top: 0.5em;
  padding-left: 1em;
  border-left: 2px solid var(--color-border);
  display: none;
}
.lesson-list.active {
  display: block;
}
.lesson-link {
  text-decoration: none;
  display: block;
  margin-bottom: 0.3em;
  padding: 0.2em 0.5em;
  border-radius: 5px;
  transition: background 0.2s;
}

/* Course Creator and Status Badges */
.course-creator {
  margin-bottom: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.creator-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mentor-badge {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: #ffffff;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.admin-badge {
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  color: #1c1c1c;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 0.8rem;
  font-weight: 500;
}

.draft-status {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.pending-status {
  background: rgba(255, 152, 0, 0.2);
  color: #ff9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

/* Course Approval Management */
.approval-section {
  margin-bottom: 40px;
}

.section-title {
  color: var(--color-border);
  font-size: 1.5rem;
  margin-bottom: 25px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(192, 255, 107, 0.2);
}

.approval-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
}

.approval-card {
  background: rgba(40, 40, 40, 0.8);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.approval-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(192, 255, 107, 0.4);
}

.approval-header {
  padding: 20px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.1);
}

.approval-header .course-title {
  color: var(--color-border);
  font-size: 1.3rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.approval-header .course-meta {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.creator-info {
  color: #4CAF50;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.difficulty-badge {
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty-beginner {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.difficulty-intermediate {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.difficulty-advanced {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.approval-content {
  padding: 20px;
}

.approval-content .course-description {
  color: #ffffff;
  line-height: 1.6;
  margin-bottom: 20px;
}

.course-details {
  display: grid;
  gap: 10px;
}

.detail-item {
  color: #b0b0b0;
  font-size: 0.9rem;
}

.detail-item strong {
  color: var(--color-border);
}

.approval-actions {
  padding: 20px;
  border-top: 1px solid rgba(192, 255, 107, 0.1);
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.quick-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-direction: column;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(192, 255, 107, 0.1);
}

/* Messages styling */
.messages-container {
  margin-bottom: 25px;
}

.alert {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.alert-success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.alert-warning {
  background: rgba(255, 193, 7, 0.1);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #ffc107;
}

.alert-error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.alert-info {
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.3);
  color: #2196f3;
}

.alert-icon {
  font-size: 1.2rem;
}

.alert-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: auto;
  padding: 0;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.3s ease;
}

.alert-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Admin Controls */
.admin-controls {
  text-align: center;
  margin-bottom: 30px;
}

.mentor-info {
  margin-top: 10px;
  text-align: center;
}

.mentor-info small {
  color: var(--color-border);
  font-style: italic;
  background: rgba(192, 255, 107, 0.1);
  padding: 5px 10px;
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.content-admin-actions {
  padding: 15px 25px;
  border-top: 1px solid rgba(192, 255, 107, 0.1);
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Admin Forms Styling */
.admin-form-container {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(40, 40, 40, 0.8);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(192, 255, 107, 0.2);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.admin-form-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
}

.admin-form-header h1 {
  color: var(--color-border);
  font-size: 2rem;
  margin-bottom: 10px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-form-header p {
  color: #b0b0b0;
  font-size: 1.1rem;
  margin: 0;
}

/* Form Field Styling */
.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  color: var(--color-border);
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 1rem;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  background: rgba(20, 20, 20, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  color: #ffffff;
  font-size: 1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--color-border);
  box-shadow: 0 0 0 3px rgba(192, 255, 107, 0.1);
  background: rgba(20, 20, 20, 0.9);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.form-group input[type="file"] {
  padding: 8px;
  background: rgba(40, 40, 40, 0.8);
  border: 2px dashed rgba(192, 255, 107, 0.3);
  border-radius: 8px;
  cursor: pointer;
}

.form-group input[type="file"]:hover {
  border-color: var(--color-border);
  background: rgba(40, 40, 40, 0.9);
}

.form-group input[type="number"] {
  max-width: 150px;
}

/* Form Help Text */
.form-help-text {
  font-size: 0.9rem;
  color: #888;
  margin-top: 5px;
  font-style: italic;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(192, 255, 107, 0.1);
}

.form-actions .btn {
  min-width: 120px;
  padding: 12px 25px;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  color: #1c1c1c;
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #a0e066, var(--color-border));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.btn-secondary {
  background: rgba(40, 40, 40, 0.8);
  color: #ffffff;
  border: 1px solid rgba(192, 255, 107, 0.3);
}

.btn-secondary:hover {
  background: rgba(60, 60, 60, 0.8);
  border-color: var(--color-border);
  transform: translateY(-2px);
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  color: #ffffff;
  border: none;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ff5252, #ff6b6b);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: #ffffff;
  border: none;
}

.btn-success:hover {
  background: linear-gradient(135deg, #45a049, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* Small buttons */
.btn-sm {
  padding: 8px 15px;
  font-size: 0.9rem;
  min-width: 80px;
}

/* Form Error Styling */
.form-errors {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.form-errors ul {
  margin: 0;
  padding-left: 20px;
  color: #ff6b6b;
}

.form-errors li {
  margin-bottom: 5px;
}

/* Course Creation Form Specific Styling */
.course-form-container {
  background: rgba(40, 40, 40, 0.9);
  border-radius: 20px;
  padding: 40px;
  margin: 20px auto;
  max-width: 900px;
  border: 2px solid rgba(192, 255, 107, 0.2);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.course-form-container h1 {
  text-align: center;
  color: var(--color-border);
  font-size: 2.5rem;
  margin-bottom: 30px;
  background: linear-gradient(135deg, var(--color-border), #a0e066);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(192, 255, 107, 0.3);
}

/* Form Row Layout */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.form-row.single {
  grid-template-columns: 1fr;
}

/* Enhanced Input Styling */
.course-title-form,
.course-description-form,
.course-difficulty-form,
.course-duration-form,
.course-status-form,
.course-prerequisites-form,
.course-objectives-form,
.chapter-title-form,
.chapter-description-form,
.chapter-order-form,
.lesson-title-form,
.lesson-type-form,
.lesson-time-form,
.lesson-order-form,
.content-type-form,
.content-title-form,
.lesson-content-form,
.code-language-form,
.content-order-form {
  width: 100%;
  padding: 15px 20px;
  background: rgba(20, 20, 20, 0.9);
  border: 2px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  color: #ffffff;
  font-size: 1.1rem;
  font-family: inherit;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.course-title-form:focus,
.course-description-form:focus,
.course-difficulty-form:focus,
.course-duration-form:focus,
.course-status-form:focus,
.course-prerequisites-form:focus,
.course-objectives-form:focus,
.chapter-title-form:focus,
.chapter-description-form:focus,
.chapter-order-form:focus,
.lesson-title-form:focus,
.lesson-type-form:focus,
.lesson-time-form:focus,
.lesson-order-form:focus,
.content-type-form:focus,
.content-title-form:focus,
.lesson-content-form:focus,
.code-language-form:focus,
.content-order-form:focus {
  outline: none;
  border-color: var(--color-border);
  box-shadow: 0 0 0 4px rgba(192, 255, 107, 0.15);
  background: rgba(20, 20, 20, 1);
  transform: translateY(-2px);
}

.course-image-form {
  padding: 20px;
  background: rgba(40, 40, 40, 0.8);
  border: 3px dashed rgba(192, 255, 107, 0.4);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  color: #ffffff;
}

.course-image-form:hover {
  border-color: var(--color-border);
  background: rgba(40, 40, 40, 1);
  transform: translateY(-2px);
}

/* Select Dropdown Styling */
select.course-difficulty-form,
select.course-status-form,
select.lesson-type-form,
select.content-type-form {
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23C0ff6b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 20px;
  padding-right: 50px;
}

/* Textarea Specific */
.course-description-form,
.course-prerequisites-form,
.course-objectives-form,
.chapter-description-form,
.lesson-content-form {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
  line-height: 1.6;
}

.lesson-content-form {
  min-height: 200px;
  font-family: 'Consolas', 'Monaco', monospace;
}

/* Number inputs */
.chapter-order-form,
.lesson-order-form,
.content-order-form {
  max-width: 120px;
}

/* Form Labels Enhancement */
.form-group label {
  display: block;
  color: var(--color-border);
  font-weight: 700;
  margin-bottom: 10px;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Required field indicator */
.form-group label.required::after {
  content: " *";
  color: #ff6b6b;
  font-weight: bold;
}

/* Form Section Headers */
.form-section {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(192, 255, 107, 0.1);
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section-title {
  color: var(--color-border);
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid rgba(192, 255, 107, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-section-title::before {
  content: "📝";
  font-size: 1.2rem;
}

/* Success Messages */
.success-message {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  color: #4CAF50;
  text-align: center;
  font-weight: 600;
}

/* Loading States */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Lesson Navigation */
.lesson-navigation {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid rgba(192, 255, 107, 0.2);
}

.nav-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}

.nav-btn {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
  text-decoration: none;
  color: #ffffff;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 15px;
  min-width: 200px;
  flex: 1;
  max-width: 300px;
}

.nav-btn:hover {
  background: rgba(192, 255, 107, 0.1);
  border-color: rgba(192, 255, 107, 0.6);
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.nav-btn.prev-btn {
  justify-content: flex-start;
}

.nav-btn.next-btn {
  justify-content: flex-end;
}

.nav-btn.chapter-btn {
  justify-content: center;
  background: linear-gradient(135deg, rgba(192, 255, 107, 0.1), rgba(40, 40, 40, 0.8));
}

.nav-icon {
  font-size: 1.5rem;
}

.nav-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.nav-btn.next-btn .nav-text {
  text-align: right;
}

.nav-btn.chapter-btn .nav-text {
  text-align: center;
}

.nav-label {
  font-size: 0.8rem;
  color: #b0b0b0;
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-title {
  font-size: 1rem;
  color: var(--color-border);
  font-weight: 600;
}

/* Empty State */
.empty-content {
  text-align: center;
  padding: 60px 20px;
  color: #b0b0b0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-content h3 {
  color: #ffffff;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.empty-content p {
  margin-bottom: 25px;
  font-size: 1.1rem;
}

/* Animations */
@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(192, 255, 107, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(192, 255, 107, 0.8);
  }
}

.slide-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
  .lesson-title {
    font-size: 2rem;
  }

  .lesson-meta-info {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .nav-buttons {
    flex-direction: column;
  }

  .nav-btn {
    max-width: 100%;
    justify-content: center !important;
  }

  .nav-text {
    text-align: center !important;
  }

  .content-body {
    padding: 0 15px 20px;
  }

  .content-header {
    padding: 15px 15px 0;
  }

  .content-title {
    font-size: 1.2rem;
  }

  .text-content {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .lesson-header {
    padding: 20px 0;
  }

  .lesson-title {
    font-size: 1.8rem;
  }

  .lesson-type-badge, .lesson-duration {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  .code-block {
    font-size: 0.85rem;
    padding: 15px;
  }
}