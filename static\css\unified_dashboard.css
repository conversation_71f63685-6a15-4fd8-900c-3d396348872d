/* Unified Dashboard Styles */

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  background: rgba(28, 28, 28, 0.8);
  border-radius: 15px;
  padding: 5px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.tab-btn {
  background: transparent;
  border: none;
  color: #ffffff;
  padding: 15px 25px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 14px;
  flex: 1;
  text-align: center;
}

.tab-btn:hover {
  background: rgba(192, 255, 107, 0.1);
  color: #C0ff6b;
}

.tab-btn.active {
  background: #C0ff6b;
  color: #000;
  box-shadow: 0 2px 10px rgba(192, 255, 107, 0.3);
}

/* Tab Content */
.tab-content {
  display: none;
  animation: fadeIn 0.5s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Management Overview */
.management-overview {
  margin-bottom: 40px;
}

.management-overview h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  font-size: 24px;
}

.management-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.stat-icon {
  font-size: 32px;
  width: 50px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #C0ff6b;
  margin-bottom: 5px;
}

.stat-label {
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 40px;
}

.quick-actions-section h3 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  text-align: center;
  display: block;
}

.action-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  text-decoration: none;
  color: inherit;
}

.action-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.action-title {
  color: #C0ff6b;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.action-description {
  color: #ffffff;
  font-size: 14px;
  opacity: 0.8;
}

/* Recent Courses */
.recent-courses-section {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
}

.recent-courses-section h3 {
  color: #C0ff6b;
  margin-bottom: 20px;
}

.courses-list {
  max-height: 400px;
  overflow-y: auto;
}

.course-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.course-item:last-child {
  border-bottom: none;
}

.course-info {
  flex: 1;
}

.course-title a {
  color: #ffffff;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
}

.course-title a:hover {
  color: #C0ff6b;
}

.course-meta {
  color: #cccccc;
  font-size: 12px;
  margin-top: 5px;
}

.course-actions {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 6px 12px;
  border-radius: 15px;
  text-decoration: none;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-primary {
  background: #C0ff6b;
  color: #000;
}

.btn-primary:hover {
  background: #a0e066;
  transform: translateY(-1px);
}

.btn-secondary {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  border: 1px solid #C0ff6b;
}

.btn-secondary:hover {
  background: #C0ff6b;
  color: #000;
}

/* AI Tools */
.ai-tools-section {
  margin-bottom: 40px;
}

.ai-tools-section h2 {
  color: #C0ff6b;
  margin-bottom: 30px;
  font-size: 24px;
}

.ai-tool-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  margin-bottom: 25px;
  overflow: hidden;
}

.tool-header {
  background: rgba(28, 28, 28, 0.8);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(192, 255, 107, 0.2);
}

.tool-header h3 {
  color: #C0ff6b;
  margin: 0;
  font-size: 20px;
}

.tool-toggle {
  background: transparent;
  border: 1px solid #C0ff6b;
  color: #C0ff6b;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-toggle:hover {
  background: #C0ff6b;
  color: #000;
}

.tool-content {
  padding: 25px;
}

/* Progress Insights */
.progress-insights {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  background: rgba(28, 28, 28, 0.5);
  padding: 20px;
  border-radius: 10px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.insight-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.insight-content h4 {
  color: #C0ff6b;
  margin: 0 0 10px 0;
  font-size: 16px;
}

.insight-content p {
  color: #ffffff;
  margin: 0;
  line-height: 1.5;
}

/* Quick Access */
.quick-access-section {
  margin-top: 50px;
}

.quick-access-section h2 {
  color: #C0ff6b;
  margin-bottom: 20px;
  text-align: center;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.quick-action-card {
  background: rgba(40, 40, 40, 0.6);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 10px;
  padding: 20px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  text-align: center;
  display: block;
}

.quick-action-card:hover {
  border-color: #C0ff6b;
  transform: translateY(-2px);
  text-decoration: none;
  color: inherit;
}

.quick-action-card .action-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.quick-action-card .action-title {
  font-size: 14px;
  margin-bottom: 5px;
}

.quick-action-card .action-description {
  font-size: 11px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-tabs {
    flex-direction: column;
    gap: 5px;
  }
  
  .tab-btn {
    padding: 12px 20px;
  }
  
  .management-stats {
    grid-template-columns: 1fr;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .course-item {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .progress-insights {
    gap: 15px;
  }
  
  .insight-item {
    flex-direction: column;
    text-align: center;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

/* Enhanced AI Recommendations for Dashboard */
.ai-recommendations-section {
  margin-bottom: 50px;
  background: linear-gradient(135deg, rgba(40, 40, 40, 0.9), rgba(28, 28, 28, 0.8));
  border: 2px solid rgba(192, 255, 107, 0.4);
  border-radius: 20px;
  padding: 35px;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 0 25px rgba(192, 255, 107, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.ai-recommendations-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #C0ff6b, #a0e066, #80c055, #a0e066, #C0ff6b);
  background-size: 200% 100%;
  animation: gradientShift 3s ease-in-out infinite;
}

.ai-recommendations-section h2 {
  color: #C0ff6b;
  margin-bottom: 30px;
  font-size: 26px;
  font-weight: 700;
  text-align: center;
  text-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
  position: relative;
}

.ai-recommendations-section h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #C0ff6b, transparent);
  border-radius: 2px;
}
