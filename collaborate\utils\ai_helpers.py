def is_similar_project(project1, project2):
    skills1 = set(project1.required_skills.all())
    skills2 = set(project2.required_skills.all())
    if not skills1:
        return False
    overlap = skills1.intersection(skills2)
    return len(overlap) / len(skills1) > 0.5

def get_skills_to_learn(user, heatmap):
    user_skill_names = set(skill.name.lower() for skill in user.skills.all())
    return [
        skill for skill, stats in heatmap.items()
        if stats['demand'] > stats['supply']
        and skill not in user_skill_names
    ]

def get_missing_skills(project, selected_users):
    """
    Detect skills that are required by a project but missing from the selected team.
    
    Args:
        project: Project model instance
        selected_users: List or QuerySet of User objects
        
    Returns:
        list: Names of skills required by the project but not covered by any team member
    """
    team_skills = set()
    for user in selected_users:
        # Get skills from UserSkill model
        user_skills = user.user_skills.all().values_list('skill__name', flat=True)
        team_skills.update([s.lower() for s in user_skills])
    
    # Get project required skills
    required_skills = set([s.name.lower() for s in project.required_skills.all()])
    
    # Find missing skills
    missing = required_skills - team_skills
    return list(missing)
