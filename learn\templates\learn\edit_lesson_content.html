{% extends "base.html" %} {% block content %}
<div class="collaborate-card fade-in visible">
  <!-- Breadcrumb navigation -->
    <nav aria-label="breadcrumb" style="margin-bottom: 1em">
    <ol
      class="breadcrumb"
      style="
        display: flex;
        flex-wrap: wrap;
        list-style: none;
        padding: 0;
        background: none;
      "
    >
      <li class="breadcrumb-item">
        <a
          href="{% url 'learn:course_list' %}"
          style="text-decoration: none; color: #007bff"
          >Courses</a
        >
      </li>
      <li style="margin: 0 0.5em; color: #6c757d">/</li>
      <li class="breadcrumb-item">
        <a
          href="{% url 'learn:course_details' lesson.chapter.course.id %}"
          style="text-decoration: none; color: #007bff"
          >{{ lesson.chapter.course.name }}</a
        >
      </li>
      <li style="margin: 0 0.5em; color: #6c757d">/</li>
      <li class="breadcrumb-item">
        <a
          href="{% url 'learn:chapter_details' lesson.chapter.id %}"
          style="text-decoration: none; color: #007bff"
          >{{ lesson.chapter.name }}</a
        >
      </li>
      <li style="margin: 0 0.5em; color: #6c757d">/</li>
      <li
        class="breadcrumb-item active"
        aria-current="page"
        style="color: #ffffff"
      >
      <a href="{% url 'learn:lesson_details' lesson.id %}" style="text-decoration: none; color: #007bff">{{ lesson.name }}</a>
      </li>
      <li style="margin: 0 0.5em; color: #6c757d">/</li>
      <li
        class="breadcrumb-item active"
        aria-current="page"
        style="color: #ffffff"
      >
        Edit Lesson Content
      </li>
    </ol>
  </nav>

<h2>Edit Lesson Content</h2>
<form method="post">
  {% csrf_token %} {{ form.as_p }}
  <button type="submit" class="btn btn-primary">Save</button>
  <a href="{% url 'learn:lesson_details' lesson_content.lesson.id %}" class="btn btn-danger">Cancel</a>
</form>

<!-- SCEditor integration -->
<link
  rel="stylesheet"
  href="https://cdn.jsdelivr.net/npm/sceditor@3/minified/themes/default.min.css"
/>
<style>
  textarea[name="content"] {
    min-height: 300px;
    min-width: 100%;
    box-sizing: border-box;
  }
  .sceditor-container {
    min-height: 300px !important;
    width: 100% !important;
    margin-bottom: 1em;
    z-index: 1;
  }
  /* Fix SCEditor dropdowns (font size/family) so options are visible */
  .sceditor-dropdown, .sceditor-dropdown * {
    background: #fff !important;
    color: #222 !important;
  }
  .sceditor-dropdown option {
    background: #fff !important;
    color: #222 !important;
  }
  .content-display ul,
.content-display ol {
  padding-left: 2em!important;
  margin-left: 1em!important;
  list-style: initial!important; /* or 'disc' for bullets, 'decimal' for numbers */
}

.content-display li {
  margin-bottom: 0.5em!important;
}

</style>
<script src="https://cdn.jsdelivr.net/npm/sceditor@3/minified/sceditor.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sceditor@3/minified/formats/xhtml.js"></script>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    var textarea = document.querySelector('textarea[name="content"]');
    if (textarea) {
      sceditor.create(textarea, {
        format: "xhtml",
        style:
          "https://cdn.jsdelivr.net/npm/sceditor@3/minified/themes/content/default.min.css",
        width: "100%",
        height: 300,
        toolbar: "bold,italic,underline,strike,subscript,superscript|left,center,right,justify|font,size,color,removeformat|bulletlist,orderedlist,table|link,unlink,image,youtube,date,time,source",
      });
    }
  });
</script>
</div>
{% endblock %}
