from django.core.management.base import BaseCommand
from learn.models import Course, Chapter, Lesson, LessonContent

class Command(BaseCommand):
    help = 'Load complete sample courses with chapters, lessons, and content'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating comprehensive sample courses...'))

        try:
            self.create_python_course()
            self.create_javascript_course()
            self.create_git_course()
            self.create_database_course()
            self.create_api_course()

            self.stdout.write(
                self.style.SUCCESS(
                    '\n🎉 Successfully created all sample courses with complete content!\n'
                    'You can now:\n'
                    '1. Visit /learn/ to see the courses\n'
                    '2. Explore chapters and lessons\n'
                    '3. Go to Django admin to manage content\n'
                )
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating sample courses: {e}')
            )

    def create_python_course(self):
        # Create Course 1: Python Fundamentals
        python_course, created = Course.objects.get_or_create(
            name="Python Fundamentals for Developers",
            defaults={
                'description': "Master the fundamentals of Python programming with hands-on examples and real-world projects. Perfect for beginners and those looking to strengthen their Python foundation.",
                'difficulty': 'beginner',
                'estimated_duration': '4-6 weeks',
                'prerequisites': 'Basic computer literacy and willingness to learn programming concepts',
                'learning_objectives': 'Understand Python syntax, data types, control structures, functions, and object-oriented programming basics'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Created Python Fundamentals course'))

        # Chapter 1: Getting Started
        chapter1, _ = Chapter.objects.get_or_create(
            name="Getting Started with Python",
            course=python_course,
            defaults={'description': 'Introduction to Python programming language, installation, and basic concepts', 'order': 1}
        )

        # Lesson 1.1: What is Python?
        lesson1_1, _ = Lesson.objects.get_or_create(
            name="What is Python?",
            chapter=chapter1,
            defaults={'lesson_type': 'theory', 'estimated_time': '15 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='text', order=1,
            defaults={
                'title': 'Introduction to Python',
                'content': '<h3>What is Python?</h3><p>Python is a high-level, interpreted programming language known for its simplicity and readability. Created by Guido van Rossum in 1991, Python has become one of the most popular programming languages in the world.</p><h4>Key Features of Python:</h4><ul><li><strong>Easy to Learn:</strong> Python\'s syntax is clean and intuitive</li><li><strong>Versatile:</strong> Used in web development, data science, AI, automation, and more</li><li><strong>Large Community:</strong> Extensive libraries and community support</li><li><strong>Cross-platform:</strong> Runs on Windows, macOS, and Linux</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='code_example', order=2,
            defaults={
                'title': 'Your First Look at Python Code',
                'content': '# This is a simple Python program\nprint("Hello, World!")\n\n# Variables are easy to create\nname = "Python"\nversion = 3.9\n\nprint(f"Welcome to {name} {version}!")',
                'code_language': 'python'
            }
        )

        # Lesson 1.2: Installing Python
        lesson1_2, _ = Lesson.objects.get_or_create(
            name="Installing Python",
            chapter=chapter1,
            defaults={'lesson_type': 'practical', 'estimated_time': '20 min', 'order': 2}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_2, content_type='text', order=1,
            defaults={
                'title': 'Installation Guide',
                'content': '<h3>Installing Python on Your System</h3><h4>For Windows:</h4><ol><li>Visit <a href="https://python.org" target="_blank">python.org</a></li><li>Download the latest Python 3.x version</li><li>Run the installer and check "Add Python to PATH"</li><li>Click "Install Now"</li></ol><h4>For macOS:</h4><ol><li>Python comes pre-installed, but install the latest version from python.org</li><li>Or use Homebrew: <code>brew install python</code></li></ol><h4>For Linux:</h4><ol><li>Most distributions include Python</li><li>Update with: <code>sudo apt update && sudo apt install python3</code></li></ol>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_2, content_type='exercise', order=2,
            defaults={
                'title': 'Verify Your Installation',
                'content': '<p>After installation, verify Python is working correctly:</p><ol><li>Open your terminal or command prompt</li><li>Type <code>python --version</code> or <code>python3 --version</code></li><li>You should see the Python version number</li><li>Try running the Python interpreter by typing <code>python</code> or <code>python3</code></li></ol>'
            }
        )

        # Chapter 2: Python Basics
        chapter2, _ = Chapter.objects.get_or_create(
            name="Python Basics",
            course=python_course,
            defaults={'description': 'Learn Python syntax, variables, data types, and basic operations', 'order': 2}
        )

        # Lesson 2.1: Variables and Data Types
        lesson2_1, _ = Lesson.objects.get_or_create(
            name="Variables and Data Types",
            chapter=chapter2,
            defaults={'lesson_type': 'theory', 'estimated_time': '25 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson2_1, content_type='text', order=1,
            defaults={
                'title': 'Understanding Variables',
                'content': '<h3>What are Variables?</h3><p>Variables are containers that store data values. In Python, you don\'t need to declare the type of variable explicitly - Python figures it out automatically!</p><h4>Python Data Types:</h4><ul><li><strong>String (str):</strong> Text data</li><li><strong>Integer (int):</strong> Whole numbers</li><li><strong>Float:</strong> Decimal numbers</li><li><strong>Boolean (bool):</strong> True or False</li><li><strong>List:</strong> Ordered collection of items</li><li><strong>Dictionary (dict):</strong> Key-value pairs</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson2_1, content_type='code_example', order=2,
            defaults={
                'title': 'Variable Examples',
                'content': '# Different types of variables\nname = "Alice"           # String\nage = 25                 # Integer\nheight = 5.6            # Float\nis_student = True       # Boolean\nhobbies = ["reading", "coding", "gaming"]  # List\nperson = {"name": "Alice", "age": 25}      # Dictionary\n\n# Variables can change\nage = 26                # Now age is 26\nage = "twenty-six"      # Now age is a string!\n\n# Multiple assignment\nx, y, z = 1, 2, 3\n\nprint(f"Name: {name}, Age: {age}, Height: {height}")',
                'code_language': 'python'
            }
        )

    def create_javascript_course(self):
        # Create Course 2: JavaScript
        js_course, created = Course.objects.get_or_create(
            name="JavaScript & Modern Web Development",
            defaults={
                'description': "Learn modern JavaScript, ES6+ features, DOM manipulation, and build interactive web applications. Includes practical projects and best practices.",
                'difficulty': 'intermediate',
                'estimated_duration': '6-8 weeks',
                'prerequisites': 'Basic HTML/CSS knowledge and programming fundamentals',
                'learning_objectives': 'Master JavaScript fundamentals, ES6+ features, asynchronous programming, and modern web development practices'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Created JavaScript course'))

        # Chapter 1: JavaScript Fundamentals
        chapter1, _ = Chapter.objects.get_or_create(
            name="JavaScript Fundamentals",
            course=js_course,
            defaults={'description': 'Core JavaScript concepts and syntax', 'order': 1}
        )

        # Lesson 1.1: Variables and Data Types
        lesson1_1, _ = Lesson.objects.get_or_create(
            name="Variables and Data Types",
            chapter=chapter1,
            defaults={'lesson_type': 'theory', 'estimated_time': '30 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='text', order=1,
            defaults={
                'title': 'JavaScript Variables',
                'content': '<h3>Variables in JavaScript</h3><p>JavaScript has three ways to declare variables: <code>var</code>, <code>let</code>, and <code>const</code>. Each has different scoping rules and use cases.</p><h4>Variable Declaration Keywords:</h4><ul><li><strong>let:</strong> Block-scoped, can be reassigned</li><li><strong>const:</strong> Block-scoped, cannot be reassigned</li><li><strong>var:</strong> Function-scoped, can be reassigned (legacy)</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='code_example', order=2,
            defaults={
                'title': 'JavaScript Variable Examples',
                'content': '// Modern variable declarations\nlet userName = "John Doe";\nconst PI = 3.14159;\nlet age = 30;\n\n// Data types\nlet message = "Hello World";     // String\nlet count = 42;                  // Number\nlet isActive = true;             // Boolean\nlet data = null;                 // Null\nlet undefined_var;               // Undefined\n\n// Objects and Arrays\nlet person = {\n    name: "Alice",\n    age: 25,\n    city: "New York"\n};\n\nlet colors = ["red", "green", "blue"];\n\nconsole.log(`Hello, ${userName}!`);\nconsole.log(person.name);\nconsole.log(colors[0]);',
                'code_language': 'javascript'
            }
        )

    def create_git_course(self):
        # Create Course 3: Git
        git_course, created = Course.objects.get_or_create(
            name="Git & Version Control Mastery",
            defaults={
                'description': "Comprehensive guide to Git version control system. Learn branching, merging, collaboration workflows, and advanced Git techniques used in professional development.",
                'difficulty': 'beginner',
                'estimated_duration': '2-3 weeks',
                'prerequisites': 'Basic command line knowledge',
                'learning_objectives': 'Master Git commands, branching strategies, collaboration workflows, and version control best practices'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Created Git course'))

        # Chapter 1: Git Basics
        chapter1, _ = Chapter.objects.get_or_create(
            name="Git Basics",
            course=git_course,
            defaults={'description': 'Introduction to version control and Git fundamentals', 'order': 1}
        )

        # Lesson 1.1: What is Version Control?
        lesson1_1, _ = Lesson.objects.get_or_create(
            name="What is Version Control?",
            chapter=chapter1,
            defaults={'lesson_type': 'theory', 'estimated_time': '20 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='text', order=1,
            defaults={
                'title': 'Understanding Version Control',
                'content': '<h3>What is Version Control?</h3><p>Version control is a system that records changes to files over time so you can recall specific versions later. It allows multiple developers to work on the same project without conflicts.</p><h4>Benefits of Version Control:</h4><ul><li><strong>Track Changes:</strong> See what changed, when, and who made the change</li><li><strong>Collaboration:</strong> Multiple developers can work on the same project</li><li><strong>Backup:</strong> Your code is stored in multiple places</li><li><strong>Branching:</strong> Work on features without affecting main code</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='code_example', order=2,
            defaults={
                'title': 'Basic Git Commands',
                'content': '# Initialize a new Git repository\ngit init\n\n# Check the status of your repository\ngit status\n\n# Add files to staging area\ngit add filename.txt\ngit add .  # Add all files\n\n# Commit changes\ngit commit -m "Your commit message"\n\n# View commit history\ngit log\n\n# Check differences\ngit diff',
                'code_language': 'bash'
            }
        )

    def create_database_course(self):
        # Create Course 4: Database
        db_course, created = Course.objects.get_or_create(
            name="Database Design & SQL",
            defaults={
                'description': "Learn database design principles, SQL queries, and database optimization. Covers relational databases, normalization, and practical database management.",
                'difficulty': 'intermediate',
                'estimated_duration': '5-7 weeks',
                'prerequisites': 'Basic programming knowledge and logical thinking',
                'learning_objectives': 'Design efficient databases, write complex SQL queries, understand normalization, and optimize database performance'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Created Database course'))

        # Chapter 1: Database Fundamentals
        chapter1, _ = Chapter.objects.get_or_create(
            name="Database Fundamentals",
            course=db_course,
            defaults={'description': 'Introduction to databases and relational database concepts', 'order': 1}
        )

        # Lesson 1.1: What is a Database?
        lesson1_1, _ = Lesson.objects.get_or_create(
            name="What is a Database?",
            chapter=chapter1,
            defaults={'lesson_type': 'theory', 'estimated_time': '25 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='text', order=1,
            defaults={
                'title': 'Understanding Databases',
                'content': '<h3>What is a Database?</h3><p>A database is an organized collection of structured information, or data, typically stored electronically in a computer system. Databases are managed by Database Management Systems (DBMS).</p><h4>Types of Databases:</h4><ul><li><strong>Relational Databases:</strong> Use tables with rows and columns (MySQL, PostgreSQL, SQLite)</li><li><strong>NoSQL Databases:</strong> Non-relational databases (MongoDB, Redis, Cassandra)</li><li><strong>Graph Databases:</strong> Store data as nodes and relationships (Neo4j)</li><li><strong>Document Databases:</strong> Store data as documents (MongoDB, CouchDB)</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='code_example', order=2,
            defaults={
                'title': 'Basic SQL Query',
                'content': '-- Create a simple table\nCREATE TABLE users (\n    id INTEGER PRIMARY KEY,\n    name VARCHAR(100) NOT NULL,\n    email VARCHAR(100) UNIQUE,\n    age INTEGER,\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n);\n\n-- Insert data\nINSERT INTO users (name, email, age) \nVALUES (\'John Doe\', \'<EMAIL>\', 30);\n\n-- Query data\nSELECT * FROM users WHERE age > 25;\n\n-- Update data\nUPDATE users SET age = 31 WHERE name = \'John Doe\';\n\n-- Delete data\nDELETE FROM users WHERE id = 1;',
                'code_language': 'sql'
            }
        )

    def create_api_course(self):
        # Create Course 5: API Development
        api_course, created = Course.objects.get_or_create(
            name="API Development & RESTful Services",
            defaults={
                'description': "Build robust APIs and RESTful web services. Learn API design principles, authentication, documentation, and testing strategies for modern applications.",
                'difficulty': 'advanced',
                'estimated_duration': '6-8 weeks',
                'prerequisites': 'Solid programming background, understanding of HTTP protocol, and web development experience',
                'learning_objectives': 'Design and implement RESTful APIs, handle authentication and authorization, implement proper error handling, and create comprehensive API documentation'
            }
        )
        if created:
            self.stdout.write(self.style.SUCCESS('✓ Created API Development course'))

        # Chapter 1: API Fundamentals
        chapter1, _ = Chapter.objects.get_or_create(
            name="API Fundamentals",
            course=api_course,
            defaults={'description': 'Introduction to APIs and RESTful web services', 'order': 1}
        )

        # Lesson 1.1: What is an API?
        lesson1_1, _ = Lesson.objects.get_or_create(
            name="What is an API?",
            chapter=chapter1,
            defaults={'lesson_type': 'theory', 'estimated_time': '30 min', 'order': 1}
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='text', order=1,
            defaults={
                'title': 'Understanding APIs',
                'content': '<h3>What is an API?</h3><p>An Application Programming Interface (API) is a set of protocols, routines, and tools for building software applications. APIs specify how software components should interact.</p><h4>Types of APIs:</h4><ul><li><strong>REST APIs:</strong> Use HTTP methods and are stateless</li><li><strong>GraphQL APIs:</strong> Query language for APIs</li><li><strong>SOAP APIs:</strong> Protocol-based web services</li><li><strong>WebSocket APIs:</strong> Real-time communication</li></ul><h4>HTTP Methods:</h4><ul><li><strong>GET:</strong> Retrieve data</li><li><strong>POST:</strong> Create new data</li><li><strong>PUT:</strong> Update existing data</li><li><strong>DELETE:</strong> Remove data</li></ul>'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='code_example', order=2,
            defaults={
                'title': 'Simple REST API Example',
                'content': '# Python Flask API Example\nfrom flask import Flask, jsonify, request\n\napp = Flask(__name__)\n\n# Sample data\nusers = [\n    {\'id\': 1, \'name\': \'John Doe\', \'email\': \'<EMAIL>\'},\n    {\'id\': 2, \'name\': \'Jane Smith\', \'email\': \'<EMAIL>\'}\n]\n\n# GET all users\<EMAIL>(\'/api/users\', methods=[\'GET\'])\ndef get_users():\n    return jsonify(users)\n\n# GET specific user\<EMAIL>(\'/api/users/<int:user_id>\', methods=[\'GET\'])\ndef get_user(user_id):\n    user = next((u for u in users if u[\'id\'] == user_id), None)\n    if user:\n        return jsonify(user)\n    return jsonify({\'error\': \'User not found\'}), 404\n\n# POST new user\<EMAIL>(\'/api/users\', methods=[\'POST\'])\ndef create_user():\n    data = request.get_json()\n    new_user = {\n        \'id\': len(users) + 1,\n        \'name\': data[\'name\'],\n        \'email\': data[\'email\']\n    }\n    users.append(new_user)\n    return jsonify(new_user), 201\n\nif __name__ == \'__main__\':\n    app.run(debug=True)',
                'code_language': 'python'
            }
        )

        LessonContent.objects.get_or_create(
            lesson=lesson1_1, content_type='tip', order=3,
            defaults={
                'title': 'API Best Practices',
                'content': '<h4>Key API Design Principles:</h4><ul><li><strong>Use meaningful URLs:</strong> /api/users instead of /api/getUsers</li><li><strong>Use HTTP status codes properly:</strong> 200 for success, 404 for not found, 500 for server errors</li><li><strong>Version your APIs:</strong> /api/v1/users</li><li><strong>Use JSON for data exchange</strong></li><li><strong>Implement proper error handling</strong></li><li><strong>Add authentication and authorization</strong></li><li><strong>Document your API thoroughly</strong></li></ul>'
            }
        )
