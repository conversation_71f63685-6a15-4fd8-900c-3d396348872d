{% extends 'base.html' %}
{% load custom_filters %}
{% block title %}Match Users{% endblock %}

{% block extra_css %}
<style>
/* Match Users Page - Dashboard Style Internal CSS */
:root {
  --color-border: #C0ff6b;
  --color-bg: #000000;
  --color-container: #656565;
  --color-secondary: #d5d5d5;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent;
  padding: 50px 0;
  min-height: 100vh;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(192, 255, 107, 0.3);
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Page title styling */
.collaborate-card h2 {
  color: #ffffff;
  font-size: 2.2rem;
  margin-bottom: 2rem;
  text-align: center;
  font-weight: 700;
}

/* Dashboard Section Styling */
.dashboard-section {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 2.5rem;
  border: 1px solid rgba(192, 255, 107, 0.2);
  margin-bottom: 3rem;
  backdrop-filter: blur(10px);
}
.dashboard-section:last-child {
  margin-bottom: 2rem;
}
.mt-2 .fas{
  margin-right:0.2rem !important;
}
.dashboard-section h3 {
  color: #C0ff6b;
  margin-bottom: 2rem;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.dashboard-section h5 {
  color: #C0ff6b;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Main page title spacing */
.collaborate-card > h2:first-of-type {
  margin-bottom: 3rem;
  margin-top: 1rem;
}

/* Card styling for project info sections */
.card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  margin-bottom: 2rem;
}

.card-header {
  background: rgba(192, 255, 107, 0.1) !important;
  border-bottom: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px 15px 0 0 !important;
  padding: 1.25rem 1.5rem;
}

.card-header h5 {
  color: #C0ff6b;
  margin-bottom: 0;
  font-weight: 600;
}

.card-body {
  background: rgba(40, 40, 40, 0.6);
  color: rgba(255, 255, 255, 0.9);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Specific spacing for card content */
.card-body .row {
  margin-bottom: 0;
}


.card-body h6 {
  margin-bottom: 1rem;
  font-weight: 600;
}

.card-body .list-inline {
  list-style: none;
}

.card-body .list-inline-item {
  margin-bottom: 0.5rem;
}

/* Alert styling */
.alert {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 12px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  padding: 1.25rem 1.5rem;
}

.alert-success {
  border-color: rgba(40, 167, 69, 0.5);
  background: rgba(40, 167, 69, 0.1);
}

.alert-warning {
  border-color: rgba(255, 193, 7, 0.5);
  background: rgba(255, 193, 7, 0.1);
}

.alert-danger {
  border-color: rgba(220, 53, 69, 0.5);
  background: rgba(220, 53, 69, 0.1);
}

.alert-info {
  border-color: rgba(13, 202, 240, 0.5);
  background: rgba(13, 202, 240, 0.1);
}

/* Alert content spacing */
.alert ul {
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.alert li {
  margin-bottom: 0.5rem;
}

.alert strong {
  display: block;
  margin-bottom: 0.75rem;
}

/* Progress bar styling */
.progress {
  background: rgba(40, 40, 40, 0.8);
  border-radius: 10px;
  height: 24px;
  margin-bottom: 1.5rem;
}

.progress-bar {
  border-radius: 10px;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Badge styling */
.badge {
  font-size: 0.85rem;
  padding: 0.6rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0.25rem;
  white-space: nowrap;
  flex-shrink: 0;
}

.badge.bg-success {
  background-color: rgba(40, 167, 69, 0.8) !important;
  border: 1px solid rgba(40, 167, 69, 0.5);
}

.badge.bg-primary {
  background-color: rgba(13, 110, 253, 0.8) !important;
  border: 1px solid rgba(13, 110, 253, 0.5);
}

.badge.bg-danger {
  background-color: rgba(220, 53, 69, 0.8) !important;
  border: 1px solid rgba(220, 53, 69, 0.5);
}

.badge.bg-warning {
  background-color: rgba(255, 193, 7, 0.8) !important;
  border: 1px solid rgba(255, 193, 7, 0.5);
  color: #000 !important;
}

.badge.bg-info {
  background-color: rgba(13, 202, 240, 0.8) !important;
  border: 1px solid rgba(13, 202, 240, 0.5);
}

.badge.bg-secondary {
  background-color: rgba(108, 117, 125, 0.8) !important;
  border: 1px solid rgba(108, 117, 125, 0.5);
}

/* Badge container improvements */
.list-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.list-inline-item {
  margin-bottom: 0;
  margin-right: 0;
}

/* User info badges container */
.user-info .d-flex.flex-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

/* Skills badges container */
.skills-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Button styling */
.btn {
  border-radius: 12px;
  padding: 0.875rem 1.75rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  margin: 0.25rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
}

.btn-primary {
  background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
  border-color: #C0ff6b;
  color: #000;
  font-weight: 600;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #a0e066 0%, #80c040 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
}

.btn-success {
  background: rgba(40, 167, 69, 0.8);
  border-color: rgba(40, 167, 69, 0.5);
}

.btn-success:hover {
  background: rgba(40, 167, 69, 1);
  transform: translateY(-2px);
}

.btn-warning {
  background: rgba(255, 193, 7, 0.8);
  border-color: rgba(255, 193, 7, 0.5);
  color: #000;
}

.btn-warning:hover {
  background: rgba(255, 193, 7, 1);
  transform: translateY(-2px);
}

.btn-info {
  background: rgba(13, 202, 240, 0.8);
  border-color: rgba(13, 202, 240, 0.5);
}

.btn-info:hover {
  background: rgba(13, 202, 240, 1);
  transform: translateY(-2px);
}

.btn-outline-primary {
  border-color: #C0ff6b;
  color: #C0ff6b;
  background: transparent;
}

.btn-outline-primary:hover {
  background: #C0ff6b;
  color: #000;
}

.btn-outline-info {
  border-color: rgba(13, 202, 240, 0.8);
  color: rgba(13, 202, 240, 0.9);
  background: transparent;
}

.btn-outline-info:hover {
  background: rgba(13, 202, 240, 0.8);
  color: #fff;
}

.btn-outline-success {
  border-color: rgba(40, 167, 69, 0.8);
  color: rgba(40, 167, 69, 0.9);
  background: transparent;
}

.btn-outline-success:hover {
  background: rgba(40, 167, 69, 0.8);
  color: #fff;
}

.btn-outline-secondary {
  border-color: rgba(108, 117, 125, 0.8);
  color: rgba(108, 117, 125, 0.9);
  background: transparent;
}

.btn-outline-secondary:hover {
  background: rgba(108, 117, 125, 0.8);
  color: #fff;
}

/* List group styling for user cards */
.list-group {
  margin-top: 2.5rem;
  margin-bottom: 2rem;
}

.list-group-item {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.2);
  border-radius: 18px;
  margin-bottom: 2rem;
  padding: 2.5rem;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  cursor: pointer;
}

.list-group-item:hover {
  transform: translateY(-3px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.2);
}

.list-group-item.border-success {
  border-color: rgba(40, 167, 69, 0.6);
}

.list-group-item:last-child {
  margin-bottom: 1rem;
}

/* User card header styling */
.user-card-header {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(192, 255, 107, 0.1);
  flex-wrap: wrap;
}

.user-card-header .user-info {
  flex: 1;
  min-width: 200px;
}

.user-card-header .text-end {
  flex-shrink: 0;
  min-width: 120px;
}

.user-profile-pic {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(192, 255, 107, 0.3);
}

.letter-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  border: 2px solid rgba(192, 255, 107, 0.3);
}

.letter-avatar[data-color="1"] { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.letter-avatar[data-color="2"] { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.letter-avatar[data-color="3"] { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.letter-avatar[data-color="4"] { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.letter-avatar[data-color="5"] { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.letter-avatar[data-color="6"] { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.letter-avatar[data-color="7"] { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
.letter-avatar[data-color="8"] { background: linear-gradient(135deg, #ff8a80 0%, #ea4c89 100%); }
.letter-avatar[data-color="9"] { background: linear-gradient(135deg, #8fd3f4 0%, #84fab0 100%); }

.user-info h5 {
  color: #C0ff6b;
  margin-bottom: 0.5rem;
  font-size: 1.3rem;
  font-weight: 600;
}

/* Match score styling */
.match-score-container {
  margin: 2rem 0;
  padding: 1.5rem;
  background: rgba(40, 40, 40, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.match-percentage {
  font-size: 1.1rem;
  font-weight: 600;
  color: #C0ff6b;
  margin-bottom: 1rem;
}

.match-score-container .d-flex {
  margin-bottom: 1rem;
}

.match-score-container strong {
  margin-right: 1rem;
}

/* Skills section styling */
.matching-skills-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(40, 40, 40, 0.4);
  border-radius: 12px;
  border: 1px solid rgba(192, 255, 107, 0.1);
}

.skills-header {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.skills-header strong {
  color: #C0ff6b;
  font-size: 1rem;
}

.toggle-skills {
  color: #C0ff6b;
  text-decoration: none;
  font-size: 0.9rem;
  padding: 0.25rem 0.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.toggle-skills:hover {
  color: #a0e066;
  background: rgba(192, 255, 107, 0.1);
}

.skills-content {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 1rem;
}

.skills-content .badge {
  margin: 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  white-space: nowrap;
}

/* Skill badge with proficiency indicators */
.skills-content .badge .ms-1 {
  margin-left: 0.5rem !important;
}

.skills-content .badge .rounded-circle {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
}

/* Top match badges */
.top-match-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
  color: #000;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3);
}

.highly-recommended-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(135deg, #40c9ff 0%, #e81cff 100%);
  color: #fff;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(64, 201, 255, 0.3);
}

/* Invite button container */
.invite-button-container {
  min-width: 160px;
  padding-left: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.invite-button-container .btn {
  width: 100%;
  min-width: 140px;
}

.invite-button-container .error-message {
  margin-top: 0.5rem;
  font-size: 0.85rem;
}

/* Form styling */
.form-check {
  margin-bottom: 1rem;
  padding: 0.75rem;
}

.form-check-input {
  background-color: rgba(40, 40, 40, 0.8);
  border-color: rgba(192, 255, 107, 0.5);
  margin-right: 0.75rem;
}

.form-check-input:checked {
  background-color: #C0ff6b;
  border-color: #C0ff6b;
}

.form-check-label {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  cursor: pointer;
}

/* Control buttons section spacing */
.d-flex.justify-content-between.align-items-center {
  padding: 1rem 0;
}

.d-flex.align-items-center.gap-3 {
  gap: 1.5rem !important;
}

.d-flex.align-items-center.gap-3 .form-check {
  margin-bottom: 0;
}

/* Modal styling */
.modal-content {
  background: rgba(28, 28, 28, 0.95);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(20px);
}

.modal-header {
  border-bottom: 1px solid rgba(192, 255, 107, 0.3);
  background: rgba(40, 40, 40, 0.6);
}

.modal-title {
  color: #C0ff6b;
  font-weight: 600;
}

.modal-body {
  color: rgba(255, 255, 255, 0.9);
}

.modal-footer {
  border-top: 1px solid rgba(192, 255, 107, 0.3);
  background: rgba(40, 40, 40, 0.6);
}

/* CV download button */
.cv-download-btn {
  background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
  color: #000;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.cv-download-btn:hover {
  background: linear-gradient(135deg, #a0e066 0%, #80c040 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
  color: #000;
  text-decoration: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .collaborate-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .dashboard-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .dashboard-section h3 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
  }

  .user-card-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
    padding-bottom: 1.5rem;
  }

  .invite-button-container {
    margin-top: 1.5rem;
    min-width: auto;
    padding-left: 0;
    width: 100%;
  }

  .list-group-item {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .match-score-container {
    margin: 1.5rem 0;
    padding: 1rem;
  }

  .matching-skills-section {
    margin-top: 1.5rem;
    padding: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .btn {
    padding: 0.75rem 1.25rem;
    margin: 0.25rem 0;
  }

  .d-flex.justify-content-between.align-items-center {
    flex-direction: column;
    align-items: stretch !important;
    gap: 1rem;
  }

  .d-flex.align-items-center.gap-3 {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .collaborate-card {
    padding: 1rem;
    margin: 0.25rem;
  }

  .dashboard-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .list-group-item {
    padding: 1rem;
  }

  .user-card-header {
    gap: 0.75rem;
  }

  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.9rem;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pulse {
  animation: pulse 0.6s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Additional spacing improvements */

/* Re-pair button spacing */
.collaborate-card > form {
  margin-bottom: 2rem;
}

/* Missing skills section spacing */
.alert.border-left {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.skill-badges {
  margin: 1.5rem 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-badges .badge {
  margin: 0;
  padding: 0.6rem 1rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

/* Project skills section improvements */
.card-body .list-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.card-body .list-inline-item {
  margin: 0;
}

/* Skill weighting alert improvements */
.alert ul {
  margin-bottom: 0.5rem;
  margin-top: 1rem;
  padding-left: 1.5rem;
}

.alert li {
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.alert li .badge {
  margin: 0;
}

/* CV indicator spacing */
.list-group-item .mt-3 {
  margin-top: 1.5rem !important;
  padding-top: 1rem;
  border-top: 1px solid rgba(192, 255, 107, 0.1);
}

/* Invitation status spacing */
.user-info .d-flex.flex-wrap {
  margin-top: 0.75rem;
  gap: 0.5rem;
}

/* Match score badge spacing */
.text-end .badge {
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
}

/* Enhanced button grid spacing */
.enhanced-btn-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Navigation buttons container */
.dashboard-section .d-flex.flex-wrap {
  gap: 1rem !important;
}

.dashboard-section .d-flex.flex-wrap .btn {
  margin: 0.25rem;
}

/* Declined users alert spacing */
.alert.alert-danger.mt-3 {
  margin-top: 2rem !important;
  margin-bottom: 2rem;
  display: flex;
  flex-direction: row;
}

.alert.alert-danger.mt-3 form {
  margin-top: 1rem;
}

/* Missing skills enhanced section */
.alert.alert-danger.mt-4.border-left {
  margin-top: 2.5rem !important;
  padding: 2rem;
}


/* Modal skills section improvements */
#modalSkills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

#modalSkills .badge {
  margin: 0;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

/* Match score badge improvements */
.text-end .badge {
  padding: 0.75rem 1rem;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

/* User availability badges */
.user-info .d-flex.flex-wrap.gap-1 {
  gap: 0.5rem !important;
  margin-top: 0.75rem;
}

/* Invitation status badges spacing */
.user-info .badge {
  margin: 0.25rem 0;
}

/* Control buttons section improvements */
.d-flex.align-items-center.gap-3 {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.d-flex.align-items-center.gap-3 .form-check {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  white-space: nowrap;
}

/* Enhanced responsive badge handling */
@media (max-width: 768px) {
  .badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
  }

  .skills-content {
    gap: 0.5rem;
  }

  .skill-badges {
    gap: 0.5rem;
  }

  .list-inline {
    gap: 0.5rem;
  }

  .user-card-header {
    align-items: center;
    flex-direction: column;
    text-align: center;
  }

  .user-card-header .text-end {
    order: -1;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
  }

  .skills-content,
  .skill-badges,
  .list-inline {
    gap: 0.25rem;
  }
}
</style>
{% endblock %}


{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="tile-wrap">
    <div class="collaborate-card fade-in">
  {% if ai_selected %}
    <h2>🔮 AI Suggested Team for "{{ project.title }}"</h2>
  {% else %}
    <h2>Matched Users for Project: {{ project.title }}</h2>
  {% endif %}

  {% if project.accepted_teammates.count < project.team_size %}
    <form method="get" action="{% url 'collaborate:auto_team' project.hash %}">
        <button class="btn btn-warning">
            ♻️ Re-Pair Remaining Team
        </button>
    </form>
  {% endif %}

  <!-- Project Skills Summary -->
  <div class="dashboard-section">
    <h3><i class="fas fa-code me-2"></i>Project Skills</h3>
    <div class="card">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h6 style="color: #C0ff6b; margin-bottom: 1rem;">Required Skills:</h6>
            <ul class="list-inline">
              {% for skill in project.required_skills.all %}
                <li class="list-inline-item">
                  <span class="badge {% if skill in project.critical_skills.all %}bg-danger{% else %}bg-primary{% endif %} mb-1">
                    {{ skill.name }}
                    {% if skill in project.critical_skills.all %}
                      <i class="fas fa-star" title="Critical Skill"></i>
                    {% endif %}
                  </span>
                </li>
              {% endfor %}
            </ul>
          </div>
          <div class="col-md-6">
            <div class="alert alert-info mb-0">
              <i class="fas fa-info-circle me-2"></i>
              <strong>Skill Weighting:</strong>
              <ul class="mb-0 mt-2">
                <li><span class="badge bg-danger">Critical Skills</span> have 2x higher weight</li>
                <li><span class="badge bg-primary">Regular Skills</span> have normal weight</li>
                <li>Extra skills provide small bonus points</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Skill Coverage Summary -->
  {% if skill_coverage_percent is not None %}
  <div class="dashboard-section">
    <h3><i class="fas fa-chart-pie me-2"></i>Skill Coverage: {{ skill_coverage_percent|floatformat:1 }}%</h3>
    <div class="card">
      <div class="card-body">
        <div class="progress mb-3">
          <div class="progress-bar {% if is_optimal_match %}bg-success{% else %}bg-warning{% endif %}"
               role="progressbar"
               style="width: {{ skill_coverage_percent }}%"
               aria-valuenow="{{ skill_coverage_percent }}"
               aria-valuemin="0"
               aria-valuemax="100">
            {{ skill_coverage_percent|floatformat:1 }}%
          </div>
        </div>

        {% if is_optimal_match %}
          <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i> This is an optimal match with good skill coverage!
          </div>
        {% else %}
          <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i> This match has incomplete skill coverage.
          </div>
        {% endif %}
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Display Declined Users Alert -->
  {% if declined_users %}
    <div class="alert alert-danger mt-3">
      <strong>⚠️ Some teammates declined the invite!</strong>
      <ul>
        {% for user in declined_users %}
          <li>{{ user.username }} (declined)</li>
        {% endfor %}
      </ul>
      <form method="post" action="{% url 'collaborate:re_pair_team' project.hash %}">
        {% csrf_token %}
        <button type="submit" class="btn btn-warning">🔁 Re-Pair Remaining Slots</button>
      </form>
    </div>
  {% endif %}

  <!-- Missing Skills Alert - Enhanced Section -->
  {% if missing_skills %}
    <div class="alert alert-danger mt-4 border-left border-danger">
      <div class="d-flex justify-content-between align-items-center">
        <h4 class="mb-2 d-flex align-items-center">
          <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
          Missing Skills
          <span class="badge bg-danger ms-2 rounded-pill" data-bs-toggle="tooltip" title="Number of missing skills">
            {{ missing_skills|length }}
          </span>
        </h4>
      </div>

      <div class="skill-badges my-3">
        {% for skill in missing_skills %}
          <span class="badge bg-secondary me-2 mb-2 px-3 py-2" data-bs-toggle="tooltip" title="Required but not yet covered">
            {{ skill }} <i class="fas fa-exclamation-circle text-warning ms-1"></i>
          </span>
        {% endfor %}
      </div>



      <div class="mt-3">
        <div class="mt-2 d-flex align-items-center" style="display: flex;">
        <i class="fas fa-lightbulb text-warning me-2" style="font-size: 10px;"></i>
        <p class="mb-0 text-body-common">Consider inviting users with these missing skills to complete your team.</p>
      </div>
        <a href="{% url 'collaborate:match_users' project.hash %}" class="btn btn-sm btn-outline-primary">
          <i class="fas fa-search me-1"></i> Find Users with These Skills
        </a>
      </div>
    </div>
  {% endif %}

  <!-- Matched Users List -->
  {% if matched_users %}
  <div class="dashboard-section">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <h3><i class="fas fa-users me-2"></i>Matched Users</h3>

      <div class="d-flex align-items-center gap-3">
        <!-- Toggle for low matches -->
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="toggleLowMatches" checked>
          <label class="form-check-label" for="toggleLowMatches">
            <span class="badge bg-secondary me-1">👻</span> Show Low Matches
          </label>
        </div>

        {% if project.owner == request.user %}
          <form id="inviteAllForm" method="POST" action="{% url 'collaborate:invite_all_matched' project.hash %}">
            {% csrf_token %}
            <button type="button" id="inviteAllButton" class="btn btn-success"
                    data-project-hash="{{ project.hash }}"
                    data-bs-toggle="tooltip"
                    title="Send invitations to all matched users with positive scores">
              <i class="fas fa-envelope me-1"></i> 📩 Invite All Matched Users
            </button>
          </form>
        {% endif %}
      </div>
    </div>

    <div class="list-group mt-4">
      {% for user_data in matched_users %}
        {% if user_data.score >= 0 or request.user.is_staff %}
        <div class="list-group-item d-flex justify-content-between align-items-start
                  {% if user_data.is_available_now %}border-success{% endif %}
                  {% if user_data.score < 20 %}low-match-card{% endif %}"
             data-match-score="{{ user_data.score|floatformat:0 }}">
          <div class="w-100">
            <!-- User Card Header with Profile Picture or Letter Avatar -->
            <div class="user-card-header">
              {% if user_data.user.profile.profile_picture %}
                <img src="{{ user_data.user.profile.profile_picture.url }}"
                     alt="{{ user_data.user.username }}" class="user-profile-pic">
              {% else %}
                {% with username=user_data.user.username|first|upper %}
                  {% with color_index=user_data.user.id|default:1|modulo:9|add:1 %}
                    <div class="letter-avatar" data-color="{{ color_index|default:'1' }}">
                      {{ username }}
                    </div>
                  {% endwith %}
                {% endwith %}
              {% endif %}
              <div class="user-info">
                <h5 class="mb-1">{{ user_data.user.username }}</h5>
                <div class="d-flex flex-wrap gap-1 mt-1">
                  {% if user_data.is_available_now %}
                    <span class="badge bg-success">Available Now</span>
                  {% endif %}
                  {% if user_data.is_member %}
                    <span class="badge bg-info">Already a Member</span>
                  {% endif %}
                  {% if user_data.score < 20 %}
                    <span class="badge bg-secondary">Low Match</span>
                  {% endif %}
                  {% if user_data.score == 0 %}
                    <span class="badge bg-danger">No Skills Matched</span>
                  {% endif %}

                  <!-- Invitation Status Badges -->
                  {% if user_data.invitation_status == "pending" %}
                    <span class="badge bg-info" data-bs-toggle="tooltip"
                          title="Invited on {{ user_data.invitation_date|date:'M d, Y' }}">
                      📩 Pending Invite
                    </span>
                  {% elif user_data.invitation_status == "accepted" %}
                    <span class="badge bg-success" data-bs-toggle="tooltip"
                          title="Accepted on {{ user_data.invitation_date|date:'M d, Y' }}">
                      ✅ Accepted
                    </span>
                  {% elif user_data.invitation_status == "declined" %}
                    <span class="badge bg-danger" data-bs-toggle="tooltip"
                          title="Declined on {{ user_data.invitation_date|date:'M d, Y' }}">
                      ❌ Declined
                    </span>
                  {% endif %}
                </div>
              </div>

              <!-- Match Score Badge -->
              <div class="text-end ms-auto">
                <div class="badge {% if user_data.score >= 70 %}bg-success{% elif user_data.score >= 40 %}bg-warning{% else %}bg-danger{% endif %} p-2">
                  <i class="fas {% if user_data.score >= 70 %}fa-check-circle{% elif user_data.score >= 40 %}fa-info-circle{% else %}fa-exclamation-circle{% endif %} me-1"></i>
                  {{ user_data.score|floatformat:1 }}% Match
                </div>
              </div>

              <!-- Top Match Badge -->
              {% if user_data.score >= 90 %}
                <span class="top-match-badge">Top Match</span>
              {% elif user_data.score >= 70 %}
                <span class="highly-recommended-badge">Highly Recommended</span>
              {% endif %}
            </div>

            <!-- Enhanced Match Score Display -->
            <div class="match-score-container">
              <div class="d-flex align-items-center mb-2">
                <strong class="me-2">Match Score:</strong>
                <span class="match-percentage"
                     aria-label="Match score: {{ user_data.score|floatformat:1 }}%"
                     data-bs-toggle="tooltip"
                     title="Score based on skills matched with this project's requirements">
                  {% if user_data.score >= 70 %}
                    <i class="fas fa-check-circle text-success me-1"></i>
                  {% elif user_data.score >= 40 %}
                    <i class="fas fa-info-circle text-warning me-1"></i>
                  {% else %}
                    <i class="fas fa-exclamation-circle text-danger me-1"></i>
                  {% endif %}
                  {{ user_data.score|floatformat:1 }}%
                </span>
              </div>

              <div class="progress">
                <div class="progress-bar
                  {% if user_data.score >= 70 %}bg-success
                  {% elif user_data.score >= 40 %}bg-warning
                  {% else %}bg-danger{% endif %}"
                  role="progressbar"
                  style="width: {{ user_data.score }}%"
                  aria-valuenow="{{ user_data.score }}"
                  aria-valuemin="0"
                  aria-valuemax="100">
                </div>
              </div>
            </div>

            {% if user_data.score == 0 %}
              <div class="alert alert-light border mt-3 mb-2">
                <i class="fas fa-times-circle text-danger me-1"></i>
                <strong>❌ No skills matched for this project.</strong>
              </div>
            {% else %}
              {% if user_data.matching_skills %}
                <div class="matching-skills-section mt-3 {% if user_data.score < 20 %}collapsible-skills{% endif %}">
                  <div class="skills-header d-flex align-items-center">
                    <strong class="text-muted">Matching Skills:</strong>
                    {% if user_data.score < 20 %}
                      <button type="button" class="btn btn-sm btn-link toggle-skills ms-2 p-0">
                        <small>Show</small>
                      </button>
                    {% endif %}
                  </div>
                  <div class="skills-content {% if user_data.score < 20 %}d-none{% endif %}">
                    {% for skill in user_data.matching_skills %}
                      <span class="badge {% if skill.is_critical %}bg-danger{% else %}bg-primary{% endif %} p-2 m-1">
                        {{ skill.name }}
                        {% if skill.proficiency_name == "Expert" or skill.proficiency_name == "Master" %}
                          <span class="ms-1 badge bg-success rounded-circle">
                            <i class="fas fa-check-circle"></i>
                          </span>
                        {% elif skill.proficiency_name == "Intermediate" or skill.proficiency_name == "Advanced" %}
                          <span class="ms-1 badge bg-warning rounded-circle">
                            <i class="fas fa-star-half-alt"></i>
                          </span>
                        {% elif skill.proficiency_name == "Beginner" %}
                          <span class="ms-1 badge bg-secondary rounded-circle">
                            <i class="fas fa-seedling"></i>
                          </span>
                        {% endif %}
                        {% if skill.is_critical %}
                          <span class="ms-1 badge bg-warning rounded-circle">
                            <i class="fas fa-star" title="Critical Skill"></i>
                          </span>
                        {% endif %}
                      </span>
                    {% endfor %}
                  </div>
                </div>
              {% endif %}
            {% endif %}

            <!-- CV Indicator if available -->
            {% if user_data.user.profile.cv %}
              <div class="mt-3">
                <span class="badge bg-info p-2">
                  <i class="fas fa-file-pdf me-1"></i> CV Available
                </span>
                <small class="text-muted ms-2">Click on user card to view details</small>
              </div>
            {% endif %}
          </div>

          {% if not user_data.is_member and project.owner == request.user %}
            <div class="invite-button-container ms-3 d-flex flex-column align-items-center justify-content-center">
              {% if user_data.invitation_status == "pending" %}
                <button class="btn btn-info" disabled data-bs-toggle="tooltip"
                        title="Invitation sent on {{ user_data.invitation_date|date:'M d, Y' }}">
                  <i class="fas fa-paper-plane me-1"></i> 📩 Invited
                </button>
                {% if user_data.invitation_date %}
                <div class="mt-1">
                  <small class="text-muted">Sent {{ user_data.invitation_date|date:'M d, Y' }}</small>
                </div>
                {% endif %}
              {% elif user_data.invitation_status == "accepted" %}
                <span class="badge bg-success p-2" data-bs-toggle="tooltip"
                      title="Accepted on {{ user_data.invitation_date|date:'M d, Y' }}">
                  <i class="fas fa-check-circle me-1"></i> ✅ Accepted Team Member
                </span>
              {% elif user_data.invitation_status == "declined" %}
                <button class="btn btn-outline-secondary invite-button"
                        data-user-id="{{ user_data.user.id }}"
                        data-project-hash="{{ project.hash }}"
                        data-bs-toggle="tooltip"
                        title="This user previously declined on {{ user_data.invitation_date|date:'M d, Y' }}. You can try inviting again.">
                  <i class="fas fa-sync me-1"></i> 🔄 Re-invite
                </button>
                <div class="mt-1">
                  <small class="text-muted text-danger">Declined {{ user_data.invitation_date|date:'M d, Y' }}</small>
                </div>
              {% else %}
                <button class="btn btn-primary invite-button"
                        data-user-id="{{ user_data.user.id }}"
                        data-project-hash="{{ project.hash }}"
                        data-bs-toggle="tooltip"
                        title="Click to invite this user to join your project">
                  <i class="fas fa-envelope me-1"></i> 📩 Invite
                </button>
              {% endif %}
              <div class="error-message d-none"></div>
            </div>
          {% endif %}
        </div>
        {% endif %}
      {% endfor %}
    </div>

    <!-- Add JavaScript for toggle functionality -->
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Toggle for showing/hiding low matches
        const toggleLowMatches = document.getElementById('toggleLowMatches');
        const updateLowMatchesVisibility = () => {
          const showLowMatches = toggleLowMatches.checked;
          document.querySelectorAll('.low-match-card').forEach(card => {
            if (showLowMatches) {
              card.classList.remove('d-none');
            } else {
              card.classList.add('d-none');
            }
          });
        };

        toggleLowMatches.addEventListener('change', updateLowMatchesVisibility);

        // Toggle for expanding/collapsing skills for low matches
        document.querySelectorAll('.toggle-skills').forEach(button => {
          button.addEventListener('click', function() {
            const skillsContent = this.closest('.matching-skills-section').querySelector('.skills-content');
            const isHidden = skillsContent.classList.contains('d-none');
            skillsContent.classList.toggle('d-none');
            this.querySelector('small').textContent = isHidden ? 'Hide' : 'Show';
          });
        });

        // Initialize tooltips
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
          const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
          tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
          });
        }

        // Function to get CSRF token
        function getCSRFToken() {
          return document.querySelector('[name=csrfmiddlewaretoken]').value;
        }

        // Handle individual invite buttons
        document.querySelectorAll('.invite-button').forEach(button => {
          button.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            const projectHash = this.getAttribute('data-project-hash');
            const errorContainer = this.parentNode.querySelector('.error-message');

            // Update button state to "Sending..."
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> ⏳ Sending...';
            this.disabled = true;
            this.classList.remove('btn-primary', 'btn-success', 'btn-danger');
            this.classList.add('btn-secondary');

            // Clear any previous error messages
            errorContainer.textContent = '';
            errorContainer.classList.add('d-none');

            // Send AJAX request to invite endpoint
            fetch(`/collaborate/project/${projectHash}/invite/${userId}/`, {
              method: 'POST',
              headers: {
                'X-CSRFToken': getCSRFToken(),
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
              },
              credentials: 'same-origin'
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              // Success handling
              this.innerHTML = '<i class="fas fa-check me-1"></i> ✅ Invited';
              this.classList.remove('btn-secondary');
              this.classList.add('btn-success');
              this.classList.add('pulse');

              // Update tooltip
              if (bootstrap && bootstrap.Tooltip) {
                const tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                  tooltip.dispose();
                }
                this.setAttribute('title', 'Invite sent successfully');
                new bootstrap.Tooltip(this);
              }

              // Keep the button disabled to prevent duplicate invites
              this.disabled = true;
            })
            .catch(error => {
              // Error handling
              console.error('Error:', error);
              this.innerHTML = '<i class="fas fa-times me-1"></i> ❌ Error — Retry';
              this.classList.remove('btn-secondary');
              this.classList.add('btn-danger', 'invite-button-error');
              this.disabled = false;

              // Show error message
              errorContainer.textContent = 'Failed to send invite. Please try again.';
              errorContainer.classList.remove('d-none');

              // Update tooltip
              if (bootstrap && bootstrap.Tooltip) {
                const tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                  tooltip.dispose();
                }
                this.setAttribute('title', 'Error sending invite');
                new bootstrap.Tooltip(this);
              }
            });
          });
        });

        // Handle "Invite All" button
        const inviteAllButton = document.getElementById('inviteAllButton');
        if (inviteAllButton) {
          inviteAllButton.addEventListener('click', function() {
            const projectHash = this.getAttribute('data-project-hash');
            const form = document.getElementById('inviteAllForm');

            // Update button state
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> ⏳ Sending Invites...';
            this.disabled = true;
            this.classList.remove('btn-success', 'btn-danger');
            this.classList.add('btn-secondary');

            // Get the form data
            const formData = new FormData(form);

            // Send AJAX request
            fetch(form.action, {
              method: 'POST',
              body: formData,
              headers: {
                'X-Requested-With': 'XMLHttpRequest'
              },
              credentials: 'same-origin'
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              // Success handling
              const invitedCount = data.invited_count || 'multiple';
              this.innerHTML = `<i class="fas fa-check me-1"></i> ✅ ${invitedCount} Users Invited`;
              this.classList.remove('btn-secondary');
              this.classList.add('btn-success');
              this.classList.add('pulse');

              // Update tooltip
              if (bootstrap && bootstrap.Tooltip) {
                const tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                  tooltip.dispose();
                }
                this.setAttribute('title', `Successfully sent ${invitedCount} invitations`);
                new bootstrap.Tooltip(this);
              }

              // Disable individual invite buttons for invited users
              if (data.invited_users && Array.isArray(data.invited_users)) {
                data.invited_users.forEach(userId => {
                  const button = document.querySelector(`.invite-button[data-user-id="${userId}"]`);
                  if (button) {
                    button.innerHTML = '<i class="fas fa-check me-1"></i> ✅ Invited';
                    button.classList.remove('btn-primary', 'btn-secondary');
                    button.classList.add('btn-success');
                    button.disabled = true;
                  }
                });
              }

              // Keep this button disabled after successful invites
              this.disabled = true;
            })
            .catch(error => {
              // Error handling
              console.error('Error:', error);
              this.innerHTML = '<i class="fas fa-times me-1"></i> ❌ Error — Retry';
              this.classList.remove('btn-secondary');
              this.classList.add('btn-danger', 'invite-button-error');
              this.disabled = false;

              // Update tooltip
              if (bootstrap && bootstrap.Tooltip) {
                const tooltip = bootstrap.Tooltip.getInstance(this);
                if (tooltip) {
                  tooltip.dispose();
                }
                this.setAttribute('title', 'Error sending invites');
                new bootstrap.Tooltip(this);
              }
            });
          });
        }

        // User Profile Modal functionality
        // Select all user cards that should be clickable
        const userCards = document.querySelectorAll('.list-group-item');
        const userProfileModal = document.getElementById('userProfileModal');
        const modalContent = document.getElementById('modalContent');
        const modalLoadingSpinner = document.getElementById('modalLoadingSpinner');

        // Create a function to get a fresh modal instance
        function getModalInstance() {
          if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
            // Dispose any existing modal instance first
            const existingModal = bootstrap.Modal.getInstance(userProfileModal);
            if (existingModal) {
              existingModal.dispose();
            }
            // Create a new modal instance
            return new bootstrap.Modal(userProfileModal);
          }
          return null;
        }

        // Add event listener for when the modal is hidden
        userProfileModal.addEventListener('hidden.bs.modal', function () {
          // Clean up when the modal is closed
          document.getElementById('modalFullName').textContent = '';
          document.getElementById('modalUsername').textContent = '';
          document.getElementById('modalBio').textContent = '';
          document.getElementById('modalSkills').innerHTML = '';
          document.getElementById('modalAvailabilityBadge').innerHTML = '';
          document.getElementById('modalTimezone').textContent = '';
          document.getElementById('modalAvailabilityType').textContent = '';
          document.getElementById('modalCountry').textContent = '';
          // Reset the availability hours span
          const availabilityHoursEl = document.getElementById('modalAvailabilityHours');
          if (availabilityHoursEl) {
            availabilityHoursEl.textContent = '';
          }
          // Hide CV section and reset link
          document.getElementById('cvSection').classList.add('d-none');
          document.getElementById('cvDownloadLink').href = '#';
          // Reset invite button container
          document.getElementById('modalInviteButtonContainer').innerHTML = '';
        });

        userCards.forEach(card => {
          // Make the whole card clickable, excluding buttons
          card.addEventListener('click', function(event) {
            // Don't trigger if clicked on a button or link
            if (event.target.closest('button') || event.target.closest('a') ||
                event.target.tagName === 'BUTTON' || event.target.tagName === 'A') {
              return;
            }

            // Get the username from the card's heading
            const usernameElement = this.querySelector('h5');
            if (!usernameElement) return;

            const username = usernameElement.textContent.trim();
            const projectHash = document.querySelector('[data-project-hash]')?.getAttribute('data-project-hash');

            // Get a fresh modal instance and show it
            const bootstrapModal = getModalInstance();
            if (bootstrapModal) {
              bootstrapModal.show();
            }

            // Display loading spinner, hide content
            modalLoadingSpinner.classList.remove('d-none');
            modalContent.classList.add('d-none');

            // Reset modal elements to default state
            document.getElementById('modalFullName').textContent = 'Loading...';
            document.getElementById('modalUsername').textContent = '@' + username;
            document.getElementById('modalBio').textContent = 'Loading...';
            document.getElementById('modalSkills').innerHTML = '';
            document.getElementById('modalAvailabilityBadge').innerHTML = '';
            document.getElementById('modalTimezone').textContent = 'Loading...';
            document.getElementById('modalAvailabilityType').textContent = 'Loading...';
            document.getElementById('modalCountry').textContent = 'Loading...';
            // Reset availability hours
            const availabilityHoursEl = document.getElementById('modalAvailabilityHours');
            if (availabilityHoursEl) {
              availabilityHoursEl.textContent = 'Loading...';
            }
            document.getElementById('modalInviteButtonContainer').innerHTML = '';

            // Fetch and populate user profile data with a cache-busting parameter
            const timestamp = new Date().getTime(); // Add timestamp to prevent caching
            fetch(`/collaborate/user_profile/${username}/?project_id=${projectId || ''}&_=${timestamp}`, {
              method: 'GET',
              headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
              },
              cache: 'no-store'
            })
              .then(response => {
                if (!response.ok) {
                  throw new Error('Network response was not ok');
                }
                return response.json();
              })
              .then(data => {
                // Update modal content with fetched data
                updateModalWithUserData(data, projectId);

                // Hide spinner, show content
                modalLoadingSpinner.classList.add('d-none');
                modalContent.classList.remove('d-none');
              })
              .catch(error => {
                console.error('Error fetching user profile:', error);
                document.getElementById('modalFullName').textContent = 'Error loading profile';
                document.getElementById('modalBio').textContent = 'There was an error loading this user profile. Please try again later.';

                // Hide spinner, show content with error message
                modalLoadingSpinner.classList.add('d-none');
                modalContent.classList.remove('d-none');
              });
          });
        });

        // Function to update modal content with user data
        function updateModalWithUserData(data, projectId) {
          // Check if data is valid
          if (!data || typeof data !== 'object') {
            console.error('Invalid user data received:', data);
            document.getElementById('modalFullName').textContent = 'Error loading profile';
            document.getElementById('modalBio').textContent = 'There was an error loading this user profile. Please try again later.';
            return;
          }

          // Set user basic info
          document.getElementById('modalFullName').textContent = data.full_name || 'User';
          document.getElementById('modalUsername').textContent = '@' + (data.username || 'unknown');

          // Set bio/description
          const bioText = data.bio || data.description || 'No bio available.';
          document.getElementById('modalBio').textContent = bioText;

          // Set project count
          document.getElementById('projectCountValue').textContent = data.projects_count || 0;

          // Set match score if available
          if (data.match_score !== null && data.match_score !== undefined) {
            document.getElementById('matchScoreValue').textContent = Math.round(data.match_score);
            document.getElementById('modalMatchScore').classList.remove('d-none');
          } else {
            document.getElementById('modalMatchScore').classList.add('d-none');
          }

          // Set availability badge
          const availabilityBadgeEl = document.getElementById('modalAvailabilityBadge');
          if (data.is_available_now) {
            availabilityBadgeEl.innerHTML = '<span class="badge bg-success"><i class="fas fa-clock me-1"></i> Available Now</span>';
          } else {
            availabilityBadgeEl.innerHTML = '<span class="badge bg-secondary"><i class="fas fa-clock me-1"></i> Not Available</span>';
          }

          // Set profile picture or letter avatar
          const profilePicContainer = document.getElementById('modalProfilePicContainer');
          if (data.profile_picture) {
            // Use profile picture
            profilePicContainer.innerHTML = `<img id="modalProfilePic" src="${data.profile_picture}" alt="${data.username}"
                                                 class="rounded-circle" width="80" height="80">`;
          } else {
            // Create letter avatar
            const firstLetter = data.username.charAt(0).toUpperCase();
            // Generate a consistent color based on user_id
            const colorIndex = data.user_id ? ((parseInt(data.user_id) % 9) + 1) : 1;
            profilePicContainer.innerHTML = `<div class="letter-avatar" data-color="${colorIndex}">
                                              ${firstLetter}
                                            </div>`;
          }

          // Set timezone and availability info
          document.getElementById('modalTimezone').textContent = data.timezone || 'Not specified';
          document.getElementById('modalAvailabilityType').textContent = data.availability_type || 'Not specified';
          document.getElementById('modalCountry').textContent = data.country || 'Not specified';

          // Format availability hours if available
          let availabilityHoursText = 'Not specified';
          if (data.availability_start && data.availability_end) {
            availabilityHoursText = `${data.availability_start} - ${data.availability_end}`;
          }

          // Target the availability hours span element
          const availabilityHoursEl = document.getElementById('modalAvailabilityHours');
          if (availabilityHoursEl) {
            availabilityHoursEl.textContent = availabilityHoursText;
          } else {
            console.warn('Could not find availability hours element');
          }

          // Handle CV section
          const cvSection = document.getElementById('cvSection');
          const cvDownloadLink = document.getElementById('cvDownloadLink');

          if (data.has_cv && data.cv_url) {
            // Show CV section and set download link
            cvSection.classList.remove('d-none');
            cvDownloadLink.href = data.cv_url;
          } else {
            // Hide CV section if no CV available
            cvSection.classList.add('d-none');
          }

          // Populate skills
          const skillsContainer = document.getElementById('modalSkills');
          skillsContainer.innerHTML = '';

          if (data.skills && Array.isArray(data.skills) && data.skills.length > 0) {
            data.skills.forEach(skill => {
              // Skip if skill is invalid
              if (!skill || !skill.name) return;

              // Create badge for each skill with appropriate color based on level
              let badgeClass = 'bg-secondary';
              let levelBadgeClass = 'bg-secondary';

              // Determine badge color based on skill level
              const levelValue = parseInt(skill.level_value) || 0;
              if (levelValue >= 4) {  // Expert/Master
                levelBadgeClass = 'bg-success';
              } else if (levelValue >= 2) {  // Intermediate/Advanced
                levelBadgeClass = 'bg-info';
              } else if (levelValue >= 1) {  // Beginner
                levelBadgeClass = 'bg-warning';
              }

              // If this is a critical skill for the project, highlight it
              if (skill.is_critical) {
                badgeClass = 'bg-danger';
              }

              // Create the skill badge with level indicator
              const skillBadge = document.createElement('div');
              skillBadge.className = `badge ${badgeClass} p-2 m-1`;
              skillBadge.innerHTML = `
                ${skill.name}
                <span class="ms-1 badge ${levelBadgeClass} rounded-pill">${skill.level || 'Unknown'}</span>
                ${skill.is_critical ? '<i class="fas fa-star ms-1" title="Critical for this project"></i>' : ''}
              `;
              skillsContainer.appendChild(skillBadge);
            });
          } else {
            skillsContainer.innerHTML = '<p class="text-muted">No skills listed.</p>';
          }

          // Configure invite button in modal if needed
          const inviteButtonContainer = document.getElementById('modalInviteButtonContainer');
          inviteButtonContainer.innerHTML = '';

          // Only show invite button if this is for a project and the user viewing is the project owner
          // and we have a valid user_id
          if (projectId && data.user_id && !data.is_member) {
            // Check if there's an invite button for this user on the page
            const existingInviteButton = document.querySelector(`.invite-button[data-user-id="${data.user_id}"]`);

            // Check if the user is already invited (button is disabled and has success class)
            const isAlreadyInvited = existingInviteButton &&
                                    existingInviteButton.disabled &&
                                    existingInviteButton.classList.contains('btn-success');

            if (existingInviteButton) {
              // Clone the existing invite button logic for the modal
              const inviteButton = document.createElement('button');

              // Set the appropriate button state based on whether the user is already invited
              if (isAlreadyInvited) {
                inviteButton.className = 'btn btn-success';
                inviteButton.innerHTML = '<i class="fas fa-check me-1"></i> ✅ Invited';
                inviteButton.disabled = true;
              } else {
                inviteButton.className = 'btn btn-primary';
                inviteButton.innerHTML = '<i class="fas fa-envelope me-1"></i> 📩 Invite to Project';
              }

              inviteButton.setAttribute('data-user-id', data.user_id);
              inviteButton.setAttribute('data-project-hash', projectHash);

              // Only add event listener if the user is not already invited
              if (!isAlreadyInvited) {
                inviteButton.addEventListener('click', function() {
                  // Update button state to "Sending..."
                  this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> ⏳ Sending...';
                  this.disabled = true;

                  // Get the CSRF token
                  const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                  // Send AJAX request to invite endpoint
                  fetch(`/collaborate/project/${projectHash}/invite/${data.user_id}/`, {
                    method: 'POST',
                    headers: {
                      'X-CSRFToken': csrfToken,
                      'Content-Type': 'application/json',
                      'X-Requested-With': 'XMLHttpRequest'
                    },
                    credentials: 'same-origin'
                  })
                  .then(response => {
                    if (!response.ok) {
                      throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                  })
                  .then(responseData => {
                    // Success handling
                    this.innerHTML = '<i class="fas fa-check me-1"></i> ✅ Invited';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-success');
                    this.disabled = true;

                    // Also update the button on the main page
                    if (existingInviteButton) {
                      existingInviteButton.innerHTML = '<i class="fas fa-check me-1"></i> ✅ Invited';
                      existingInviteButton.classList.remove('btn-primary');
                      existingInviteButton.classList.add('btn-success');
                      existingInviteButton.disabled = true;
                    }
                  })
                  .catch(error => {
                    // Error handling
                    console.error('Error:', error);
                    this.innerHTML = '<i class="fas fa-times me-1"></i> ❌ Error — Retry';
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-danger');
                    this.disabled = false;
                  });
                });
              }

              inviteButtonContainer.appendChild(inviteButton);
            }
          }
        }
      });
    </script>
  </div>
  {% else %}
  <div class="dashboard-section">
    <h3><i class="fas fa-search me-2"></i>No Matching Users</h3>
    <div class="alert alert-warning">
      <i class="fas fa-exclamation-triangle me-2"></i>
      <strong>No matching users found!</strong> This could be because:
      <ul class="mb-0 mt-2">
        <li>There are no users with the required skills for this project</li>
        <li>The project doesn't have any required skills specified</li>
        <li>All potential matches are already members of the project</li>
      </ul>
    </div>

    <div class="card">
      <div class="card-body">
        <h6 style="color: #C0ff6b; margin-bottom: 1rem;">To improve matches, try:</h6>
        <ul style="color: rgba(255, 255, 255, 0.9);">
          <li>Adding more required skills to your project</li>
          <li>Adjusting the required skill set to match available users</li>
          <li>Inviting users manually or switching to application-based recruitment</li>
        </ul>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Navigation and Analysis Tools -->
  <div class="dashboard-section">
    <h3><i class="fas fa-tools me-2"></i>Project Tools & Navigation</h3>
    <div class="d-flex flex-wrap gap-3 justify-content-center">
      <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-lg btn-primary">
        <i class="fas fa-arrow-left me-2"></i> Back to Project
      </a>
      {% if request.user == project.owner %}
        <a href="{% url 'collaborate:team_balance' project.hash %}"
           class="btn btn-outline-success">
          <i class="fas fa-balance-scale me-1"></i> Team Balance Analysis
        </a>
        <a href="{% url 'collaborate:match_feedback' project.hash %}"
           class="btn btn-outline-secondary">
          <i class="fas fa-comment me-1"></i> Match Feedback
        </a>
      {% endif %}
    </div>
  </div>
</div>

<!-- User Profile Modal -->
<div class="modal fade" id="userProfileModal" tabindex="-1" aria-labelledby="userProfileModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="userProfileModalLabel">User Profile</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="text-center mb-4" id="modalLoadingSpinner">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-2">Loading profile data...</p>
        </div>

        <div id="modalContent" class="d-none">
          <!-- Profile Header Section -->
          <div class="d-flex align-items-center mb-4">
            <div id="modalProfilePicContainer" class="profile-picture-container me-3">
              <!-- Profile picture or letter avatar will be inserted here by JavaScript -->
            </div>
            <div>
              <h4 id="modalFullName" class="mb-1">User Name</h4>
              <p id="modalUsername" class="text-muted mb-1">@username</p>
              <div id="modalAvailabilityBadge" class="mt-1"></div>
            </div>
            <div class="ms-auto text-end">
              <div id="modalProjectCount" class="mb-2">
                <span class="badge bg-secondary">
                  <i class="fas fa-folder me-1"></i> <span id="projectCountValue">0</span> Projects
                </span>
              </div>
              <div id="modalMatchScore" class="mb-0">
                <span class="badge bg-primary">
                  <i class="fas fa-percentage me-1"></i> Match Score: <span id="matchScoreValue">0</span>%
                </span>
              </div>
            </div>
          </div>

          <!-- Bio Section -->
          <div class="card mb-3">
            <div class="card-header bg-light">
              <i class="fas fa-user me-2"></i> About
            </div>
            <div class="card-body">
              <p id="modalBio" class="mb-0">No bio available.</p>
            </div>
          </div>

          <!-- Skills Section -->
          <div class="card mb-3">
            <div class="card-header bg-light">
              <i class="fas fa-code me-2"></i> Skills
            </div>
            <div class="card-body">
              <div id="modalSkills" class="d-flex flex-wrap gap-2">
                <!-- Skills will be inserted here -->
              </div>
            </div>
          </div>

          <!-- Availability & Timezone Section -->
          <div class="card mb-3">
            <div class="card-header bg-light">
              <i class="fas fa-clock me-2"></i> Availability & Timezone
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <p><strong>Timezone:</strong> <span id="modalTimezone">UTC</span></p>
                  <p><strong>Availability Type:</strong> <span id="modalAvailabilityType">Flexible</span></p>
                </div>
                <div class="col-md-6" id="modalAvailabilityContainer">
                  <p><strong>Available Hours:</strong> <span id="modalAvailabilityHours">Not specified</span></p>
                  <p><strong>Country:</strong> <span id="modalCountry">Not specified</span></p>
                </div>
              </div>
            </div>
          </div>

          <!-- CV Section -->
          <div class="card mb-3 d-none" id="cvSection">
            <div class="card-header bg-light">
              <i class="fas fa-file-pdf me-2"></i> Curriculum Vitae
            </div>
            <div class="card-body">
              <div id="cvContent" class="text-center">
                <p class="mb-3">This user has uploaded their CV. You can download it to review their qualifications in detail.</p>
                <a href="#" id="cvDownloadLink" class="cv-download-btn" target="_blank">
                  <i class="fas fa-download me-2"></i> Download CV
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <div id="modalInviteButtonContainer">
          <!-- Invite button will be added here if needed -->
        </div>
      </div>
    </div>
  </div>
</div>
</div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c040", "#60a020"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);
});
</script>
{% endblock %}