{% extends "base.html" %}
{% load static %}
{% block title %}{{ lesson.name }} - {{ lesson.chapter.course.name }} - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="collaborate-card fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">📚 Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_details' lesson.chapter.course.id %}">{{ lesson.chapter.course.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:chapter_details' lesson.chapter.id %}">{{ lesson.chapter.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active breadcrumb-current" aria-current="page">
        {{ lesson.name }}
      </li>
    </ol>
  </nav>

  <!-- Lesson Header -->
  <div class="lesson-header">
    <div class="lesson-meta-info">
      <span class="lesson-type-badge lesson-type-{{ lesson.lesson_type }}">
        {% if lesson.lesson_type == 'theory' %}📖 Theory
        {% elif lesson.lesson_type == 'practical' %}🛠️ Practical
        {% elif lesson.lesson_type == 'exercise' %}💪 Exercise
        {% elif lesson.lesson_type == 'project' %}🚀 Project
        {% else %}📝 Lesson{% endif %}
      </span>
      <span class="lesson-duration">⏱️ {{ lesson.estimated_time }}</span>
    </div>
    <h1 class="lesson-title">{{ lesson.name }}</h1>
    <div class="lesson-progress-info">
      <div class="course-info">
        <span class="course-name">{{ lesson.chapter.course.name }}</span>
        <span class="chapter-name">{{ lesson.chapter.name }}</span>
      </div>
    </div>
  </div>

  <!-- Course Management Controls -->
  {% if can_manage_course %}
  <div class="admin-controls">
    <a href="{% url 'learn:create_lesson_content' lesson.id %}" class="btn btn-success">
      <i class="icon">➕</i> Add Content
    </a>
    <a href="{% url 'learn:edit_lesson' lesson.id %}" class="btn btn-primary">
      <i class="icon">✏️</i> Edit Lesson
    </a>
  </div>
  {% endif %}

  <!-- Lesson Content -->
  <div class="lesson-content-container">
    {% if lesson_content %}
      {% for content in lesson_content %}
      <div class="content-block content-type-{{ content.content_type }} slide-up" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;">

        <!-- Content Header -->
        {% if content.title %}
        <div class="content-header">
          <h3 class="content-title">
            {% if content.content_type == 'code_example' %}💻
            {% elif content.content_type == 'tip' %}💡
            {% elif content.content_type == 'warning' %}⚠️
            {% elif content.content_type == 'exercise' %}🏋️
            {% elif content.content_type == 'summary' %}📋
            {% else %}📝{% endif %}
            {{ content.title }}
          </h3>
        </div>
        {% endif %}

        <!-- Content Body -->
        <div class="content-body">
          {% if content.content_type == 'code_example' and content.code_language %}
            <div class="code-example-container">
              <div class="code-header">
                <span class="code-language">{{ content.code_language|upper }}</span>
                <div class="code-actions">
                  <button class="copy-code-btn" onclick="copyCode(this)">📋 Copy</button>
                  {% if ai_enabled %}
                  <button class="analyze-code-btn" onclick="analyzeThisCode(this)" data-language="{{ content.code_language }}">🔍 AI Analyze</button>
                  {% endif %}
                </div>
              </div>
              <pre class="code-block"><code class="language-{{ content.code_language }}">{{ content.content }}</code></pre>
            </div>
          {% else %}
            <div class="text-content">
              {{ content.content|safe }}
            </div>
          {% endif %}
        </div>

        <!-- Content Management Actions -->
        {% if can_manage_course %}
        <div class="content-admin-actions">
          <a href="{% url 'learn:edit_lesson_content' content.id %}" class="btn btn-secondary btn-sm">
            <i class="icon">✏️</i> Edit
          </a>
          <a href="{% url 'learn:delete_lesson_content' content.id %}"
             onclick="return confirm('Delete this content?')"
             class="btn btn-danger btn-sm">
            <i class="icon">🗑️</i> Delete
          </a>
        </div>
        {% endif %}
      </div>
      {% endfor %}
    {% else %}
      <div class="empty-content">
        <div class="empty-icon">📝</div>
        <h3>No Content Available Yet</h3>
        <p>This lesson content is being prepared. Check back soon!</p>
        {% if can_manage_course %}
        <a href="{% url 'learn:create_lesson_content' lesson.id %}" class="btn btn-primary">
          Add First Content
        </a>
        {% endif %}
      </div>
    {% endif %}
  </div>

  <!-- AI Learning Features -->
  {% if ai_enabled %}
  <div class="ai-learning-section">
    <h2 class="ai-section-title">🤖 AI Learning Assistant</h2>

    <!-- Progress Tracking -->
    {% if lesson_progress %}
    <div class="ai-progress-card">
      <div class="progress-header">
        <h3>📊 Your Progress</h3>
        <div class="progress-stats">
          <span class="progress-status status-{{ lesson_progress.status }}">
            {% if lesson_progress.status == 'completed' %}✅ Completed
            {% elif lesson_progress.status == 'in_progress' %}🔄 In Progress
            {% elif lesson_progress.status == 'mastered' %}🏆 Mastered
            {% else %}📝 Not Started{% endif %}
          </span>
          {% if lesson_progress.score %}
          <span class="progress-score">{{ lesson_progress.score|floatformat:0 }}% Score</span>
          {% endif %}
          <span class="study-time">⏱️ {{ lesson_progress.time_spent }}m studied</span>
        </div>
      </div>
      <div class="progress-actions">
        <button onclick="markAsCompleted()" class="btn btn-success">Mark as Completed ✅</button>
        <button onclick="rateLesson()" class="btn btn-secondary">Rate Difficulty ⭐</button>
      </div>
    </div>
    {% endif %}

    <!-- AI Assistant Panel -->
    <div class="ai-assistant-panel">
      <div class="assistant-header">
        <h3>💬 Ask AI Assistant</h3>
        <button class="toggle-assistant" onclick="toggleAssistant()">
          <span id="assistant-toggle-text">Show Assistant</span>
        </button>
      </div>
      <div class="assistant-content" id="ai-assistant" style="display: none;">
        <div class="chat-container">
          <div class="chat-messages" id="chat-messages">
            {% for conversation in recent_conversations %}
            <div class="message user-message">
              <div class="message-content">{{ conversation.user_message }}</div>
              <div class="message-time">{{ conversation.created_at|timesince }} ago</div>
            </div>
            <div class="message ai-message">
              <div class="message-content">{{ conversation.ai_response|safe }}</div>
            </div>
            {% endfor %}
          </div>
          <div class="chat-input-container">
            <textarea id="chat-input" placeholder="Ask me anything about this lesson..." rows="3"></textarea>
            <button onclick="sendMessage()" class="send-btn">Send 🚀</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Code Analysis Panel -->
    <div class="code-analysis-panel">
      <div class="analysis-header">
        <h3>🔍 AI Code Analyzer</h3>
        <button class="toggle-analysis" onclick="toggleCodeAnalysis()">
          <span id="analysis-toggle-text">Show Code Analyzer</span>
        </button>
      </div>
      <div class="analysis-content" id="code-analysis" style="display: none;">
        <div class="code-input-section">
          <div class="input-controls">
            <select id="code-language">
              <option value="python">Python</option>
              <option value="javascript">JavaScript</option>
              <option value="java">Java</option>
              <option value="cpp">C++</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
            </select>
            <select id="analysis-type">
              <option value="comprehensive">Comprehensive Analysis</option>
              <option value="syntax_check">Syntax Check</option>
              <option value="best_practices">Best Practices</option>
              <option value="performance">Performance</option>
              <option value="security">Security</option>
            </select>
            <button onclick="analyzeCode()" class="analyze-btn">Analyze Code 🔍</button>
          </div>
          <textarea id="code-input" placeholder="Paste your code here for AI analysis..." rows="10"></textarea>
        </div>
        <div class="analysis-results" id="analysis-results" style="display: none;">
          <div class="score-display">
            <span class="score-circle" id="code-score">0</span>
            <span class="score-label">Code Quality Score</span>
          </div>
          <div class="feedback-content" id="ai-feedback"></div>
          <div class="suggestions-list" id="suggestions-list"></div>
        </div>
      </div>
    </div>

    <!-- AI Recommendations -->
    {% if recommendations %}
    <div class="ai-recommendations">
      <h3>🎯 Personalized Recommendations</h3>
      <div class="recommendations-grid">
        {% for rec in recommendations %}
        <div class="recommendation-card">
          <div class="rec-type">{{ rec.type|title }}</div>
          <h4>{{ rec.title }}</h4>
          <p>{{ rec.description }}</p>
          {% if rec.lesson %}
          <a href="{% url 'learn:lesson_details' rec.lesson.id %}" class="rec-action">Start Learning →</a>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>
  {% elif setup_required %}
  <div class="ai-setup-notice">
    <h3>🤖 AI Learning Features Available</h3>
    <p>Run database migrations to enable AI-powered learning assistance, code analysis, and personalized recommendations.</p>
    <code>python manage.py makemigrations learn && python manage.py migrate</code>
  </div>
  {% endif %}

  <!-- Lesson Navigation -->
  <div class="lesson-navigation">
    <div class="nav-buttons">
      {% if prev_lesson %}
      <a href="{% url 'learn:lesson_details' prev_lesson.id %}" class="nav-btn prev-btn">
        <span class="nav-icon">⬅️</span>
        <div class="nav-text">
          <span class="nav-label">Previous Lesson</span>
          <span class="nav-title">{{ prev_lesson.name }}</span>
        </div>
      </a>
      {% elif prev_chapter_last_lesson %}
      <a href="{% url 'learn:lesson_details' prev_chapter_last_lesson.id %}" class="nav-btn prev-btn">
        <span class="nav-icon">⬅️</span>
        <div class="nav-text">
          <span class="nav-label">Previous Chapter</span>
          <span class="nav-title">{{ prev_chapter_last_lesson.name }}</span>
        </div>
      </a>
      {% endif %}

      <a href="{% url 'learn:chapter_details' lesson.chapter.id %}" class="nav-btn chapter-btn">
        <span class="nav-icon">📚</span>
        <div class="nav-text">
          <span class="nav-label">Back to Chapter</span>
          <span class="nav-title">{{ lesson.chapter.name }}</span>
        </div>
      </a>

      {% if next_lesson %}
      <a href="{% url 'learn:lesson_details' next_lesson.id %}" class="nav-btn next-btn">
        <div class="nav-text">
          <span class="nav-label">Next Lesson</span>
          <span class="nav-title">{{ next_lesson.name }}</span>
        </div>
        <span class="nav-icon">➡️</span>
      </a>
      {% elif next_chapter_first_lesson %}
      <a href="{% url 'learn:lesson_details' next_chapter_first_lesson.id %}" class="nav-btn next-btn">
        <div class="nav-text">
          <span class="nav-label">Next Chapter</span>
          <span class="nav-title">{{ next_chapter_first_lesson.name }}</span>
        </div>
        <span class="nav-icon">➡️</span>
      </a>
      {% endif %}
    </div>
  </div>
</div>
</section>

<!-- Include CSS and JavaScript for AI features -->
<link rel="stylesheet" href="{% static 'css/ai_learning.css' %}">
<script src="{% static 'js/ai_learning.js' %}"></script>

<!-- Particles.js and animations script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css">

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 40,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.2,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 2,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.1,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-up');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    // Run animations
    setTimeout(revealElements, 200);
  });

  // Copy code functionality
  function copyCode(button) {
    const codeBlock = button.parentElement.nextElementSibling.querySelector('code');
    const text = codeBlock.textContent;

    navigator.clipboard.writeText(text).then(function() {
      button.textContent = '✅ Copied!';
      setTimeout(() => {
        button.textContent = '📋 Copy';
      }, 2000);
    });
  }

  // Enhanced copy code functionality for AI features
  function analyzeThisCode(button) {
    const codeBlock = button.closest('.code-example-container').querySelector('code');
    const language = button.getAttribute('data-language') || 'python';
    const code = codeBlock.textContent;

    // Fill the code analysis form
    document.getElementById('code-input').value = code;
    document.getElementById('code-language').value = language;

    // Show code analysis panel
    if (!isCodeAnalysisVisible) {
        toggleCodeAnalysis();
    }

    // Scroll to code analysis
    document.getElementById('code-analysis').scrollIntoView({ behavior: 'smooth' });

    // Auto-analyze
    setTimeout(analyzeCode, 500);
  }
</script>

{% if ai_enabled %}
<script>
// Initialize lesson data for JavaScript
window.lessonData = {
    lessonId: {{ lesson.id }},
    startTime: Date.now(),
    timeSpent: {{ lesson_progress.time_spent|default:0 }},
    status: '{{ lesson_progress.status|default:"not_started" }}'
};
</script>
{% endif %}
{% endblock %}