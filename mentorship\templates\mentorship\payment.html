{% extends 'base.html' %}
{% load static %}

{% block title %}Payment - ForgeX{% endblock %}

{% block content %}
<div class="payment-page">
    <div class="container">
        <div class="payment-container">
            <!-- Session Details -->
            <div class="session-summary">
                <h2>Complete Your Booking</h2>

                <div class="session-card">
                    <div class="mentor-info">
                        <div class="mentor-avatar">
                            {% if session.mentor.profile and session.mentor.profile.profile_picture %}
                                <img src="{{ session.mentor.profile.profile_picture.url }}"
                                     alt="{{ session.mentor.get_full_name }}"
                                     class="profile-image">
                            {% else %}
                                <div class="avatar-initials">
                                    {{ session.mentor.first_name|first|upper }}{{ session.mentor.last_name|first|upper }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mentor-details">
                            <h3>{{ session.mentor.get_full_name|default:session.mentor.username }}</h3>
                            <p class="session-type">{{ session.get_duration_hours }} Hour Mentorship Session</p>
                        </div>
                    </div>

                    <div class="session-details">
                        <div class="detail-row">
                            <span class="label">Date & Time:</span>
                            <span class="value">{{ session.scheduled_time|date:"M d, Y" }} at {{ session.scheduled_time|time:"g:i A" }}</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Duration:</span>
                            <span class="value">{{ session.duration_minutes }} minutes</span>
                        </div>
                        <div class="detail-row">
                            <span class="label">Hourly Rate:</span>
                            <span class="value">${{ session.hourly_rate }}</span>
                        </div>
                    </div>

                    <div class="pricing-breakdown">
                        <div class="price-row">
                            <span>Session Cost:</span>
                            <span>${{ session.total_amount }}</span>
                        </div>
                        <div class="price-row commission">
                            <span>Platform Fee (15%):</span>
                            <span>${{ session.commission_amount }}</span>
                        </div>
                        <div class="price-row total">
                            <span>Total:</span>
                            <span>${{ session.total_amount }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="payment-form-section">
                <h3>Payment Information</h3>

                <form id="payment-form">
                    <div class="payment-element">
                        <!-- Stripe Elements will create form elements here -->
                        <div id="payment-element">
                            <!-- Elements will create form elements here -->
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" id="submit-button" class="btn btn-primary btn-large">
                            <span id="button-text">Pay ${{ session.total_amount }}</span>
                            <div id="spinner" class="spinner hidden"></div>
                        </button>
                    </div>

                    <div id="payment-message" class="payment-message hidden"></div>
                </form>

                <div class="security-info">
                    <div class="security-badges">
                        <i class="fas fa-lock"></i>
                        <span>Secured by Stripe</span>
                    </div>
                    <p class="security-text">Your payment information is encrypted and secure. We never store your card details.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.payment-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 2rem 0;
}

.payment-container {
    max-width: 1000px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.session-summary h2 {
    color: #C0ff6b;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.session-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.mentor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mentor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #C0ff6b;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    overflow: hidden;
}

.mentor-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mentor-details h3 {
    margin: 0 0 0.5rem 0;
    color: #ffffff;
}

.session-type {
    color: #C0ff6b;
    margin: 0;
    font-weight: 600;
}

.session-details {
    margin-bottom: 2rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.detail-row .label {
    color: rgba(255,255,255,0.7);
}

.detail-row .value {
    font-weight: 600;
}

.pricing-breakdown {
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 1.5rem;
}

.price-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.price-row.commission {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.price-row.total {
    font-weight: 700;
    font-size: 1.2rem;
    color: #C0ff6b;
    border-top: 1px solid rgba(255,255,255,0.1);
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

.payment-form-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.payment-form-section h3 {
    color: #C0ff6b;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

#payment-element {
    margin-bottom: 2rem;
}

.form-actions {
    margin-bottom: 2rem;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    width: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #666, #555);
    box-shadow: none;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #1a1a1a;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner.hidden {
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.payment-message {
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.payment-message.hidden {
    display: none;
}

.payment-message.error {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    color: #ff6b6b;
}

.payment-message.success {
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    color: #C0ff6b;
}

.security-info {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.security-badges {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #C0ff6b;
    font-weight: 600;
}

.security-text {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
    margin: 0;
}

@media (max-width: 768px) {
    .payment-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mentor-info {
        flex-direction: column;
        text-align: center;
    }
}
</style>

<script src="https://js.stripe.com/v3/"></script>
<script>
const stripe = Stripe('{{ stripe_public_key }}');
let elements;
let paymentElement;

document.addEventListener('DOMContentLoaded', async () => {
    await initializePayment();
});

async function initializePayment() {
    try {
        // Create payment intent
        const response = await fetch(`/mentorship/payment/{{ session.id }}/create-intent/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            }
        });

        const { client_secret, payment_intent_id } = await response.json();

        // Initialize Stripe Elements
        elements = stripe.elements({
            clientSecret: client_secret,
            appearance: {
                theme: 'night',
                variables: {
                    colorPrimary: '#C0ff6b',
                    colorBackground: 'rgba(255,255,255,0.05)',
                    colorText: '#ffffff',
                    colorDanger: '#ff6b6b',
                    fontFamily: 'system-ui, sans-serif',
                    spacingUnit: '4px',
                    borderRadius: '8px',
                }
            }
        });

        paymentElement = elements.create('payment');
        paymentElement.mount('#payment-element');

        // Handle form submission
        const form = document.getElementById('payment-form');
        form.addEventListener('submit', handleSubmit);

    } catch (error) {
        showMessage('Failed to initialize payment. Please refresh the page.', 'error');
    }
}

async function handleSubmit(event) {
    event.preventDefault();

    setLoading(true);

    const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
            return_url: `${window.location.origin}/mentorship/payment/{{ session.id }}/success/`,
        },
    });

    if (error) {
        if (error.type === "card_error" || error.type === "validation_error") {
            showMessage(error.message, 'error');
        } else {
            showMessage("An unexpected error occurred.", 'error');
        }
    }

    setLoading(false);
}

function setLoading(isLoading) {
    const submitButton = document.getElementById('submit-button');
    const buttonText = document.getElementById('button-text');
    const spinner = document.getElementById('spinner');

    if (isLoading) {
        submitButton.disabled = true;
        buttonText.style.display = 'none';
        spinner.classList.remove('hidden');
    } else {
        submitButton.disabled = false;
        buttonText.style.display = 'inline';
        spinner.classList.add('hidden');
    }
}

function showMessage(messageText, type = 'error') {
    const messageContainer = document.getElementById('payment-message');
    messageContainer.textContent = messageText;
    messageContainer.className = `payment-message ${type}`;
    messageContainer.classList.remove('hidden');

    setTimeout(() => {
        messageContainer.classList.add('hidden');
    }, 5000);
}
</script>
{% endblock %}
