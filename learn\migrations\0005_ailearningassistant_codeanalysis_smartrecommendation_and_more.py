# Generated by Django 5.2.1 on 2025-06-09 05:08

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("learn", "0004_alter_course_options_course_created_by_and_more"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AILearningAssistant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "interaction_type",
                    models.CharField(
                        choices=[
                            ("question", "Question"),
                            ("explanation", "Explanation Request"),
                            ("code_help", "Code Help"),
                            ("concept_clarification", "Concept Clarification"),
                            ("study_plan", "Study Plan Request"),
                        ],
                        default="question",
                        max_length=30,
                    ),
                ),
                ("user_message", models.TextField()),
                ("ai_response", models.TextField()),
                (
                    "context_data",
                    models.J<PERSON><PERSON>ield(
                        default=dict, help_text="Additional context for AI response"
                    ),
                ),
                (
                    "helpful_rating",
                    models.IntegerField(
                        blank=True, help_text="User rating 1-5", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "lesson",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="learn.lesson",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="CodeAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code_content", models.TextField()),
                ("language", models.CharField(max_length=50)),
                (
                    "analysis_type",
                    models.CharField(
                        choices=[
                            ("syntax_check", "Syntax Check"),
                            ("best_practices", "Best Practices"),
                            ("performance", "Performance Analysis"),
                            ("security", "Security Review"),
                            ("style", "Code Style"),
                        ],
                        default="syntax_check",
                        max_length=30,
                    ),
                ),
                ("ai_feedback", models.TextField()),
                (
                    "suggestions",
                    models.JSONField(
                        default=list, help_text="List of improvement suggestions"
                    ),
                ),
                ("score", models.FloatField(help_text="Code quality score 0-100")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "lesson",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="learn.lesson",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="SmartRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "recommendation_type",
                    models.CharField(
                        choices=[
                            ("next_lesson", "Next Lesson"),
                            ("review_content", "Review Content"),
                            ("practice_exercise", "Practice Exercise"),
                            ("skill_improvement", "Skill Improvement"),
                            ("course_suggestion", "Course Suggestion"),
                        ],
                        max_length=30,
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "confidence_score",
                    models.FloatField(
                        help_text="AI confidence in recommendation (0-1)"
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(default=1, help_text="Priority level 1-5"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("clicked", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField(blank=True, null=True)),
                (
                    "target_course",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="learn.course",
                    ),
                ),
                (
                    "target_lesson",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="learn.lesson",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-priority", "-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserLearningProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "learning_style",
                    models.CharField(
                        choices=[
                            ("visual", "Visual Learner"),
                            ("auditory", "Auditory Learner"),
                            ("kinesthetic", "Kinesthetic Learner"),
                            ("reading", "Reading/Writing Learner"),
                        ],
                        default="visual",
                        max_length=20,
                    ),
                ),
                (
                    "current_skill_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        default="beginner",
                        max_length=20,
                    ),
                ),
                (
                    "preferred_difficulty",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        default="beginner",
                        max_length=20,
                    ),
                ),
                (
                    "learning_goals",
                    models.TextField(
                        blank=True, help_text="User's learning objectives"
                    ),
                ),
                (
                    "strengths",
                    models.JSONField(default=list, help_text="Areas where user excels"),
                ),
                (
                    "weaknesses",
                    models.JSONField(
                        default=list, help_text="Areas needing improvement"
                    ),
                ),
                (
                    "total_study_time",
                    models.PositiveIntegerField(
                        default=0, help_text="Total minutes spent learning"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="learning_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="LessonProgress",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("mastered", "Mastered"),
                        ],
                        default="not_started",
                        max_length=20,
                    ),
                ),
                (
                    "time_spent",
                    models.PositiveIntegerField(
                        default=0, help_text="Minutes spent on this lesson"
                    ),
                ),
                ("attempts", models.PositiveIntegerField(default=0)),
                (
                    "score",
                    models.FloatField(
                        blank=True,
                        help_text="AI-calculated comprehension score",
                        null=True,
                    ),
                ),
                (
                    "difficulty_rating",
                    models.FloatField(
                        blank=True, help_text="User's difficulty rating 1-5", null=True
                    ),
                ),
                (
                    "ai_insights",
                    models.JSONField(
                        default=dict, help_text="AI-generated learning insights"
                    ),
                ),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("last_accessed", models.DateTimeField(auto_now=True)),
                (
                    "lesson",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="learn.lesson"
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "lesson")},
            },
        ),
    ]
