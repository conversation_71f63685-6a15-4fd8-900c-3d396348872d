/* Parallax and Animation CSS for ForgeX */

/* Fix for text visibility - ensure all text is visible */
.hero-content h1,
.hero-content h2,
.feature-content h2,
.feature-content p,
.testimonial-content p,
.testimonial-author p,
.section-title,
.cta-content h2,
.cta-content p {
  color: #ffffff !important;
}

/* General Parallax Styles */
.parallax-section {
  position: relative;
  overflow: hidden;
}

.parallax-img {
  transition: transform 0.5s ease-out;
  will-change: transform;
}

.parallax-card {
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* Particles Background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
  opacity: 0.5; /* Reduce opacity to ensure text is more visible */
  pointer-events: none; /* Ensure it doesn't interfere with clicks */
}

/* Hero Section Enhancements */
.hero {
  position: relative;
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 64px 100px;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(28, 28, 28, 0.5) 0%, rgba(28, 28, 28, 0.9) 100%);
  z-index: -1;
}

.hero-content {
  max-width: 576px;
  z-index: 2;
}

.animated-text {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.animated-text::after {
  content: '|';
  position: absolute;
  right: 0;
  animation: blink 0.7s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.highlight {
  color: var(--color-border);
  text-shadow: 0 0 10px var(--color-border), 0 0 20px var(--color-border);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 0 5px var(--color-border), 0 0 10px var(--color-border); }
  to { text-shadow: 0 0 10px var(--color-border), 0 0 20px var(--color-border), 0 0 30px var(--color-border); }
}

.animated-subtitle {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 30px;
  opacity: 0;
  animation: fadeIn 1s ease-out 1s forwards;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.pulse-button {
  background-color: var(--color-border);
  color: #000;
  border: none;
  padding: 12px 30px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.pulse-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  z-index: -1;
  transform: scale(0);
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.pulse-button:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px var(--color-border), 0 0 30px var(--color-border);
}

.pulse-button.active::before {
  transform: scale(2);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

/* Features Section Enhancements */
.features {
  padding: 100px 100px;
  position: relative;
  z-index: 1;
}

.feature-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 90px;
  margin-bottom: 100px;
  padding: 30px;
  border-radius: 15px;
  background: rgba(28, 28, 28, 0.5);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.feature-content {
  max-width: 500px;
}

.animated-heading {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  position: relative;
}

.animated-bar {
  width: 50px;
  height: 3px;
  background: var(--color-border);
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
}

.animated-bar::before {
  content: '';
  position: absolute;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% { left: 100%; }
}

.feature-image img {
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.5s ease;
}

.feature-image img:hover {
  transform: scale(1.05);
}

/* Testimonials Section Enhancements */
.testimonials {
  padding: 100px 100px;
  text-align: center;
  position: relative;
  background: linear-gradient(to bottom, rgba(28, 28, 28, 0.8), rgba(28, 28, 28, 1));
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 50px;
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: var(--color-border);
}

.testimonials-grid {
  display: flex;
  gap: 32px;
  justify-content: center;
  flex-wrap: wrap;
}

.testimonial-card {
  position: relative;
  max-width: 376px;
  padding: 30px;
  border-radius: 15px;
  background: rgba(40, 40, 40, 0.5);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
}

.floating-card {
  transition: transform 0.5s ease;
}

.testimonial-card:hover {
  transform: translateY(-10px) !important;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.glow-effect {
  position: relative;
  z-index: 1;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--color-border), transparent, var(--color-border));
  z-index: -1;
  border-radius: 15px;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.testimonial-card:hover .glow-effect::before {
  opacity: 0.5;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.profile-img-animate {
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.testimonial-card:hover .profile-img-animate {
  transform: scale(1.1);
}

/* Call to Action Section */
.cta-section {
  padding: 100px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1c1c1c 0%, #2d2d2d 100%);
  z-index: -1;
}

.cta-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.1) 0%, transparent 70%);
  animation: pulse 4s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  z-index: 1;
  position: relative;
}

.glow-button {
  background-color: transparent;
  color: #fff;
  border: 2px solid var(--color-border);
  padding: 12px 30px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  margin-top: 30px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.glow-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.3), transparent);
  transition: left 0.5s ease;
}

.glow-button:hover, .glow-button.active {
  background-color: var(--color-border);
  color: #000;
  box-shadow: 0 0 15px var(--color-border), 0 0 30px var(--color-border);
}

.glow-button.active::before {
  left: 100%;
}

/* Reveal Animations */
.reveal-element {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.reveal-element.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in {
  opacity: 0;
  animation: fadeIn 1s ease forwards;
  animation-play-state: paused;
}

.slide-right {
  opacity: 0;
  transform: translateX(-50px);
  animation: slideRight 1s ease forwards;
  animation-play-state: paused;
}

.slide-left {
  opacity: 0;
  transform: translateX(50px);
  animation: slideLeft 1s ease forwards;
  animation-play-state: paused;
}

@keyframes slideRight {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideLeft {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Header and Footer Enhancements */
.header-main {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  background-color: rgba(28, 28, 28, 0.8);
  backdrop-filter: blur(10px);
}

.header-main.scrolled {
  background-color: rgba(28, 28, 28, 0.95);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.logo-anchor {
  position: relative;
  display: inline-block;
}

.logo-anchor::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.logo-anchor:hover::before {
  width: 100%;
}

.nav-link {
  position: relative;
  transition: color 0.3s ease, transform 0.3s ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: var(--color-border);
  transition: width 0.3s ease;
}

.nav-link:hover::after {
  width: 100%;
}

footer {
  position: relative;
  padding: 30px 0;
  background-color: rgba(28, 28, 28, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(192, 255, 107, 0.2);
}

.copyright-text {
  position: relative;
  text-align: center;
}

.copyright-text::before {
  content: '';
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 2px;
  background-color: var(--color-border);
}

/* Main Content Adjustment for Fixed Header */
main {
  padding-top: 80px;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .hero, .features, .testimonials {
    padding: 64px 50px;
  }

  .feature-card {
    flex-direction: column;
    gap: 40px;
  }

  .feature-image, .feature-content {
    width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .hero, .features, .testimonials {
    padding: 50px 30px;
  }

  .testimonials-grid {
    flex-direction: column;
    align-items: center;
  }

  .animated-text {
    font-size: 36px;
  }

  .animated-subtitle {
    font-size: 18px;
  }
}
