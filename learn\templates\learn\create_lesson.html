{% extends "base.html" %}
{% load static %}
{% block title %}Create Lesson - {{ chapter.name }} - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="course-form-container fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">📚 Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_details' chapter.course.id %}">{{ chapter.course.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:chapter_details' chapter.id %}">{{ chapter.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active" aria-current="page">Create Lesson</li>
    </ol>
  </nav>

  <!-- Form Header -->
  <div class="admin-form-header">
    <h1>📝 Create New Lesson</h1>
    <p>Add a new lesson to <strong>{{ chapter.name }}</strong></p>
  </div>

  <!-- Lesson Creation Form -->
  <form method="POST" enctype="multipart/form-data" class="lesson-creation-form">
    {% csrf_token %}

    <!-- Display form errors if any -->
    {% if form.errors %}
    <div class="form-errors">
      <h4>Please correct the following errors:</h4>
      <ul>
        {% for field, errors in form.errors.items %}
          {% for error in errors %}
            <li>{{ field|title }}: {{ error }}</li>
          {% endfor %}
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- Lesson Information Section -->
    <div class="form-section">
      <h3 class="form-section-title">Lesson Information</h3>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.name.id_for_label }}" class="required">Lesson Title</label>
          {{ form.name }}
          {% if form.name.help_text %}
            <div class="form-help-text">{{ form.name.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="{{ form.lesson_type.id_for_label }}" class="required">Lesson Type</label>
          {{ form.lesson_type }}
          {% if form.lesson_type.help_text %}
            <div class="form-help-text">{{ form.lesson_type.help_text }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.estimated_time.id_for_label }}" class="required">Estimated Time</label>
          {{ form.estimated_time }}
          {% if form.estimated_time.help_text %}
            <div class="form-help-text">{{ form.estimated_time.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.order.id_for_label }}" class="required">Lesson Order</label>
          {{ form.order }}
          {% if form.order.help_text %}
            <div class="form-help-text">{{ form.order.help_text }}</div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <a href="{% url 'learn:chapter_details' chapter.id %}" class="btn btn-secondary">
        <span>❌</span> Cancel
      </a>
      <button type="submit" class="btn btn-primary">
        <span>✅</span> Create Lesson
      </button>
    </div>
  </form>
</div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 20,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.1,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.03,
            "sync": false
          }
        },
        "size": {
          "value": 2,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.05,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 0.5,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for form
    setTimeout(() => {
      document.querySelector('.course-form-container').classList.add('visible');
    }, 200);
  });
</script>
{% endblock %}
