{% extends "base.html" %}
{% load static %}
{% block title %}Create Content - {{ lesson.name }} - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="course-form-container fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">📚 Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_details' lesson.chapter.course.id %}">{{ lesson.chapter.course.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:chapter_details' lesson.chapter.id %}">{{ lesson.chapter.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:lesson_details' lesson.id %}">{{ lesson.name }}</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active" aria-current="page">Create Content</li>
    </ol>
  </nav>

  <!-- Form Header -->
  <div class="admin-form-header">
    <h1>📄 Create Lesson Content</h1>
    <p>Add content to <strong>{{ lesson.name }}</strong></p>
  </div>

  <!-- Content Creation Form -->
  <form method="POST" enctype="multipart/form-data" class="content-creation-form">
    {% csrf_token %}

    <!-- Display form errors if any -->
    {% if form.errors %}
    <div class="form-errors">
      <h4>Please correct the following errors:</h4>
      <ul>
        {% for field, errors in form.errors.items %}
          {% for error in errors %}
            <li>{{ field|title }}: {{ error }}</li>
          {% endfor %}
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- Content Information Section -->
    <div class="form-section">
      <h3 class="form-section-title">Content Information</h3>

      <div class="form-row">
        <div class="form-group">
          <label for="{{ form.content_type.id_for_label }}" class="required">Content Type</label>
          {{ form.content_type }}
          {% if form.content_type.help_text %}
            <div class="form-help-text">{{ form.content_type.help_text }}</div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.order.id_for_label }}" class="required">Content Order</label>
          {{ form.order }}
          {% if form.order.help_text %}
            <div class="form-help-text">{{ form.order.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.title.id_for_label }}">Content Title</label>
          {{ form.title }}
          {% if form.title.help_text %}
            <div class="form-help-text">{{ form.title.help_text }}</div>
          {% endif %}
        </div>
      </div>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.code_language.id_for_label }}">Code Language (for code examples)</label>
          {{ form.code_language }}
          {% if form.code_language.help_text %}
            <div class="form-help-text">{{ form.code_language.help_text }}</div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Content Body Section -->
    <div class="form-section">
      <h3 class="form-section-title">Content Body</h3>

      <div class="form-row single">
        <div class="form-group">
          <label for="{{ form.content.id_for_label }}" class="required">Content</label>
          {{ form.content }}
          {% if form.content.help_text %}
            <div class="form-help-text">{{ form.content.help_text }}</div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
      <a href="{% url 'learn:lesson_details' lesson.id %}" class="btn btn-secondary">
        <span>❌</span> Cancel
      </a>
      <button type="submit" class="btn btn-primary">
        <span>✅</span> Create Content
      </button>
    </div>
  </form>
</div>
</section>

<!-- Enhanced Rich Text Editor -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sceditor@3/minified/themes/default.min.css" />
<script src="https://cdn.jsdelivr.net/npm/sceditor@3/minified/sceditor.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sceditor@3/minified/formats/xhtml.js"></script>
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<style>
  /* Enhanced SCEditor styling */
  textarea[name="content"] {
    min-height: 300px;
    min-width: 100%;
    box-sizing: border-box;
  }

  .sceditor-container {
    min-height: 300px !important;
    width: 100% !important;
    margin-bottom: 1em;
    z-index: 1;
    border-radius: 12px !important;
    overflow: hidden;
    border: 2px solid rgba(192, 255, 107, 0.3) !important;
  }

  .sceditor-toolbar {
    background: rgba(40, 40, 40, 0.9) !important;
    border-bottom: 1px solid rgba(192, 255, 107, 0.2) !important;
  }

  .sceditor-group {
    background: transparent !important;
  }

  .sceditor-button {
    background: rgba(60, 60, 60, 0.8) !important;
    border: 1px solid rgba(192, 255, 107, 0.2) !important;
    border-radius: 4px !important;
    margin: 2px !important;
  }

  .sceditor-button:hover {
    background: rgba(192, 255, 107, 0.2) !important;
    border-color: var(--color-border) !important;
  }

  .sceditor-button.active {
    background: var(--color-border) !important;
    color: #1c1c1c !important;
  }

  /* Fix SCEditor dropdowns */
  .sceditor-dropdown,
  .sceditor-dropdown * {
    background: #fff !important;
    color: #222 !important;
  }

  .sceditor-dropdown option {
    background: #fff !important;
    color: #222 !important;
  }

  /* Content area styling */
  .sceditor-container iframe {
    background: rgba(247, 247, 247, 0.9) !important;
  }
</style>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 15,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.08,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.02,
            "sync": false
          }
        },
        "size": {
          "value": 2,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.03,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 0.3,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Initialize rich text editor
    var textarea = document.querySelector('textarea[name="content"]');
    if (textarea) {
      sceditor.create(textarea, {
        format: "xhtml",
        style: "https://cdn.jsdelivr.net/npm/sceditor@3/minified/themes/content/default.min.css",
        width: "100%",
        height: 350,
        toolbar: "bold,italic,underline,strike,subscript,superscript|left,center,right,justify|font,size,color,removeformat|bulletlist,orderedlist,table|link,unlink,image,youtube,date,time,source",
        resizeEnabled: true,
        resizeMinHeight: 300,
        resizeMaxHeight: 600
      });
    }

    // Animation for form
    setTimeout(() => {
      document.querySelector('.course-form-container').classList.add('visible');
    }, 200);
  });
</script>
{% endblock %}
