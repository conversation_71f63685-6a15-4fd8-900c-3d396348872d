"""
Django management command to test Docker connection
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import requests
import subprocess
import time

class Command(BaseCommand):
    help = 'Test Docker connection and start services if needed'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start',
            action='store_true',
            help='Try to start Docker services if they are not running',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🐳 Testing Docker Connection'))
        self.stdout.write('=' * 50)
        
        # Test FastAPI connection
        fastapi_url = getattr(settings, 'FASTAPI_SERVICE_URL', 'http://localhost:8002')
        self.stdout.write(f'Testing FastAPI at: {fastapi_url}')
        
        try:
            response = requests.get(fastapi_url, timeout=5)
            self.stdout.write(self.style.SUCCESS(f'✅ FastAPI Response: {response.status_code}'))
            
            # Test files endpoint
            test_response = requests.get(f'{fastapi_url}/api/files/test', timeout=5)
            self.stdout.write(self.style.SUCCESS(f'✅ Files endpoint: {test_response.status_code}'))
            
            self.stdout.write(self.style.SUCCESS('🎉 Docker services are working!'))
            return
            
        except requests.ConnectionError:
            self.stdout.write(self.style.ERROR('❌ Cannot connect to FastAPI service'))
        except requests.Timeout:
            self.stdout.write(self.style.ERROR('❌ FastAPI service timeout'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))
        
        # If connection failed and --start flag is provided
        if options['start']:
            self.stdout.write('🚀 Attempting to start Docker services...')
            
            try:
                # Check if Docker is running
                result = subprocess.run(['docker', 'ps'], capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    self.stdout.write(self.style.ERROR('❌ Docker is not running'))
                    return
                
                self.stdout.write('✅ Docker is running')
                
                # Start services
                result = subprocess.run(['docker-compose', 'up', '-d'], 
                                      capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    self.stdout.write(self.style.SUCCESS('✅ Docker services started'))
                    self.stdout.write('⏳ Waiting for services to initialize...')
                    time.sleep(10)
                    
                    # Test connection again
                    try:
                        response = requests.get(fastapi_url, timeout=10)
                        self.stdout.write(self.style.SUCCESS(f'✅ FastAPI now responding: {response.status_code}'))
                    except:
                        self.stdout.write(self.style.ERROR('❌ FastAPI still not responding'))
                else:
                    self.stdout.write(self.style.ERROR('❌ Failed to start Docker services'))
                    self.stdout.write(result.stderr)
                    
            except subprocess.TimeoutExpired:
                self.stdout.write(self.style.ERROR('❌ Docker command timed out'))
            except FileNotFoundError:
                self.stdout.write(self.style.ERROR('❌ Docker not found - is Docker Desktop installed?'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'❌ Error: {e}'))
        else:
            self.stdout.write(self.style.WARNING('💡 Use --start flag to attempt starting Docker services'))
            self.stdout.write('💡 Manual steps:')
            self.stdout.write('   1. Make sure Docker Desktop is running')
            self.stdout.write('   2. Run: docker-compose up -d')
            self.stdout.write('   3. Wait for services to start')
            self.stdout.write('   4. Test again')
