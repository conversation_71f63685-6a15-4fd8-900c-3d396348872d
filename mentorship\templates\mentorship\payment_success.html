{% extends 'base.html' %}
{% load static %}

{% block title %}Payment Successful - ForgeX{% endblock %}

{% block content %}
<div class="payment-success-page">
    <div class="container">
        <div class="success-container">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>

            <h1>Payment Successful!</h1>
            <p class="success-message">Your mentorship session has been booked and paid for successfully.</p>

            <div class="session-details-card">
                <h3>Session Details</h3>

                <div class="mentor-info">
                    <div class="mentor-avatar">
                        {% if session.mentor.profile and session.mentor.profile.profile_picture %}
                            <img src="{{ session.mentor.profile.profile_picture.url }}"
                                 alt="{{ session.mentor.get_full_name }}"
                                 class="profile-image">
                        {% else %}
                            <div class="avatar-initials">
                                {{ session.mentor.first_name|first|upper }}{{ session.mentor.last_name|first|upper }}
                            </div>
                        {% endif %}
                    </div>
                    <div class="mentor-details">
                        <h4>{{ session.mentor.get_full_name|default:session.mentor.username }}</h4>
                        <p>Your Mentor</p>
                    </div>
                </div>

                <div class="session-info">
                    <div class="info-row">
                        <span class="label">Date & Time:</span>
                        <span class="value">{{ session.scheduled_time|date:"M d, Y" }} at {{ session.scheduled_time|time:"g:i A" }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Duration:</span>
                        <span class="value">{{ session.get_duration_hours }} hour{{ session.get_duration_hours|pluralize }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Amount Paid:</span>
                        <span class="value">${{ session.total_amount }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">Session ID:</span>
                        <span class="value">#{{ session.id }}</span>
                    </div>
                </div>
            </div>

            <div class="next-steps">
                <h3>What's Next?</h3>
                <div class="steps-grid">
                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <h4>Session Confirmed</h4>
                        <p>Your session is confirmed and your mentor has been notified.</p>
                    </div>

                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <h4>Get Reminders</h4>
                        <p>You'll receive email reminders before your session starts.</p>
                    </div>

                    <div class="step">
                        <div class="step-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <h4>Join Session</h4>
                        <p>Join your session room 5 minutes before the scheduled time.</p>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="{% url 'mentorship:my_sessions' %}" class="btn btn-primary">
                    <i class="fas fa-calendar-alt"></i>
                    View My Sessions
                </a>
                <a href="{% url 'mentorship:marketplace' %}" class="btn btn-secondary">
                    <i class="fas fa-search"></i>
                    Book Another Session
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.payment-success-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
    padding: 2rem 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 4rem);
}

.success-container {
    max-width: 650px;
    width: 100%;
    margin: 0 auto;
    text-align: center;
    padding: 2rem;
    background: rgba(255,255,255,0.02);
    border-radius: 20px;
    border: 1px solid rgba(192,255,107,0.1);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.success-icon {
    font-size: 5rem;
    color: #C0ff6b;
    margin-bottom: 1.5rem;
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.success-container h1 {
    font-size: 2.8rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.success-message {
    font-size: 1.3rem;
    color: rgba(255,255,255,0.9);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-weight: 400;
}

.session-details-card {
    background: rgba(255,255,255,0.08);
    border-radius: 18px;
    padding: 2.5rem;
    border: 1px solid rgba(192,255,107,0.3);
    margin-bottom: 2.5rem;
    text-align: left;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
}

.session-details-card h3 {
    color: #C0ff6b;
    margin-bottom: 2rem;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
}

.mentor-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mentor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #C0ff6b;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    overflow: hidden;
}

.mentor-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.mentor-details h4 {
    margin: 0 0 0.25rem 0;
    color: #ffffff;
}

.mentor-details p {
    margin: 0;
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

.session-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-row .label {
    color: rgba(255,255,255,0.7);
    font-weight: 500;
}

.info-row .value {
    font-weight: 600;
    color: #ffffff;
}

.next-steps {
    margin-bottom: 3rem;
}

.next-steps h3 {
    color: #C0ff6b;
    margin-bottom: 2rem;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.step {
    text-align: center;
}

.step-icon {
    font-size: 2rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.step h4 {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.step p {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

.btn-primary {
    background: #C0ff6b;
    color: #1a1a1a;
}

.btn-primary:hover {
    background: #a0e066;
    transform: translateY(-2px);
}

.btn-secondary {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
    border: 1px solid rgba(255,255,255,0.2);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .payment-success-page {
        padding: 1rem 0.5rem;
    }

    .container {
        min-height: calc(100vh - 2rem);
    }

    .success-container {
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .success-icon {
        font-size: 4rem;
    }

    .success-container h1 {
        font-size: 2.2rem;
    }

    .success-message {
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }

    .session-details-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .steps-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        padding: 0.875rem 1.5rem;
    }

    .mentor-info {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .info-row {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .success-container h1 {
        font-size: 1.8rem;
    }

    .success-icon {
        font-size: 3.5rem;
    }

    .session-details-card {
        padding: 1.25rem;
    }

    .steps-grid {
        gap: 1rem;
    }
}
</style>
{% endblock %}
