# Step 1: Create the env with Python 3.10
conda create -n hello python=3.10 -y

# Step 2: Activate the environment
conda activate hello

# Step 3: Install dependencies
pip install numpy pandas nltk spacy pdfminer.six

# Step 4: Download NLTK + spaCy models
python -m nltk.downloader punkt
python -m spacy download en_core_web_sm

# Step 5: Install pyresparser
pip install pyresparser

from pyresparser import ResumeParser
print(ResumeParser('your_resume.pdf').get_extracted_data())



H:\anaconda3\envs\hello\lib\site-packages\pyresparser\resume_parser.py

custom_nlp = spacy.load(os.path.dirname(os.path.abspath(__file__)))
change this line to this
custom_nlp = spacy.load('en_core_web_sm')



H:\anaconda3\envs\hello\lib\site-packages\pyresparser\utils.py

matcher.add('NAME', None, *pattern)
change this to this
matcher.add('NAME', pattern)


edits in docker volume 
docker exec -it forgex-pyresparser-1 /bin/bash
find / -path "*pyresparser/utils.py" 2>/dev/null
find / -type f -name "resume_parser.py" 2>/dev/null


