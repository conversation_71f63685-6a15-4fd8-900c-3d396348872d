<!-- filepath: d:\Project_Main\django-template\accounts\templates\accounts\profile.html -->
{% extends 'base.html' %}
{% load static %}
{% block title %}{{ user.username }}'s Profile{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="account-page">
    <div class="account-card fade-in expanded-profile">
        <h2 class="header-one">{{ user.username }}'s Profile</h2>

        <div class="profile-container">
            <div class="profile-sidebar slide-right">
                <div class="profile-picture-container">
                    {% if user_profile.profile_picture %}
                        <img src="{{ user_profile.profile_picture.url }}" alt="{{ user.username }}'s profile picture" class="profile-picture">
                    {% else %}
                        <div class="profile-picture default-avatar">
                            <span>{{ user.username|make_list|first|upper }}</span>
                        </div>
                    {% endif %}
                    <h3 class="profile-name">
                        {% if user.first_name or user.last_name %}
                            {{ user.first_name }} {{ user.last_name }}
                        {% else %}
                            {{ user.username }}
                        {% endif %}
                    </h3>
                    {% if user.first_name or user.last_name %}
                        <p class="username-display">@{{ user.username }}</p>
                    {% endif %}
                    {% if user_profile.professional_title %}
                        <p class="professional-title">{{ user_profile.professional_title }}</p>
                    {% endif %}
                </div>

                <div class="profile-section">
                    <h3>Bio</h3>
                    <p>{{ user_profile.bio|default:"No bio information provided." }}</p>
                </div>

                {% if user_profile.phone_number or user_profile.linkedin_url or user_profile.github_url or user_profile.website_url %}
                <div class="profile-section">
                    <h3>Contact & Links</h3>
                    {% if user_profile.phone_number %}
                        <p><strong>Phone:</strong> {{ user_profile.phone_number }}</p>
                    {% endif %}
                    {% if user_profile.linkedin_url %}
                        <p><strong>LinkedIn:</strong> <a href="{{ user_profile.linkedin_url }}" target="_blank" class="profile-link">View Profile</a></p>
                    {% endif %}
                    {% if user_profile.github_url %}
                        <p><strong>GitHub:</strong> <a href="{{ user_profile.github_url }}" target="_blank" class="profile-link">View Profile</a></p>
                    {% endif %}
                    {% if user_profile.website_url %}
                        <p><strong>Website:</strong> <a href="{{ user_profile.website_url }}" target="_blank" class="profile-link">Visit Website</a></p>
                    {% endif %}
                </div>
                {% endif %}

                <div class="profile-section">
                    <h3>Availability</h3>
                    <p><strong>Country:</strong> {{ user_profile.country|default:"Not specified" }}</p>
                    <p><strong>Timezone:</strong> {{ user_profile.timezone }}</p>
                    <p><strong>Type:</strong> {{ user_profile.get_availability_type_display }}</p>
                    <p><strong>From:</strong>
                       {% if user_profile.availability_start %}
                         {{ user_profile.availability_start|time:"g:i A" }}
                       {% else %}
                         Not specified
                       {% endif %}
                    </p>
                    <p><strong>Until:</strong>
                       {% if user_profile.availability_end %}
                         {{ user_profile.availability_end|time:"g:i A" }}
                       {% else %}
                         Not specified
                       {% endif %}
                    </p>
                </div>

                <div class="profile-section">
                    <h3>Uploaded CV</h3>
                    {% if user_profile.cv %}
                    <a href="{{ user_profile.cv.url }}" target="_blank" class="account-btn">Download CV</a>
                    {% else %}
                    <p>No CV uploaded.</p>
                    {% endif %}
                </div>

                <div class="profile-actions">
                    <a href="{% url 'accounts:profile_setup' %}" class="account-btn">Edit Profile</a>
                    {% if user.is_superuser %}
                    <a href="{% url 'accounts:threat_management_dashboard' %}" class="account-btn admin-btn">
                         Security Dashboard
                    </a>
                    {% endif %}
                    <a href="{% url 'accounts:contact' %}" class="account-btn contact-btn">
                        <i class="contact-icon">📧</i> Contact Us
                    </a>
                    <a href="{% url 'accounts:logout' %}" class="account-btn logout-btn">
                        <i class="logout-icon">🚪</i> Logout
                    </a>
                </div>
            </div>

            <div class="profile-main slide-left">
                <div class="profile-section">
                    <h3>Skills</h3>
                    {% with skill_levels=user_profile.get_skills_with_levels %}
                        {% if skill_levels %}
                            <div class="skills-grid">
                                {% for skill, level_name, level_value in skill_levels %}
                                    <div class="skill-item">
                                        <h4>{{ skill.name }}</h4>
                                        <div class="skill-level">
                                            <span class="skill-badge level-{{ level_value }}">
                                                {{ level_name }}
                                            </span>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p>No skills added yet.</p>
                        {% endif %}
                    {% endwith %}
                </div>

                <div class="profile-section">
                    <h3>Skills You Might Want to Learn 👨‍💻</h3>

                    {% if total_suggestions > 0 %}
                        <!-- High Demand Skills Section -->
                        {% if high_demand_skills %}
                        <div class="high-demand-section">
                            <h4 class="section-subtitle">🔥 High Demand Skills</h4>
                            <div class="high-demand-skills">
                                {% for skill in high_demand_skills %}
                                    <span class="high-demand-skill">{{ skill }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- Categorized Skills Section -->
                        <div class="skills-categories">
                            {% for category, skills in categorized_suggestions.items %}
                                <div class="skill-category">
                                    <h4 class="category-title">
                                        {% if category == "Programming Languages" %}💻
                                        {% elif category == "Web Development" %}🌐
                                        {% elif category == "Data & Analytics" %}📊
                                        {% elif category == "Cloud & DevOps" %}☁️
                                        {% elif category == "Mobile Development" %}📱
                                        {% elif category == "AI & Machine Learning" %}🤖
                                        {% elif category == "Design & UX" %}🎨
                                        {% else %}🔧{% endif %}
                                        {{ category }}
                                    </h4>
                                    <div class="category-skills">
                                        {% for skill in skills %}
                                            <div class="suggested-skill-item">
                                                <span class="skill-name">{{ skill.name }}</span>
                                                <div class="skill-stats">
                                                    <span class="demand-indicator" title="Demand: {{ skill.demand }}, Supply: {{ skill.supply }}">
                                                        {% if skill.demand_ratio >= 3 %}🔥
                                                        {% elif skill.demand_ratio >= 2 %}⚡
                                                        {% else %}📈{% endif %}
                                                    </span>
                                                    <span class="demand-ratio">{{ skill.demand_ratio|floatformat:1 }}x</span>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>

                        <!-- Skills Summary -->
                        <div class="skills-summary">
                            <p class="summary-text">
                                📈 Found <strong>{{ total_suggestions }}</strong> skills in high demand across <strong>{{ categorized_suggestions|length }}</strong> categories
                            </p>
                        </div>
                    {% else %}
                        <div class="no-suggestions">
                            <div class="success-message">
                                <span class="success-icon">🎉</span>
                                <h4>Excellent Coverage!</h4>
                                <p>You're covering all high-demand skills in the platform! Keep up the great work!</p>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
        },
        "shape": {
          "type": ["circle", "triangle"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<style>
/* Additional styles specific to profile page */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.skill-item {
    background-color: rgba(50, 50, 50, 0.5);
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
    border-left: 3px solid var(--color-border);
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background-color: rgba(60, 60, 60, 0.7);
}

.skill-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin-top: 8px;
}

.level-1 { background-color: #6c757d; color: white; }
.level-2 { background-color: #17a2b8; color: white; }
.level-3 { background-color: #007bff; color: white; }
.level-4 { background-color: #28a745; color: white; }
.level-5 { background-color: #ffc107; color: black; }

/* Enhanced Skills Suggestions Styling - Compact Version */
.high-demand-section {
    margin-bottom: 15px;
    padding: 15px;
    background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 165, 0, 0.1));
    border-radius: 8px;
    border: 1px solid rgba(255, 107, 107, 0.3);
}

.section-subtitle {
    color: #ff6b6b;
    font-size: 1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.high-demand-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.high-demand-skill {
    background: linear-gradient(135deg, #ff6b6b, #ff8e53);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.high-demand-skill:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(255, 107, 107, 0.4);
}

.skills-categories {
    display: grid;
    gap: 12px;
    margin-bottom: 15px;
}

.skill-category {
    background-color: rgba(40, 40, 40, 0.4);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid rgba(192, 255, 107, 0.2);
    transition: all 0.3s ease;
}

.skill-category:hover {
    border-color: rgba(192, 255, 107, 0.4);
    background-color: rgba(40, 40, 40, 0.6);
}

.category-title {
    color: var(--color-border);
    font-size: 1rem;
    margin-bottom: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.category-skills {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 8px;
}

.suggested-skill-item {
    background-color: rgba(60, 60, 60, 0.3);
    border-radius: 6px;
    padding: 10px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.suggested-skill-item:hover {
    background-color: rgba(60, 60, 60, 0.5);
    border-left-color: var(--color-border);
    transform: translateX(3px);
}

.skill-name {
    color: #ffffff;
    font-weight: 500;
    text-transform: capitalize;
}

.skill-stats {
    display: flex;
    align-items: center;
    gap: 8px;
}

.demand-indicator {
    font-size: 1.2rem;
    cursor: help;
}

.demand-ratio {
    background-color: rgba(192, 255, 107, 0.2);
    color: var(--color-border);
    padding: 3px 6px;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
}

.skills-summary {
    background-color: rgba(192, 255, 107, 0.1);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid rgba(192, 255, 107, 0.3);
    text-align: center;
    margin-top: 10px;
}

.summary-text {
    color: #ffffff;
    margin: 0;
    font-size: 0.9rem;
}

.no-suggestions {
    text-align: center;
    padding: 30px 15px;
}

.success-message {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(32, 201, 151, 0.2));
    border-radius: 8px;
    padding: 20px;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.success-icon {
    font-size: 2.5rem;
    display: block;
    margin-bottom: 10px;
}

.success-message h4 {
    color: #28a745;
    margin-bottom: 8px;
    font-size: 1.2rem;
}

.success-message p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.95rem;
}

/* Responsive Design - Compact */
@media (max-width: 768px) {
    .category-skills {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .high-demand-skills {
        justify-content: center;
        gap: 4px;
    }

    .high-demand-skill {
        padding: 5px 10px;
        font-size: 0.8rem;
    }

    .suggested-skill-item {
        padding: 8px 10px;
        flex-direction: row;
        justify-content: space-between;
    }

    .skill-category {
        padding: 12px;
    }

    .high-demand-section {
        padding: 12px;
        margin-bottom: 12px;
    }

    .skills-categories {
        gap: 10px;
    }
}

/* Profile Actions Styles */
.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 20px;
}

.logout-form {
    margin: 0;
}

.logout-btn {
    background-color: rgba(244, 67, 54, 0.2) !important;
    border: 2px solid #f44336 !important;
    color: #f44336 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    text-decoration: none;
    width: 100%;
    padding: 12px 20px;
    border-radius: 50px;
}

.logout-btn:hover {
    background-color: #f44336 !important;
    color: #ffffff !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(244, 67, 54, 0.3);
}

.logout-icon {
    font-style: normal;
    font-size: 1.1em;
}

.contact-btn {
    background-color: rgba(192, 255, 107, 0.2) !important;
    border: 2px solid #C0ff6b !important;
    color: #C0ff6b !important;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.contact-btn:hover {
    background-color: #C0ff6b !important;
    color: #000000 !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(192, 255, 107, 0.3);
}

.contact-icon {
    font-style: normal;
    font-size: 1.1em;
}

/* New profile elements styling */
.username-display {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 5px 0;
    text-align: center;
}

.professional-title {
    color: var(--color-border);
    font-size: 1rem;
    font-weight: 500;
    margin: 10px 0;
    text-align: center;
    font-style: italic;
}

.profile-link {
    color: var(--color-border);
    text-decoration: none;
    transition: all 0.3s ease;
}

.profile-link:hover {
    color: #ffffff;
    text-shadow: 0 0 8px rgba(192, 255, 107, 0.5);
}

/* Admin Button Styling */
.admin-btn {
    background: linear-gradient(135deg, var(--color-border) 0%, #a8e063 100%) !important;
    color: #000 !important;
    border: 2px solid var(--color-border) !important;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(192, 255, 107, 0.3) !important;
    text-decoration: none !important;
}

.admin-btn:hover {
    background: linear-gradient(135deg, #a8e063 0%, #90c956 100%) !important;
    border-color: #90c956 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(192, 255, 107, 0.4) !important;
    color: #000 !important;
}

.admin-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.admin-btn:hover::before {
    left: 100%;
}

.admin-icon {
    font-size: 1.2rem;
    margin-right: 8px;
    animation: pulse-shield 2s infinite;
}

@keyframes pulse-shield {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .profile-actions {
        flex-direction: column;
        gap: 10px;
    }

    .admin-btn {
        padding: 10px 16px !important;
        font-size: 0.9rem !important;
    }

    .admin-icon {
        font-size: 1rem;
        margin-right: 6px;
    }
}
</style>
{% endblock %}