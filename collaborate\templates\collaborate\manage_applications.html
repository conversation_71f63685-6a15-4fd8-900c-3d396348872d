{% extends "base.html" %} {% block content %}
<div class="applications-container">
  <h1>Manage Project Applications</h1>

  {% if messages %}
  <div class="messages">
    {% for message in messages %}
    <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
      {{ message }}
    </div>
    {% endfor %}
  </div>
  {% endif %}

  <!-- Pending Applications Section -->
  <section class="applications-section">
    <h2>Pending Applications</h2>
    {% if pending_applications %}
    <div class="applications-list">
      {% for application in pending_applications %}
      <div class="application-card status-pending">
        <div class="application-header">
          <h3>Application for {{ application.project.title }}</h3>
          <span class="status-badge status-pending">Pending</span>
        </div>

        <div class="applicant-info">
          <h4>Applicant: {{ application.user.username }}</h4>
          {% if application.user.email %}
          <p>Email: {{ application.user.email }}</p>
          {% endif %}
          <p>Applied on: {{ application.created_at|date:"F j, Y" }}</p>
        </div>

        {% if application.message %}
        <div class="application-message">
          <h4>Message from Applicant:</h4>
          <p>{{ application.message }}</p>
        </div>
        {% endif %}

        <div class="application-actions">
          <form
            method="post"
            action="{% url 'collaborate:handle_application' application.id 'accept' %}"
            class="inline-form"
          >
            {% csrf_token %}
            <button type="submit" class="btn btn-success">Accept</button>
          </form>
          <form
            method="post"
            action="{% url 'collaborate:handle_application' application.id 'reject' %}"
            class="inline-form"
          >
            {% csrf_token %}
            <button type="submit" class="btn btn-danger">Reject</button>
          </form>
        </div>
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="no-applications">
      <p>No pending applications at this time.</p>
    </div>
    {% endif %}
  </section>

  <!-- Processed Applications Section -->
  <section class="applications-section">
    <h2>Processed Applications</h2>
    {% if processed_applications %}
    <div class="applications-list">
      {% for application in processed_applications %}
      <div class="application-card status-{{ application.status }}">
        <div class="application-header">
          <h3>Application for {{ application.project.title }}</h3>
          <span class="status-badge status-{{ application.status }}">
            {{ application.get_status_display }}
          </span>
        </div>

        <div class="applicant-info">
          <h4>Applicant: {{ application.user.username }}</h4>
          {% if application.user.email %}
          <p>Email: {{ application.user.email }}</p>
          {% endif %}
          <p>Applied on: {{ application.created_at|date:"F j, Y" }}</p>
          <p>Decision made on: {{ application.updated_at|date:"F j, Y" }}</p>
        </div>

        {% if application.message %}
        <div class="application-message">
          <h4>Message from Applicant:</h4>
          <p>{{ application.message }}</p>
        </div>
        {% endif %}
      </div>
      {% endfor %}
    </div>
    {% else %}
    <div class="no-applications">
      <p>No processed applications yet.</p>
    </div>
    {% endif %}
  </section>
</div>
{% endblock %}
