from django.core.management.base import BaseCommand
from django.test import Client
from django.contrib.auth.models import User
from mentorship.models import MentorshipSession


class Command(BaseCommand):
    help = 'Test chat history API endpoint'

    def handle(self, *args, **options):
        # Get the first session
        session = MentorshipSession.objects.first()
        if not session:
            self.stdout.write(self.style.ERROR('No mentorship sessions found'))
            return

        # Get the first user
        user = User.objects.first()
        if not user:
            self.stdout.write(self.style.ERROR('No users found'))
            return

        # Create a test client
        client = Client()
        client.force_login(user)

        # Test the API endpoint
        url = f'/mentorship/api/chat-history/{session.room_id}/'
        self.stdout.write(f'Testing API endpoint: {url}')
        self.stdout.write(f'Session room_id: {session.room_id}')
        self.stdout.write(f'User: {user.username}')
        self.stdout.write(f'Session learner: {session.learner.username}')
        self.stdout.write(f'Session mentor: {session.mentor.username}')

        response = client.get(url)

        self.stdout.write(f'Response status: {response.status_code}')
        self.stdout.write(f'Response content: {response.content.decode()}')

        if response.status_code == 200:
            import json
            data = json.loads(response.content)
            if data.get('success'):
                messages = data.get('messages', [])
                self.stdout.write(f'Found {len(messages)} messages in API response')
            else:
                self.stdout.write('API returned success=False')
        else:
            self.stdout.write(self.style.ERROR(f'API returned error: {response.status_code}'))
