import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from asgiref.sync import sync_to_async
import asyncio
from django.contrib.auth.models import User
from django.utils import timezone
from .models import MentorshipSession, ChatMessage, SessionChatMessage



class MentorshipSessionConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for mentorship sessions"""

    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f'mentorship_session_{self.room_id}'
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Send connection confirmation
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': f'Connected to mentorship session {self.room_id}',
            'user_id': self.user.id,
            'username': self.user.username
        }))

    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)

    async def receive(self, text_data=None, bytes_data=None):
        if text_data:
            try:
                data = json.loads(text_data)
                message_type = data.get('type')

                if message_type == 'chat_message':
                    # Handle text chat
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'chat_message',
                            'message': data.get('message'),
                            'username': self.user.username,
                            'user_id': self.user.id,
                            'timestamp': data.get('timestamp')
                        }
                    )

                elif message_type == 'session_control':
                    # Handle session control messages (start, end, etc.)
                    action = data.get('action')

                    # If ending session, update session status
                    if action == 'end_session':
                        session = await self.get_session()
                        if session and session.status in ['scheduled', 'active']:
                            session.status = 'completed'
                            session.ended_at = timezone.now()
                            await sync_to_async(session.save)()

                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'session_control',
                            'action': action,
                            'user_id': self.user.id,
                            'username': self.user.username
                        }
                    )

                elif message_type == 'timer_sync_request':
                    # Handle timer synchronization request
                    session = await self.get_session()
                    if session:
                        remaining_seconds = await sync_to_async(session.get_remaining_time_seconds)()
                        session_started = session.status == 'active' and session.started_at is not None

                        await self.send(text_data=json.dumps({
                            'type': 'timer_sync_response',
                            'remaining_seconds': remaining_seconds,
                            'session_started': session_started,
                            'session_expired': remaining_seconds <= 0
                        }))

                elif message_type == 'timer_warning_dismissed':
                    # Broadcast that mentor dismissed the timer warning
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'timer_warning_dismissed',
                            'user_id': self.user.id,
                            'username': self.user.username
                        }
                    )

                elif message_type == 'edit_permission':
                    # Handle edit permission changes
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'edit_permission',
                            'can_edit': data.get('can_edit'),
                            'target_user': data.get('target_user'),
                            'changed_by': self.user.username
                        }
                    )

                elif message_type == 'voice_signal':
                    # Handle WebRTC signaling for voice chat
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'voice_signal',
                            'signal_data': data.get('signal_data'),
                            'signal_type': data.get('signal_type'),
                            'from_user': self.user.id,
                            'to_user': data.get('to_user')
                        }
                    )

            except json.JSONDecodeError:
                await self.send(text_data=json.dumps({
                    'type': 'error',
                    'message': 'Invalid JSON data'
                }))

        elif bytes_data:
            # Handle binary data (voice audio)
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'voice_audio',
                    'audio_data': bytes_data,
                    'from_user': self.user.id
                }
            )

    async def chat_message(self, event):
        """Send chat message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'username': event['username'],
            'user_id': event['user_id'],
            'timestamp': event['timestamp']
        }))

    async def session_control(self, event):
        """Send session control message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'session_control',
            'action': event['action'],
            'user_id': event['user_id'],
            'username': event['username']
        }))

    async def edit_permission(self, event):
        """Send edit permission change to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'edit_permission',
            'can_edit': event['can_edit'],
            'target_user': event['target_user'],
            'changed_by': event['changed_by']
        }))

    async def voice_signal(self, event):
        """Send WebRTC signaling data to WebSocket"""
        # Only send to the target user
        if event.get('to_user') == self.user.id or event.get('from_user') != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'voice_signal',
                'signal_data': event['signal_data'],
                'signal_type': event['signal_type'],
                'from_user': event['from_user']
            }))

    async def voice_audio(self, event):
        """Send voice audio data to WebSocket"""
        # Don't send audio back to the sender
        if event.get('from_user') != self.user.id:
            await self.send(bytes_data=event['audio_data'])

    async def timer_warning_dismissed(self, event):
        """Send timer warning dismissed message to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'timer_warning_dismissed',
            'user_id': event['user_id'],
            'username': event['username']
        }))

    @database_sync_to_async
    def get_session(self):
        """Get the mentorship session for this room"""
        try:
            return MentorshipSession.objects.get(room_id=self.room_id)
        except MentorshipSession.DoesNotExist:
            return None


# New Mentorship Communication Consumers for Video/Audio/Chat
class MentorshipVideoConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f"mentorship_video_{self.room_id}"
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Notify others about new participant
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'participant_joined',
                'user_id': self.user.id,
                'username': self.user.username,
                'user_role': self.get_user_role()
            }
        )

        print(f"Video: User {self.user.username} connected to room {self.room_id}")

    async def disconnect(self, close_code):
        # Notify others about participant leaving
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'participant_left',
                'user_id': self.user.id,
                'username': self.user.username,
                'user_role': self.get_user_role()
            }
        )

        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        print(f"Video: User {self.user.username} disconnected from room {self.room_id}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            data['sender_id'] = self.user.id
            data['sender_username'] = self.user.username
            data['sender_role'] = self.get_user_role()

            # Handle different video message types
            message_type = data.get('type')
            if message_type in ['offer', 'answer', 'ice-candidate', 'video-toggle', 'screen-share']:
                # Forward to specific target or broadcast to room
                target_user = data.get('target_user')
                if target_user:
                    # Send to specific user
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'video_message_targeted',
                            'message': data,
                            'target_user_id': target_user
                        }
                    )
                else:
                    # Broadcast to all in room
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'video_message',
                            'message': data
                        }
                    )
        except json.JSONDecodeError:
            pass

    async def participant_joined(self, event):
        """Send participant joined message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'participant_joined',
                'user_id': event['user_id'],
                'username': event['username'],
                'user_role': event['user_role']
            }))

    async def participant_left(self, event):
        """Send participant left message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'participant_left',
                'user_id': event['user_id'],
                'username': event['username'],
                'user_role': event['user_role']
            }))

    async def video_message(self, event):
        """Send video message to all participants except sender"""
        message = event['message']
        if message['sender_id'] != self.user.id:  # Don't send to sender
            await self.send(text_data=json.dumps(message))

    async def video_message_targeted(self, event):
        """Send video message to specific target user"""
        message = event['message']
        if event['target_user_id'] == self.user.id:  # Only send to target
            await self.send(text_data=json.dumps(message))

    def get_user_role(self):
        """Get user role in the mentorship session"""
        # You can implement logic to determine if user is mentor or learner
        # For now, return a default or check session participants
        return 'participant'


class MentorshipAudioConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f"mentorship_audio_{self.room_id}"
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Notify others about new audio participant
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'audio_participant_joined',
                'user_id': self.user.id,
                'username': self.user.username,
                'user_role': self.get_user_role()
            }
        )

        print(f"Audio: User {self.user.username} connected to room {self.room_id}")

    async def disconnect(self, close_code):
        # Notify others about participant leaving
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'audio_participant_left',
                'user_id': self.user.id,
                'username': self.user.username,
                'user_role': self.get_user_role()
            }
        )

        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        print(f"Audio: User {self.user.username} disconnected from room {self.room_id}")

    async def receive(self, text_data=None, bytes_data=None):
        if text_data:
            try:
                data = json.loads(text_data)
                data['sender_id'] = self.user.id
                data['sender_username'] = self.user.username
                data['sender_role'] = self.get_user_role()

                # Handle audio control messages
                if data.get('type') in ['audio-toggle', 'mute', 'unmute']:
                    await self.channel_layer.group_send(
                        self.room_group_name,
                        {
                            'type': 'audio_control',
                            'message': data
                        }
                    )
            except json.JSONDecodeError:
                pass

        elif bytes_data:
            # Forward binary audio data to all other participants
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'audio_data',
                    'audio_data': bytes_data,
                    'sender_id': self.user.id
                }
            )

    async def audio_participant_joined(self, event):
        """Send audio participant joined message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'audio_participant_joined',
                'user_id': event['user_id'],
                'username': event['username'],
                'user_role': event['user_role']
            }))

    async def audio_participant_left(self, event):
        """Send audio participant left message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'audio_participant_left',
                'user_id': event['user_id'],
                'username': event['username'],
                'user_role': event['user_role']
            }))

    async def audio_control(self, event):
        """Send audio control message"""
        message = event['message']
        if message['sender_id'] != self.user.id:  # Don't send to sender
            await self.send(text_data=json.dumps(message))

    async def audio_data(self, event):
        """Send binary audio data to participants except sender"""
        if event['sender_id'] != self.user.id:  # Don't send to sender
            await self.send(bytes_data=event['audio_data'])

    def get_user_role(self):
        """Get user role in the mentorship session"""
        return 'participant'


class MentorshipChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.room_id = self.scope['url_route']['kwargs']['room_id']
        self.room_group_name = f"mentorship_chat_{self.room_id}"
        self.user = self.scope['user']

        if not self.user.is_authenticated:
            await self.close()
            return

        # Get the session object
        self.session = await self.get_session()
        if not self.session:
            await self.close()
            return

        # Join room group
        await self.channel_layer.group_add(self.room_group_name, self.channel_name)
        await self.accept()

        # Send chat history to the user
        await self.send_chat_history()

        # Send welcome message to user
        await self.send(text_data=json.dumps({
            'type': 'system_message',
            'message': 'Connected to session chat',
            'timestamp': asyncio.get_event_loop().time()
        }))

        # Save and notify others about new participant
        await self.save_system_message(f'{self.user.username} joined the session', 'user_joined')
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_joined_message',
                'user_id': self.user.id,
                'username': self.user.username,
                'message': f'{self.user.username} joined the session'
            }
        )

        print(f"Chat: User {self.user.username} connected to room {self.room_id}")

    async def disconnect(self, close_code):
        # Save and notify others about participant leaving
        await self.save_system_message(f'{self.user.username} left the session', 'user_left')
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'user_left_message',
                'user_id': self.user.id,
                'username': self.user.username,
                'message': f'{self.user.username} left the session'
            }
        )

        # Leave room group
        await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
        print(f"Chat: User {self.user.username} disconnected from room {self.room_id}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            data['sender_id'] = self.user.id
            data['sender_username'] = self.user.username
            data['timestamp'] = asyncio.get_event_loop().time()

            message_type = data.get('type')

            if message_type == 'chat_message':
                # Save chat message to database
                await self.save_chat_message(data['message'])

                # Send chat message to room
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'chat_message_broadcast',
                        'message': data['message'],
                        'username': self.user.username,
                        'user_id': self.user.id,
                        'timestamp': data['timestamp']
                    }
                )
            elif message_type == 'typing':
                # Send typing indicator to room
                await self.channel_layer.group_send(
                    self.room_group_name,
                    {
                        'type': 'typing_indicator',
                        'username': self.user.username,
                        'user_id': self.user.id,
                        'typing': data.get('typing', False)
                    }
                )
        except json.JSONDecodeError:
            pass

    async def chat_message_broadcast(self, event):
        """Send chat message to WebSocket (including sender)"""
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': event['message'],
            'username': event['username'],
            'user_id': event['user_id'],
            'timestamp': event['timestamp']
        }))

    async def user_joined_message(self, event):
        """Send user joined message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'user_joined',
                'message': event['message'],
                'username': event['username'],
                'user_id': event['user_id'],
                'timestamp': asyncio.get_event_loop().time()
            }))

    async def user_left_message(self, event):
        """Send user left message"""
        if event['user_id'] != self.user.id:  # Don't send to self
            await self.send(text_data=json.dumps({
                'type': 'user_left',
                'message': event['message'],
                'username': event['username'],
                'user_id': event['user_id'],
                'timestamp': asyncio.get_event_loop().time()
            }))

    async def typing_indicator(self, event):
        """Send typing indicator"""
        if event['user_id'] != self.user.id:  # Don't send to sender
            await self.send(text_data=json.dumps({
                'type': 'typing',
                'username': event['username'],
                'user_id': event['user_id'],
                'typing': event['typing']
            }))

    @database_sync_to_async
    def get_session(self):
        """Get the mentorship session by room_id"""
        try:
            return MentorshipSession.objects.get(room_id=self.room_id)
        except MentorshipSession.DoesNotExist:
            return None

    @database_sync_to_async
    def save_chat_message(self, content):
        """Save a chat message to the database using new SessionChatMessage model"""
        from django.db import transaction
        try:
            with transaction.atomic():
                return SessionChatMessage.objects.create(
                    session=self.session,
                    sender=self.user,
                    message=content,
                    message_type='chat'
                )
        except Exception as e:
            # Fallback to old model if new one fails
            return ChatMessage.objects.create(
                session=self.session,
                sender=self.user,
                content=content,
                message_type='chat'
            )

    @database_sync_to_async
    def save_system_message(self, content, message_type):
        """Save a system message to the database"""
        from django.db import transaction
        try:
            with transaction.atomic():
                return SessionChatMessage.objects.create(
                    session=self.session,
                    sender=None,  # System messages have no sender
                    message=content,
                    message_type=message_type
                )
        except Exception as e:
            # Fallback to old model if new one fails
            return ChatMessage.objects.create(
                session=self.session,
                sender=None,
                content=content,
                message_type=message_type
            )

    @database_sync_to_async
    def get_chat_history(self):
        """Get chat history for the session from both models"""
        # Get messages from new model
        new_messages = list(SessionChatMessage.objects.filter(session=self.session).select_related('sender').order_by('created_at'))

        # Get messages from old model for backward compatibility
        old_messages = list(ChatMessage.objects.filter(session=self.session).select_related('sender').order_by('timestamp'))

        # Combine and sort by timestamp
        all_messages = []

        # Convert new messages to common format
        for msg in new_messages:
            all_messages.append({
                'content': msg.message,
                'sender': msg.sender,
                'message_type': msg.message_type,
                'timestamp': msg.created_at,
                'model_type': 'new'
            })

        # Convert old messages to common format
        for msg in old_messages:
            all_messages.append({
                'content': msg.content,
                'sender': msg.sender,
                'message_type': msg.message_type,
                'timestamp': msg.timestamp,
                'model_type': 'old'
            })

        # Sort by timestamp
        all_messages.sort(key=lambda x: x['timestamp'])
        return all_messages

    async def send_chat_history(self):
        """Send chat history to the user"""
        messages = await self.get_chat_history()
        for message in messages:
            await self.send(text_data=json.dumps({
                'type': 'chat_history',
                'message': message['content'],
                'username': message['sender'].username if message['sender'] else 'System',
                'user_id': message['sender'].id if message['sender'] else None,
                'message_type': message['message_type'],
                'timestamp': message['timestamp'].timestamp() if hasattr(message['timestamp'], 'timestamp') else message['timestamp']
            }))