"""
Views for team analysis features.
"""
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.contrib import messages
from .models import Project, ProjectMembership, TeamMatchFeedback, TeamMatchLog, TeamMatchAnalysisLog
from .utils.visualizations import generate_skill_coverage_data, generate_timezone_overlap_data, generate_team_balance_data
from .utils.team_analysis import analyze_team_balance, identify_skill_gaps, suggest_optimal_meeting_times
from .utils.feedback import record_match_feedback, get_project_feedback_summary
from django.utils import timezone

@login_required
def team_balance_view(request, project_hash):
    """View to show team balance analysis."""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Get team members
    team_members = [membership.user for membership in ProjectMembership.objects.filter(project=project)]

    # If the project owner is not in the team members, add them
    if project.owner not in team_members:
        team_members.append(project.owner)

    # Generate skill coverage data
    skill_coverage = generate_skill_coverage_data(project, team_members)

    # Analyze team balance
    team_balance = analyze_team_balance(team_members, project)

    # Identify skill gaps
    skill_gaps = identify_skill_gaps(team_members, project)

    # Get the latest match logs for this project
    latest_match = TeamMatchLog.objects.filter(project=project).order_by('-timestamp').first()
    latest_analysis = TeamMatchAnalysisLog.objects.filter(project=project).order_by('-timestamp').first()

    context = {
        'project': project,
        'team_members': team_members,
        'skill_coverage': skill_coverage,
        'skill_coverage_percent': skill_coverage.get('covered_skills', 0) / skill_coverage.get('total_skills', 1) * 100 if skill_coverage.get('total_skills', 0) > 0 else 0,
        'is_optimal_match': (skill_coverage.get('covered_skills', 0) / skill_coverage.get('total_skills', 1) * 100 if skill_coverage.get('total_skills', 0) > 0 else 0) >= 80,
        'team_balance': team_balance,
        'skill_gaps': skill_gaps,
        'latest_match': latest_match,
        'latest_analysis': latest_analysis
    }

    return render(request, 'collaborate/team_balance.html', context)

@login_required
def timezone_overlap_view(request, project_hash):
    """View to show timezone overlap analysis."""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Get team members
    team_members = [membership.user for membership in ProjectMembership.objects.filter(project=project)]

    # If the project owner is not in the team members, add them
    if project.owner not in team_members:
        team_members.append(project.owner)

    # Generate timezone overlap data
    timezone_data = generate_timezone_overlap_data(team_members)

    # Get meeting suggestions
    meeting_suggestions = suggest_optimal_meeting_times(team_members)

    context = {
        'project': project,
        'team_members': team_members,
        'timezone_data': timezone_data,
        'meeting_suggestions': meeting_suggestions
    }

    return render(request, 'collaborate/timezone_overlap.html', context)

@login_required
def match_feedback_view(request, project_hash):
    """View to show feedback on team matches."""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Check if the user has already submitted feedback
    user_has_feedback = TeamMatchFeedback.objects.filter(project=project, user=request.user).exists()

    # Get feedback summary
    feedback_summary = get_project_feedback_summary(project)

    context = {
        'project': project,
        'feedback_summary': feedback_summary,
        'user_has_feedback': user_has_feedback,
        'form': None  # No form in the summary view
    }

    return render(request, 'collaborate/match_feedback.html', context)

@login_required
def submit_match_feedback_view(request, project_hash):
    """View to submit feedback on team matches."""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        messages.error(request, "You don't have permission to access this project.")
        return redirect('collaborate:project_list')

    # Check if the user has already submitted feedback
    if TeamMatchFeedback.objects.filter(project=project, user=request.user).exists():
        messages.info(request, "You have already submitted feedback for this project.")
        return redirect('collaborate:match_feedback', project_id=project.id)

    if request.method == 'POST':
        # Process the form submission
        try:
            rating = int(request.POST.get('rating'))
            feedback_text = request.POST.get('feedback_text')

            # Parse match aspects from form
            match_aspects = {}
            for key, value in request.POST.items():
                if key.startswith('match_aspects[') and key.endswith(']'):
                    aspect_name = key[len('match_aspects['):-1]
                    try:
                        match_aspects[aspect_name] = int(value)
                    except ValueError:
                        pass

            # Record the feedback
            record_match_feedback(project, request.user, rating, feedback_text, match_aspects)

            messages.success(request, "Thank you for your feedback!")
            # Redirect to the feedback summary page
            return redirect('collaborate:match_feedback', project_hash=project.hash)

        except (ValueError, KeyError) as e:
            messages.error(request, f"Error submitting feedback: {str(e)}")
            # Log the error for debugging
            print(f"Error submitting feedback: {str(e)}")
            print(f"POST data: {request.POST}")

    # Display the feedback form
    context = {
        'project': project,
        'form': True  # Show the form
    }

    return render(request, 'collaborate/match_feedback.html', context)

@login_required
def feedback_summary_api(request, project_hash):
    """API endpoint to get feedback summary data."""
    project = get_object_or_404(Project, hash=project_hash)

    # Check if user has permission to access this project
    if not project.can_access(request.user):
        return JsonResponse({'error': 'Permission denied'}, status=403)

    # Get feedback summary
    feedback_summary = get_project_feedback_summary(project)

    # Convert to JSON-serializable format
    data = {
        'average_rating': feedback_summary['average_rating'],
        'feedback_count': feedback_summary['feedback_count'],
        'rating_counts': feedback_summary['rating_counts'],
        'aspect_ratings': feedback_summary['aspect_ratings'],
    }

    return JsonResponse(data)

@login_required
def test_template_tag_view(request):
    """Test view for template tags."""
    test_dict = {
        'key1': 'value1',
        'key2': 'value2',
        'key3': 'value3'
    }

    context = {
        'test_dict': test_dict,
        'test_keys': ['key1', 'key2', 'key3']
    }

    return render(request, 'collaborate/test_template_tag.html', context)
