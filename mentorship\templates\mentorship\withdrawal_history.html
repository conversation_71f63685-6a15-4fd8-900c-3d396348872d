{% extends 'base.html' %}
{% load static %}

{% block title %}Withdrawal History - ForgeX{% endblock %}

{% block content %}
<div class="withdrawal-history">
    <!-- Header -->
    <section class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="page-title">
                    <h1>Withdrawal History</h1>
                    <p>Complete history of your withdrawal requests</p>
                </div>
                <div class="header-actions">
                    <a href="{% url 'mentorship:withdrawal_dashboard' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Content -->
    <section class="page-content">
        <div class="container">
            <div class="content-grid">
                <!-- Summary Cards -->
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="card-info">
                            <span class="card-value">${{ mentor_profile.total_earnings }}</span>
                            <span class="card-label">Total Earnings</span>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="card-info">
                            <span class="card-value">${{ mentor_profile.withdrawn_earnings }}</span>
                            <span class="card-label">Total Withdrawn</span>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="card-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="card-info">
                            <span class="card-value">${{ mentor_profile.get_available_balance }}</span>
                            <span class="card-label">Available Balance</span>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal History Table -->
                <div class="history-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> All Withdrawal Requests</h2>
                    </div>

                    {% if withdrawal_requests %}
                        <div class="history-table-container">
                            <table class="history-table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Processing Fee</th>
                                        <th>Net Amount</th>
                                        <th>Status</th>
                                        <th>Estimated Arrival</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for withdrawal in withdrawal_requests %}
                                        <tr class="withdrawal-row status-{{ withdrawal.status }}">
                                            <td>
                                                <div class="date-info">
                                                    <span class="date">{{ withdrawal.requested_at|date:"M d, Y" }}</span>
                                                    <span class="time">{{ withdrawal.requested_at|time:"g:i A" }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="amount">${{ withdrawal.amount }}</span>
                                            </td>
                                            <td>
                                                <span class="fee">${{ withdrawal.processing_fee }}</span>
                                            </td>
                                            <td>
                                                <span class="net-amount">${{ withdrawal.net_amount }}</span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-{{ withdrawal.status }}">
                                                    {{ withdrawal.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="arrival-time">{{ withdrawal.get_estimated_arrival }}</span>
                                            </td>
                                            <td>
                                                <div class="action-buttons">
                                                    {% if withdrawal.can_cancel %}
                                                        <button class="btn btn-sm btn-outline" 
                                                                onclick="cancelWithdrawal({{ withdrawal.id }})">
                                                            <i class="fas fa-times"></i>
                                                            Cancel
                                                        </button>
                                                    {% else %}
                                                        <span class="no-actions">-</span>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-money-bill-wave"></i>
                            <h3>No Withdrawal History</h3>
                            <p>You haven't made any withdrawal requests yet.</p>
                            <a href="{% url 'mentorship:withdrawal_dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                Make Your First Withdrawal
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.withdrawal-history {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.page-header {
    background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.page-title h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.page-title p {
    font-size: 1.1rem;
    opacity: 0.8;
}

.page-content {
    padding: 4rem 0;
}

.content-grid {
    max-width: 1400px;
    margin: 0 auto;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.summary-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.card-icon {
    width: 60px;
    height: 60px;
    background: rgba(192,255,107,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon i {
    color: #C0ff6b;
    font-size: 1.5rem;
}

.card-info {
    display: flex;
    flex-direction: column;
}

.card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #C0ff6b;
}

.card-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.history-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
}

.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.section-header h2 {
    color: #C0ff6b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
}

.history-table-container {
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.history-table th,
.history-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(192,255,107,0.1);
}

.history-table th {
    background: rgba(192,255,107,0.1);
    color: #C0ff6b;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.withdrawal-row {
    transition: all 0.3s ease;
}

.withdrawal-row:hover {
    background: rgba(255,255,255,0.02);
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date {
    font-weight: 600;
}

.time {
    font-size: 0.8rem;
    opacity: 0.7;
}

.amount {
    font-weight: 600;
    color: #C0ff6b;
    font-size: 1.1rem;
}

.fee {
    color: #ffc107;
}

.net-amount {
    font-weight: 600;
    color: #28a745;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: rgba(255,193,7,0.2); color: #ffc107; }
.status-processing { background: rgba(0,123,255,0.2); color: #007bff; }
.status-completed { background: rgba(40,167,69,0.2); color: #28a745; }
.status-failed { background: rgba(220,53,69,0.2); color: #dc3545; }
.status-cancelled { background: rgba(108,117,125,0.2); color: #6c757d; }

.arrival-time {
    font-size: 0.9rem;
    opacity: 0.8;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.no-actions {
    opacity: 0.5;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #C0ff6b;
    color: #1a1a1a;
}

.btn-outline {
    background: transparent;
    color: #C0ff6b;
    border: 1px solid rgba(192,255,107,0.3);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn:hover {
    transform: translateY(-2px);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    opacity: 0.8;
}

.empty-state i {
    font-size: 4rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #C0ff6b;
}

.empty-state p {
    margin-bottom: 2rem;
    opacity: 0.7;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .history-table {
        font-size: 0.8rem;
    }

    .history-table th,
    .history-table td {
        padding: 0.5rem;
    }

    .date-info {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Cancel withdrawal function
async function cancelWithdrawal(withdrawalId) {
    if (!confirm('Are you sure you want to cancel this withdrawal request?')) {
        return;
    }
    
    try {
        const response = await fetch(`/mentorship/withdrawal/${withdrawalId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert(result.message);
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
}
</script>
{% endblock %}
