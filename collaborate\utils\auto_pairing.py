"""
Auto Pairing Utility - ForgeX

This module provides functionality for automatically selecting the best team members
for a project based on skill matching, skill coverage, and team balance.
"""

from collaborate.utils.ranking import rank_users_for_project, get_team_skill_coverage
from collaborate.models import UserSkill

def auto_select_teammates_for_project(project):
    """
    Automatically selects best teammates based on ranking and skill coverage.
    
    The algorithm prioritizes:
    1. Users who cover critical skills not yet covered
    2. Users who cover any required skills not yet covered 
    3. Users with highest overall skill match scores
    
    Args:
        project: Project model instance
        
    Returns:
        list: Selected user objects forming the recommended team
    """
    from collaborate.models import TeamMatchLog
    
    top_candidates = rank_users_for_project(project)
    required_team_size = max(1, project.team_size - 1)  # minus project owner
    selected_users = []
    covered_skills = set()
    
    # Get project required and critical skills as sets of IDs
    project_required_skills = set(project.required_skills.values_list('id', flat=True))
    project_critical_skills = set(project.critical_skills.values_list('id', flat=True))
    
    # Track candidates and their match data for logging
    all_candidates_data = {}
    backup_users = set()
    
    # First pass: prioritize users who bring critical skills
    if project_critical_skills:
        remaining_critical_skills = project_critical_skills.copy()
        
        for user, score in top_candidates:
            # Get this user's skills
            user_skills = set(UserSkill.objects.filter(user=user).values_list('skill_id', flat=True))
            
            # Store skill data for logging
            skill_names = list(UserSkill.objects.filter(
                user=user, 
                skill_id__in=project_required_skills
            ).values_list('skill__name', flat=True))
            
            missing_skill_names = list(project.required_skills.exclude(
                id__in=user_skills
            ).values_list('name', flat=True))
            
            all_candidates_data[user.id] = {
                'user': user,
                'score': score,
                'overlap_skills': skill_names,
                'missing_skills': missing_skill_names
            }
            
            if len(selected_users) >= required_team_size:
                backup_users.add(user.id)
                continue
                
            # Check if user has any critical skills we haven't covered yet
            critical_skill_contribution = user_skills.intersection(remaining_critical_skills)
            if critical_skill_contribution:
                selected_users.append(user)
                covered_skills.update(user_skills)
                remaining_critical_skills -= critical_skill_contribution
    
    # Second pass: add users who bring new required skills
    if len(selected_users) < required_team_size and project_required_skills:
        remaining_required_skills = project_required_skills - covered_skills
        
        for user, score in top_candidates:
            if user in selected_users:
                continue
                
            if user.id not in all_candidates_data:
                # Get this user's skills
                user_skills = set(UserSkill.objects.filter(user=user).values_list('skill_id', flat=True))
                
                # Store skill data for logging
                skill_names = list(UserSkill.objects.filter(
                    user=user, 
                    skill_id__in=project_required_skills
                ).values_list('skill__name', flat=True))
                
                missing_skill_names = list(project.required_skills.exclude(
                    id__in=user_skills
                ).values_list('name', flat=True))
                
                all_candidates_data[user.id] = {
                    'user': user,
                    'score': score,
                    'overlap_skills': skill_names,
                    'missing_skills': missing_skill_names
                }
            else:
                user_skills = set(UserSkill.objects.filter(user=user).values_list('skill_id', flat=True))
            
            if len(selected_users) >= required_team_size:
                backup_users.add(user.id)
                continue
                
            # Check if user has any required skills we haven't covered yet
            skill_contribution = user_skills.intersection(remaining_required_skills)
            if skill_contribution:
                selected_users.append(user)
                covered_skills.update(user_skills)
                remaining_required_skills -= skill_contribution
    
    # Final pass: simply add top-ranked users until team is complete
    for user, score in top_candidates:
        if user in selected_users:
            continue
            
        if user.id not in all_candidates_data:
            # Get this user's skills
            user_skills = set(UserSkill.objects.filter(user=user).values_list('skill_id', flat=True))
            
            # Store skill data for logging
            skill_names = list(UserSkill.objects.filter(
                user=user, 
                skill_id__in=project_required_skills
            ).values_list('skill__name', flat=True))
            
            missing_skill_names = list(project.required_skills.exclude(
                id__in=user_skills
            ).values_list('name', flat=True))
            
            all_candidates_data[user.id] = {
                'user': user,
                'score': score,
                'overlap_skills': skill_names,
                'missing_skills': missing_skill_names
            }
            
        if len(selected_users) < required_team_size:
            selected_users.append(user)
        else:
            backup_users.add(user.id)
            break
    
    # Log all matches for debugging
    for user_id, data in all_candidates_data.items():
        TeamMatchLog.objects.create(
            project=project,
            user=data['user'],
            match_score=data['score'],
            overlap_skills=data['overlap_skills'],
            missing_skills=data['missing_skills'],
            is_backup=user_id in backup_users
        )
    
    return selected_users

def get_team_coverage_stats(project, team):
    """
    Get detailed statistics about how well a team covers the project's required skills.
    
    Args:
        project: Project model instance
        team: List of User objects
        
    Returns:
        dict: Statistics about skill coverage
    """
    # Get basic coverage stats
    coverage_stats = get_team_skill_coverage(project, team)
    
    # Add more detailed analysis
    project_critical_skills = set(project.critical_skills.values_list('id', flat=True))
    
    # Get all skills from team members
    team_skills = set()
    for user in team:
        user_skills = UserSkill.objects.filter(user=user).values_list('skill_id', flat=True)
        team_skills.update(user_skills)
    
    # Calculate critical skill coverage
    if project_critical_skills:
        covered_critical_skills = team_skills.intersection(project_critical_skills)
        critical_coverage_percent = (len(covered_critical_skills) / len(project_critical_skills)) * 100
        missing_critical_skills = project_critical_skills - team_skills
    else:
        critical_coverage_percent = 100
        missing_critical_skills = set()
    
    # Add to stats
    coverage_stats.update({
        'critical_coverage_percent': round(critical_coverage_percent, 1),
        'missing_critical_skills': list(missing_critical_skills),
        'has_complete_critical_coverage': len(missing_critical_skills) == 0,
        'team_size': len(team)
    })
    
    return coverage_stats

def optimize_team_composition(project, candidate_pool=None, max_iterations=10):
    """
    Advanced team formation that optimizes for skill coverage and complementarity.
    Uses a greedy approach with multiple iterations to find an optimal team.
    
    Args:
        project: Project model instance
        candidate_pool: Optional list of User objects to choose from (defaults to all users)
        max_iterations: Maximum optimization iterations (default: 10)
        
    Returns:
        tuple: (team list, coverage stats)
    """
    from django.contrib.auth import get_user_model
    
    # Get initial candidate pool if not provided
    if candidate_pool is None:
        User = get_user_model()
        candidate_pool = User.objects.exclude(id=project.owner.id).filter(is_active=True)
    
    # Start with a basic team selection
    current_team = auto_select_teammates_for_project(project)
    current_coverage = get_team_coverage_stats(project, current_team)
    
    # If we already have perfect coverage, return the team
    if current_coverage['is_complete_coverage'] and (
       'has_complete_critical_coverage' not in current_coverage or
       current_coverage['has_complete_critical_coverage']):
        return current_team, current_coverage
    
    # Try to optimize the team for better coverage
    best_team = current_team
    best_coverage = current_coverage
    
    for _ in range(max_iterations):
        # Try replacing each team member with candidates not in the team
        for i, member in enumerate(current_team):
            remaining_team = [u for j, u in enumerate(current_team) if j != i]
            
            for candidate in candidate_pool:
                if candidate in current_team:
                    continue
                
                # Create a test team with this replacement
                test_team = remaining_team + [candidate]
                test_coverage = get_team_coverage_stats(project, test_team)
                
                # Compare coverage metrics
                if test_coverage['coverage_percent'] > best_coverage['coverage_percent']:
                    best_team = test_team
                    best_coverage = test_coverage
                elif test_coverage['coverage_percent'] == best_coverage['coverage_percent']:
                    # If equal coverage, prefer team with better critical skill coverage
                    if test_coverage.get('critical_coverage_percent', 0) > best_coverage.get('critical_coverage_percent', 0):
                        best_team = test_team
                        best_coverage = test_coverage
        
        # If we didn't improve, stop trying
        if best_team == current_team:
            break
            
        current_team = best_team
        current_coverage = best_coverage
    
    return best_team, best_coverage