/**
 * Tag Input System
 * Converts select fields into tag-based inputs for better user experience
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tag inputs for all elements with class "tag-select"
    initializeTagInputs();
});

function initializeTagInputs() {
    const selectElements = document.querySelectorAll('.tag-select');
    
    selectElements.forEach(selectElement => {
        createTagInput(selectElement);
    });
}

function createTagInput(selectElement) {
    // Hide the original select element
    selectElement.classList.add('tag-select-hidden');
    
    // Create the tag input container
    const tagContainer = document.createElement('div');
    tagContainer.className = 'tag-input-container';
    
    // Create the tag input field
    const tagInput = document.createElement('div');
    tagInput.className = 'tag-input';
    
    // Create input for new tags
    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'tag-input-field';
    input.placeholder = selectElement.getAttribute('data-placeholder') || 'Add items...';
    
    // Create suggestions container
    const suggestionsContainer = document.createElement('div');
    suggestionsContainer.className = 'tag-suggestions';
    
    // Add elements to the DOM
    tagInput.appendChild(input);
    tagContainer.appendChild(tagInput);
    tagContainer.appendChild(suggestionsContainer);
    selectElement.parentNode.insertBefore(tagContainer, selectElement.nextSibling);
    
    // Get available options from the select element
    const options = Array.from(selectElement.options).map(option => ({
        value: option.value,
        text: option.textContent
    }));
    
    // Add existing selected options as tags
    const selectedOptions = Array.from(selectElement.selectedOptions);
    selectedOptions.forEach(option => {
        addTag(tagInput, input, selectElement, option.value, option.textContent);
    });
    
    // Set up event listeners
    setupTagInputEvents(tagInput, input, suggestionsContainer, selectElement, options);
}

function setupTagInputEvents(tagInput, input, suggestionsContainer, selectElement, options) {
    // Focus on input when clicking the tag container
    tagInput.addEventListener('click', function(e) {
        if (e.target === tagInput) {
            input.focus();
        }
    });
    
    // Handle input events for suggestions
    input.addEventListener('input', function() {
        const value = input.value.trim().toLowerCase();
        
        if (value) {
            // Filter options based on input
            const filtered = options.filter(option => 
                option.text.toLowerCase().includes(value) && 
                !isTagSelected(selectElement, option.value)
            );
            
            // Show suggestions
            showSuggestions(filtered, suggestionsContainer, tagInput, input, selectElement);
        } else {
            // Hide suggestions if input is empty
            suggestionsContainer.style.display = 'none';
        }
    });
    
    // Handle keyboard navigation and selection
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && input.value.trim()) {
            e.preventDefault();
            
            // Check if the entered value matches any existing option
            const value = input.value.trim();
            const matchedOption = options.find(option => 
                option.text.toLowerCase() === value.toLowerCase() && 
                !isTagSelected(selectElement, option.value)
            );
            
            if (matchedOption) {
                addTag(tagInput, input, selectElement, matchedOption.value, matchedOption.text);
            } else {
                // Add as a new option if allowed
                if (selectElement.hasAttribute('data-allow-new')) {
                    const newValue = value.replace(/,/g, ''); // Remove commas
                    addTag(tagInput, input, selectElement, newValue, newValue);
                    
                    // Add to options list
                    options.push({ value: newValue, text: newValue });
                    
                    // Add as an option to the select element
                    const newOption = document.createElement('option');
                    newOption.value = newValue;
                    newOption.textContent = newValue;
                    selectElement.appendChild(newOption);
                }
            }
            
            // Clear input and hide suggestions
            input.value = '';
            suggestionsContainer.style.display = 'none';
        } else if (e.key === 'Backspace' && input.value === '') {
            // Remove the last tag when backspace is pressed with empty input
            const tags = tagInput.querySelectorAll('.tag');
            if (tags.length > 0) {
                const lastTag = tags[tags.length - 1];
                const value = lastTag.getAttribute('data-value');
                removeTag(lastTag, selectElement, value);
            }
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!tagInput.contains(e.target)) {
            suggestionsContainer.style.display = 'none';
        }
    });
}

function showSuggestions(suggestions, container, tagInput, input, selectElement) {
    // Clear previous suggestions
    container.innerHTML = '';
    
    if (suggestions.length === 0) {
        container.style.display = 'none';
        return;
    }
    
    // Add suggestion items
    suggestions.forEach(suggestion => {
        const item = document.createElement('div');
        item.className = 'tag-suggestion-item';
        item.textContent = suggestion.text;
        
        item.addEventListener('click', function() {
            addTag(tagInput, input, selectElement, suggestion.value, suggestion.text);
            input.value = '';
            container.style.display = 'none';
            input.focus();
        });
        
        container.appendChild(item);
    });
    
    // Show suggestions container
    container.style.display = 'block';
}

function addTag(tagInput, input, selectElement, value, text) {
    // Create tag element
    const tag = document.createElement('div');
    tag.className = 'tag';
    tag.setAttribute('data-value', value);
    
    const tagText = document.createElement('span');
    tagText.className = 'tag-text';
    tagText.textContent = text;
    
    const closeBtn = document.createElement('span');
    closeBtn.className = 'tag-close';
    closeBtn.textContent = '×';
    closeBtn.addEventListener('click', function() {
        removeTag(tag, selectElement, value);
    });
    
    tag.appendChild(tagText);
    tag.appendChild(closeBtn);
    
    // Insert tag before the input
    tagInput.insertBefore(tag, input);
    
    // Update the select element
    updateSelectElement(selectElement, value, true);
}

function removeTag(tag, selectElement, value) {
    // Remove tag element
    tag.remove();
    
    // Update the select element
    updateSelectElement(selectElement, value, false);
}

function updateSelectElement(selectElement, value, selected) {
    const option = Array.from(selectElement.options).find(opt => opt.value === value);
    
    if (option) {
        option.selected = selected;
    }
    
    // Trigger change event
    const event = new Event('change', { bubbles: true });
    selectElement.dispatchEvent(event);
}

function isTagSelected(selectElement, value) {
    return Array.from(selectElement.selectedOptions).some(option => option.value === value);
}