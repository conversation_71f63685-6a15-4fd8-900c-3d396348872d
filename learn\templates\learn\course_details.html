{% extends "base.html" %} {% block content %} {% load static %}
<section class="collaborate-page">
  <div class="collaborate-card fade-in visible">
    <!-- Breadcrumb navigation -->
    <nav aria-label="breadcrumb" class="breadcrumb-nav">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <a href="{% url 'learn:course_list' %}">Courses</a>
        </li>
        <li class="breadcrumb-separator">/</li>
        <li class="breadcrumb-item active" aria-current="page">
          {{ course.name }}
        </li>
      </ol>
    </nav>

    <div class="course-header">
      <h1>{{course.name}}</h1>
      <img src="{{course.image}}" alt="" />
    </div>
    <div class="course-description">
      <p>{{course.description}}</p>
    </div>
    {% if can_manage_course %}
    <div class="course-actions">
      <a
        href="{% url 'learn:create_chapter' course.id %}"
        class="btn btn-success"
        >📚 Create Chapter</a
      >
      <a
        href="{% url 'learn:edit_course' course.id %}"
        class="btn btn-primary"
        >✏️ Edit Course</a
      >
      {% if is_superuser or course.created_by == user %}
      <a
        href="{% url 'learn:delete_course' course.id %}"
        onclick="return confirm('Are you sure you want to delete this entire course? This action cannot be undone.')"
        class="btn btn-danger"
        >🗑️ Delete Course</a
      >
      {% endif %}
    </div>
    {% endif %}
    <ul class="chapter-list">
      {% for chapter in chapters.all %}
      <li class="chapter-box">
        <a
          href="javascript:void(0);"
          class="chapter-title"
          onclick="toggleLessons('lessons-{{ chapter.id }}')"
          >Chapter: {{ chapter.name }}</a
        >
        {% if can_manage_course %}
        <a
          href="{% url 'learn:create_lesson' chapter.id %}"
          class="btn btn-success btn-sm"
          >➕ Add Lesson</a
        >
        <a
          href="{% url 'learn:edit_chapter' chapter.id %}"
          class="btn btn-secondary btn-sm"
          >✏️ Edit</a
        >
        <a
          href="{% url 'learn:delete_chapter' chapter.id %}"
          onclick="return confirm('Delete this chapter and all its lessons?')"
          class="btn btn-danger btn-sm"
          >🗑️ Delete</a
        >
        {% endif %}
        <ul class="lesson-list" id="lessons-{{ chapter.id }}">
          {% for lesson in chapter.lesson_set.all %}
          <li>
            <a
              href="{% url 'learn:lesson_details' lesson.id %}"
              class="lesson-link"
              >Lesson : {{lesson.name}}</a
            >
            {% if can_manage_course %}
            <a
              href="{% url 'learn:edit_lesson' lesson.id %}"
              class="btn btn-secondary btn-sm"
              >✏️ Edit</a
            >
            <a
              href="{% url 'learn:delete_lesson' lesson.id %}"
              onclick="return confirm('Delete this lesson and all its content?')"
              class="btn btn-danger btn-sm"
              >🗑️ Delete</a
            >
            {% endif %}
          </li>
          {% endfor %}
        </ul>
      </li>
      {% endfor %}
    </ul>
  </div>
</section>
<script>
  function toggleLessons(id) {
    var el = document.getElementById(id);
    if (el) {
      el.classList.toggle("active");
    }
  }
</script>
{% endblock %}
