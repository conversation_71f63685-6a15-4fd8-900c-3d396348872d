"""
Local File Storage Service for ForgeX
Provides file operations when Docker services are not available
"""

import os
import json
import logging
from pathlib import Path
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import mimetypes

logger = logging.getLogger(__name__)

# Base directory for project files
PROJECTS_BASE_DIR = os.path.join(settings.BASE_DIR, 'project_files')

def ensure_projects_directory():
    """Ensure the projects directory exists"""
    if not os.path.exists(PROJECTS_BASE_DIR):
        os.makedirs(PROJECTS_BASE_DIR, exist_ok=True)
        logger.info(f"Created projects directory: {PROJECTS_BASE_DIR}")

def get_project_directory(project_id):
    """Get the directory path for a specific project"""
    ensure_projects_directory()
    project_dir = os.path.join(PROJECTS_BASE_DIR, str(project_id))
    if not os.path.exists(project_dir):
        os.makedirs(project_dir, exist_ok=True)
        # Create a default README.md file
        readme_path = os.path.join(project_dir, 'README.md')
        if not os.path.exists(readme_path):
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(f"# Project {project_id}\n\nWelcome to your ForgeX project!\n\n## Getting Started\n\n1. Create your first file\n2. Start coding\n3. Collaborate with your team\n")
        logger.info(f"Created project directory: {project_dir}")
    return project_dir

def scan_directory(directory_path, base_path=""):
    """Scan a directory and return file tree structure"""
    items = []
    
    if not os.path.exists(directory_path):
        return items
    
    try:
        for item_name in sorted(os.listdir(directory_path)):
            if item_name.startswith('.'):
                continue  # Skip hidden files
                
            item_path = os.path.join(directory_path, item_name)
            relative_path = os.path.join(base_path, item_name) if base_path else item_name
            
            if os.path.isdir(item_path):
                # Directory
                children = scan_directory(item_path, relative_path)
                items.append({
                    "name": item_name,
                    "path": relative_path.replace("\\", "/"),
                    "type": "directory",
                    "children": children
                })
            else:
                # File
                try:
                    file_size = os.path.getsize(item_path)
                    modified_time = os.path.getmtime(item_path)
                    
                    items.append({
                        "name": item_name,
                        "path": relative_path.replace("\\", "/"),
                        "type": "file",
                        "size": file_size,
                        "modified": modified_time
                    })
                except (OSError, IOError) as e:
                    logger.warning(f"Could not get info for file {item_path}: {e}")
                    continue
                    
    except (OSError, IOError) as e:
        logger.error(f"Error scanning directory {directory_path}: {e}")
        
    return items

def get_project_files(project_id):
    """Get file tree for a project"""
    try:
        project_dir = get_project_directory(project_id)
        file_tree = scan_directory(project_dir)
        logger.info(f"Retrieved file tree for project {project_id}: {len(file_tree)} items")
        return file_tree
    except Exception as e:
        logger.error(f"Error getting files for project {project_id}: {e}")
        return []

def get_file_content(project_id, file_path):
    """Get content of a specific file"""
    try:
        project_dir = get_project_directory(project_id)
        full_path = os.path.join(project_dir, file_path)
        
        # Security check - ensure file is within project directory
        if not os.path.abspath(full_path).startswith(os.path.abspath(project_dir)):
            raise ValueError("Invalid file path")
        
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        if os.path.isdir(full_path):
            raise ValueError("Path is a directory, not a file")
        
        # Try to read as text first
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"Read file {file_path} from project {project_id}")
            return content
        except UnicodeDecodeError:
            # If it's a binary file, return base64 encoded content
            import base64
            with open(full_path, 'rb') as f:
                content = base64.b64encode(f.read()).decode('utf-8')
            logger.info(f"Read binary file {file_path} from project {project_id}")
            return content
            
    except Exception as e:
        logger.error(f"Error reading file {file_path} from project {project_id}: {e}")
        raise

def save_file_content(project_id, file_path, content, is_directory=False):
    """Save content to a file"""
    try:
        project_dir = get_project_directory(project_id)
        full_path = os.path.join(project_dir, file_path)
        
        # Security check - ensure file is within project directory
        if not os.path.abspath(full_path).startswith(os.path.abspath(project_dir)):
            raise ValueError("Invalid file path")
        
        # Create parent directories if they don't exist
        parent_dir = os.path.dirname(full_path)
        if parent_dir and not os.path.exists(parent_dir):
            os.makedirs(parent_dir, exist_ok=True)
        
        if is_directory:
            # Create directory
            os.makedirs(full_path, exist_ok=True)
            logger.info(f"Created directory {file_path} in project {project_id}")
        else:
            # Save file content
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content or "")
            logger.info(f"Saved file {file_path} in project {project_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error saving file {file_path} in project {project_id}: {e}")
        raise

def delete_file_or_directory(project_id, file_path):
    """Delete a file or directory"""
    try:
        project_dir = get_project_directory(project_id)
        full_path = os.path.join(project_dir, file_path)
        
        # Security check - ensure file is within project directory
        if not os.path.abspath(full_path).startswith(os.path.abspath(project_dir)):
            raise ValueError("Invalid file path")
        
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"File or directory not found: {file_path}")
        
        if os.path.isdir(full_path):
            import shutil
            shutil.rmtree(full_path)
            logger.info(f"Deleted directory {file_path} from project {project_id}")
        else:
            os.remove(full_path)
            logger.info(f"Deleted file {file_path} from project {project_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error deleting {file_path} from project {project_id}: {e}")
        raise

def rename_file_or_directory(project_id, old_path, new_path):
    """Rename a file or directory"""
    try:
        project_dir = get_project_directory(project_id)
        old_full_path = os.path.join(project_dir, old_path)
        new_full_path = os.path.join(project_dir, new_path)
        
        # Security checks
        if not os.path.abspath(old_full_path).startswith(os.path.abspath(project_dir)):
            raise ValueError("Invalid old file path")
        if not os.path.abspath(new_full_path).startswith(os.path.abspath(project_dir)):
            raise ValueError("Invalid new file path")
        
        if not os.path.exists(old_full_path):
            raise FileNotFoundError(f"File or directory not found: {old_path}")
        
        if os.path.exists(new_full_path):
            raise ValueError(f"Target already exists: {new_path}")
        
        # Create parent directories for new path if needed
        new_parent_dir = os.path.dirname(new_full_path)
        if new_parent_dir and not os.path.exists(new_parent_dir):
            os.makedirs(new_parent_dir, exist_ok=True)
        
        os.rename(old_full_path, new_full_path)
        logger.info(f"Renamed {old_path} to {new_path} in project {project_id}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error renaming {old_path} to {new_path} in project {project_id}: {e}")
        raise

# Initialize projects directory on import
ensure_projects_directory()
