# Generated by Django 5.2.1 on 2025-06-09 16:18

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("accounts", "0017_alter_userprofile_timezone"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SecuritySettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("obfuscation_enabled", models.BooleanField(default=True)),
                ("encryption_enabled", models.BooleanField(default=True)),
                ("devtools_protection", models.BooleanField(default=True)),
                ("console_protection", models.BooleanField(default=True)),
                ("rate_limiting_enabled", models.BooleanField(default=True)),
                ("max_requests_per_minute", models.Integer<PERSON>ield(default=100)),
                (
                    "security_logging_level",
                    models.CharField(
                        choices=[
                            ("minimal", "Minimal"),
                            ("standard", "Standard"),
                            ("verbose", "Verbose"),
                            ("debug", "Debug"),
                        ],
                        default="standard",
                        max_length=20,
                    ),
                ),
                ("api_encryption_required", models.BooleanField(default=True)),
                ("api_token_expiry_minutes", models.IntegerField(default=60)),
                ("session_timeout_minutes", models.IntegerField(default=60)),
                ("force_https", models.BooleanField(default=True)),
                ("disable_right_click", models.BooleanField(default=True)),
                ("disable_text_selection", models.BooleanField(default=False)),
                ("disable_keyboard_shortcuts", models.BooleanField(default=True)),
                ("monitor_api_calls", models.BooleanField(default=True)),
                ("monitor_file_access", models.BooleanField(default=True)),
                ("monitor_user_behavior", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "updated_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Security Settings",
                "verbose_name_plural": "Security Settings",
            },
        ),
        migrations.CreateModel(
            name="UserSecurityProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "security_clearance",
                    models.CharField(
                        choices=[
                            ("user", "Regular User"),
                            ("developer", "Developer"),
                            ("tester", "Tester"),
                            ("admin", "Administrator"),
                            ("security_admin", "Security Administrator"),
                        ],
                        default="user",
                        max_length=20,
                    ),
                ),
                ("can_access_debug_mode", models.BooleanField(default=False)),
                ("can_view_source_code", models.BooleanField(default=False)),
                ("can_access_api_docs", models.BooleanField(default=False)),
                ("can_modify_security_settings", models.BooleanField(default=False)),
                ("failed_login_attempts", models.IntegerField(default=0)),
                ("last_failed_login", models.DateTimeField(blank=True, null=True)),
                ("account_locked_until", models.DateTimeField(blank=True, null=True)),
                ("max_concurrent_sessions", models.IntegerField(default=3)),
                ("force_password_change", models.BooleanField(default=False)),
                ("password_last_changed", models.DateTimeField(blank=True, null=True)),
                ("last_security_review", models.DateTimeField(blank=True, null=True)),
                ("security_notes", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="security_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SecurityLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "event_type",
                    models.CharField(
                        choices=[
                            ("devtools_open", "Developer Tools Opened"),
                            ("console_access", "Console Access Attempt"),
                            ("script_injection", "Script Injection Detected"),
                            ("api_abuse", "API Abuse Detected"),
                            ("rate_limit_exceeded", "Rate Limit Exceeded"),
                            ("invalid_token", "Invalid Token Attempt"),
                            ("security_threat", "General Security Threat"),
                            ("login_attempt", "Login Attempt"),
                            ("permission_denied", "Permission Denied"),
                            ("data_access", "Sensitive Data Access"),
                            ("settings_change", "Security Settings Changed"),
                            ("other", "Other Security Event"),
                        ],
                        max_length=50,
                    ),
                ),
                ("event_data", models.JSONField(blank=True, default=dict)),
                ("ip_address", models.GenericIPAddressField()),
                ("user_agent", models.TextField()),
                ("timestamp", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "severity",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                ("resolved", models.BooleanField(default=False)),
                ("notes", models.TextField(blank=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="security_logs",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="accounts_se_user_id_e4a20b_idx",
                    ),
                    models.Index(
                        fields=["event_type", "timestamp"],
                        name="accounts_se_event_t_25c8d0_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "timestamp"],
                        name="accounts_se_ip_addr_67792a_idx",
                    ),
                ],
            },
        ),
    ]
