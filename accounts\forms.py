from django import forms
from django.contrib.auth.forms import UserC<PERSON><PERSON>Form, AuthenticationForm, PasswordResetForm, SetPasswordForm
from django.contrib.auth.models import User
from django.contrib.auth import authenticate
from .models import UserProfile, Skill, Event, SupportCategory, SupportTicket, SupportMessage
from collaborate.models import UserSkill as CollaborateUserSkill
from collaborate.models import Skill as CollaborateSkill
import re

class SignUpForm(UserCreationForm):
    first_name = forms.CharField(max_length=30, required=True)
    last_name = forms.CharField(max_length=30, required=True)
    email = forms.EmailField(max_length=254, required=True)

    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'username', 'email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


        for field_name, field in self.fields.items():
            field.widget.attrs['class'] = 'form-control'

        self.fields['first_name'].widget.attrs['placeholder'] = 'First Name'
        self.fields['last_name'].widget.attrs['placeholder'] = 'Last Name'
        self.fields['username'].widget.attrs['placeholder'] = 'Username'
        self.fields['email'].widget.attrs['placeholder'] = 'Email'
        self.fields['password1'].widget.attrs['placeholder'] = 'Password'
        self.fields['password2'].widget.attrs['placeholder'] = 'Confirm Password'


    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError("This username is already taken.")
        return username

    def clean_email(self):
        email = self.cleaned_data.get('email')

        # Enhanced email validation
        if not email:
            raise forms.ValidationError("Email is required.")

        # Check email format
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, email):
            raise forms.ValidationError("Please enter a valid email address.")

        # Check against allowed domains
        from django.conf import settings
        if hasattr(settings, 'ALLOWED_EMAIL_DOMAINS'):
            allowed_domains = [d.strip().lower() for d in settings.ALLOWED_EMAIL_DOMAINS]
            domain = email.split('@')[-1].lower()
            if domain not in allowed_domains:
                raise forms.ValidationError(
                    f"Email domain '{domain}' is not allowed. Please use an email from: {', '.join(allowed_domains)}"
                )

        # Check for existing email
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("This email is already registered. Please use a different email or try logging in.")

        return email.lower()  # Store email in lowercase

    def clean_password1(self):
        password = self.cleaned_data.get('password1')

        # Enhanced password validation
        if not password:
            raise forms.ValidationError("Password is required.")

        if len(password) < 8:
            raise forms.ValidationError("Password must be at least 8 characters long.")

        if not re.search(r'[A-Z]', password):
            raise forms.ValidationError("Password must contain at least one uppercase letter.")

        if not re.search(r'[a-z]', password):
            raise forms.ValidationError("Password must contain at least one lowercase letter.")

        if not re.search(r'[0-9]', password):
            raise forms.ValidationError("Password must contain at least one number.")

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise forms.ValidationError("Password must contain at least one special character (!@#$%^&*(),.?\":{}|<>).")

        # Check for common weak passwords
        weak_passwords = ['password', '12345678', 'qwerty123', 'abc123456', 'password123']
        if password.lower() in weak_passwords:
            raise forms.ValidationError("This password is too common. Please choose a stronger password.")

        return password

    def clean_first_name(self):
        first_name = self.cleaned_data.get('first_name')
        if not first_name:
            raise forms.ValidationError("First name is required.")
        if len(first_name) < 2:
            raise forms.ValidationError("First name must be at least 2 characters long.")
        if not re.match(r'^[a-zA-Z\s]+$', first_name):
            raise forms.ValidationError("First name can only contain letters and spaces.")
        return first_name.strip().title()

    def clean_last_name(self):
        last_name = self.cleaned_data.get('last_name')
        if not last_name:
            raise forms.ValidationError("Last name is required.")
        if len(last_name) < 2:
            raise forms.ValidationError("Last name must be at least 2 characters long.")
        if not re.match(r'^[a-zA-Z\s]+$', last_name):
            raise forms.ValidationError("Last name can only contain letters and spaces.")
        return last_name.strip().title()

class EmailAuthenticationForm(AuthenticationForm):
    """Custom authentication form that allows login with either username or email"""

    remember_me = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
            'id': 'remember_me'
        }),
        label='Keep me logged in'
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # self.error_class = 'alert text-body'

        for field_name, field in self.fields.items():
            if field_name != 'remember_me':  # Skip styling for checkbox
                field.widget.attrs['class'] = 'form-control'
                field.widget.attrs['autocomplete'] = 'off'

        self.fields['username'].widget.attrs['placeholder'] = 'Username or Email'
        self.fields['password'].widget.attrs['placeholder'] = 'Password'

    def clean(self):
        username = self.cleaned_data.get('username')
        password = self.cleaned_data.get('password')

        if not username:
            self.add_error('username', "This field is required.")

        if not password:
            self.add_error('password', "This field is required.")

        if username and password:
            # Check if the input is an email
            if '@' in username:
                try:
                    user = User.objects.get(email=username)
                    username = user.username  # Convert email to username
                except User.DoesNotExist:
                    self.add_error('username', "This email is not registered.")
                    return  # Stop execution if email does not exist

            self.user_cache = authenticate(self.request, username=username, password=password)

            if self.user_cache is None:
                self.add_error('username', "Please enter a correct username or password")
            else:
                self.confirm_login_allowed(self.user_cache)

        return self.cleaned_data

class UserSkillsForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ['bio', 'skills']
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'skills': forms.CheckboxSelectMultiple(),
        }

class UserProfileForm(forms.ModelForm):
    class Meta:
        model = UserProfile
        fields = ['bio', 'skills', 'profile_picture', 'cv', 'timezone', 'country', 'availability_type', 'availability_start', 'availability_end']
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'skills': forms.CheckboxSelectMultiple(),
            'profile_picture': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'cv': forms.FileInput(attrs={'class': 'form-control'}),
            'timezone': forms.Select(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            'availability_type': forms.Select(attrs={'class': 'form-control'}),
            'availability_start': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'availability_end': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
        }

    def clean(self):
        cleaned_data = super().clean()
        availability_start = cleaned_data.get('availability_start')
        availability_end = cleaned_data.get('availability_end')

        # Validate that start time is before end time if both are provided
        if availability_start and availability_end and availability_start >= availability_end:
            self.add_error('availability_end', "End time must be after start time.")

        return cleaned_data


class UserAccountForm(forms.ModelForm):
    """Form for updating user account information (username, first_name, last_name)"""

    class Meta:
        model = User
        fields = ['username', 'first_name', 'last_name', 'email']
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'readonly': True}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs['placeholder'] = 'Enter your username'
        self.fields['first_name'].widget.attrs['placeholder'] = 'Enter your first name'
        self.fields['last_name'].widget.attrs['placeholder'] = 'Enter your last name'
        self.fields['email'].help_text = 'Email cannot be changed here. Contact support for email changes.'

        # Add labels
        self.fields['username'].label = 'Username'
        self.fields['first_name'].label = 'First Name'
        self.fields['last_name'].label = 'Last Name'
        self.fields['email'].label = 'Email Address'

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if username:
            # Check if username is taken by another user
            if User.objects.filter(username=username).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError('This username is already taken.')

            # Validate username format
            if len(username) < 3:
                raise forms.ValidationError('Username must be at least 3 characters long.')

            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                raise forms.ValidationError('Username can only contain letters, numbers, and underscores.')

        return username

    def clean_first_name(self):
        first_name = self.cleaned_data.get('first_name')
        if first_name and len(first_name.strip()) < 1:
            raise forms.ValidationError('First name cannot be empty.')
        return first_name.strip() if first_name else ''

    def clean_last_name(self):
        last_name = self.cleaned_data.get('last_name')
        if last_name and len(last_name.strip()) < 1:
            raise forms.ValidationError('Last name cannot be empty.')
        return last_name.strip() if last_name else ''


class ExtendedUserProfileForm(forms.ModelForm):
    """Extended form for comprehensive profile setup including new fields"""

    class Meta:
        model = UserProfile
        fields = [
            'bio', 'skills', 'profile_picture', 'cv', 'timezone', 'country',
            'availability_type', 'availability_start', 'availability_end',
            'phone_number', 'linkedin_url', 'github_url', 'professional_title', 'website_url'
        ]
        widgets = {
            'bio': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'skills': forms.CheckboxSelectMultiple(),
            'profile_picture': forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'cv': forms.FileInput(attrs={'class': 'form-control'}),
            'timezone': forms.Select(attrs={'class': 'form-control'}),
            'country': forms.TextInput(attrs={'class': 'form-control'}),
            'availability_type': forms.Select(attrs={'class': 'form-control'}),
            'availability_start': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'availability_end': forms.TimeInput(attrs={'class': 'form-control', 'type': 'time'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'linkedin_url': forms.URLInput(attrs={'class': 'form-control'}),
            'github_url': forms.URLInput(attrs={'class': 'form-control'}),
            'professional_title': forms.TextInput(attrs={'class': 'form-control'}),
            'website_url': forms.URLInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['bio'].widget.attrs['placeholder'] = 'Tell us about yourself...'
        self.fields['country'].widget.attrs['placeholder'] = 'e.g., United States'
        self.fields['phone_number'].widget.attrs['placeholder'] = '+****************'
        self.fields['linkedin_url'].widget.attrs['placeholder'] = 'https://linkedin.com/in/yourprofile'
        self.fields['github_url'].widget.attrs['placeholder'] = 'https://github.com/yourusername'
        self.fields['professional_title'].widget.attrs['placeholder'] = 'e.g., Software Engineer, Data Scientist'
        self.fields['website_url'].widget.attrs['placeholder'] = 'https://yourwebsite.com'

        # Add labels
        self.fields['phone_number'].label = 'Phone Number'
        self.fields['linkedin_url'].label = 'LinkedIn Profile'
        self.fields['github_url'].label = 'GitHub Profile'
        self.fields['professional_title'].label = 'Professional Title'
        self.fields['website_url'].label = 'Personal Website'

    def clean_phone_number(self):
        phone = self.cleaned_data.get('phone_number')
        if phone:
            # Basic phone number validation
            phone_clean = re.sub(r'[^\d+]', '', phone)
            if len(phone_clean) < 10:
                raise forms.ValidationError('Please enter a valid phone number.')
        return phone

    def clean_linkedin_url(self):
        url = self.cleaned_data.get('linkedin_url')
        if url and 'linkedin.com' not in url.lower():
            raise forms.ValidationError('Please enter a valid LinkedIn URL.')
        return url

    def clean_github_url(self):
        url = self.cleaned_data.get('github_url')
        if url and 'github.com' not in url.lower():
            raise forms.ValidationError('Please enter a valid GitHub URL.')
        return url

    def clean_website_url(self):
        url = self.cleaned_data.get('website_url')
        if url:
            # Basic URL validation
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
        return url

    def clean(self):
        cleaned_data = super().clean()
        availability_start = cleaned_data.get('availability_start')
        availability_end = cleaned_data.get('availability_end')

        # Validate that start time is before end time if both are provided
        if availability_start and availability_end and availability_start >= availability_end:
            self.add_error('availability_end', "End time must be after start time.")

        return cleaned_data


class SkillWithLevelForm(forms.Form):
    """Form for adding a skill with proficiency level"""
    skill = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'list': 'skill-list',
            'autocomplete': 'off'
        })
    )

    level = forms.ChoiceField(
        choices=CollaborateUserSkill.PROFICIENCY_CHOICES,
        initial=1,  # Default to Beginner
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['skill'].label = "Skill Name"
        self.fields['level'].label = "Proficiency Level"

    def clean_skill(self):
        skill_name = self.cleaned_data.get('skill')

        # If the skill field is empty, return it as is (it's optional)
        if not skill_name or skill_name.strip() == '':
            return ''

        # Check if the skill exists in the database
        try:
            # First check in the Collaborate app's Skill model
            # Use filter().first() instead of get() to avoid MultipleObjectsReturned error
            skill = CollaborateSkill.objects.filter(name__iexact=skill_name).first()
            if skill:
                # Use the exact case from the database
                return skill.name

            # If not found by exact match, try to find a close match
            from collaborate.utils.skill_standardization import SKILL_ALIASES

            # Check if it's a known alias
            for standard_name, aliases in SKILL_ALIASES.items():
                if skill_name.lower() in [alias.lower() for alias in aliases]:
                    # Use the standardized name instead
                    standard_skill = CollaborateSkill.objects.filter(name__iexact=standard_name).first()
                    if standard_skill:
                        return standard_skill.name
                    return standard_name

            # If we get here, no match was found
            # Create the skill if it's a reasonable length
            if len(skill_name) <= 50 and len(skill_name) >= 2:
                # Create a new skill
                new_skill, created = CollaborateSkill.objects.get_or_create(
                    name=skill_name.strip().title()  # Capitalize the first letter of each word
                )
                return new_skill.name

            # Otherwise, raise a validation error
            raise forms.ValidationError(
                f"'{skill_name}' is not a recognized skill. Please select from the suggested list."
            )

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error validating skill '{skill_name}': {str(e)}")

            # Return a user-friendly error
            raise forms.ValidationError(
                "There was an error processing this skill. Please try another one."
            )

class SkillLevelFormSet(forms.BaseFormSet):
    """Formset for managing multiple skill level forms"""
    def clean(self):
        """Validates that no two skills are the same"""
        if any(self.errors):
            # Don't validate if individual forms have errors
            return

        skills = []
        for form in self.forms:
            if form.cleaned_data and form.cleaned_data.get('skill'):
                skill = form.cleaned_data.get('skill').strip()
                if skill and skill in skills:
                    raise forms.ValidationError("Please enter each skill only once.")
                if skill:  # Only add non-empty skills
                    skills.append(skill)


class EventForm(forms.ModelForm):
    """Form for creating and editing events"""

    class Meta:
        model = Event
        fields = ['title', 'description', 'event_date', 'location']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter event title'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Enter event description'
            }),
            'event_date': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
            'location': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter event location (optional)'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['title'].label = "Event Title"
        self.fields['description'].label = "Event Description"
        self.fields['event_date'].label = "Event Date & Time"
        self.fields['location'].label = "Location"
        self.fields['location'].required = False

    def clean_event_date(self):
        event_date = self.cleaned_data.get('event_date')
        from django.utils import timezone

        if event_date and event_date <= timezone.now():
            raise forms.ValidationError("Event date must be in the future.")

        return event_date


class SupportTicketForm(forms.ModelForm):
    """Form for creating support tickets"""

    class Meta:
        model = SupportTicket
        fields = ['category', 'subject', 'description', 'priority']
        widgets = {
            'category': forms.Select(attrs={
                'class': 'form-control',
                'required': True
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Brief description of your issue',
                'maxlength': 200
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 6,
                'placeholder': 'Please provide detailed information about your issue...'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].label = "Category"
        self.fields['subject'].label = "Subject"
        self.fields['description'].label = "Description"
        self.fields['priority'].label = "Priority"

        # Filter only active categories
        self.fields['category'].queryset = SupportCategory.objects.filter(is_active=True)

        # Set help texts
        self.fields['subject'].help_text = "Summarize your issue in a few words"
        self.fields['description'].help_text = "Provide as much detail as possible to help us assist you"
        self.fields['priority'].help_text = "Select the urgency level of your issue"


class SupportMessageForm(forms.ModelForm):
    """Form for adding messages to support tickets"""

    class Meta:
        model = SupportMessage
        fields = ['message']
        widgets = {
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Type your message here...',
                'style': 'resize: vertical;'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['message'].label = ""
        self.fields['message'].help_text = "Press Ctrl+Enter to send"


class SupportCategoryForm(forms.ModelForm):
    """Form for creating/editing support categories (admin only)"""

    class Meta:
        model = SupportCategory
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Category name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Brief description of this category'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['name'].label = "Category Name"
        self.fields['description'].label = "Description"
        self.fields['is_active'].label = "Active"
        self.fields['description'].required = False


class SupportTicketUpdateForm(forms.ModelForm):
    """Form for updating support ticket status and assignment (admin only)"""

    class Meta:
        model = SupportTicket
        fields = ['status', 'priority', 'assigned_to']
        widgets = {
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-control'
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-control'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['status'].label = "Status"
        self.fields['priority'].label = "Priority"
        self.fields['assigned_to'].label = "Assign to"

        # Filter assigned_to to only staff users
        from django.db import models as django_models
        self.fields['assigned_to'].queryset = User.objects.filter(
            django_models.Q(is_staff=True) | django_models.Q(is_superuser=True)
        ).order_by('username')
        self.fields['assigned_to'].required = False


class CustomPasswordResetForm(PasswordResetForm):
    """Custom password reset form with ForgeX styling and enhanced validation"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['email'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter your email address',
            'autocomplete': 'email'
        })
        self.fields['email'].label = "Email Address"
        self.fields['email'].help_text = "Enter the email address associated with your account"

    def clean_email(self):
        email = self.cleaned_data.get('email')

        if not email:
            raise forms.ValidationError("Email address is required.")

        # Check if email exists in the system
        if not User.objects.filter(email=email, is_active=True).exists():
            raise forms.ValidationError(
                "No active account found with this email address. "
                "Please check your email or create a new account."
            )

        # Check against allowed domains
        from django.conf import settings
        if hasattr(settings, 'ALLOWED_EMAIL_DOMAINS'):
            allowed_domains = [d.strip().lower() for d in settings.ALLOWED_EMAIL_DOMAINS]
            domain = email.split('@')[-1].lower()
            if domain not in allowed_domains:
                raise forms.ValidationError(
                    f"Email domain '{domain}' is not supported. "
                    f"Please use an email from: {', '.join(allowed_domains)}"
                )

        return email.lower()


class CustomSetPasswordForm(SetPasswordForm):
    """Custom set password form with ForgeX styling and enhanced validation"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.fields['new_password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Enter new password',
            'autocomplete': 'new-password'
        })
        self.fields['new_password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': 'Confirm new password',
            'autocomplete': 'new-password'
        })

        self.fields['new_password1'].label = "New Password"
        self.fields['new_password2'].label = "Confirm New Password"

        self.fields['new_password1'].help_text = (
            "Password must be at least 8 characters long and contain uppercase, "
            "lowercase, numbers, and special characters."
        )

    def clean_new_password1(self):
        password = self.cleaned_data.get('new_password1')

        if not password:
            raise forms.ValidationError("Password is required.")

        if len(password) < 8:
            raise forms.ValidationError("Password must be at least 8 characters long.")

        if not re.search(r'[A-Z]', password):
            raise forms.ValidationError("Password must contain at least one uppercase letter.")

        if not re.search(r'[a-z]', password):
            raise forms.ValidationError("Password must contain at least one lowercase letter.")

        if not re.search(r'[0-9]', password):
            raise forms.ValidationError("Password must contain at least one number.")

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise forms.ValidationError("Password must contain at least one special character.")

        # Check for common weak passwords
        weak_passwords = ['password', '12345678', 'qwerty123', 'abc123456', 'password123']
        if password.lower() in weak_passwords:
            raise forms.ValidationError("This password is too common. Please choose a stronger password.")

        return password
