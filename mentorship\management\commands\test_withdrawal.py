from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from mentorship.models import WithdrawalRequest, MentorProfile
from decimal import Decimal

User = get_user_model()

class Command(BaseCommand):
    help = 'Test withdrawal system functionality'

    def handle(self, *args, **options):
        self.stdout.write('Testing withdrawal system...')
        
        try:
            # Test if WithdrawalRequest model exists
            count = WithdrawalRequest.objects.count()
            self.stdout.write(f'✓ WithdrawalRequest model accessible. Current count: {count}')
            
            # Test if we can create a withdrawal request
            # First, find a mentor user
            mentors = User.objects.filter(mentor_profile__isnull=False)[:1]
            
            if mentors.exists():
                mentor = mentors.first()
                self.stdout.write(f'✓ Found mentor: {mentor.username}')
                
                # Check mentor profile
                mentor_profile = mentor.mentor_profile
                self.stdout.write(f'✓ Mentor profile accessible. Total earnings: ${mentor_profile.total_earnings}')
                self.stdout.write(f'✓ Withdrawn earnings: ${mentor_profile.withdrawn_earnings}')
                self.stdout.write(f'✓ Available balance: ${mentor_profile.get_available_balance()}')
                
                # Test creating a withdrawal request (but don't save it)
                test_withdrawal = WithdrawalRequest(
                    mentor=mentor,
                    amount=Decimal('50.00')
                )
                self.stdout.write('✓ Can create WithdrawalRequest instance')
                
            else:
                self.stdout.write('⚠ No mentors found in database')
                
        except Exception as e:
            self.stdout.write(f'✗ Error: {str(e)}')
            self.stdout.write('This likely means the migration hasn\'t been applied yet.')
            
        self.stdout.write('Test completed.')
