/**
 * Terminal Instance - Real-time interactive terminal
 * 
 * This class provides a real-time interactive terminal that connects
 * to a WebSocket backend for full-duplex communication.
 */
class TerminalInstance {
  /**
   * Create a new terminal instance
   * @param {string} containerId - The DOM element ID where the terminal will be mounted
   * @param {string} projectId - The project ID for this terminal
   * @param {Object} options - Configuration options
   */
  constructor(containerId, projectId, options = {}) {
    this.containerId = containerId;
    this.projectId = projectId;
    this.options = Object.assign({
      initialCommands: true,
      theme: {
        background: '#1e1e1e',
        foreground: '#ffffff',
        cursor: '#ffffff',
        selection: 'rgba(255, 255, 255, 0.3)',
        black: '#000000',
        red: '#e06c75',
        green: '#98c379',
        yellow: '#e5c07b',
        blue: '#61afef',
        magenta: '#c678dd',
        cyan: '#56b6c2',
        white: '#dcdfe4'
      },
      fontSize: 14,
      fontFamily: 'Consolas, "Courier New", monospace',
      cursorBlink: true,
      scrollback: 1000
    }, options);
    
    this.isActive = false;
    this.isInitialized = false;
    this.fitAddon = null;
    
    // Initialize immediately if the container exists
    const container = document.getElementById(this.containerId);
    if (container) {
      this.initialize();
    }
  }
  
  /**
   * Initialize the terminal and WebSocket connection
   */
  initialize() {
    if (this.isInitialized) return;
    
    const container = document.getElementById(this.containerId);
    if (!container) {
      console.error(`Terminal container #${this.containerId} not found`);
      return;
    }
    
    // Create the xterm.js terminal
    this.term = new Terminal({
      theme: this.options.theme,
      fontSize: this.options.fontSize,
      fontFamily: this.options.fontFamily,
      cursorBlink: this.options.cursorBlink,
      scrollback: this.options.scrollback,
      convertEol: true
    });
    
    // Create the fit addon to resize terminal to container
    this.fitAddon = new FitAddon.FitAddon();
    this.term.loadAddon(this.fitAddon);
    
    // Open the terminal in the container
    this.term.open(container);
    
    // Connect to WebSocket
    this.connectWebSocket();
    
    // Add window resize handler
    this.resizeHandler = () => this.resize();
    window.addEventListener('resize', this.resizeHandler);
    
    // Apply initial resize
    this.resize();
    
    // Mark as initialized
    this.isInitialized = true;
  }
  
  /**
   * Connect to the WebSocket backend
   */
  connectWebSocket() {
    // Construct WebSocket URL based on current location
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const terminalId = `${this.projectId}-${this.containerId}`;
    const wsUrl = `${protocol}//${host}/ws/terminal/${terminalId}`;
    
    // Close existing WebSocket if any
    if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
      this.socket.close();
    }
    
    // Create new WebSocket connection
    this.socket = new WebSocket(wsUrl);
    
    // Handle WebSocket events
    this.socket.onopen = () => {
      this.term.writeln('\r\n\x1b[32mConnected to terminal.\x1b[0m\r\n');
      
      // Register terminal data event handler
      this.term.onData(data => {
        if (this.socket.readyState === WebSocket.OPEN) {
          // Send data as JSON with type indicator
          this.socket.send(JSON.stringify({
            type: 'input',
            data: data
          }));
        }
      });
      
      // Run initial commands if enabled
      if (this.options.initialCommands) {
        setTimeout(() => {
          this.term.writeln('Running initial commands...');
          this.executeCommand('pwd');
          this.executeCommand('ls -la');
        }, 500);
      }
    };
    
    // Handle messages from server
    this.socket.onmessage = (event) => {
      try {
        // Try to parse as JSON first (new protocol)
        const message = JSON.parse(event.data);
        if (message.type === 'output') {
          this.term.write(message.data);
        }
      } catch (e) {
        // Fall back to raw data handling (old protocol)
        this.term.write(event.data);
      }
    };
    
    // Handle WebSocket errors
    this.socket.onerror = (error) => {
      console.error('WebSocket Error:', error);
      this.term.writeln('\r\n\x1b[31mError: WebSocket connection failed\x1b[0m\r\n');
      this.term.writeln('Please make sure the terminal server is running.');
    };
    
    // Handle WebSocket close
    this.socket.onclose = () => {
      this.term.writeln('\r\n\x1b[33mTerminal connection closed.\x1b[0m\r\n');
      
      // Attempt to reconnect after a delay
      setTimeout(() => {
        if (this.isActive) {
          this.term.writeln('\r\nAttempting to reconnect...\r\n');
          this.connectWebSocket();
        }
      }, 3000);
    };
  }
  
  /**
   * Activate this terminal instance
   */
  activate() {
    this.isActive = true;
    if (!this.isInitialized) {
      this.initialize();
    } else {
      this.resize();
      this.term.focus();
    }
  }
  
  /**
   * Deactivate this terminal instance
   */
  deactivate() {
    this.isActive = false;
  }
  
  /**
   * Resize the terminal to fit its container
   */
  resize() {
    if (this.isInitialized && this.fitAddon) {
      try {
        this.fitAddon.fit();
        
        // Send terminal resize info to server
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
          const { cols, rows } = this.term;
          this.socket.send(JSON.stringify({
            type: 'resize',
            data: { cols, rows }
          }));
        }
      } catch (e) {
        console.warn('Error fitting terminal to container:', e);
      }
    }
  }
  
  /**
   * Clear the terminal content
   */
  clear() {
    if (this.isInitialized) {
      this.term.clear();
    }
  }
  
  /**
   * Close and dispose the terminal instance
   */
  close() {
    // Remove resize event listener
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler);
    }
    
    // Close WebSocket connection
    if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
      this.socket.close();
    }
    
    // Dispose terminal instance
    if (this.term) {
      try {
        this.term.dispose();
      } catch (e) {
        console.warn('Error disposing terminal:', e);
      }
    }
    
    this.isInitialized = false;
    this.isActive = false;
  }
  
  /**
   * Execute a command in the terminal
   * @param {string} command - The command to execute
   */
  executeCommand(command) {
    if (this.isInitialized && this.socket && this.socket.readyState === WebSocket.OPEN) {
      // Send command as JSON with type indicator
      this.socket.send(JSON.stringify({
        type: 'input',
        data: command + '\r'
      }));
    }
  }
}