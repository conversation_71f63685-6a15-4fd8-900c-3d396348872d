# Generated manually for payment system
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('mentorship', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='mentorshipsession',
            name='payment_status',
            field=models.CharField(
                choices=[
                    ('pending', 'Pending'),
                    ('processing', 'Processing'),
                    ('succeeded', 'Succeeded'),
                    ('failed', 'Failed'),
                    ('canceled', 'Canceled'),
                    ('refunded', 'Refunded'),
                ],
                default='pending',
                help_text='Current payment status',
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='stripe_session_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='payment_method_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='mentorshipsession',
            name='refund_id',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
    ]
