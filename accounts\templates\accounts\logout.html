<!-- filepath: d:\Project_Main\django-template\accounts\templates\accounts\logout.html -->
{% extends 'base.html' %}
{% load static %}
{% block content %}

<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="account-page">
    <div class="account-card fade-in">
        <h2>Logout</h2>

        <div class="logout-content">
            <div class="logout-icon">
                <i class="fas fa-sign-out-alt"></i>
            </div>

            <p>Are you sure you want to logout from your account?</p>

            <form method="post" class="logout-actions">
                {% csrf_token %}
                <button type="submit" class="account-btn logout-btn">Yes, logout</button>
                <a href="{% url 'home_view' %}" class="account-btn cancel-btn">Cancel</a>
            </form>
        </div>
    </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 80,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
        },
        "shape": {
          "type": ["circle", "triangle"],
          "stroke": {
            "width": 0,
            "color": "#000000"
          }
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
});
</script>

<style>
/* Logout page specific styles */
.logout-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px 0;
}

.logout-icon {
    font-size: 60px;
    color: #f44336;
    margin-bottom: 20px;
    animation: shake 2s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% {
        transform: rotate(0deg);
    }
    25% {
        transform: rotate(-5deg);
    }
    75% {
        transform: rotate(5deg);
    }
}

.logout-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.logout-btn {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: #f44336;
    color: #f44336;
}

.logout-btn:hover {
    background-color: #f44336;
    color: white;
}

.cancel-btn {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

.cancel-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}
</style>
{% endblock %}