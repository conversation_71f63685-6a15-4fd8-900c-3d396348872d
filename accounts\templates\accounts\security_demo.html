{% extends "base.html" %}
{% load security_tags %}

{% block title %}Security Demo - ForgeX{% endblock %}

{% block extra_css %}
<style>
.security-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.demo-section {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.demo-section h3 {
  color: #495057;
  margin-top: 0;
  border-bottom: 2px solid #007acc;
  padding-bottom: 10px;
}

.security-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  text-align: center;
}

.status-card.active {
  border-color: #28a745;
  background: #f8fff9;
}

.status-card.inactive {
  border-color: #dc3545;
  background: #fff8f8;
}

.status-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.status-card.active .status-icon {
  color: #28a745;
}

.status-card.inactive .status-icon {
  color: #dc3545;
}

.demo-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.demo-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.demo-btn.primary {
  background: #007acc;
  color: white;
}

.demo-btn.secondary {
  background: #6c757d;
  color: white;
}

.demo-btn.danger {
  background: #dc3545;
  color: white;
}

.demo-btn:hover {
  opacity: 0.8;
  transform: translateY(-1px);
}

.demo-output {
  background: #1a1a1a;
  color: #00ff00;
  font-family: 'Courier New', monospace;
  padding: 15px;
  border-radius: 4px;
  min-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
}

.user-info {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.security-warning {
  background: #fff3cd;
  border: 1px solid #ffc107;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.api-demo {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.api-request, .api-response {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
}

@media (max-width: 768px) {
  .api-demo {
    grid-template-columns: 1fr;
  }
  
  .demo-controls {
    flex-direction: column;
  }
  
  .demo-btn {
    width: 100%;
  }
}
</style>
{% endblock %}

{% block content %}
<div class="security-demo">
  <h1>🔒 ForgeX Security System Demo</h1>
  
  <!-- User Information -->
  <div class="user-info">
    <h4>👤 Current User Information</h4>
    <p><strong>Username:</strong> {{ user.username|default:"Anonymous" }}</p>
    <p><strong>Role:</strong> <span id="user-role">Loading...</span></p>
    <p><strong>Debug Access:</strong> <span id="debug-access">Loading...</span></p>
    <p><strong>Security Level:</strong> <span id="security-level">Loading...</span></p>
  </div>
  
  <!-- Security Status -->
  <div class="demo-section">
    <h3>🛡️ Security Status</h3>
    <div class="security-status">
      <div class="status-card" id="obfuscation-status">
        <div class="status-icon">🔐</div>
        <h5>Code Obfuscation</h5>
        <p id="obfuscation-text">Checking...</p>
      </div>
      
      <div class="status-card" id="encryption-status">
        <div class="status-icon">🔒</div>
        <h5>API Encryption</h5>
        <p id="encryption-text">Checking...</p>
      </div>
      
      <div class="status-card" id="devtools-status">
        <div class="status-icon">🚫</div>
        <h5>DevTools Protection</h5>
        <p id="devtools-text">Checking...</p>
      </div>
      
      <div class="status-card" id="console-status">
        <div class="status-icon">🔇</div>
        <h5>Console Protection</h5>
        <p id="console-text">Checking...</p>
      </div>
    </div>
  </div>
  
  <!-- Security Tests -->
  <div class="demo-section">
    <h3>🧪 Security Tests</h3>
    
    {% if user.is_authenticated and user.is_superuser %}
    <div class="security-warning">
      <strong>⚠️ Admin Notice:</strong> You have admin privileges, so some security measures are relaxed for debugging purposes.
    </div>
    {% endif %}
    
    <div class="demo-controls">
      <button class="demo-btn primary" onclick="testConsoleProtection()">
        Test Console Protection
      </button>
      <button class="demo-btn primary" onclick="testAPIEncryption()">
        Test API Encryption
      </button>
      <button class="demo-btn primary" onclick="testRateLimiting()">
        Test Rate Limiting
      </button>
      <button class="demo-btn secondary" onclick="testDevToolsDetection()">
        Test DevTools Detection
      </button>
      <button class="demo-btn danger" onclick="simulateSecurityThreat()">
        Simulate Security Threat
      </button>
    </div>
    
    <div class="demo-output" id="test-output">
Security test output will appear here...
    </div>
  </div>
  
  <!-- API Demo -->
  <div class="demo-section">
    <h3>🔗 API Security Demo</h3>
    <div class="api-demo">
      <div class="api-request">
        <h5>📤 Request (Your View)</h5>
        <div class="code-block" id="api-request-view">
// Making a secure API call
const response = await SecureAPIClient.post('user_profile', {
  name: 'John Doe',
  email: '<EMAIL>'
});
        </div>
      </div>
      
      <div class="api-response">
        <h5>📥 Network Traffic (Encrypted)</h5>
        <div class="code-block" id="api-network-view">
POST /api/x7f9a2b8/
Content-Type: application/json
X-Encrypted: true

{
  "encrypted_data": "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y=",
  "timestamp": 1640995200000
}
        </div>
      </div>
    </div>
    
    <button class="demo-btn primary" onclick="makeSecureAPICall()" style="margin-top: 15px;">
      Make Secure API Call
    </button>
  </div>
  
  <!-- Code Obfuscation Demo -->
  <div class="demo-section">
    <h3>🔐 Code Obfuscation Demo</h3>
    <p>Compare how the same code appears to different user types:</p>
    
    <div class="api-demo">
      <div>
        <h5>👑 Admin/Debug View</h5>
        <div class="code-block">
function authenticateUser(username, password) {
  const hashedPassword = hashPassword(password);
  return validateCredentials(username, hashedPassword);
}
        </div>
      </div>
      
      <div>
        <h5>👤 Regular User View</h5>
        <div class="code-block">
var _0x1a2b=['authenticate','hash','validate'];
function _0x3c4d(_0x5e6f,_0x7g8h){
  var _0x9i0j=_0x1a2b[1](_0x7g8h);
  return _0x1a2b[2](_0x5e6f,_0x9i0j);
}
        </div>
      </div>
    </div>
  </div>
  
  <!-- Security Monitoring -->
  {% if user.is_superuser %}
  <div class="demo-section">
    <h3>📊 Security Monitoring (Admin Only)</h3>
    <p>As an admin, you can monitor security events in real-time:</p>
    
    <div class="demo-controls">
      <button class="demo-btn primary" onclick="viewSecurityLogs()">
        View Security Logs
      </button>
      <button class="demo-btn secondary" onclick="exportSecurityData()">
        Export Security Data
      </button>
      <button class="demo-btn secondary" onclick="openSecuritySettings()">
        Security Settings
      </button>
    </div>
    
    <div id="security-monitoring-output" class="demo-output">
Security monitoring data will appear here...
    </div>
  </div>
  {% endif %}
</div>

<!-- Include security system -->
{% load_security_bundle %}

<script>
// Demo JavaScript functions
let testOutput = null;

document.addEventListener('DOMContentLoaded', function() {
  testOutput = document.getElementById('test-output');
  initializeSecurityDemo();
});

async function initializeSecurityDemo() {
  log('🚀 Initializing Security Demo...');
  
  // Wait for security system to load
  if (typeof window.ForgeXSecurity !== 'undefined') {
    await updateUserInfo();
    await updateSecurityStatus();
    log('✅ Security Demo initialized successfully');
  } else {
    log('⚠️ Security system not loaded, using fallback mode');
    updateFallbackStatus();
  }
}

async function updateUserInfo() {
  try {
    const response = await fetch('/accounts/api/user-role/');
    const data = await response.json();
    
    document.getElementById('user-role').textContent = data.role;
    document.getElementById('debug-access').textContent = data.debug_enabled ? 'Yes' : 'No';
    document.getElementById('security-level').textContent = data.security_level;
    
    log(`👤 User role: ${data.role}, Debug: ${data.debug_enabled}`);
  } catch (error) {
    log('❌ Failed to load user information');
  }
}

async function updateSecurityStatus() {
  const security = window.ForgeXSecurity;
  
  // Obfuscation status
  const obfuscationActive = security.isSecureMode();
  updateStatusCard('obfuscation', obfuscationActive, 
    obfuscationActive ? 'Active' : 'Disabled (Debug Mode)');
  
  // Encryption status
  const encryptionActive = security.isSecureMode();
  updateStatusCard('encryption', encryptionActive,
    encryptionActive ? 'Active' : 'Disabled (Debug Mode)');
  
  // DevTools protection
  const devtoolsActive = !security.canShowDebugInfo();
  updateStatusCard('devtools', devtoolsActive,
    devtoolsActive ? 'Protected' : 'Allowed (Admin)');
  
  // Console protection
  const consoleActive = !security.canShowDebugInfo();
  updateStatusCard('console', consoleActive,
    consoleActive ? 'Protected' : 'Allowed (Admin)');
}

function updateFallbackStatus() {
  updateStatusCard('obfuscation', false, 'Not Available');
  updateStatusCard('encryption', false, 'Not Available');
  updateStatusCard('devtools', false, 'Not Available');
  updateStatusCard('console', false, 'Not Available');
}

function updateStatusCard(type, active, text) {
  const card = document.getElementById(`${type}-status`);
  const textElement = document.getElementById(`${type}-text`);
  
  card.className = `status-card ${active ? 'active' : 'inactive'}`;
  textElement.textContent = text;
}

function testConsoleProtection() {
  log('🧪 Testing Console Protection...');
  
  try {
    console.log('This is a test console message');
    console.warn('This is a test warning');
    console.error('This is a test error');
    
    if (window.ForgeXSecurity && window.ForgeXSecurity.canShowDebugInfo()) {
      log('✅ Console access allowed (Admin/Debug mode)');
    } else {
      log('🔒 Console protection active - messages should be hidden');
    }
  } catch (error) {
    log('❌ Console test failed: ' + error.message);
  }
}

async function testAPIEncryption() {
  log('🧪 Testing API Encryption...');
  
  try {
    if (window.SecureAPIClient) {
      const testData = { test: 'encryption', timestamp: Date.now() };
      log('📤 Sending encrypted request...');
      
      // This would normally make an encrypted API call
      log('🔒 Request encrypted and sent');
      log('📥 Response received and decrypted');
      log('✅ API encryption test completed');
    } else {
      log('⚠️ SecureAPIClient not available');
    }
  } catch (error) {
    log('❌ API encryption test failed: ' + error.message);
  }
}

function testRateLimiting() {
  log('🧪 Testing Rate Limiting...');
  
  // Simulate multiple rapid requests
  let requestCount = 0;
  const maxRequests = 10;
  
  const interval = setInterval(() => {
    requestCount++;
    log(`📡 Request ${requestCount}/${maxRequests}`);
    
    if (requestCount >= maxRequests) {
      clearInterval(interval);
      log('🚫 Rate limiting should kick in after multiple requests');
      log('✅ Rate limiting test completed');
    }
  }, 100);
}

function testDevToolsDetection() {
  log('🧪 Testing DevTools Detection...');
  log('👀 Try opening browser developer tools (F12)');
  log('🚫 If protection is active, you should see a warning');
  
  if (window.ForgeXSecurity && window.ForgeXSecurity.canShowDebugInfo()) {
    log('✅ DevTools allowed for admin users');
  } else {
    log('🔒 DevTools protection should be active');
  }
}

function simulateSecurityThreat() {
  log('🚨 Simulating Security Threat...');
  
  if (window.ForgeXSecurity) {
    window.ForgeXSecurity.logSecurityEvent('Demo security threat simulation');
    log('⚠️ Security threat logged');
    log('📊 Check security logs for the event');
  } else {
    log('⚠️ Security system not available for threat simulation');
  }
}

async function makeSecureAPICall() {
  log('🔗 Making secure API call...');
  
  try {
    // Simulate API call
    const requestData = {
      action: 'demo_request',
      timestamp: Date.now(),
      user: '{{ user.username|default:"anonymous" }}'
    };
    
    log('📤 Original request: ' + JSON.stringify(requestData, null, 2));
    
    // Show encrypted version
    setTimeout(() => {
      log('🔒 Encrypted request sent to server');
      log('📥 Encrypted response received');
      log('🔓 Response decrypted successfully');
      log('✅ Secure API call completed');
    }, 1000);
    
  } catch (error) {
    log('❌ Secure API call failed: ' + error.message);
  }
}

{% if user.is_superuser %}
async function viewSecurityLogs() {
  const output = document.getElementById('security-monitoring-output');
  output.textContent = '📊 Loading security logs...\n';
  
  try {
    const response = await fetch('/accounts/api/security-dashboard/');
    const data = await response.json();
    
    output.textContent = `📊 Security Dashboard Data:
    
📈 Statistics:
- Total events this week: ${data.stats.total_events_week}
- Unique users: ${data.stats.unique_users_week}
- Threat events: ${data.stats.threat_events_week}
- DevTools detections: ${data.stats.devtools_detections_week}

📋 Recent Events:
${data.recent_events.map(event => 
  `- ${event.timestamp}: ${event.user} - ${event.event_type}`
).join('\n')}`;
    
  } catch (error) {
    output.textContent = '❌ Failed to load security logs: ' + error.message;
  }
}

function exportSecurityData() {
  const output = document.getElementById('security-monitoring-output');
  output.textContent = '📥 Exporting security data...\n';
  
  // Simulate export
  setTimeout(() => {
    output.textContent += '✅ Security data exported successfully\n';
    output.textContent += '📁 File: security_logs_' + new Date().toISOString().split('T')[0] + '.csv\n';
  }, 1000);
}

function openSecuritySettings() {
  const output = document.getElementById('security-monitoring-output');
  output.textContent = '⚙️ Opening security settings...\n';
  output.textContent += '🔧 Security settings panel would open here\n';
  output.textContent += '📝 Admins can modify security parameters\n';
}
{% endif %}

function log(message) {
  if (testOutput) {
    const timestamp = new Date().toLocaleTimeString();
    testOutput.textContent += `[${timestamp}] ${message}\n`;
    testOutput.scrollTop = testOutput.scrollHeight;
  }
}

// Initialize demo when security system is ready
if (window.ForgeXSecurity) {
  window.addEventListener('forgex:initialized', initializeSecurityDemo);
}
</script>
{% endblock %}
