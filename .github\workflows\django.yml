name: Django CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      max-parallel: 4
      matrix:
        python-version: [3.7, 3.8, 3.9]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Install Docker Compose
      run: |
        sudo apt-get update
        sudo apt-get install docker-compose -y

    - name: Set environment variables
      run: |
        echo "DJANGO_SECRET_KEY=github_ci_secret" >> $GITHUB_ENV
        echo "DEBUG=0" >> $GITHUB_ENV
        echo "POSTGRES_DB=test_db" >> $GITHUB_ENV
        echo "POSTGRES_USER=postgres" >> $GITHUB_ENV
        echo "POSTGRES_PASSWORD=postgres" >> $GITHUB_ENV
        echo "DATABASE_URL=************************************/test_db" >> $GITHUB_ENV

    - name: Build and start Docker containers
      run: |
        docker-compose up --build -d

    - name: Wait for PostgreSQL (from Compose) to be ready
      run: |
        echo "Waiting for postgres container..."
        until docker exec $(docker ps -qf "name=db") pg_isready -U postgres; do
          sleep 2
        done
        echo "PostgreSQL is ready."

    - name: Run Django migrations inside API container
      run: |
        docker exec $(docker ps -qf "name=api") python manage.py migrate

    - name: Run Django tests inside API container
      run: |
        docker exec $(docker ps -qf "name=api") python manage.py test

    - name: Stop and clean up containers
      if: always()
      run: docker-compose down --volumes
