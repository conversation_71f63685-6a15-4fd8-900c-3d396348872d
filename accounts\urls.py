from django.urls import path
from . import views, api_views
from . import security_views
from . import threat_management_views as threat_views

app_name = 'accounts'

urlpatterns = [
    path('signup/', views.signup, name='signup'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('profile/', views.profile, name='profile'),
    path('profile-setup/', views.profile_setup_view, name='profile_setup'),
    path('verify-email/<uidb64>/<token>/', views.verify_email, name='verify_email'),
    path('resend-verification-email/', views.resend_verification_email, name='resend_verification_email'),
    path('verification-pending/', views.verification_pending, name='verification_pending'),
    path('check-verification-status/', views.check_verification_status, name='check_verification_status'),

    # Password Reset URLs
    path('password-reset/', views.password_reset_request, name='password_reset'),
    path('password-reset/done/', views.password_reset_done, name='password_reset_done'),
    path('password-reset-confirm/<uidb64>/<token>/', views.password_reset_confirm, name='password_reset_confirm'),
    path('test-service-communication/', views.test_service_communication, name='test_service_communication'),
    path('notifications/', views.notification_list, name='notification_list'),
    path('notification/read/<int:notification_id>/', views.mark_notification_read, name='mark_notification_read'),
    path('notifications/unread_count/', views.unread_notification_count, name='unread_notification_count'),
    path('notifications/mark_all_read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('notifications/respond/<int:invite_id>/', views.respond_invite, name='respond_invite'),
    path('contact/', views.contact_view, name='contact'),
    path('documentation/', views.documentation_view, name='documentation'),
    path('forms/', views.forms_view, name='forms'),
    path('status/', views.status_view, name='status'),
    path('events/', views.events_view, name='events'),
    path('events/create/', views.create_event_view, name='create_event'),
    path('events/<int:event_id>/edit/', views.edit_event_view, name='edit_event'),
    path('events/<int:event_id>/delete/', views.delete_event_view, name='delete_event'),

    # Support/Help Center URLs
    path('help-center/', views.help_center_view, name='help_center'),
    path('support/create/', views.create_support_ticket, name='create_support_ticket'),
    path('support/ticket/<str:ticket_id>/', views.support_ticket_detail, name='support_ticket_detail'),
    path('support/my-tickets/', views.my_support_tickets, name='my_support_tickets'),
    path('support/admin/', views.support_admin_dashboard, name='support_admin_dashboard'),
    path('support/ticket/<str:ticket_id>/update/', views.support_ticket_update, name='support_ticket_update'),
    path('support/categories/', views.support_categories_manage, name='support_categories_manage'),
    path('support/create-default-categories/', views.create_default_categories, name='create_default_categories'),

    # Social Account Management
    path('social-accounts/', views.social_accounts_management, name='social_accounts_management'),
    path('social-accounts/disconnect/<int:account_id>/', views.disconnect_social_account, name='disconnect_social_account'),
    path('account-security/', views.account_security, name='account_security'),

    # Debug URLs (for development)
    path('session-debug/', views.session_debug, name='session_debug'),
    path('security-demo/', views.security_demo, name='security_demo'),

    # Security Management URLs
    path('security-logs/', security_views.security_logs_view, name='security_logs'),
    path('test-security/', security_views.test_security_page, name='test_security'),

    # Threat Management URLs (Admin Only)
    path('threat-management/', threat_views.threat_management_dashboard, name='threat_management_dashboard'),
    path('threat-management/events/', threat_views.security_events_list, name='security_events_list'),
    path('threat-management/ip-blocks/', threat_views.ip_blocks_list, name='ip_blocks_list'),
    path('threat-management/threat-levels/', threat_views.user_threat_levels, name='user_threat_levels'),
    path('threat-management/user/<int:user_id>/', threat_views.user_security_profile, name='user_security_profile'),

    # User Warning System
    path('warnings/', threat_views.user_warnings, name='user_warnings'),
    path('warnings/acknowledge/<int:warning_id>/', threat_views.acknowledge_warning, name='acknowledge_warning'),

    # Security API URLs
    path('api/user-role/', security_views.get_user_role, name='api_user_role'),
    path('api/security-log/', security_views.log_security_event, name='api_security_log'),
    path('api/security-dashboard/', security_views.security_dashboard, name='api_security_dashboard'),
    path('api/security-settings/', security_views.get_security_settings, name='api_get_security_settings'),
    path('api/security-settings/update/', security_views.update_security_settings, name='api_update_security_settings'),
    path('api/generate-token/', security_views.generate_secure_token, name='api_generate_token'),
    path('api/validate-token/', security_views.validate_secure_token, name='api_validate_token'),
    path('api/export-security-logs/', security_views.export_security_logs, name='api_export_security_logs'),

    # Threat Management API URLs
    path('api/send-warning/', threat_views.send_warning, name='api_send_warning'),
    path('api/block-ip/', threat_views.block_ip, name='api_block_ip'),
    path('api/unblock-ip/<int:block_id>/', threat_views.unblock_ip, name='api_unblock_ip'),
    path('api/update-threat-level/', threat_views.update_threat_level, name='api_update_threat_level'),
    path('api/suspend-user/', threat_views.suspend_user, name='api_suspend_user'),
    path('api/pending-warnings/', threat_views.get_pending_warnings, name='api_pending_warnings'),
    path('api/mark-warning-delivered/<int:warning_id>/', threat_views.mark_warning_delivered, name='api_mark_warning_delivered'),
]