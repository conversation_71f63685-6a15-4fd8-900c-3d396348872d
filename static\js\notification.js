// Enhanced WebSocket Notification Handler
document.addEventListener('DOMContentLoaded', function() {
    // Only connect if user is authenticated
    if (document.body.dataset.userId) {
        connectNotificationSocket();

        // Initialize notification sounds
        initNotificationSounds();
    }
});

// Global socket reference
let notificationSocket = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 3000; // 3 seconds

// Sound effects for notifications
let notificationSounds = {
    info: null,
    success: null,
    warning: null,
    error: null,
    system: null
};

function initNotificationSounds() {
    // Create audio objects for different notification types
    notificationSounds.info = new Audio('/static/sounds/notification.mp3');
    notificationSounds.success = new Audio('/static/sounds/success.mp3');
    notificationSounds.warning = new Audio('/static/sounds/warning.mp3');
    notificationSounds.error = new Audio('/static/sounds/error.mp3');
    notificationSounds.system = new Audio('/static/sounds/system.mp3');

    // Preload sounds
    Object.values(notificationSounds).forEach(sound => {
        if (sound) {
            sound.load();
            sound.volume = 0.5; // Set volume to 50%
        }
    });
}

function connectNotificationSocket() {
    // Close existing socket if any
    if (notificationSocket && notificationSocket.readyState !== WebSocket.CLOSED) {
        notificationSocket.close();
    }

    // Use centralized WebSocket configuration if available, otherwise fallback to old method
    let websocketUrl;
    if (window.WEBSOCKET_CONFIG && window.WEBSOCKET_CONFIG.notificationsUrl) {
        websocketUrl = window.WEBSOCKET_CONFIG.notificationsUrl;
    } else {
        // Fallback to old method for backward compatibility
        const protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
        websocketUrl = protocol + window.location.hostname + ':8001/ws/notifications/';
    }

    notificationSocket = new WebSocket(websocketUrl);

    // Handle connection open
    notificationSocket.onopen = function(e) {
        console.log('Notification WebSocket connection established');
        // Reset reconnect attempts on successful connection
        reconnectAttempts = 0;

        // Show connection status
        showConnectionStatus(true);
    };

    // Handle incoming messages
    notificationSocket.onmessage = function(e) {
        try {
            const data = JSON.parse(e.data);

            // Handle different message types
            if (data.type === 'notification' || data.type === 'system_notification') {
                showNotification(
                    data.title,
                    data.message,
                    data.notification_type || 'info',
                    data.notification_id
                );
            } else if (data.type === 'connection_established') {
                console.log('Notification service connected:', data.message);
            } else if (data.type === 'ack') {
                console.log('Notification acknowledged:', data.notification_id);
            }
        } catch (error) {
            console.error('Error processing notification:', error);
        }
    };

    // Handle errors
    notificationSocket.onerror = function(e) {
        console.error('WebSocket error:', e);
        showConnectionStatus(false);
    };

    // Handle connection close
    notificationSocket.onclose = function(e) {
        console.log('Notification WebSocket connection closed');
        showConnectionStatus(false);

        // Try to reconnect with exponential backoff
        if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            const delay = RECONNECT_DELAY * Math.pow(1.5, reconnectAttempts);
            reconnectAttempts++;

            console.log(`Reconnecting in ${delay/1000} seconds... (Attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

            setTimeout(function() {
                connectNotificationSocket();
            }, delay);
        } else {
            console.error('Maximum reconnection attempts reached. Please refresh the page.');
        }
    };
}

function showConnectionStatus(connected) {
    // Optional: Show connection status indicator
    const statusIndicator = document.getElementById('notification-status');
    if (statusIndicator) {
        if (connected) {
            statusIndicator.classList.remove('bg-danger');
            statusIndicator.classList.add('bg-success');
            statusIndicator.title = 'Notification service connected';
        } else {
            statusIndicator.classList.remove('bg-success');
            statusIndicator.classList.add('bg-danger');
            statusIndicator.title = 'Notification service disconnected';
        }
    }
}

function showNotification(title, message, type = 'info', notificationId = null) {
    // Play notification sound based on type
    playNotificationSound(type);

    // Create notification element with enhanced styling
    const notificationContainer = document.createElement('div');
    notificationContainer.className = 'notification-toast show';
    notificationContainer.dataset.notificationId = notificationId;

    // Set color based on notification type
    const colorClass = getNotificationColorClass(type);

    // Get icon based on notification type
    const icon = getNotificationIcon(type);

    notificationContainer.innerHTML = `
        <div class="notification-content ${colorClass}">
            <div class="notification-header">
                <div class="notification-icon">${icon}</div>
                <div class="notification-title">${title}</div>
                <button type="button" class="notification-close" aria-label="Close">&times;</button>
            </div>
            <div class="notification-body">
                ${message}
            </div>
            <div class="notification-progress"></div>
        </div>
    `;

    // Add notification to the notification container
    const notificationsContainer = document.getElementById('notifications-container');
    if (!notificationsContainer) {
        // Create container if it doesn't exist
        const container = document.createElement('div');
        container.id = 'notifications-container';
        container.className = 'notifications-container';
        document.body.appendChild(container);
        container.appendChild(notificationContainer);
    } else {
        notificationsContainer.appendChild(notificationContainer);
    }

    // Add animation with enhanced entrance effect
    setTimeout(() => {
        notificationContainer.classList.add('notification-show');

        // Start progress bar animation after entrance animation
        setTimeout(() => {
            const progressBar = notificationContainer.querySelector('.notification-progress');
            if (progressBar) {
                progressBar.style.animation = 'notification-progress 5s linear forwards';
            }
        }, 100);
    }, 10);

    // Remove after 5 seconds
    const removeTimeout = setTimeout(() => {
        removeNotification(notificationContainer);
    }, 5000);

    // Add click handler to close button
    const closeButton = notificationContainer.querySelector('.notification-close');
    if (closeButton) {
        closeButton.addEventListener('click', () => {
            clearTimeout(removeTimeout);
            removeNotification(notificationContainer);
        });
    }

    // Mark as read when clicked
    notificationContainer.addEventListener('click', (e) => {
        if (!e.target.classList.contains('notification-close')) {
            markNotificationAsRead(notificationId);
        }
    });

    // Also update notification counter
    updateNotificationCounter();

    return notificationContainer;
}

function removeNotification(notificationElement) {
    notificationElement.classList.remove('notification-show');
    notificationElement.classList.add('notification-hide');

    // Remove from DOM after animation completes (increased timeout for enhanced animations)
    setTimeout(() => {
        if (notificationElement.parentNode) {
            notificationElement.parentNode.removeChild(notificationElement);
        }
    }, 500);
}

function markNotificationAsRead(notificationId) {
    if (!notificationId || !notificationSocket) return;

    // Send message to server to mark notification as read
    if (notificationSocket.readyState === WebSocket.OPEN) {
        notificationSocket.send(JSON.stringify({
            type: 'mark_read',
            notification_id: notificationId
        }));
    }
}

function getNotificationColorClass(type) {
    const colorMap = {
        'info': 'notification-info',
        'success': 'notification-success',
        'warning': 'notification-warning',
        'error': 'notification-error',
        'system': 'notification-system',
        'invitation': 'notification-invitation'
    };
    return colorMap[type] || 'notification-info';
}

function getNotificationIcon(type) {
    const iconMap = {
        'info': '<i class="fas fa-info-circle"></i>',
        'success': '<i class="fas fa-check-circle"></i>',
        'warning': '<i class="fas fa-exclamation-triangle"></i>',
        'error': '<i class="fas fa-times-circle"></i>',
        'system': '<i class="fas fa-bullhorn"></i>',
        'invitation': '<i class="fas fa-user-plus"></i>'
    };
    return iconMap[type] || iconMap['info'];
}

function playNotificationSound(type) {
    // Play sound based on notification type
    const sound = notificationSounds[type] || notificationSounds.info;
    if (sound) {
        // Reset sound to beginning and play
        sound.currentTime = 0;
        sound.play().catch(e => {
            // Browser may block autoplay, just log the error
            console.log('Could not play notification sound:', e);
        });
    }
}

function updateNotificationCounter() {
    const counter = document.getElementById('notification-counter');
    if (counter) {
        // Fetch the current count from the server
        fetch('/accounts/unread-count/')
            .then(response => response.json())
            .then(data => {
                counter.textContent = data.count;

                // Show/hide based on count
                if (data.count > 0) {
                    counter.style.display = 'inline-block';
                } else {
                    counter.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error updating notification count:', error);
            });
    }
}