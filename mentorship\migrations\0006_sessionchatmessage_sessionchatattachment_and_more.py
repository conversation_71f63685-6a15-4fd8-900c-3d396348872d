# Generated by Django 5.2.1 on 2025-05-30 06:41

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "mentorship",
            "0005_rename_mentorship_withdrawalrequest_mentor_status_idx_mentorship__mentor__b6591a_idx_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="SessionChatMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message", models.TextField()),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("chat", "Chat Message"),
                            ("system", "System Message"),
                            ("file", "File Share"),
                        ],
                        default="chat",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
                (
                    "sender",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="session_chat_messages",
                        to="mentorship.mentorshipsession",
                    ),
                ),
            ],
            options={
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="SessionChatAttachment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("file", models.FileField(upload_to="session_chat_attachments/")),
                ("filename", models.CharField(max_length=255)),
                ("file_size", models.PositiveIntegerField()),
                (
                    "uploaded_at",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="attachments",
                        to="mentorship.sessionchatmessage",
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name="sessionchatmessage",
            index=models.Index(
                fields=["session", "created_at"], name="mentorship__session_e614c1_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="sessionchatmessage",
            index=models.Index(
                fields=["sender", "created_at"], name="mentorship__sender__1eee1c_idx"
            ),
        ),
    ]
