# Generated manually for version control models

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('collaborate', '0019_add_project_hash'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectCommit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commit_hash', models.CharField(help_text='Unique hash for this commit', max_length=64, unique=True)),
                ('message', models.TextField(help_text='Commit message describing the changes')),
                ('branch_name', models.CharField(default='main', help_text='Branch name for this commit', max_length=100)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('files_changed', models.PositiveIntegerField(default=0, help_text='Number of files changed in this commit')),
                ('lines_added', models.PositiveIntegerField(default=0, help_text='Number of lines added')),
                ('lines_removed', models.PositiveIntegerField(default=0, help_text='Number of lines removed')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('parent_commit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_commits', to='collaborate.projectcommit')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commits', to='collaborate.project')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProjectBranch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Branch name', max_length=100)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('is_default', models.BooleanField(default=False, help_text='True if this is the default branch')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('head_commit', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='branch_heads', to='collaborate.projectcommit')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='collaborate.project')),
            ],
        ),
        migrations.CreateModel(
            name='ProjectFileVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_path', models.CharField(help_text='Relative path to the file within the project', max_length=1000)),
                ('content', models.TextField(help_text='File content at this version')),
                ('content_hash', models.CharField(help_text='Hash of the file content for deduplication', max_length=64)),
                ('file_size', models.PositiveIntegerField(default=0, help_text='File size in bytes')),
                ('is_deleted', models.BooleanField(default=False, help_text='True if file was deleted in this commit')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('commit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='file_versions', to='collaborate.projectcommit')),
            ],
        ),
        migrations.AddIndex(
            model_name='projectcommit',
            index=models.Index(fields=['project', 'created_at'], name='collaborate_projectcommit_project_created_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcommit',
            index=models.Index(fields=['project', 'branch_name', 'created_at'], name='collaborate_projectcommit_project_branch_created_idx'),
        ),
        migrations.AddIndex(
            model_name='projectcommit',
            index=models.Index(fields=['commit_hash'], name='collaborate_projectcommit_commit_hash_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='projectbranch',
            unique_together={('project', 'name')},
        ),
        migrations.AddIndex(
            model_name='projectbranch',
            index=models.Index(fields=['project', 'name'], name='collaborate_projectbranch_project_name_idx'),
        ),
        migrations.AddIndex(
            model_name='projectbranch',
            index=models.Index(fields=['project', 'is_default'], name='collaborate_projectbranch_project_default_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='projectfileversion',
            unique_together={('commit', 'file_path')},
        ),
        migrations.AddIndex(
            model_name='projectfileversion',
            index=models.Index(fields=['commit', 'file_path'], name='collaborate_projectfileversion_commit_path_idx'),
        ),
        migrations.AddIndex(
            model_name='projectfileversion',
            index=models.Index(fields=['content_hash'], name='collaborate_projectfileversion_content_hash_idx'),
        ),
        migrations.AddIndex(
            model_name='projectfileversion',
            index=models.Index(fields=['file_path', 'created_at'], name='collaborate_projectfileversion_path_created_idx'),
        ),
    ]
