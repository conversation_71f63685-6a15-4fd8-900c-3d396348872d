{% extends 'base.html' %} {% load static %} {% block title %}Session Feedback -
ForgeX{% endblock %} {% block content %}
<div class="feedback-page">
  <!-- Header Section -->
  <div class="feedback-header">
    <div class="container">
      <div class="tile-wrap">
        <div class="header-content">
          <div class="session-summary">
            <h1>How was your session?</h1>
            <p>Please rate your experience with {{ mentor.get_full_name }}</p>
            <div class="session-details">
              <div class="detail-item">
                <i class="fas fa-calendar"></i>
                <span>{{ session.scheduled_time|date:"F d, Y" }}</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-clock"></i>
                <span>{{ session.duration_minutes }} minutes</span>
              </div>
              <div class="detail-item">
                <i class="fas fa-user-graduate"></i>
                <span>{{ mentor.get_full_name }}</span>
              </div>
            </div>
          </div>
          <div class="mentor-avatar">
            {% if mentor.profile and mentor.profile.profile_picture %}
            <img
              src="{{ mentor.profile.profile_picture.url }}"
              alt="{{ mentor.get_full_name }}"
              class="avatar-image"
            />
            {% else %}
            <div class="avatar-initials">
              {{ mentor.first_name|first|upper }}{{ mentor.last_name|first|upper
              }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Feedback Form Section -->
  <div class="feedback-content">
    <div class="container">
      <form id="feedbackForm" class="feedback-form">
        {% csrf_token %}

        <!-- Rating Section -->
        <div class="rating-section">
          <h3><i class="fas fa-star"></i> Rate Your Experience</h3>
          <p class="section-subtitle">
            Your feedback helps us maintain quality mentorship
          </p>

          <!-- Progress Indicator -->
          <div class="progress-indicator">
            <div class="progress-item" id="progress-communication">
              <i class="fas fa-comments"></i>
              <span>Communication</span>
            </div>
            <div class="progress-item" id="progress-knowledge">
              <i class="fas fa-brain"></i>
              <span>Knowledge</span>
            </div>
            <div class="progress-item" id="progress-helpfulness">
              <i class="fas fa-hands-helping"></i>
              <span>Helpfulness</span>
            </div>
          </div>

          <div class="rating-categories">
            <div class="rating-item">
              <div class="rating-header">
                <i class="fas fa-comments rating-icon"></i>
                <div>
                  <label>Communication</label>
                  <p class="rating-description">
                    How well did the mentor communicate and explain concepts?
                  </p>
                </div>
              </div>
              <div class="star-rating" data-rating="communication_rating">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
              </div>
              <div class="rating-labels">
                <span>Poor</span>
                <span>Excellent</span>
              </div>
            </div>

            <div class="rating-item">
              <div class="rating-header">
                <i class="fas fa-brain rating-icon"></i>
                <div>
                  <label>Knowledge</label>
                  <p class="rating-description">
                    How knowledgeable was the mentor about the subject?
                  </p>
                </div>
              </div>
              <div class="star-rating" data-rating="knowledge_rating">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
              </div>
              <div class="rating-labels">
                <span>Poor</span>
                <span>Excellent</span>
              </div>
            </div>

            <div class="rating-item">
              <div class="rating-header">
                <i class="fas fa-hands-helping rating-icon"></i>
                <div>
                  <label>Helpfulness</label>
                  <p class="rating-description">
                    How helpful was the session for your learning goals?
                  </p>
                </div>
              </div>
              <div class="star-rating" data-rating="helpfulness_rating">
                <span class="star" data-value="1">★</span>
                <span class="star" data-value="2">★</span>
                <span class="star" data-value="3">★</span>
                <span class="star" data-value="4">★</span>
                <span class="star" data-value="5">★</span>
              </div>
              <div class="rating-labels">
                <span>Poor</span>
                <span>Excellent</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Recommendation Section -->
        <div class="recommendation-section">
          <div class="checkbox-item">
            <input
              type="checkbox"
              id="would_recommend"
              name="would_recommend"
            />
            <label for="would_recommend">
              <i class="fas fa-thumbs-up"></i>
              I would recommend this mentor to others
            </label>
          </div>
        </div>

        <!-- Written Feedback Section -->
        <div class="written-feedback-section">
          <h3><i class="fas fa-pen"></i> Share Your Thoughts</h3>
          <p class="section-subtitle">
            Help us understand your experience better
          </p>

          <div class="feedback-field">
            <label for="what_went_well">
              <i class="fas fa-thumbs-up"></i>
              What went well in this session?
            </label>
            <textarea
              id="what_went_well"
              name="what_went_well"
              placeholder="Tell us what you enjoyed about the session..."
              rows="4"
            ></textarea>
          </div>

          <div class="feedback-field">
            <label for="areas_for_improvement">
              <i class="fas fa-lightbulb"></i>
              What could be improved?
            </label>
            <textarea
              id="areas_for_improvement"
              name="areas_for_improvement"
              placeholder="Any suggestions for improvement..."
              rows="4"
            ></textarea>
          </div>

          <div class="feedback-field">
            <label for="additional_comments">
              <i class="fas fa-comment-dots"></i>
              Additional comments (optional)
            </label>
            <textarea
              id="additional_comments"
              name="additional_comments"
              placeholder="Any other thoughts you'd like to share..."
              rows="4"
            ></textarea>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="form-actions">
          <button type="submit" class="btn btn-primary" id="submitFeedback">
            <i class="fas fa-paper-plane"></i>
            Submit Feedback
          </button>
          <a
            href="{% url 'mentorship:my_sessions' %}"
            class="btn btn-secondary"
          >
            <i class="fas fa-arrow-left"></i>
            Skip for Now
          </a>
        </div>
      </form>
    </div>
  </div>
</div>

<style>
  .feedback-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
  }

  .feedback-header {
    background: linear-gradient(135deg, #c0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
  }

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin-left: auto;
    margin-right: auto;
  }

  .session-summary h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
  }

  .session-summary p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    opacity: 0.8;
  }

  .session-details {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }

  .detail-item i {
    color: #1a1a1a;
    opacity: 0.7;
  }

  .mentor-avatar {
    flex-shrink: 0;
  }

  .avatar-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(26, 26, 26, 0.1);
  }

  .avatar-initials {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(26, 26, 26, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 800;
    color: #1a1a1a;
    border: 4px solid rgba(26, 26, 26, 0.1);
  }

  .feedback-content {
    padding: 3rem 0;
  }

  .feedback-form {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  .rating-section h3,
  .written-feedback-section h3 {
    color: #c0ff6b;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .section-subtitle {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    text-align: center;
  }

  .progress-indicator {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .progress-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0.5;
  }

  .progress-item.completed {
    opacity: 1;
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    transform: scale(1.05);
  }

  .progress-item i {
    font-size: 1.2rem;
    color: #c0ff6b;
  }

  .progress-item span {
    font-size: 0.8rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
  }

  .rating-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .rating-item {
    background: rgba(255, 255, 255, 0.03);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .rating-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(192, 255, 107, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  .rating-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .rating-icon {
    color: #c0ff6b;
    font-size: 1.5rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
  }

  .rating-item label {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #c0ff6b;
  }

  .rating-description {
    font-size: 0.95rem;
    opacity: 0.8;
    margin: 0;
    line-height: 1.4;
  }

  .rating-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.6;
  }

  .star-rating {
    display: flex;
    gap: 0.25rem;
  }

  .star {
    font-size: 2.5rem;
    color: #555;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    margin: 0 0.1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .star:hover {
    color: #ffd700;
    transform: scale(1.15);
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }

  .star.active {
    color: #ffd700;
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
  }

  .star.active:hover {
    transform: scale(1.2);
  }

  .star.hover-preview {
    color: #ffed4e;
    transform: scale(1.1);
    text-shadow: 0 0 8px rgba(255, 237, 78, 0.6);
  }

  .recommendation-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(192, 255, 107, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .checkbox-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #c0ff6b;
  }

  .checkbox-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
  }

  .checkbox-item i {
    color: #c0ff6b;
  }

  .written-feedback-section {
    margin-top: 2rem;
  }

  .feedback-field {
    margin-bottom: 1.5rem;
  }

  .feedback-field label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #c0ff6b;
    font-size: 1.1rem;
  }

  .feedback-field label i {
    color: #c0ff6b;
    opacity: 0.8;
  }

  .feedback-field textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
  }

  .feedback-field textarea:focus {
    outline: none;
    border-color: #c0ff6b;
    box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #c0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      text-align: center;
    }

    .session-details {
      justify-content: center;
    }

    .feedback-form {
      margin: 0 1rem;
      padding: 1.5rem;
    }

    .form-actions {
      flex-direction: column;
    }

    .rating-categories {
      gap: 1.5rem;
    }

    .star {
      font-size: 1.5rem;
    }

    .progress-indicator {
      gap: 1rem;
      flex-wrap: wrap;
    }

    .progress-item {
      padding: 0.5rem;
    }

    .rating-header {
      flex-direction: column;
      text-align: center;
      gap: 0.5rem;
    }
  }
</style>

<script>
  // Star rating functionality
  let ratings = {
    communication_rating: 0,
    knowledge_rating: 0,
    helpfulness_rating: 0,
  };

  // Update progress indicator
  function updateProgressIndicator(ratingType) {
    const progressMap = {
      communication_rating: "progress-communication",
      knowledge_rating: "progress-knowledge",
      helpfulness_rating: "progress-helpfulness",
    };

    const progressElement = document.getElementById(progressMap[ratingType]);
    if (progressElement && ratings[ratingType] > 0) {
      progressElement.classList.add("completed");

      // Add a celebration effect
      progressElement.style.animation = "pulse 0.6s ease-in-out";
      setTimeout(() => {
        progressElement.style.animation = "";
      }, 600);
    }

    // Check if all ratings are completed
    const allCompleted = Object.values(ratings).every((rating) => rating > 0);
    if (allCompleted) {
      // Enable submit button or show completion message
      const submitButton = document.getElementById("submitFeedback");
      if (submitButton) {
        submitButton.style.background =
          "linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%)";
        submitButton.style.transform = "scale(1.05)";
      }
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Initialize star ratings
    document.querySelectorAll(".star-rating").forEach((ratingContainer) => {
      const ratingType = ratingContainer.dataset.rating;
      const stars = ratingContainer.querySelectorAll(".star");

      stars.forEach((star, index) => {
        star.addEventListener("click", function () {
          const value = parseInt(this.dataset.value);
          ratings[ratingType] = value;

          console.log(`Rating ${ratingType} set to ${value}`);

          // Update visual state - remove all active classes first
          stars.forEach((s) => s.classList.remove("active"));

          // Add active class to clicked star and all previous stars
          for (let i = 0; i < value; i++) {
            stars[i].classList.add("active");
          }

          // Update progress indicator
          updateProgressIndicator(ratingType);

          // Add a visual feedback effect
          this.style.transform = "scale(1.3)";
          setTimeout(() => {
            this.style.transform = "";
          }, 200);
        });

        // Hover effect - preview rating
        star.addEventListener("mouseenter", function () {
          const value = parseInt(this.dataset.value);
          stars.forEach((s, i) => {
            s.classList.remove("hover-preview");
            if (i < value) {
              s.classList.add("hover-preview");
            }
          });
        });
      });

      // Reset hover effect
      ratingContainer.addEventListener("mouseleave", function () {
        stars.forEach((s) => s.classList.remove("hover-preview"));
      });
    });

    // Form submission
    document
      .getElementById("feedbackForm")
      .addEventListener("submit", async function (e) {
        e.preventDefault();

        // Validate ratings
        if (Object.values(ratings).some((rating) => rating === 0)) {
          alert("Please provide ratings for all categories.");
          return;
        }

        const formData = new FormData(this);
        const data = {
          ...ratings,
          would_recommend: formData.get("would_recommend") === "on",
          what_went_well: formData.get("what_went_well"),
          areas_for_improvement: formData.get("areas_for_improvement"),
          additional_comments: formData.get("additional_comments"),
        };

        const submitButton = document.getElementById("submitFeedback");
        submitButton.disabled = true;
        submitButton.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> Submitting...';

        try {
          const response = await fetch(
            `/mentorship/session/{{ session.id }}/feedback/submit/`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-CSRFToken": document.querySelector(
                  "[name=csrfmiddlewaretoken]"
                ).value,
              },
              body: JSON.stringify(data),
            }
          );

          const result = await response.json();

          if (result.success) {
            // Show success message and redirect
            alert("Thank you for your feedback!");
            window.location.href = "/mentorship/my-sessions/";
          } else {
            alert("Error: " + result.error);
            submitButton.disabled = false;
            submitButton.innerHTML =
              '<i class="fas fa-paper-plane"></i> Submit Feedback';
          }
        } catch (error) {
          alert("An error occurred. Please try again.");
          console.error("Error:", error);
          submitButton.disabled = false;
          submitButton.innerHTML =
            '<i class="fas fa-paper-plane"></i> Submit Feedback';
        }
      });
  });
</script>
{% endblock %}
