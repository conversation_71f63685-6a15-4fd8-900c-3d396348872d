# WebSocket Configuration Guide

## Overview

This document explains the centralized WebSocket configuration system implemented in ForgeX. All WebSocket connections across the application (chat, voice, video, screen share, notifications, terminal, and mentorship) are now controlled from a single configuration point in Django settings.

## Configuration Files

### 1. Environment Variables (.env)

Add these variables to your `.env` file:

```env
# WebSocket Configuration - Centralized Control
WEBSOCKET_HOST=127.0.0.1
WEBSOCKET_PORT=8001
WEBSOCKET_PROTOCOL=ws
```

### 2. Django Settings (web_django/settings.py)

The settings automatically construct WebSocket URLs from environment variables:

```python
# WebSocket Configuration - Centralized for all services
WEBSOCKET_HOST = config('WEBSOCKET_HOST', default='127.0.0.1')
WEBSOCKET_PORT = config('WEBSOCKET_PORT', default=8001, cast=int)
WEBSOCKET_PROTOCOL = config('WEBSOCKET_PROTOCOL', default='ws')

# Construct WebSocket base URL
WEBSOCKET_BASE_URL = f"{WEBSOCKET_PROTOCOL}://{WEBSOCKET_HOST}:{WEBSOCKET_PORT}"

# WebSocket endpoint configurations for different services
WEBSOCKET_ENDPOINTS = {
    'collaboration': '/ws/collaborate/',
    'collab_chat': '/ws/collab/',
    'voice': '/ws/voice/',
    'file_changes': '/ws/project/',
    'yjs_room': '/ws/yjs-room/',
    'mentorship_session': '/ws/mentorship/session/',
    'mentorship_video': '/ws/mentorship/video/',
    'mentorship_audio': '/ws/mentorship/audio/',
    'mentorship_chat': '/ws/mentorship/chat/',
    'notifications': '/ws/notifications/',
    'account_notifications': '/ws/account/notifications/',
    'terminal': '/ws/terminal/',
}
```

## Environment-Specific Configuration

### Development Environment
```env
WEBSOCKET_HOST=127.0.0.1
WEBSOCKET_PORT=8001
WEBSOCKET_PROTOCOL=ws
```

### Production Environment
```env
WEBSOCKET_HOST=your-domain.com
WEBSOCKET_PORT=443
WEBSOCKET_PROTOCOL=wss
```

### Cloudflare Tunnel Environment
```env
WEBSOCKET_HOST=songs-planet-nicaragua-appointed.trycloudflare.com
WEBSOCKET_PORT=443
WEBSOCKET_PROTOCOL=wss
```

## Services Using WebSocket Configuration

### 1. Editor (collaborate/templates/collaborate/editor.html)
- **Collaboration**: Real-time code editing
- **Chat**: Project chat functionality
- **Voice**: Voice communication
- **File Changes**: Live file synchronization
- **YJS Room**: Collaborative editing backend

### 2. Mentorship (mentorship/templates/mentorship/session_room.html)
- **Session**: Main mentorship session
- **Video**: Video calling
- **Audio**: Audio communication
- **Chat**: Session chat

### 3. Notifications (templates/base.html)
- **Notifications**: Global notification system
- **Account Notifications**: User-specific notifications

### 4. Terminal (staticfiles/js/terminal-manager.js)
- **Terminal**: Interactive terminal sessions

## How It Works

1. **Context Processor**: `web_django/context_processors.py` makes WebSocket URLs available in all templates
2. **Template Variables**: Templates receive `WEBSOCKET_BASE_URL` and `WEBSOCKET_URLS` variables
3. **JavaScript Configuration**: WebSocket URLs are passed to JavaScript via template variables
4. **Fallback Support**: All JavaScript files include fallback logic for backward compatibility

## Benefits

1. **Centralized Control**: Change WebSocket configuration in one place
2. **Environment Flexibility**: Easy switching between development and production
3. **Cloudflare Tunnel Support**: Seamless integration with tunnel domains
4. **Backward Compatibility**: Fallback logic ensures existing functionality continues to work
5. **Security**: Proper protocol handling (ws/wss) based on environment

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**: Check that WEBSOCKET_HOST and WEBSOCKET_PORT are correct
2. **Mixed Content Errors**: Ensure WEBSOCKET_PROTOCOL matches your site's protocol (ws for HTTP, wss for HTTPS)
3. **CORS Issues**: Verify that your WebSocket server allows connections from your domain

### Debugging

1. Check browser console for WebSocket connection errors
2. Verify environment variables are loaded correctly
3. Ensure Django context processor is registered in settings
4. Check that WebSocket server is running on the configured port

## Migration Notes

This centralized configuration is backward compatible. If the new configuration is not available, the system falls back to the previous hardcoded URLs. This ensures a smooth transition without breaking existing functionality.
