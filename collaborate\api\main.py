import os
import shutil
import subprocess
import tempfile
import sys
import logging
import json
from typing import Dict, List, Optional, Set
from pathlib import Path
import asyncio
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from fastapi.responses import JSONResponse, PlainTextResponse, HTMLResponse, Response, FileResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
import json
from datetime import datetime
import uuid
import base64


# Import our terminal WebSocket router
from terminal_ws import router as terminal_router

# Removed Django dependencies for standalone FastAPI operation

# Helper function to convert project identifier to project ID (simplified without Django)
async def get_project_id_from_identifier(project_identifier: str) -> Optional[str]:
    """
    Convert project identifier (hash or ID) to project ID.
    Simplified version without Django dependency - just returns the identifier as-is.

    Args:
        project_identifier: Can be either project ID (integer as string) or project hash

    Returns:
        Project ID as string
    """
    # Without Django, we'll just use the identifier as-is
    # This assumes the project directory exists or will be created
    return project_identifier

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("code-editor-api")

app = FastAPI(title="Code Editor API")

# CORS configuration
origins = [
    "http://localhost:8000",
    "http://127.0.0.1:8000",
    "http://localhost:8002",
    "http://127.0.0.1:8002",
    "ws://localhost:8002",
    "ws://127.0.0.1:8002",
    'http://*************:8000',
    "http://*************:8002",
    "http://*************:8000",
    "http://*************:8002",
    "ws://*************:8002",
     "https://20e8-103-41-172-9.ngrok-free.app",

]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Include our terminal WebSocket router
app.include_router(terminal_router)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests to help with debugging."""
    path = request.url.path
    method = request.method
    logger.info(f"Request: {method} {path}")

    # Process the request
    response = await call_next(request)

    # Log the response status
    logger.info(f"Response: {method} {path} - Status: {response.status_code}")
    return response

# Base directory for project files - use local path for Windows compatibility
PROJECTS_DIR = os.environ.get("PROJECTS_DIR", os.path.join(os.getcwd(), "projects"))
logger.info(f"Using projects directory: {PROJECTS_DIR}")

# Ensure the projects directory exists
os.makedirs(PROJECTS_DIR, exist_ok=True)

# Terminal Session Manager
class TerminalSession:
    """Represents an individual terminal session"""

    def __init__(self, terminal_id: str, project_id: str, terminal_name: str = None):
        self.terminal_id = terminal_id
        self.project_id = project_id
        self.terminal_name = terminal_name or f"Terminal {terminal_id[-8:]}"
        self.working_directory = ""  # Relative to project root
        self.created_at = datetime.now().isoformat()
        self.last_used = datetime.now().isoformat()
        self.is_active = True
        self.command_history = []
        self.environment_vars = {}  # Terminal-specific environment variables

    def update_last_used(self):
        """Update the last used timestamp"""
        self.last_used = datetime.now().isoformat()

    def add_to_history(self, command: str):
        """Add command to history"""
        self.command_history.append({
            "command": command,
            "timestamp": datetime.now().isoformat()
        })
        # Keep only last 100 commands
        if len(self.command_history) > 100:
            self.command_history = self.command_history[-100:]

    def get_full_working_directory(self) -> str:
        """Get the full filesystem path for this terminal's working directory"""
        project_path = get_project_path(self.project_id)
        if self.working_directory:
            return os.path.join(project_path, self.working_directory)
        return project_path

    def set_working_directory(self, new_dir: str):
        """Set the working directory (relative to project root)"""
        self.working_directory = new_dir.strip('/')
        self.update_last_used()

    def to_dict(self) -> dict:
        """Convert to dictionary for API responses"""
        return {
            "terminal_id": self.terminal_id,
            "terminal_name": self.terminal_name,
            "project_id": self.project_id,
            "working_directory": self.working_directory,
            "created_at": self.created_at,
            "last_used": self.last_used,
            "is_active": self.is_active,
            "command_count": len(self.command_history)
        }

class TerminalManager:
    """Manages multiple terminal sessions"""

    def __init__(self):
        self.terminals: Dict[str, TerminalSession] = {}

    def create_terminal(self, project_id: str, terminal_name: str = None) -> TerminalSession:
        """Create a new terminal session"""
        terminal_id = f"term_{project_id}_{uuid.uuid4().hex[:8]}"
        terminal = TerminalSession(terminal_id, project_id, terminal_name)
        self.terminals[terminal_id] = terminal
        logger.info(f"Created new terminal: {terminal_id} for project: {project_id}")
        return terminal

    def get_terminal(self, terminal_id: str) -> Optional[TerminalSession]:
        """Get a terminal session by ID"""
        return self.terminals.get(terminal_id)

    def get_project_terminals(self, project_id: str) -> List[TerminalSession]:
        """Get all terminals for a specific project"""
        return [t for t in self.terminals.values() if t.project_id == project_id and t.is_active]

    def close_terminal(self, terminal_id: str) -> bool:
        """Close a terminal session"""
        if terminal_id in self.terminals:
            self.terminals[terminal_id].is_active = False
            logger.info(f"Closed terminal: {terminal_id}")
            return True
        return False

    def cleanup_inactive_terminals(self):
        """Remove inactive terminals older than 1 hour"""
        cutoff_time = datetime.now().timestamp() - 3600  # 1 hour ago
        to_remove = []

        for terminal_id, terminal in self.terminals.items():
            if not terminal.is_active:
                last_used_time = datetime.fromisoformat(terminal.last_used).timestamp()
                if last_used_time < cutoff_time:
                    to_remove.append(terminal_id)

        for terminal_id in to_remove:
            del self.terminals[terminal_id]
            logger.info(f"Cleaned up inactive terminal: {terminal_id}")

# Global terminal manager instance
terminal_manager = TerminalManager()

# Allowed terminal commands (whitelist approach for security)
ALLOWED_COMMANDS = {
    # Python
    "python", "python3", "pip", "pip3", "pytest", "coverage", "flask", "uvicorn",
    # JavaScript/Node
    "node", "npm", "npx", "yarn",
    # Git
    "git", "gh",
    # Build tools
    "make", "cmake", "mvn", "gradle",
    # Unix utilities
    "ls", "find", "cat", "echo", "mkdir", "touch", "rm", "pwd", "cd", "cp", "mv",
    # Windows utilities
    "dir", "type", "copy", "move", "del", "md", "rd", "cls", "where", "findstr",
    "tasklist", "taskkill", "help",
    # Package managers
    "apt-get", "apt", "yum", "dnf", "choco", "winget",
}

# Models
class FileItem(BaseModel):
    name: str
    path: str
    type: str
    children: Optional[List['FileItem']] = None

class FileContent(BaseModel):
    path: str
    content: Optional[str] = None
    isDirectory: bool = False

class FileRename(BaseModel):
    oldPath: str
    newPath: str

class RunCodeRequest(BaseModel):
    file_path: str
    language: str

class RunCommandRequest(BaseModel):
    command: str
    working_directory: str = ""
    terminal_id: Optional[str] = None

class CreateTerminalRequest(BaseModel):
    project_id: str
    terminal_name: Optional[str] = None

class TerminalInfo(BaseModel):
    terminal_id: str
    terminal_name: str
    project_id: str
    working_directory: str
    created_at: str
    last_used: str
    is_active: bool

class CommandResponse(BaseModel):
    output: Optional[str] = None
    error: Optional[str] = None
    exit_code: int = 0

class RunCodeResponse(BaseModel):
    output: Optional[str] = None
    error: Optional[str] = None

class FileDuplicate(BaseModel):
    path: str
    type: str



# Helper functions
def get_project_path(project_id: str) -> str:
    """Get the full path to a project directory."""
    project_path = os.path.join(PROJECTS_DIR, project_id)

    # Ensure the project directory exists
    if not os.path.exists(project_path):
        os.makedirs(project_path, exist_ok=True)
        logger.info(f"Created new project directory: {project_path}")

        # Initialize new project with basic files if it's empty
        if not os.listdir(project_path):
            init_project_directory(project_path)

    logger.info(f"Project path: {project_path}")
    return project_path

def init_project_directory(project_path: str):
    """Initialize a new project directory with basic files."""
    try:
        # Create a README file
        readme_path = os.path.join(project_path, "README.md")
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write("# Project\n\nThis is a new project created in ForgeX Code Editor.\n")

        # Create a main Python file
        main_py_path = os.path.join(project_path, "main.py")
        with open(main_py_path, 'w', encoding='utf-8') as f:
            f.write("# Main Python file\n\nprint('Hello, World!')\n")

        # Create a sample HTML file
        html_path = os.path.join(project_path, "index.html")
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write("<!DOCTYPE html>\n<html>\n<head>\n    <title>My Project</title>\n</head>\n<body>\n    <h1>Hello, World!</h1>\n</body>\n</html>\n")

        logger.info(f"Initialized project with sample files in {project_path}")
    except Exception as e:
        logger.error(f"Failed to initialize project directory: {str(e)}")
        # Continue even if initialization fails - the directory still exists

def is_command_allowed(command: str) -> bool:
    """Check if a command is allowed to be executed."""
    # Re-enable security restrictions
    logger.info(f"Checking command: {command}")

    # Parse the command to get the base command
    try:
        import shlex
        parts = shlex.split(command.strip())
        if not parts:
            return True

        base_command = parts[0]

        # Check against whitelist
        if base_command in ALLOWED_COMMANDS:
            return True

        # Check for dangerous patterns
        dangerous_patterns = [
            r'rm\s+.*-rf\s*/',
            r'chmod\s+777\s*/',
            r'sudo\s+',
            r'su\s+',
            r'passwd\s+',
            r'mount\s+',
            r'umount\s+',
            r'systemctl\s+',
            r'service\s+',
            r'iptables\s+',
            r'dd\s+if=',
            r'mkfs\s+',
            r'fdisk\s+',
        ]

        import re
        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                logger.warning(f"Blocked dangerous command: {command}")
                return False

        # Allow if not explicitly dangerous
        logger.info(f"Allowing command: {command}")
        return True

    except Exception as e:
        logger.error(f"Error checking command: {e}")
        return False  # Deny on error for security







def scan_directory(directory: str, base_path: str = "") -> List[FileItem]:
    """Recursively scan a directory and return a list of file/directory items."""
    items = []

    # Files and directories to ignore (like .gitignore)
    ignored_names = {
        '.forgex_vc',
        '.git',
        '.gitignore',
        'node_modules',
        '__pycache__',
        '.DS_Store',
        'Thumbs.db',
        '.vscode',
        '.idea',
        '.env',
        '.pytest_cache',
        '.coverage',
        'venv',
        'env'
    }

    try:
        logger.info(f"Scanning directory: {directory}")
        for entry in os.scandir(directory):
            # Skip ignored files and directories
            if entry.name in ignored_names:
                logger.debug(f"Ignoring: {entry.name}")
                continue

            path = os.path.join(base_path, entry.name) if base_path else entry.name

            if entry.is_dir():
                children = scan_directory(entry.path, path)
                items.append(FileItem(
                    name=entry.name,
                    path=path,
                    type="directory",
                    children=children
                ))
            else:
                items.append(FileItem(
                    name=entry.name,
                    path=path,
                    type="file"
                 ))
        logger.info(f"Found {len(items)} items in {directory}")
    except FileNotFoundError:
        logger.error(f"Directory not found: {directory}")
    except Exception as e:
        logger.error(f"Error scanning directory {directory}: {str(e)}")

    # Sort items: directories first, then files, both alphabetically
    return sorted(items, key=lambda x: (x.type != "directory", x.name.lower()))

def run_code_in_docker(project_id: str, file_path: str, language: str) -> Dict[str, str]:
    """Run code in a Docker container for isolation and security."""
    project_path = get_project_path(project_id)
    full_file_path = os.path.join(project_path, file_path)

    logger.info(f"Running file at path: {full_file_path} with language: {language}")

    if not os.path.exists(full_file_path):
        logger.error(f"File not found: {full_file_path}")
        return {"error": f"File not found: {file_path}"}

    # Read the file content to debug
    try:
        with open(full_file_path, 'r') as f:
            content = f.read()
            logger.debug(f"File content size: {len(content)} bytes")
    except Exception as e:
        logger.error(f"Error reading file: {str(e)}")
        return {"error": f"Error reading file: {str(e)}"}

    # Map language to Docker image and command
    language_config = {
        "python": {"image": "python:3.9-slim", "command": "python"},
        "javascript": {"image": "node:16-alpine", "command": "node"},
        "java": {"image": "openjdk:11-jdk-slim", "command": "java"},
        "csharp": {"image": "mcr.microsoft.com/dotnet/sdk:6.0", "command": "dotnet run"},
        "go": {"image": "golang:1.18", "command": "go run"},
        "ruby": {"image": "ruby:3.1", "command": "ruby"},
    }
    config = language_config.get(language.lower(), language_config["python"])

    try:
        # Get the filename and directory
        filename = os.path.basename(file_path)
        file_dir = os.path.dirname(file_path)

        # Simple approach: Run the Python file directly on the host
        if language.lower() == "python":
            try:
                # First attempt with subprocess directly
                cmd = ["python", full_file_path]
                logger.info(f"Executing direct command: {' '.join(cmd)}")
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                stdout, stderr = process.communicate(timeout=30)

                logger.info(f"Command completed with return code: {process.returncode}")
                logger.debug(f"stdout: {stdout}")
                if stderr:
                    logger.debug(f"stderr: {stderr}")

                if process.returncode == 0:
                    if stdout.strip():
                        return {"output": stdout.strip()}
                    else:
                        return {"output": "Code executed successfully but produced no output."}
                else:
                    return {"error": stderr.strip()}
            except Exception as e:
                logger.error(f"Direct execution failed: {str(e)}. Falling back to Docker.")

        # Docker execution as fallback or for other languages
        # Use a double mount approach: mount the specific project directory to a fixed location
        cmd = [
            "docker", "run", "--rm",
            # Mount the project directory to a fixed location in the container
            "-v", f"{project_path}:/code",
            # Set working directory where the file is located
            "-w", f"/code/{file_dir}" if file_dir else "/code",
            "--network", "none",  # No network access for security
            config["image"],
            config["command"], filename  # Execute the file by name
        ]

        logger.info(f"Docker command: {' '.join(cmd)}")

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        stdout, stderr = process.communicate(timeout=30)
        logger.info(f"Docker command completed with return code: {process.returncode}")
        logger.debug(f"stdout: {stdout}")
        if stderr:
            logger.debug(f"stderr: {stderr}")

        if process.returncode == 0:
            if not stdout.strip():
                return {"output": "Code executed successfully but produced no output."}
            return {"output": stdout.strip()}
        else:
            return {"error": stderr.strip()}
    except subprocess.TimeoutExpired:
        logger.error(f"Execution timed out for file: {file_path}")
        return {"error": "Execution timed out (30s limit)"}
    except Exception as e:
        logger.exception(f"Exception running code: {str(e)}")
        return {"error": f"Exception: {str(e)}"}

def run_terminal_command(project_id: str, command: str, terminal_id: str = None, working_directory: str = "") -> Dict[str, any]:
    """Run a terminal command in a specific terminal session."""
    if not is_command_allowed(command):
        return {
            "error": f"Command not allowed for security reasons: {command}",
            "exit_code": 1,
            "terminal_id": terminal_id
        }

    # Get or create terminal session
    terminal_session = None
    if terminal_id:
        terminal_session = terminal_manager.get_terminal(terminal_id)

    if not terminal_session:
        # Create a new terminal if none exists
        terminal_session = terminal_manager.create_terminal(project_id)
        terminal_id = terminal_session.terminal_id
        logger.info(f"Created new terminal session: {terminal_id}")

    # Update terminal session
    terminal_session.update_last_used()
    terminal_session.add_to_history(command)

    # Get the actual project path on the host
    project_path = get_project_path(project_id)
    logger.info(f"Running terminal command in project: {project_id}, terminal: {terminal_id}, path: {project_path}")

    # Special handling for cd command - keep track of directory changes within the project
    if command.strip().startswith('cd '):
        # Extract the target directory
        target_dir = command.strip()[3:].strip()

        # Use terminal session's working directory
        current_dir = terminal_session.working_directory

        # If it's a relative path, combine with current working directory
        if not target_dir.startswith('/'):
            if target_dir == "..":
                # Go up one directory
                if current_dir:
                    parts = current_dir.split('/')
                    if len(parts) > 1:
                        current_dir = '/'.join(parts[:-1])
                    else:
                        current_dir = ""
            elif target_dir == ".":
                # Stay in current directory
                pass
            else:
                # Go into subdirectory
                current_dir = f"{current_dir}/{target_dir}" if current_dir else target_dir

            # Normalize the path (remove double slashes, etc)
            current_dir = current_dir.replace('//', '/').strip('/')

            # Verify this directory exists in the project
            full_path = os.path.join(project_path, current_dir)
            if os.path.exists(full_path) and os.path.isdir(full_path):
                terminal_session.set_working_directory(current_dir)
                return {
                    "output": "",
                    "working_directory": current_dir,
                    "terminal_id": terminal_id,
                    "exit_code": 0
                }
            else:
                return {
                    "error": f"cd: {target_dir}: No such file or directory",
                    "terminal_id": terminal_id,
                    "exit_code": 1
                }
        else:
            # Absolute paths are not supported for security
            return {
                "error": "cd: Absolute paths not supported",
                "terminal_id": terminal_id,
                "exit_code": 1
            }

    # Special handling for pwd command to always show the correct path
    if command.strip() == 'pwd':
        # Format the working directory path properly
        wd_path = f"/projects/{project_id}"
        if terminal_session.working_directory:
            wd_path = f"{wd_path}/{terminal_session.working_directory}"
        return {
            "output": wd_path,
            "terminal_id": terminal_id,
            "exit_code": 0
        }

    # Determine the real working directory on the filesystem
    real_work_dir = terminal_session.get_full_working_directory()

    # For ls commands, we need to make sure we only show the project files
    if command.strip() in ['ls', 'ls -l', 'ls -la', 'ls -al']:
        try:
            # List the files in the specified directory
            file_list = os.listdir(real_work_dir)

            # Format the output to look like ls command result
            if command.strip() == 'ls':
                # Simple ls just shows filenames
                return {
                    "output": "\n".join(file_list),
                    "exit_code": 0
                }
            else:
                # For more detailed ls commands, include file info
                result_lines = []
                result_lines.append(f"total {len(file_list)}")

                for file_name in file_list:
                    file_path = os.path.join(real_work_dir, file_name)
                    # Get file stats
                    stat_info = os.stat(file_path)
                    # Format the file info
                    is_dir = 'd' if os.path.isdir(file_path) else '-'
                    permissions = "rwxr-xr-x"  # Simplified permissions
                    size = stat_info.st_size

                    # Format similar to ls -la but more controlled
                    result_lines.append(f"{is_dir}{permissions} {size:8d} {file_name}")

                return {
                    "output": "\n".join(result_lines),
                    "exit_code": 0
                }
        except Exception as e:
            logger.error(f"Error listing directory: {str(e)}")
            return {
                "error": str(e),
                "exit_code": 1
            }

    if not os.path.exists(real_work_dir):
        return {
            "error": f"Working directory does not exist: {working_directory}",
            "exit_code": 1
        }

    try:
        # Execute the command in a way that keeps it within the project directory
        # Change to the working directory first
        current_dir = os.getcwd()
        os.chdir(real_work_dir)

        try:
            # Execute the command directly in the shell with platform-specific settings
            if os.name == 'nt':  # Windows
                # Use cmd.exe for Windows
                process = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=120,  # 2 minute timeout
                    executable='cmd.exe'
                )
            else:
                # Use bash for Unix-like systems
                process = subprocess.run(
                    command,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=120,  # 2 minute timeout
                    executable='/bin/bash' if os.path.exists('/bin/bash') else None
                )

            # Return to the original directory
            os.chdir(current_dir)

            # Return the command output with terminal info
            return {
                "output": process.stdout.strip(),
                "error": process.stderr.strip() if process.returncode != 0 else None,
                "exit_code": process.returncode,
                "terminal_id": terminal_id,
                "working_directory": terminal_session.working_directory
            }
        except Exception as e:
            # Ensure we change back to the original directory even if there's an error
            os.chdir(current_dir)
            raise e

    except subprocess.TimeoutExpired:
        return {
            "error": "Command execution timed out (120s limit)",
            "exit_code": 124
        }
    except Exception as e:
        logger.error(f"Error executing command: {str(e)}")
        return {
            "error": str(e),
            "exit_code": 1
        }

# API Endpoints
@app.get("/api/files/{project_identifier}")
async def get_files(project_identifier: str) -> List[FileItem]:
    """Get a list of files and directories for a project."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        logger.info(f"Getting file tree for project: {project_identifier} at path: {project_path}")

        # Check if directory exists
        if not os.path.exists(project_path):
            logger.warning(f"Project directory does not exist, creating it: {project_path}")
            os.makedirs(project_path, exist_ok=True)

        # Scan the directory
        result = scan_directory(project_path)
        logger.info(f"Retrieved file tree with {len(result)} top-level items")
        return result
    except Exception as e:
        logger.exception(f"Error getting file tree: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get file tree: {str(e)}")

@app.get("/api/file/{project_identifier}", response_class=PlainTextResponse)
async def get_file(project_identifier: str, path: str) -> str:
    """Get the content of a file."""
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    project_path = get_project_path(project_id)
    file_path = os.path.join(project_path, path)

    logger.info(f"Getting file content for: {file_path}")

    # Validate the path to prevent directory traversal attacks
    if not os.path.abspath(file_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {file_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            logger.info(f"Successfully read file {path}, size: {len(content)} bytes")
            return content  # Return raw file content as plain text
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise HTTPException(status_code=404, detail=f"File not found: {path}")
    except IsADirectoryError:
        logger.error(f"Path is a directory: {file_path}")
        raise HTTPException(status_code=400, detail=f"Path is a directory: {path}")
    except Exception as e:
        logger.exception(f"Error reading file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/file/{project_identifier}")
async def create_file(project_identifier: str, file_data: FileContent):
    """Create a new file or directory."""
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    project_path = get_project_path(project_id)
    file_path = os.path.join(project_path, file_data.path)

    logger.info(f"Creating {'directory' if file_data.isDirectory else 'file'}: {file_path}")

    # Validate the path to prevent directory traversal attacks
    if not os.path.abspath(file_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {file_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    # Calculate current storage usage
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(project_path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            total_size += os.path.getsize(fp)

    # Convert total size to MB
    used_mb = total_size / (1024 * 1024)
    limit_mb = 3072  # 3GB limit

    # Estimate the size of the new file
    new_file_size = len(file_data.content.encode('utf-8')) if file_data.content else 0
    new_total_size_mb = used_mb + (new_file_size / (1024 * 1024))

    # Log current storage usage
    logger.info(f"Current storage usage: {used_mb:.2f}MB")

    # Log estimated size of the new file
    logger.info(f"Estimated size of new file: {new_file_size / (1024 * 1024):.2f}MB")

    # Log total storage usage after adding the new file
    logger.info(f"Total storage usage after adding file: {new_total_size_mb:.2f}MB")

    if new_total_size_mb > limit_mb:
        logger.warning(f"Storage limit exceeded: {new_total_size_mb}MB > {limit_mb}MB")
        raise HTTPException(status_code=400, detail="Storage limit exceeded. Cannot create file.")

    try:
        if file_data.isDirectory:
            os.makedirs(file_path, exist_ok=True)
            logger.info(f"Directory created successfully: {file_path}")
        else:
            # Ensure the directory exists
            dir_path = os.path.dirname(file_path)
            if dir_path:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"Created parent directory: {dir_path}")

            # Handle duplicate file names
            base_name, ext = os.path.splitext(file_path)
            counter = 0
            while os.path.exists(file_path):
                counter += 1
                file_path = f"{base_name}_copy({counter}){ext}"

            # Create the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_data.content or "")
            logger.info(f"File created successfully: {file_path}")
    except Exception as e:
        logger.exception(f"Error creating {'directory' if file_data.isDirectory else 'file'} {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"status": "success", "path": file_path}

@app.put("/api/file/{project_identifier}")
async def update_file(project_identifier: str, file_data: FileContent):
    """Update the content of a file."""
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    project_path = get_project_path(project_id)
    file_path = os.path.join(project_path, file_data.path)

    logger.info(f"Updating file content: {file_path}")

    # Validate the path to prevent directory traversal attacks
    if not os.path.abspath(file_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {file_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        # Check if file exists
        if not os.path.exists(file_path):
            logger.warning(f"File not found during update: {file_path}")
            if os.path.exists(os.path.dirname(file_path)):
                logger.info(f"Creating new file during update: {file_path}")
                # Create the file if directory exists
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(file_data.content or "")
                return {"status": "success", "created": True}
            else:
                raise HTTPException(status_code=404, detail=f"File not found: {file_data.path}")

        # Update the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(file_data.content or "")
        logger.info(f"File updated successfully: {file_path}")
    except IsADirectoryError:
        logger.error(f"Cannot update a directory as a file: {file_path}")
        raise HTTPException(status_code=400, detail=f"Path is a directory: {file_data.path}")
    except Exception as e:
        logger.exception(f"Error updating file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"status": "success", "updated": True}

@app.delete("/api/file/{project_identifier}")
async def delete_file(project_identifier: str, path: str):
    """Delete a file or directory."""
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    project_path = get_project_path(project_id)
    file_path = os.path.join(project_path, path)

    logger.info(f"Deleting {'directory' if os.path.isdir(file_path) else 'file'}: {file_path}")

    # Validate the path to prevent directory traversal attacks
    if not os.path.abspath(file_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {file_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        if os.path.isdir(file_path):
            shutil.rmtree(file_path)
            logger.info(f"Directory deleted successfully: {file_path}")
        else:
            os.remove(file_path)
            logger.info(f"File deleted successfully: {file_path}")
    except FileNotFoundError:
        logger.error(f"File not found for deletion: {file_path}")
        raise HTTPException(status_code=404, detail=f"File not found: {path}")
    except Exception as e:
        logger.exception(f"Error deleting {'directory' if os.path.isdir(file_path) else 'file'} {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

    return {"status": "success", "path": path}

@app.post("/api/file/{project_id}/rename")
async def rename_file(project_id: str, rename_data: FileRename):
    """Rename a file or directory."""
    project_path = get_project_path(project_id)
    old_path = os.path.join(project_path, rename_data.oldPath)
    new_path = os.path.join(project_path, rename_data.newPath)

    # Validate the paths to prevent directory traversal attacks
    if not (os.path.abspath(old_path).startswith(os.path.abspath(project_path)) and
            os.path.abspath(new_path).startswith(os.path.abspath(project_path))):
        raise HTTPException(status_code=403, detail="Access denied")

    try:
        # Ensure the target directory exists
        os.makedirs(os.path.dirname(new_path), exist_ok=True)

        # Rename the file or directory
        shutil.move(old_path, new_path)
    except FileNotFoundError:
        raise HTTPException(status_code=404, detail=f"File not found: {rename_data.oldPath}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

    return {"status": "success"}

@app.post("/api/file/{project_id}/duplicate")
async def duplicate_file(project_id: str, duplicate_data: FileDuplicate):
    """Duplicate a file or directory with auto-incrementing naming."""
    project_path = get_project_path(project_id)

    # Get the original path
    original_path = os.path.join(project_path, duplicate_data.path)

    # Validate the path to prevent directory traversal attacks
    if not os.path.abspath(original_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {original_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    # Check if original file/folder exists
    if not os.path.exists(original_path):
        logger.error(f"File/folder not found for duplication: {original_path}")
        raise HTTPException(status_code=404, detail=f"File/folder not found: {duplicate_data.path}")

    # Calculate current storage usage
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(project_path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            total_size += os.path.getsize(fp)

    # Convert total size to MB
    used_mb = total_size / (1024 * 1024)
    limit_mb = 3072  # 3GB limit

    # Estimate the size of the duplicate (for files only)
    duplicate_size = os.path.getsize(original_path) / (1024 * 1024) if duplicate_data.type == "file" else 0
    new_total_size_mb = used_mb + duplicate_size

    # Log current storage usage and estimated size of the duplicate
    logger.info(f"Current storage usage: {used_mb:.2f}MB")
    logger.info(f"Estimated size of duplicate: {duplicate_size:.2f}MB")
    logger.info(f"Total storage usage after duplication: {new_total_size_mb:.2f}MB")

    # Additional logging to confirm storage limit enforcement
    logger.info(f"Checking storage limit: Used {used_mb:.2f}MB, Duplicate Size {duplicate_size:.2f}MB, Limit {limit_mb}MB")

    if new_total_size_mb > limit_mb:
        logger.error(f"Storage limit exceeded during duplication: {new_total_size_mb:.2f}MB > {limit_mb}MB")
        raise HTTPException(status_code=400, detail="Storage limit exceeded. Cannot duplicate file or directory.")

    try:
        # Determine the parent directory and base name
        dir_path = os.path.dirname(original_path)
        base_name = os.path.basename(original_path)

        # Create new name with auto-incrementing suffix
        name_without_ext, ext = os.path.splitext(base_name)

        # If the name already ends with _copy or _copy_N
        if name_without_ext.endswith('_copy'):
            new_name = f"{name_without_ext}_1{ext}"
        elif '_copy_' in name_without_ext:
            # Extract the existing number and increment it
            try:
                base, num_str = name_without_ext.rsplit('_copy_', 1)
                if num_str.isdigit():
                    num = int(num_str) + 1
                    new_name = f"{base}_copy_{num}{ext}"
                else:
                    new_name = f"{name_without_ext}_copy{ext}"
            except:
                new_name = f"{name_without_ext}_copy{ext}"
        else:
            new_name = f"{name_without_ext}_copy{ext}"

        # Create the new path
        new_path = os.path.join(dir_path, new_name)

        # Check if the new name already exists, and append a number if it does
        counter = 1
        while os.path.exists(new_path):
            if counter == 1:
                new_name = f"{name_without_ext}_copy_1{ext}"
            else:
                new_name = f"{name_without_ext}_copy_{counter}{ext}"
            new_path = os.path.join(dir_path, new_name)
            counter += 1

        # Get the relative paths for return value
        rel_new_path = os.path.join(os.path.dirname(duplicate_data.path), new_name)

        # Perform the copy operation based on the type
        if duplicate_data.type == "directory":
            logger.info(f"Duplicating directory from {original_path} to {new_path}")
            shutil.copytree(original_path, new_path)
        else:
            logger.info(f"Duplicating file from {original_path} to {new_path}")
            shutil.copy2(original_path, new_path)

        return {"status": "success", "new_path": rel_new_path}

    except Exception as e:
        logger.exception(f"Error duplicating {duplicate_data.type}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/run/{project_identifier}", response_model=RunCodeResponse)
async def run_code(project_identifier: str, request: RunCodeRequest):
    """Run code in a Docker container."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Validate that the file exists
        project_path = get_project_path(project_id)
        full_path = os.path.join(project_path, request.file_path)

        if not os.path.exists(full_path):
            return {"error": f"File not found: {request.file_path}", "type": "error"}

        if os.path.isdir(full_path):
            return {"error": f"Cannot execute a directory: {request.file_path}", "type": "error"}

        # Execute the code
        result = run_code_in_docker(project_id, request.file_path, request.language)

        # Determine output type
        if result.get("error"):
            output_type = "error"
        elif "warning" in result.get("output", "").lower():
            output_type = "warning"
        else:
            output_type = "success"

        return {"output": result.get("output", ""), "type": output_type}
    except Exception as e:
        return {"error": str(e), "type": "error"}

# Terminal session storage
terminal_sessions = {}  # { terminal_id: {"cwd": "/path", "history": []} }

@app.post("/api/terminal/{project_identifier}", response_model=CommandResponse)
async def terminal_command(project_identifier: str, payload: RunCommandRequest):
    """Execute a terminal command with multi-terminal session support."""
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    try:
        # Use the new terminal manager for better session handling
        result = run_terminal_command(
            project_id=project_id,
            command=payload.command,
            terminal_id=payload.terminal_id,
            working_directory=payload.working_directory
        )

        return CommandResponse(
            output=result.get("output"),
            error=result.get("error"),
            exit_code=result.get("exit_code", 0)
        )
    except Exception as e:
        logger.exception(f"Error executing terminal command: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error executing command: {str(e)}")

@app.get("/api/terminal/allowed-commands")
async def get_allowed_commands():
    """Get the list of allowed terminal commands."""
    return {"commands": sorted(list(ALLOWED_COMMANDS))}

# Multi-Terminal Management Endpoints

@app.post("/api/terminals/{project_identifier}/create")
async def create_terminal(project_identifier: str, request: CreateTerminalRequest) -> TerminalInfo:
    """Create a new terminal session for the project."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Create new terminal session
        terminal_session = terminal_manager.create_terminal(project_id, request.terminal_name)

        return TerminalInfo(**terminal_session.to_dict())
    except Exception as e:
        logger.exception(f"Error creating terminal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating terminal: {str(e)}")

@app.get("/api/terminals/{project_identifier}")
async def list_terminals(project_identifier: str) -> List[TerminalInfo]:
    """List all active terminals for the project."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get all terminals for this project
        terminals = terminal_manager.get_project_terminals(project_id)

        return [TerminalInfo(**terminal.to_dict()) for terminal in terminals]
    except Exception as e:
        logger.exception(f"Error listing terminals: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing terminals: {str(e)}")

@app.get("/api/terminals/{project_identifier}/{terminal_id}")
async def get_terminal_info(project_identifier: str, terminal_id: str) -> TerminalInfo:
    """Get information about a specific terminal."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get terminal session
        terminal_session = terminal_manager.get_terminal(terminal_id)
        if not terminal_session or terminal_session.project_id != project_id:
            raise HTTPException(status_code=404, detail="Terminal not found")

        return TerminalInfo(**terminal_session.to_dict())
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting terminal info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting terminal info: {str(e)}")

@app.delete("/api/terminals/{project_identifier}/{terminal_id}")
async def close_terminal(project_identifier: str, terminal_id: str) -> dict:
    """Close a specific terminal session."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get terminal session to verify it belongs to this project
        terminal_session = terminal_manager.get_terminal(terminal_id)
        if not terminal_session or terminal_session.project_id != project_id:
            raise HTTPException(status_code=404, detail="Terminal not found")

        # Close the terminal
        success = terminal_manager.close_terminal(terminal_id)

        if success:
            return {"message": f"Terminal {terminal_id} closed successfully"}
        else:
            raise HTTPException(status_code=404, detail="Terminal not found")
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error closing terminal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error closing terminal: {str(e)}")

@app.get("/api/terminals/{project_identifier}/{terminal_id}/history")
async def get_terminal_history(project_identifier: str, terminal_id: str) -> dict:
    """Get command history for a specific terminal."""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        # Get terminal session
        terminal_session = terminal_manager.get_terminal(terminal_id)
        if not terminal_session or terminal_session.project_id != project_id:
            raise HTTPException(status_code=404, detail="Terminal not found")

        return {
            "terminal_id": terminal_id,
            "history": terminal_session.command_history
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting terminal history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting terminal history: {str(e)}")

# Add a root preview endpoint without /api prefix for compatibility
@app.get("/preview/{project_identifier}/", response_class=Response)
@app.get("/preview/{project_identifier}/{path:path}", response_class=Response)
async def preview_project_root(project_identifier: str, path: str = ""):
    """
    Root preview endpoint for compatibility with Django redirect.
    """
    return await preview_file(project_identifier, path)

@app.get("/api/preview/{project_identifier}/", response_class=Response)
@app.get("/api/preview/{project_identifier}/{path:path}", response_class=Response)
async def preview_file(project_identifier: str, path: str = ""):
    """
    Serve project files for preview. Similar to VS Code's Live Server functionality.

    This endpoint serves HTML files and other static assets like CSS, JS, and images
    directly from the project directory.

    Args:
        project_identifier: Can be either project ID (integer) or project hash (string)
        path: File path within the project
    """
    # Convert hash to project ID if needed
    project_id = await get_project_id_from_identifier(project_identifier)
    if not project_id:
        raise HTTPException(status_code=404, detail="Project not found")

    project_path = get_project_path(project_id)

    # If no specific path provided, default to index.html
    if not path:
        path = "index.html"

    # Get the full file path
    file_path = os.path.join(project_path, path)

    # Security check to prevent directory traversal
    if not os.path.abspath(file_path).startswith(os.path.abspath(project_path)):
        logger.warning(f"Access denied - attempted path traversal: {file_path}")
        raise HTTPException(status_code=403, detail="Access denied")

    # Check if file exists
    if not os.path.exists(file_path):
        # Check if specifically looking for index.html or any HTML file
        if path == "index.html" or path.endswith('.html'):
            # Return a helpful error message for missing HTML files
            error_message = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Preview Error</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        max-width: 800px;
                        margin: 2rem auto;
                        padding: 0 1rem;
                        background-color: #f8f9fa;
                    }}
                    .error-container {{
                        background-color: #ffeeee;
                        border-left: 4px solid #ff5555;
                        padding: 1rem;
                        border-radius: 4px;
                        margin: 1rem 0;
                    }}
                    .info-container {{
                        background-color: #e7f3ff;
                        border-left: 4px solid #007bff;
                        padding: 1rem;
                        border-radius: 4px;
                        margin: 1rem 0;
                    }}
                    code {{
                        background-color: #f5f5f5;
                        padding: 0.2rem 0.4rem;
                        border-radius: 3px;
                        font-family: monospace;
                    }}
                    .btn {{
                        display: inline-block;
                        padding: 0.5rem 1rem;
                        background-color: #007bff;
                        color: white;
                        text-decoration: none;
                        border-radius: 4px;
                        margin: 0.5rem 0.5rem 0.5rem 0;
                    }}
                    .btn:hover {{
                        background-color: #0056b3;
                    }}
                </style>
            </head>
            <body>
                <h1>HTML File Not Found</h1>
                <div class="error-container">
                    <p>Could not find the HTML file: <code>{path}</code></p>
                    <p>Make sure the file exists in your project directory.</p>
                </div>
                <div class="info-container">
                    <h3>Quick Actions:</h3>
                    <p>• Create the file in the editor and save it</p>
                    <p>• Make sure the file name is spelled correctly</p>
                    <p>• Check that the file is in the correct directory</p>
                </div>
                <p><strong>Project ID:</strong> {project_id}</p>
                <p><strong>Expected file path:</strong> <code>{file_path}</code></p>
                <hr>
                <a href="javascript:history.back()" class="btn">← Go back to editor</a>
                <a href="javascript:window.close()" class="btn">Close tab</a>
            </body>
            </html>
            """
            return HTMLResponse(content=error_message, status_code=404)
        else:
            # For other files, return a standard 404
            raise HTTPException(status_code=404, detail=f"File not found: {path}")

    # Determine content type based on file extension
    content_type = None
    if file_path.endswith('.html'):
        content_type = 'text/html'
    elif file_path.endswith('.css'):
        content_type = 'text/css'
    elif file_path.endswith('.js'):
        content_type = 'application/javascript'
    elif file_path.endswith('.json'):
        content_type = 'application/json'
    elif file_path.endswith('.png'):
        content_type = 'image/png'
    elif file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
        content_type = 'image/jpeg'
    elif file_path.endswith('.gif'):
        content_type = 'image/gif'
    elif file_path.endswith('.svg'):
        content_type = 'image/svg+xml'

    # Serve the file - using FileResponse for binary files is more efficient
    if content_type and content_type.startswith('image/'):
        return FileResponse(file_path, media_type=content_type)

    # For text-based files, read and return the content
    try:
        with open(file_path, 'rb') as f:
            content = f.read()

        # Return the file with proper content type
        return Response(content=content, media_type=content_type or 'application/octet-stream')
    except Exception as e:
        logger.exception(f"Error serving file {file_path}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stream/{project_id}")
def stream_code_execution(project_id: str):
    def generate():
        # Adjust the command dynamically based on the project
        cmd = ["python", f"projects/{project_id}/main.py"]
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)

        for line in process.stdout:
            yield f"data: {line.strip()}\n\n"
        yield "data: >>>END<<<\n\n"

    return StreamingResponse(generate(), media_type="text/event-stream")



@app.get("/")
async def root():
    return JSONResponse(
        content={"message": "FastAPI service is running"},
        headers={"Access-Control-Allow-Origin": "*"}
    )
# Define the root directory for extensions
EXTENSIONS_DIR = Path("extensions")

# Serve static files for extensions
app.mount("/extensions", StaticFiles(directory=EXTENSIONS_DIR), name="extensions")

@app.get("/extensions/{ext_name}/manifest.json")
async def get_manifest(ext_name: str):
    manifest_path = EXTENSIONS_DIR / ext_name / "manifest.json"
    if not manifest_path.exists():
        raise HTTPException(status_code=404, detail="Manifest not found")
    return JSONResponse(content=manifest_path.read_text())

@app.get("/extensions/{ext_name}/{filename}")
async def get_extension_file(ext_name: str, filename: str):
    file_path = EXTENSIONS_DIR / ext_name / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    return FileResponse(file_path)

from fastapi.staticfiles import StaticFiles

# Mount the extensions directory to serve static files
app.mount("/extensions", StaticFiles(directory="extensions"), name="extensions")

@app.get("/extensions/manifest.json")
async def get_extensions_manifest():
    """Serve the manifest file listing all available extensions."""
    manifest_path = EXTENSIONS_DIR / "manifest.json"
    if not manifest_path.exists():
        raise HTTPException(status_code=404, detail="Manifest not found")
    return JSONResponse(content=manifest_path.read_text())

# WebSocket-based terminal session management
@app.websocket("/ws/terminal/{terminal_id}")
async def terminal_websocket(websocket: WebSocket, terminal_id: str):
    await websocket.accept()
    if terminal_id not in terminal_sessions:
        terminal_sessions[terminal_id] = {"history": [], "cwd": "/"}

    try:
        while True:
            data = await websocket.receive_text()
            command = data.strip()

            # Execute the command (mocked for now)
            output = f"Executed: {command} in terminal {terminal_id}"

            # Update session history
            terminal_sessions[terminal_id]["history"].append(command)

            # Send the output back to the client
            await websocket.send_text(output)
    except WebSocketDisconnect:
        print(f"Terminal {terminal_id} disconnected.")
        del terminal_sessions[terminal_id]

clients = {}

@app.websocket("/ws/terminal/{terminal_id}")
async def websocket_terminal(websocket: WebSocket, terminal_id: str):
    await websocket.accept()
    clients[terminal_id] = websocket

    # Launch shell
    process = await asyncio.create_subprocess_shell(
        "/bin/bash",
        stdin=asyncio.subprocess.PIPE,
        stdout=asyncio.subprocess.PIPE,
        stderr=asyncio.subprocess.STDOUT
    )

    async def read_output():
        while True:
            line = await process.stdout.read(1024)
            if not line:
                break
            await websocket.send_json({"type": "output", "data": line.decode()})

    output_task = asyncio.create_task(read_output())

    try:
        while True:
            data = await websocket.receive_json()
            if data["type"] == "input":
                if process.stdin:
                    process.stdin.write(data["data"].encode())
                    await process.stdin.drain()
    except WebSocketDisconnect:
        output_task.cancel()
        if process.stdin:
            process.stdin.close()
        await process.wait()

# Terminal clients dictionary to track active websocket connections
terminal_clients = {}

# YJS Room Management for Collaborative Editing
class YjsRoomManager:
    def __init__(self):
        self.rooms: Dict[str, Dict[str, WebSocket]] = {}
        self.room_documents: Dict[str, bytes] = {}  # Store YJS document state per room

    async def join_room(self, room_name: str, client_id: str, websocket: WebSocket):
        """Add a client to a YJS room"""
        if room_name not in self.rooms:
            self.rooms[room_name] = {}
            self.room_documents[room_name] = b''  # Initialize empty document

        self.rooms[room_name][client_id] = websocket
        logger.info(f"Client {client_id} joined YJS room: {room_name}")

        # Send current document state to the new client if it exists
        if self.room_documents[room_name]:
            await self.send_to_client(websocket, {
                'type': 'sync',
                'data': base64.b64encode(self.room_documents[room_name]).decode()
            })

    async def leave_room(self, room_name: str, client_id: str):
        """Remove a client from a YJS room"""
        if room_name in self.rooms and client_id in self.rooms[room_name]:
            del self.rooms[room_name][client_id]
            logger.info(f"Client {client_id} left YJS room: {room_name}")

            # Clean up empty rooms
            if not self.rooms[room_name]:
                del self.rooms[room_name]
                if room_name in self.room_documents:
                    del self.room_documents[room_name]
                logger.info(f"Cleaned up empty YJS room: {room_name}")

    async def broadcast_to_room(self, room_name: str, message: dict, exclude_client: str = None):
        """Broadcast a message to all clients in a room except the sender"""
        if room_name not in self.rooms:
            return

        disconnected_clients = []
        for client_id, websocket in self.rooms[room_name].items():
            if client_id != exclude_client:
                try:
                    await self.send_to_client(websocket, message)
                except Exception as e:
                    logger.error(f"Error sending to client {client_id}: {e}")
                    disconnected_clients.append(client_id)

        # Clean up disconnected clients
        for client_id in disconnected_clients:
            await self.leave_room(room_name, client_id)

    async def send_to_client(self, websocket: WebSocket, message: dict):
        """Send a message to a specific client"""
        try:
            if isinstance(message.get('data'), bytes):
                # Send binary data
                await websocket.send_bytes(message['data'])
            else:
                # Send JSON message
                await websocket.send_json(message)
        except Exception as e:
            logger.error(f"Error sending message to client: {e}")
            raise

    def update_document(self, room_name: str, update_data: bytes):
        """Update the document state for a room"""
        if room_name not in self.room_documents:
            self.room_documents[room_name] = b''

        # Store the update data (YJS handles merging on the client side)
        # We keep the latest state for new clients joining
        if len(update_data) > 0:
            self.room_documents[room_name] = update_data
            logger.debug(f"Updated document for room {room_name}, size: {len(update_data)} bytes")

    def get_room_info(self):
        """Get information about all active rooms for debugging"""
        info = {}
        for room_name, clients in self.rooms.items():
            info[room_name] = {
                'client_count': len(clients),
                'clients': list(clients.keys()),
                'document_size': len(self.room_documents.get(room_name, b''))
            }
        return info

# Global YJS room manager
yjs_manager = YjsRoomManager()

@app.get("/api/yjs/rooms")
async def get_yjs_rooms():
    """Debug endpoint to check YJS room status"""
    return {
        "status": "YJS WebSocket server is running",
        "rooms": yjs_manager.get_room_info(),
        "total_rooms": len(yjs_manager.rooms)
    }

@app.websocket("/ws/yjs-room/{room_name}")
async def yjs_websocket(websocket: WebSocket, room_name: str):
    """
    YJS WebSocket endpoint for collaborative editing.
    Handles YJS document synchronization between multiple clients.
    Improved implementation with proper binary/text handling.
    """
    await websocket.accept()
    client_id = str(uuid.uuid4())

    logger.info(f"YJS WebSocket connection accepted for room: {room_name}, client: {client_id}")

    try:
        # Join the YJS room
        await yjs_manager.join_room(room_name, client_id, websocket)

        # Send welcome message
        await websocket.send_json({
            'type': 'connected',
            'room': room_name,
            'client_id': client_id
        })

        # Main message loop with improved error handling
        while True:
            try:
                # Wait for any message (binary or text)
                message = await websocket.receive()

                if message["type"] == "websocket.receive":
                    if "bytes" in message:
                        # Handle binary YJS update data
                        data = message["bytes"]
                        logger.debug(f"Received binary YJS update in room {room_name} from client {client_id}, size: {len(data)} bytes")

                        # Store the document update
                        yjs_manager.update_document(room_name, data)

                        # Broadcast the update to all other clients in the room
                        await yjs_manager.broadcast_to_room(
                            room_name,
                            {'type': 'update', 'data': data},
                            exclude_client=client_id
                        )

                        logger.debug(f"Broadcasted YJS update in room {room_name} from client {client_id}")

                    elif "text" in message:
                        # Handle text messages (control messages)
                        try:
                            text_data = message["text"]
                            json_message = json.loads(text_data)

                            if json_message.get('type') == 'join':
                                # Handle explicit join message
                                logger.info(f"Client {client_id} explicitly joined room: {room_name}")
                                await websocket.send_json({
                                    'type': 'joined',
                                    'room': room_name,
                                    'client_id': client_id
                                })
                            elif json_message.get('type') == 'ping':
                                # Handle ping/pong for connection keepalive
                                await websocket.send_json({'type': 'pong'})
                            elif json_message.get('type') == 'sync-request':
                                # Handle sync request - send current document state
                                if room_name in yjs_manager.room_documents and yjs_manager.room_documents[room_name]:
                                    await websocket.send_bytes(yjs_manager.room_documents[room_name])
                                    logger.debug(f"Sent sync data to client {client_id} in room {room_name}")
                            else:
                                logger.warning(f"Unknown message type from client {client_id}: {json_message}")

                        except json.JSONDecodeError:
                            logger.error(f"Invalid JSON from client {client_id}: {text_data}")
                        except Exception as e:
                            logger.error(f"Error processing text message from client {client_id}: {e}")

                elif message["type"] == "websocket.disconnect":
                    logger.info(f"YJS WebSocket disconnect message received for client: {client_id}")
                    break

            except WebSocketDisconnect:
                logger.info(f"YJS WebSocket disconnected for client: {client_id}")
                break
            except Exception as e:
                logger.error(f"Error in YJS message loop for client {client_id}: {e}")
                # Don't break on individual message errors, continue listening
                continue

    except WebSocketDisconnect:
        logger.info(f"YJS WebSocket disconnected for client: {client_id}")
    except Exception as e:
        logger.error(f"Error in YJS WebSocket for client {client_id}: {e}")
    finally:
        # Clean up: remove client from room
        await yjs_manager.leave_room(room_name, client_id)
        logger.info(f"YJS WebSocket connection closed for client: {client_id}")

@app.websocket("/ws/terminal/{terminal_id}")
async def websocket_terminal(websocket: WebSocket, terminal_id: str):
    """
    WebSocket endpoint for terminal connections.
    Each terminal instance gets its own process and bi-directional communication.
    """
    await websocket.accept()
    logger.info(f"Terminal WebSocket connection accepted for terminal: {terminal_id}")

    # Store the websocket connection
    terminal_clients[terminal_id] = websocket

    # Determine the shell command based on OS
    shell_cmd = "cmd.exe" if sys.platform == "win32" else "/bin/bash"
    shell_args = ["/c"] if sys.platform == "win32" else []

    # Get the project path (if terminal_id contains a project_id)
    project_id = None
    working_dir = None

    if '-' in terminal_id:
        # Format could be like 'project_id-timestamp'
        parts = terminal_id.split('-', 1)
        if len(parts) == 2:
            project_id = parts[0]
            try:
                working_dir = get_project_path(project_id)
                logger.info(f"Setting terminal working directory to project: {working_dir}")
            except Exception as e:
                logger.error(f"Error getting project path: {str(e)}")

    try:
        # Create subprocess
        if sys.platform == "win32":
            # Windows specific settings
            process = await asyncio.create_subprocess_exec(
                shell_cmd, *shell_args,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                cwd=working_dir
            )
        else:
            # Linux/Unix specific settings
            process = await asyncio.create_subprocess_shell(
                shell_cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT,
                cwd=working_dir
            )

        logger.info(f"Started terminal process for {terminal_id}")

        # Task to read output from the process and send it to the WebSocket
        async def read_output():
            try:
                while True:
                    line = await process.stdout.read(1024)
                    if not line:
                        break
                    # Send the output to the WebSocket client
                    try:
                        await websocket.send_json({
                            "type": "output",
                            "data": line.decode('utf-8', errors='replace')
                        })
                    except Exception as e:
                        logger.error(f"Error sending terminal output to WebSocket: {str(e)}")
                        break
            except Exception as e:
                logger.error(f"Error reading terminal output: {str(e)}")
            finally:
                logger.info(f"Terminal output reader closed for {terminal_id}")

        # Start the output reader task
        output_task = asyncio.create_task(read_output())

        try:
            # Main loop to receive input from the WebSocket
            while True:
                try:
                    # Wait for input from the WebSocket
                    data = await websocket.receive_json()

                    if data["type"] == "input":
                        # Write the input to the process stdin
                        if process.stdin:
                            # Convert the input to bytes and write it to stdin
                            input_bytes = data["data"].encode('utf-8')
                            process.stdin.write(input_bytes)
                            await process.stdin.drain()
                except WebSocketDisconnect:
                    logger.info(f"WebSocket disconnected for terminal: {terminal_id}")
                    break
                except Exception as e:
                    logger.error(f"Error in terminal WebSocket loop: {str(e)}")
                    break
        finally:
            # Clean up when the WebSocket is closed
            output_task.cancel()
            try:
                if process.stdin:
                    process.stdin.close()
            except:
                pass

            # Terminate the process
            try:
                process.terminate()
                await process.wait()
            except:
                pass

            # Remove the client from the dictionary
            if terminal_id in terminal_clients:
                del terminal_clients[terminal_id]

            logger.info(f"Terminal connection closed for: {terminal_id}")

    except Exception as e:
        logger.error(f"Error setting up terminal: {str(e)}")
        await websocket.send_json({
            "type": "output",
            "data": f"\r\n\x1b[31mTerminal error: {str(e)}\x1b[0m\r\n"
        })
        if terminal_id in terminal_clients:
            del terminal_clients[terminal_id]


# Base directory for project storage
PROJECT_STORAGE_BASE = "/app/projects"

# Ensure the base directory exists
os.makedirs(PROJECT_STORAGE_BASE, exist_ok=True)

@app.post("/projects/{project_id}/create")
def create_project_folder(project_id: str):
    """Create a folder for the project."""
    project_path = os.path.join(PROJECT_STORAGE_BASE, project_id)
    if not os.path.exists(project_path):
        os.makedirs(project_path)
        return {"message": f"Folder created for project {project_id}"}
    else:
        raise HTTPException(status_code=400, detail="Project folder already exists.")

@app.get("/usage/{project_id}")
def get_project_storage_usage(project_id: str):
    """Get storage usage for a project."""
    project_path = os.path.join(PROJECT_STORAGE_BASE, project_id)
    if not os.path.exists(project_path):
        raise HTTPException(status_code=404, detail="Project folder not found.")

    # Calculate folder size
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(project_path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            total_size += os.path.getsize(fp)

    used_mb = total_size / (1024 * 1024)  # Convert bytes to MB
    limit_mb = 3072  # 3GB limit

    return {"used_mb": used_mb, "limit_mb": limit_mb}


# ===== VERSION CONTROL ENDPOINTS =====

class CommitRequest(BaseModel):
    message: str
    branch_name: str = "main"
    author_username: str
    files: List[dict]  # List of {path: str, content: str} objects

class CommitResponse(BaseModel):
    commit_hash: str
    message: str
    created_at: str
    files_changed: int

class HistoryResponse(BaseModel):
    commits: List[dict]
    total_count: int

class FileVersionResponse(BaseModel):
    content: str
    commit_hash: str
    file_path: str
    created_at: str

@app.post("/api/version-control/{project_identifier}/commit", response_model=CommitResponse)
async def create_commit(project_identifier: str, commit_data: CommitRequest):
    """Create a new commit with the current project state"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)

        # Generate commit hash
        import hashlib
        import uuid
        unique_string = f"{uuid.uuid4()}-{project_id}-{commit_data.message}-{datetime.now().isoformat()}"
        commit_hash = hashlib.sha256(unique_string.encode()).hexdigest()

        # Create commit metadata
        commit_info = {
            "commit_hash": commit_hash,
            "message": commit_data.message,
            "branch_name": commit_data.branch_name,
            "author_username": commit_data.author_username,
            "created_at": datetime.now().isoformat(),
            "files_changed": len(commit_data.files),
            "files": []
        }

        # Process each file
        for file_info in commit_data.files:
            file_path = file_info.get("path", "")
            content = file_info.get("content", "")

            # Calculate file metadata
            content_hash = hashlib.sha256(content.encode()).hexdigest()
            file_size = len(content.encode('utf-8'))

            commit_info["files"].append({
                "file_path": file_path,
                "content": content,
                "content_hash": content_hash,
                "file_size": file_size,
                "is_deleted": False
            })

        # Save commit to version control directory
        vc_dir = os.path.join(project_path, ".forgex_vc")
        os.makedirs(vc_dir, exist_ok=True)

        commit_file = os.path.join(vc_dir, f"{commit_hash}.json")
        with open(commit_file, 'w', encoding='utf-8') as f:
            import json
            json.dump(commit_info, f, indent=2, ensure_ascii=False)

        # Update refs (branch pointers)
        refs_dir = os.path.join(vc_dir, "refs")
        os.makedirs(refs_dir, exist_ok=True)

        branch_file = os.path.join(refs_dir, f"{commit_data.branch_name}.txt")
        with open(branch_file, 'w') as f:
            f.write(commit_hash)

        logger.info(f"Created commit {commit_hash[:8]} for project {project_id}")

        return CommitResponse(
            commit_hash=commit_hash,
            message=commit_data.message,
            created_at=commit_info["created_at"],
            files_changed=len(commit_data.files)
        )

    except Exception as e:
        logger.exception(f"Error creating commit: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create commit: {str(e)}")

@app.get("/api/version-control/{project_identifier}/history", response_model=HistoryResponse)
async def get_commit_history(project_identifier: str, branch: str = "main", limit: int = 50, offset: int = 0):
    """Get commit history for a project"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")

        if not os.path.exists(vc_dir):
            return HistoryResponse(commits=[], total_count=0)

        # Get all commit files
        commits = []
        commit_files = [f for f in os.listdir(vc_dir) if f.endswith('.json')]

        import json
        for commit_file in commit_files:
            try:
                with open(os.path.join(vc_dir, commit_file), 'r', encoding='utf-8') as f:
                    commit_data = json.load(f)

                    # Filter by branch if specified
                    if branch and commit_data.get("branch_name") != branch:
                        continue

                    commits.append({
                        "commit_hash": commit_data["commit_hash"],
                        "message": commit_data["message"],
                        "author_username": commit_data["author_username"],
                        "branch_name": commit_data["branch_name"],
                        "created_at": commit_data["created_at"],
                        "files_changed": commit_data["files_changed"]
                    })
            except Exception as e:
                logger.warning(f"Failed to read commit file {commit_file}: {e}")
                continue

        # Sort by creation time (newest first)
        commits.sort(key=lambda x: x["created_at"], reverse=True)

        # Apply pagination
        total_count = len(commits)
        commits = commits[offset:offset + limit]

        return HistoryResponse(commits=commits, total_count=total_count)

    except Exception as e:
        logger.exception(f"Error getting commit history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get commit history: {str(e)}")

@app.get("/api/version-control/{project_identifier}/file/{commit_hash}", response_model=FileVersionResponse)
async def get_file_version(project_identifier: str, commit_hash: str, file_path: str):
    """Get a specific file version from a commit"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")

        commit_file = os.path.join(vc_dir, f"{commit_hash}.json")
        if not os.path.exists(commit_file):
            raise HTTPException(status_code=404, detail="Commit not found")

        import json
        with open(commit_file, 'r', encoding='utf-8') as f:
            commit_data = json.load(f)

        # Find the requested file in the commit
        for file_info in commit_data.get("files", []):
            if file_info["file_path"] == file_path:
                return FileVersionResponse(
                    content=file_info["content"],
                    commit_hash=commit_hash,
                    file_path=file_path,
                    created_at=commit_data["created_at"]
                )

        raise HTTPException(status_code=404, detail="File not found in commit")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting file version: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get file version: {str(e)}")

@app.post("/api/version-control/{project_identifier}/restore/{commit_hash}")
async def restore_commit(project_identifier: str, commit_hash: str):
    """Restore project to a specific commit"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")

        commit_file = os.path.join(vc_dir, f"{commit_hash}.json")
        if not os.path.exists(commit_file):
            raise HTTPException(status_code=404, detail="Commit not found")

        import json
        with open(commit_file, 'r', encoding='utf-8') as f:
            commit_data = json.load(f)

        # STEP 1: Get list of files that should exist after restore
        commit_files = set()
        for file_info in commit_data.get("files", []):
            if not file_info.get("is_deleted", False):
                commit_files.add(file_info["file_path"])

        # STEP 2: Get list of current files in project (excluding .forgex_vc)
        current_files = set()
        ignored_names = {'.forgex_vc', '.git', 'node_modules', '__pycache__', '.DS_Store', 'Thumbs.db', '.vscode', '.idea'}

        for root, dirs, files in os.walk(project_path):
            # Remove ignored directories from traversal
            dirs[:] = [d for d in dirs if d not in ignored_names]

            for file in files:
                if file not in ignored_names:
                    rel_path = os.path.relpath(os.path.join(root, file), project_path)
                    # Normalize path separators for cross-platform compatibility
                    rel_path = rel_path.replace('\\', '/')
                    # Skip files that start with . (hidden files) for safety
                    if not rel_path.startswith('.') and rel_path != '.':
                        current_files.add(rel_path)

        logger.info(f"Current files in project: {len(current_files)}")
        logger.info(f"Files in commit: {len(commit_files)}")

        # STEP 3: Delete files that exist now but weren't in the commit
        files_to_delete = current_files - commit_files
        deleted_files = []

        for file_path in files_to_delete:
            try:
                # Additional safety checks
                if file_path.startswith('.') or file_path in ignored_names:
                    logger.info(f"Skipping deletion of protected file: {file_path}")
                    continue

                full_file_path = os.path.join(project_path, file_path)

                # Ensure the file is within the project directory (security check)
                if not os.path.abspath(full_file_path).startswith(os.path.abspath(project_path)):
                    logger.warning(f"Skipping deletion of file outside project: {file_path}")
                    continue

                if os.path.exists(full_file_path) and os.path.isfile(full_file_path):
                    os.remove(full_file_path)
                    logger.info(f"Deleted extra file: {file_path}")
                    deleted_files.append(file_path)
            except Exception as delete_error:
                logger.error(f"Failed to delete file {file_path}: {delete_error}")

        # STEP 4: Restore all files from the commit
        restored_files = []
        for file_info in commit_data.get("files", []):
            if file_info.get("is_deleted", False):
                continue  # Skip deleted files

            file_path = file_info["file_path"]
            content = file_info["content"]

            logger.info(f"Restoring file: {file_path}")

            # Create full file path
            full_file_path = os.path.join(project_path, file_path)

            # Create directory if it doesn't exist (only if not empty path)
            file_dir = os.path.dirname(full_file_path)
            if file_dir and file_dir != project_path:
                logger.info(f"Creating directory: {file_dir}")
                os.makedirs(file_dir, exist_ok=True)

            # Write file content
            try:
                with open(full_file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                logger.info(f"Successfully restored file: {file_path} ({len(content)} chars)")
                restored_files.append(file_path)
            except Exception as file_error:
                logger.error(f"Failed to restore file {file_path}: {file_error}")
                # Continue with other files even if one fails

        # STEP 5: Clean up empty directories
        try:
            for root, dirs, files in os.walk(project_path, topdown=False):
                # Skip .forgex_vc directory
                if '.forgex_vc' in root:
                    continue

                # Remove empty directories (except project root)
                if not files and not dirs and root != project_path:
                    try:
                        os.rmdir(root)
                        logger.info(f"Removed empty directory: {os.path.relpath(root, project_path)}")
                    except OSError:
                        pass  # Directory not empty or permission issue
        except Exception as cleanup_error:
            logger.warning(f"Error during directory cleanup: {cleanup_error}")

        logger.info(f"Restore complete - Restored: {len(restored_files)}, Deleted: {len(deleted_files)} files from commit {commit_hash[:8]} for project {project_id}")

        return {
            "status": "success",
            "message": f"Restored to commit {commit_hash[:8]}",
            "files_restored": len(restored_files),
            "files_deleted": len(deleted_files),
            "commit_hash": commit_hash,
            "details": {
                "restored_files": restored_files,
                "deleted_files": deleted_files
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error restoring commit: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to restore commit: {str(e)}")

@app.get("/api/version-control/{project_identifier}/branches")
async def get_branches(project_identifier: str):
    """Get all branches for a project"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")
        refs_dir = os.path.join(vc_dir, "refs")

        branches = []
        if os.path.exists(refs_dir):
            for branch_file in os.listdir(refs_dir):
                if branch_file.endswith('.txt'):
                    branch_name = branch_file[:-4]  # Remove .txt extension
                    with open(os.path.join(refs_dir, branch_file), 'r') as f:
                        head_commit = f.read().strip()

                    branches.append({
                        "name": branch_name,
                        "head_commit": head_commit,
                        "is_default": branch_name == "main"
                    })

        # If no branches exist, create default main branch
        if not branches:
            branches.append({
                "name": "main",
                "head_commit": None,
                "is_default": True
            })

        return {"branches": branches}

    except Exception as e:
        logger.exception(f"Error getting branches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get branches: {str(e)}")

@app.get("/api/version-control/{project_identifier}/debug")
async def debug_version_control(project_identifier: str):
    """Debug endpoint to check version control status"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")

        debug_info = {
            "project_id": project_id,
            "project_path": project_path,
            "vc_dir_exists": os.path.exists(vc_dir),
            "commits": [],
            "branches": [],
            "project_files": [],
            "current_file_contents": {}
        }

        # List commits with details
        if os.path.exists(vc_dir):
            commit_files = [f for f in os.listdir(vc_dir) if f.endswith('.json')]
            debug_info["commits"] = []

            for commit_file in commit_files[:3]:  # Show details for last 3 commits
                try:
                    with open(os.path.join(vc_dir, commit_file), 'r', encoding='utf-8') as f:
                        commit_data = json.load(f)

                    commit_info = {
                        "file": commit_file,
                        "hash": commit_data.get("commit_hash", "")[:8],
                        "message": commit_data.get("message", ""),
                        "files_count": len(commit_data.get("files", [])),
                        "files": []
                    }

                    # Show first few files in commit
                    for file_info in commit_data.get("files", [])[:3]:
                        commit_info["files"].append({
                            "path": file_info.get("file_path", ""),
                            "size": len(file_info.get("content", "")),
                            "content_preview": file_info.get("content", "")[:100] + "..." if len(file_info.get("content", "")) > 100 else file_info.get("content", "")
                        })

                    debug_info["commits"].append(commit_info)
                except Exception as e:
                    debug_info["commits"].append({"file": commit_file, "error": str(e)})

            # List branches
            refs_dir = os.path.join(vc_dir, "refs")
            if os.path.exists(refs_dir):
                branch_files = [f[:-4] for f in os.listdir(refs_dir) if f.endswith('.txt')]
                debug_info["branches"] = branch_files

        # List current project files with content preview
        if os.path.exists(project_path):
            for root, dirs, files in os.walk(project_path):
                # Skip .forgex_vc directory
                if '.forgex_vc' in dirs:
                    dirs.remove('.forgex_vc')

                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), project_path)
                    debug_info["project_files"].append(rel_path)

                    # Show content preview for first few files
                    if len(debug_info["current_file_contents"]) < 3:
                        try:
                            full_path = os.path.join(root, file)
                            with open(full_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            debug_info["current_file_contents"][rel_path] = {
                                "size": len(content),
                                "preview": content[:200] + "..." if len(content) > 200 else content
                            }
                        except Exception:
                            debug_info["current_file_contents"][rel_path] = {"error": "Could not read file"}

        return debug_info

    except Exception as e:
        logger.exception(f"Error in debug endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Debug failed: {str(e)}")

@app.post("/api/version-control/{project_identifier}/test-restore")
async def test_restore_functionality(project_identifier: str):
    """Test endpoint to verify restore functionality"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)

        # Create some test files to verify restore works
        test_files = {
            "test_restore.py": "print('This is a test file for restore functionality')",
            "temp_file.txt": "This file should be deleted during restore",
            "subfolder/nested_file.py": "print('Nested file for testing')"
        }

        for file_path, content in test_files.items():
            full_path = os.path.join(project_path, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(content)

        return {
            "status": "success",
            "message": "Test files created for restore testing",
            "files_created": list(test_files.keys()),
            "note": "You can now create a commit and test the restore functionality"
        }

    except Exception as e:
        logger.exception(f"Error in test restore endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")

@app.get("/api/version-control/{project_identifier}/compare/{commit_hash}")
async def compare_commit_with_current(project_identifier: str, commit_hash: str):
    """Compare a commit with current project state"""
    try:
        # Convert hash to project ID if needed
        project_id = await get_project_id_from_identifier(project_identifier)
        if not project_id:
            raise HTTPException(status_code=404, detail="Project not found")

        project_path = get_project_path(project_id)
        vc_dir = os.path.join(project_path, ".forgex_vc")

        commit_file = os.path.join(vc_dir, f"{commit_hash}.json")
        if not os.path.exists(commit_file):
            raise HTTPException(status_code=404, detail="Commit not found")

        # Load commit data
        with open(commit_file, 'r', encoding='utf-8') as f:
            commit_data = json.load(f)

        # Get current files
        current_files = {}
        for root, dirs, files in os.walk(project_path):
            if '.forgex_vc' in dirs:
                dirs.remove('.forgex_vc')

            for file in files:
                rel_path = os.path.relpath(os.path.join(root, file), project_path)
                rel_path = rel_path.replace('\\', '/')

                try:
                    with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                        content = f.read()
                    current_files[rel_path] = content
                except Exception:
                    current_files[rel_path] = "[Could not read file]"

        # Compare
        comparison = {
            "commit_hash": commit_hash,
            "commit_message": commit_data.get("message", ""),
            "files_in_commit": {},
            "files_current": current_files,
            "differences": []
        }

        # Get files from commit
        for file_info in commit_data.get("files", []):
            path = file_info["file_path"]
            content = file_info["content"]
            comparison["files_in_commit"][path] = content

            # Compare with current
            if path in current_files:
                if current_files[path] != content:
                    comparison["differences"].append({
                        "file": path,
                        "type": "modified",
                        "commit_size": len(content),
                        "current_size": len(current_files[path]),
                        "commit_preview": content[:100] + "..." if len(content) > 100 else content,
                        "current_preview": current_files[path][:100] + "..." if len(current_files[path]) > 100 else current_files[path]
                    })
            else:
                comparison["differences"].append({
                    "file": path,
                    "type": "deleted_from_current",
                    "commit_size": len(content),
                    "commit_preview": content[:100] + "..." if len(content) > 100 else content
                })

        # Check for new files
        for path in current_files:
            if path not in comparison["files_in_commit"]:
                comparison["differences"].append({
                    "file": path,
                    "type": "added_to_current",
                    "current_size": len(current_files[path]),
                    "current_preview": current_files[path][:100] + "..." if len(current_files[path]) > 100 else current_files[path]
                })

        return comparison

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error comparing commit: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to compare: {str(e)}")

logger = logging.getLogger("websocket")

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        self.locks: Dict[str, asyncio.Lock] = {}

    async def connect(self, websocket: WebSocket, project_id: str, client_id: str):
        """Register a new WebSocket connection"""
        if project_id not in self.active_connections:
            self.active_connections[project_id] = {}
            self.locks[project_id] = asyncio.Lock()

        async with self.locks[project_id]:
            self.active_connections[project_id][client_id] = websocket

    async def disconnect(self, project_id: str, client_id: str):
        """Remove a WebSocket connection"""
        if project_id in self.active_connections and client_id in self.active_connections[project_id]:
            async with self.locks.get(project_id, asyncio.Lock()):
                del self.active_connections[project_id][client_id]
                if not self.active_connections[project_id]:
                    del self.active_connections[project_id]
                    del self.locks[project_id]

    async def broadcast(self, message: dict, project_id: str, sender_id: str = None):
        """Send message to all clients in a project except sender"""
        if project_id not in self.active_connections:
            return

        async with self.locks.get(project_id, asyncio.Lock()):
            for client_id, connection in list(self.active_connections[project_id].items()):
                if client_id != sender_id:
                    try:
                        await connection.send_json(message)
                    except Exception as e:
                        logger.error(f"Failed to send to client {client_id}: {str(e)}")
                        self.disconnect(project_id, client_id)

manager = ConnectionManager()

@app.websocket("/ws/collaborate/{project_id}")
async def websocket_endpoint(websocket: WebSocket, project_id: str):
    await websocket.accept()
    client_id = str(uuid.uuid4())

    # Register client
    await manager.connect(websocket, project_id, client_id)

    try:
        # Get initial content from file
        project_path = get_project_path(project_id)
        main_file = os.path.join(project_path, "main.py")
        initial_content = ""

        if os.path.exists(main_file):
            with open(main_file, 'r') as f:
                initial_content = f.read()

        # Send initialization message with current content
        await websocket.send_json({
            "type": "init",
            "content": initial_content,
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        })

        # Notify others about new connection
        await manager.broadcast({
            "type": "user_connected",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }, project_id, client_id)

        while True:
            # Wait for messages from client
            data = await websocket.receive_json()

            if data.get("type") == "code":
                # Save content to file
                content = data.get("content", "")
                with open(main_file, 'w') as f:
                    f.write(content)

                # Broadcast update to all other clients
                await manager.broadcast({
                    "type": "code_update",
                    "content": content,
                    "sender_id": client_id,
                    "timestamp": datetime.now().isoformat()
                }, project_id, client_id)

            elif data.get("type") == "cursor_update":
                # Broadcast cursor position to others
                await manager.broadcast({
                    "type": "cursor_update",
                    "position": data.get("position"),
                    "sender_id": client_id,
                    "timestamp": datetime.now().isoformat()
                }, project_id, client_id)

    except WebSocketDisconnect:
        logger.info(f"Client {client_id} disconnected")
        # Notify others about disconnection
        await manager.broadcast({
            "type": "user_disconnected",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }, project_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {str(e)}")
    finally:
        manager.disconnect(project_id, client_id)

# FastAPI WebSocket endpoint for chat and WebRTC signaling (renamed to avoid conflict)
@app.websocket("/ws/chat/{project_id}")
async def chat_websocket(websocket: WebSocket, project_id: str):
    """
    FastAPI WebSocket endpoint for project chat and WebRTC signaling.
    Handles chat messages and WebRTC signaling (separate from YJS collaboration).
    """
    await websocket.accept()
    client_id = str(uuid.uuid4())

    logger.info(f"Chat WebSocket connected for project: {project_id}, client: {client_id}")

    try:
        while True:
            data = await websocket.receive_json()

            if data.get("type") == "chat":
                # Broadcast chat message to all clients
                await websocket.send_json({
                    "type": "chat",
                    "username": data.get("username"),
                    "message": data.get("message"),
                    "timestamp": datetime.now().isoformat()
                })

            elif data.get("type") in ["offer", "answer"]:
                # Handle WebRTC SDP signaling
                await websocket.send_json({
                    "type": data.get("type"),
                    "sdp": data.get("sdp"),
                    "username": data.get("username")
                })

            elif data.get("type") == "ice-candidate":
                # Handle ICE candidate
                await websocket.send_json({
                    "type": "ice-candidate",
                    "candidate": data.get("candidate"),
                    "username": data.get("username")
                })

    except WebSocketDisconnect:
        logger.info(f"Chat WebSocket disconnected for project: {project_id}")
    except Exception as e:
        logger.error(f"Error in chat WebSocket for project {project_id}: {e}")

if __name__ == "__main__":
    # Create a test project directory for debugging
    test_project = get_project_path("5")
    print(f"Test project path: {test_project}")
    # Run the app
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)



