from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Mentorship WebSocket routes
    re_path(r'ws/mentorship/session/(?P<room_id>[0-9a-f-]+)/$', consumers.MentorshipSessionConsumer.as_asgi()),

    # New Mentorship Communication WebSocket routes (port 8001)
    re_path(r'ws/mentorship/video/(?P<room_id>[0-9a-f-]+)/$', consumers.MentorshipVideoConsumer.as_asgi()),
    re_path(r'ws/mentorship/audio/(?P<room_id>[0-9a-f-]+)/$', consumers.MentorshipAudioConsumer.as_asgi()),
    re_path(r'ws/mentorship/chat/(?P<room_id>[0-9a-f-]+)/$', consumers.MentorshipChatConsumer.as_asgi()),
]