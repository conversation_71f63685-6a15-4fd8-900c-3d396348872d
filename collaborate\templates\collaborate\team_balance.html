{% extends "base.html" %}
{% load static %}
{% block title %}Team Balance Analysis - {{ project.title }}{% endblock %}

{% block extra_css %}
<style>
/* Team Balance Analysis - Dashboard Style CSS */
:root {
  --color-border: #C0ff6b;
  --color-bg: #000000;
  --color-container: #656565;
  --color-secondary: #d5d5d5;
}

/* Dashboard-style body and background */
body {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Dashboard-style collaborate page */
.collaborate-page {
  background: transparent;
  padding: 50px 0;
  min-height: 100vh;
}

/* Dashboard-style card */
.collaborate-card {
  background-color: rgba(28, 28, 28, 0.9);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(192, 255, 107, 0.3);
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

/* Page title styling */
.collaborate-card h1 {
  color: #ffffff;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-align: center;
  font-weight: 700;
}

.collaborate-card .lead {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  text-align: center;
  margin-bottom: 3rem;
}

/* Dashboard Section Styling */
.dashboard-section {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 2rem;
  border: 1px solid rgba(192, 255, 107, 0.2);
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.dashboard-section h2 {
  color: #C0ff6b;
  margin-bottom: 25px;
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.dashboard-section h3 {
  color: #C0ff6b;
  margin-bottom: 20px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Stats Grid - Dashboard Style */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: rgba(40, 40, 40, 0.8);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.2);
  border-color: rgba(192, 255, 107, 0.6);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #C0ff6b;
  text-shadow: 0 0 15px rgba(192, 255, 107, 0.5);
}

.stat-content h3 {
  font-size: 2.2rem;
  color: #C0ff6b;
  margin-bottom: 8px;
  margin-top: 0;
  font-weight: 700;
}

.stat-content p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Progress Bars */
.progress-section {
  margin: 30px 0;
  padding: 25px;
  background: rgba(60, 60, 60, 0.4);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.progress-bar-container {
  margin-bottom: 20px;
}

.progress-label {
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 16px;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  height: 16px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.progress-fill {
  background: linear-gradient(90deg, #C0ff6b, #a0e066);
  height: 100%;
  border-radius: 12px;
  transition: width 0.5s ease;
  box-shadow: 0 2px 8px rgba(192, 255, 107, 0.3);
}

.progress-text {
  color: #ffffff;
  font-size: 15px;
  margin-top: 8px;
  text-align: right;
  font-weight: 500;
}

/* Status Alerts - Dashboard Style */
.status-alert {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px 25px;
  border-radius: 12px;
  margin: 25px 0;
  border: 1px solid;
  backdrop-filter: blur(10px);
}

.status-alert.success {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #22c55e;
}

.status-alert.warning {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.status-alert.danger {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.status-alert i {
  font-size: 24px;
  flex-shrink: 0;
}

.status-alert div {
  flex: 1;
}

.status-alert strong {
  display: block;
  font-size: 16px;
  margin-bottom: 5px;
  font-weight: 600;
}

.status-alert p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.4;
}

/* Info Boxes - Dashboard Style */
.info-box {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  background: rgba(40, 40, 40, 0.6);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin: 25px 0;
  border-left: 4px solid #C0ff6b;
  backdrop-filter: blur(10px);
}

.info-box i {
  font-size: 28px;
  color: #C0ff6b;
  margin-top: 2px;
  flex-shrink: 0;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.info-box div strong {
  color: #C0ff6b;
  display: block;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
}

.info-box div p,
.info-box div ul {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
}

.info-box div ul {
  margin-top: 12px;
  padding-left: 25px;
}

.info-box div li {
  margin-bottom: 8px;
}

/* Level Badges - Dashboard Style */
.level-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(5px);
}

.level-badge.beginner {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.4);
}

.level-badge.intermediate {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.4);
}

.level-badge.expert {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.4);
}

/* Experience Stats Grid */
.experience-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 25px;
  margin: 30px 0;
}

.experience-stat {
  background: rgba(40, 40, 40, 0.6);
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  border: 1px solid rgba(192, 255, 107, 0.3);
  transition: all 0.3s ease;
}

.experience-stat:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.experience-level {
  color: #C0ff6b;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.experience-count {
  color: #ffffff;
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.experience-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* Skills Grid */
.skills-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-top: 30px;
}

.skills-column {
  background: rgba(40, 40, 40, 0.7);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  backdrop-filter: blur(10px);
}

.skills-column-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.skills-column-title.covered {
  color: #28a745;
}

.skills-column-title.missing {
  color: #dc3545;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.skill-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 22px;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-height: 55px;
  backdrop-filter: blur(5px);
}

.skill-item:hover {
  transform: translateX(8px);
}

.skill-item.covered {
  background: rgba(40, 167, 69, 0.2);
  border: 1px solid rgba(40, 167, 69, 0.5);
}

.skill-item.missing {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.5);
}

.skill-item.critical {
  border-width: 2px;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.skill-name {
  color: #ffffff;
  font-weight: 500;
  font-size: 15px;
}

.critical-icon {
  color: #ffc107;
  margin-left: 10px;
  font-size: 16px;
  text-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
}

.skill-count,
.skill-status {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.no-skills {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 40px 20px;
  font-size: 16px;
}

/* Coverage Summary */
.skill-coverage-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 40px;
  align-items: center;
  margin-bottom: 35px;
  padding: 30px;
  background: rgba(60, 60, 60, 0.4);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

.coverage-summary {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.coverage-stat {
  text-align: center;
  padding: 25px;
  background: rgba(40, 40, 40, 0.7);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  min-width: 140px;
  transition: all 0.3s ease;
}

.coverage-stat:hover {
  transform: translateY(-5px);
  border-color: rgba(192, 255, 107, 0.5);
}

.coverage-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #C0ff6b;
  display: block;
  line-height: 1;
  text-shadow: 0 0 10px rgba(192, 255, 107, 0.3);
}

.coverage-label {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 10px;
  font-weight: 500;
}

/* Action Buttons - Dashboard Style */
.action-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
  padding: 25px;
  flex-wrap: wrap;
  background: rgba(40, 40, 40, 0.4);
  border-radius: 15px;
  border: 1px solid rgba(192, 255, 107, 0.2);
}

/* Members Grid */
.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.member-card {
  background: rgba(40, 40, 40, 0.8);
  border: 1px solid rgba(192, 255, 107, 0.3);
  border-radius: 15px;
  padding: 30px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.member-card:hover {
  transform: translateY(-8px);
  border-color: rgba(192, 255, 107, 0.6);
  box-shadow: 0 15px 40px rgba(192, 255, 107, 0.2);
}

.member-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.member-avatar {
  width: 60px;
  height: 60px;
  background: rgba(192, 255, 107, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #C0ff6b;
  font-size: 24px;
  border: 2px solid rgba(192, 255, 107, 0.3);
}

.member-info {
  flex: 1;
}

.member-name {
  color: #C0ff6b;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.member-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.member-stat {
  display: flex;
  align-items: center;
  gap: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 15px;
}

.member-stat i {
  color: #C0ff6b;
  width: 18px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
}

.stat-value {
  color: #C0ff6b;
  font-weight: 600;
}

.member-skills {
  margin-top: 20px;
}

.member-skills h5 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 12px;
  font-weight: 600;
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-tag {
  background: rgba(192, 255, 107, 0.2);
  color: #C0ff6b;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 13px;
  border: 1px solid rgba(192, 255, 107, 0.3);
  font-weight: 500;
}

/* Suggestions */
.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 30px;
  margin-top: 30px;
}

.suggestion-card {
  background: rgba(40, 40, 40, 0.7);
  border-radius: 15px;
  padding: 30px;
  border-left: 5px solid;
  transition: all 0.3s ease;
  min-height: 180px;
  backdrop-filter: blur(10px);
}

.suggestion-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.suggestion-card.high {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.08);
}

.suggestion-card.medium {
  border-left-color: #ffc107;
  background: rgba(255, 193, 7, 0.08);
}

.suggestion-card.low {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.08);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-header i {
  font-size: 28px;
  flex-shrink: 0;
}

.suggestion-card.high .suggestion-header i { color: #dc3545; }
.suggestion-card.medium .suggestion-header i { color: #ffc107; }
.suggestion-card.low .suggestion-header i { color: #28a745; }

.priority-label {
  color: #C0ff6b;
  font-weight: 600;
  font-size: 18px;
}

.suggestion-text {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-size: 16px;
}

/* No Data State */
.no-data {
  text-align: center;
  padding: 60px 30px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(40, 40, 40, 0.4);
  border-radius: 15px;
  border: 2px dashed rgba(192, 255, 107, 0.3);
  margin: 30px 0;
}

.no-data i {
  font-size: 4rem;
  color: rgba(192, 255, 107, 0.5);
  margin-bottom: 20px;
  display: block;
}

.no-data strong {
  display: block;
  color: #ffffff;
  margin-bottom: 12px;
  font-size: 1.3rem;
  font-weight: 600;
}

.no-data p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .collaborate-card {
    padding: 30px;
    margin: 0 20px;
  }

  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .members-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .collaborate-card {
    padding: 25px;
    margin: 0 15px;
  }

  .collaborate-card h1 {
    font-size: 2rem;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .experience-stats {
    grid-template-columns: 1fr;
  }

  .members-grid {
    grid-template-columns: 1fr;
  }

  .suggestions-grid {
    grid-template-columns: 1fr;
  }

  .skill-coverage-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .coverage-summary {
    flex-direction: column;
    gap: 15px;
  }

  .skills-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .collaborate-card {
    padding: 20px;
    margin: 0 10px;
  }

  .dashboard-section {
    padding: 1.5rem;
  }

  .stat-card {
    padding: 20px;
  }

  .member-card {
    padding: 20px;
  }

  .suggestion-card {
    padding: 20px;
  }

  .member-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
}
</style>
{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="tile-wrap">
    <div class="collaborate-card fade-in">
      <h1><i class="fas fa-balance-scale me-3"></i>Team Balance Analysis</h1>
      <p class="lead">Comprehensive analysis for "{{ project.title }}"</p>

    <!-- Overall Balance Score Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-chart-line me-2"></i>Overall Balance Score</h2>

      <div class="dashboard-stats">
        <div class="stat-card">
          <div class="stat-icon">⚖️</div>
          <div class="stat-content">
            <h3>{{ team_balance.balance_score }}%</h3>
            <p>Team Balance</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-content">
            <h3>{{ team_members|length }}</h3>
            <p>Team Members</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon">🧩</div>
          <div class="stat-content">
            <h3>{{ skill_coverage_percent|floatformat:1 }}%</h3>
            <p>Skills Covered</p>
          </div>
        </div>
      </div>

      <div class="progress-section">
        <div class="progress-bar-container">
          <div class="progress-label">Team Balance Score</div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: {{ team_balance.balance_score }}%"></div>
          </div>
          <div class="progress-text">{{ team_balance.balance_score }}% Balanced</div>
        </div>
      </div>

      {% if team_balance.is_balanced %}
        <div class="status-alert success">
          <i class="fas fa-check-circle"></i>
          <div>
            <strong>Excellent Team Balance!</strong>
            <p>This team has a good balance of skills and experience levels. Well-balanced teams typically perform better on complex projects.</p>
          </div>
        </div>
      {% else %}
        <div class="status-alert warning">
          <i class="fas fa-exclamation-triangle"></i>
          <div>
            <strong>Team Could Be More Balanced</strong>
            <p>Teams with diverse experience levels tend to be more resilient and innovative. Consider adding members with different skill levels.</p>
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Experience Distribution Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-chart-pie me-2"></i>Experience Distribution</h2>

      <div class="experience-stats">
        <div class="experience-stat">
          <div class="experience-level">Beginner</div>
          <div class="experience-count">{{ team_balance.experience_levels.beginner }}</div>
          <div class="experience-label">Avg. Proficiency: 1-2</div>
        </div>

        <div class="experience-stat">
          <div class="experience-level">Intermediate</div>
          <div class="experience-count">{{ team_balance.experience_levels.intermediate }}</div>
          <div class="experience-label">Avg. Proficiency: 2-3.5</div>
        </div>

        <div class="experience-stat">
          <div class="experience-level">Expert</div>
          <div class="experience-count">{{ team_balance.experience_levels.expert }}</div>
          <div class="experience-label">Avg. Proficiency: 3.5-5</div>
        </div>
      </div>

      <div class="info-box">
        <i class="fas fa-lightbulb"></i>
        <div>
          <strong>Ideal Team Composition:</strong>
          <p>A balanced team typically includes a mix of experience levels. Experts provide guidance, intermediates handle most tasks efficiently, and beginners bring fresh perspectives while learning.</p>
        </div>
      </div>
    </div>

    <!-- Skill Coverage Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-puzzle-piece me-2"></i>Skill Coverage Analysis</h2>

      <div class="skill-coverage-grid">
        <div class="coverage-summary">
          <div class="coverage-stat">
            <span class="coverage-number">{{ skill_coverage.covered_skills }}</span>
            <span class="coverage-label">Skills Covered</span>
          </div>
          <div class="coverage-stat">
            <span class="coverage-number">{{ skill_coverage.total_skills }}</span>
            <span class="coverage-label">Total Required</span>
          </div>
        </div>

        <div class="progress-section">
          <div class="progress-bar-container">
            <div class="progress-label">Skill Coverage</div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: {{ skill_coverage_percent }}%"></div>
            </div>
            <div class="progress-text">{{ skill_coverage_percent|floatformat:1 }}% Complete</div>
          </div>
        </div>
      </div>

      {% if skill_coverage.critical_skills > 0 %}
        <div class="critical-skills-section">
          {% if skill_coverage.covered_critical == skill_coverage.critical_skills %}
            <div class="status-alert success">
              <i class="fas fa-check-circle"></i>
              <div>
                <strong>All Critical Skills Covered!</strong>
                <p>{{ skill_coverage.covered_critical }} of {{ skill_coverage.critical_skills }} critical skills are covered by your team.</p>
              </div>
            </div>
          {% else %}
            <div class="status-alert danger">
              <i class="fas fa-exclamation-triangle"></i>
              <div>
                <strong>Missing Critical Skills!</strong>
                <p>Only {{ skill_coverage.covered_critical }} of {{ skill_coverage.critical_skills }} critical skills are covered. Missing critical skills can significantly impact project success.</p>
              </div>
            </div>
          {% endif %}
        </div>
      {% endif %}

      {% if skill_coverage_percent < 100 %}
        <div class="action-buttons">
          <a href="{% url 'collaborate:match_users' project.hash %}" class="btn btn-primary">
            <i class="fas fa-search me-2"></i>Find Members with Missing Skills
          </a>
        </div>
      {% endif %}
    </div>

    <!-- Skill Details Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-list-check me-2"></i>Skill Coverage Details</h2>

      <div class="skills-grid">
        <div class="skills-column">
          <h4 class="skills-column-title covered">
            <i class="fas fa-check-circle me-2"></i>Covered Skills
          </h4>
          <div class="skills-list">
            {% for skill in skill_coverage.skills %}
              {% if skill.covered %}
                <div class="skill-item covered {% if skill.is_critical %}critical{% endif %}">
                  <span class="skill-name">{{ skill.name }}</span>
                  {% if skill.is_critical %}
                    <i class="fas fa-star critical-icon" title="Critical Skill"></i>
                  {% endif %}
                  <span class="skill-count">{{ skill.team_members|length }} member{{ skill.team_members|length|pluralize }}</span>
                </div>
              {% endif %}
            {% endfor %}
            {% if not skill_coverage.skills or not skill_coverage.skills|length %}
              <div class="no-skills">No covered skills found</div>
            {% endif %}
          </div>
        </div>

        <div class="skills-column">
          <h4 class="skills-column-title missing">
            <i class="fas fa-exclamation-circle me-2"></i>Missing Skills
          </h4>
          <div class="skills-list">
            {% for skill in skill_coverage.skills %}
              {% if not skill.covered %}
                <div class="skill-item missing {% if skill.is_critical %}critical{% endif %}">
                  <span class="skill-name">{{ skill.name }}</span>
                  {% if skill.is_critical %}
                    <i class="fas fa-star critical-icon" title="Critical Skill"></i>
                  {% endif %}
                  <span class="skill-status">Not covered</span>
                </div>
              {% endif %}
            {% endfor %}
            {% if not skill_coverage.skills or skill_coverage_percent == 100 %}
              <div class="no-skills">All required skills are covered!</div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Team Member Analysis Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-users me-2"></i>Team Member Analysis</h2>

      <div class="info-box">
        <i class="fas fa-info-circle"></i>
        <div>
          <strong>Understanding Experience Levels:</strong>
          <ul>
            <li><span class="level-badge beginner">Beginner</span> - Average proficiency of 1-2</li>
            <li><span class="level-badge intermediate">Intermediate</span> - Average proficiency of 2-3.5</li>
            <li><span class="level-badge expert">Expert</span> - Average proficiency of 3.5-5</li>
          </ul>
        </div>
      </div>

      <div class="members-grid">
        {% for member in team_balance.member_data %}
          <div class="member-card">
            <div class="member-header">
              <div class="member-avatar">
                <i class="fas fa-user"></i>
              </div>
              <div class="member-info">
                <h4 class="member-name">{{ member.username }}</h4>
                <span class="level-badge {{ member.level }}">{{ member.level|title }}</span>
              </div>
            </div>

            <div class="member-stats">
              <div class="member-stat">
                <i class="fas fa-chart-line"></i>
                <span class="stat-label">Avg Proficiency:</span>
                <span class="stat-value">{{ member.avg_proficiency|floatformat:1 }}/5</span>
              </div>

              <div class="member-stat">
                <i class="fas fa-puzzle-piece"></i>
                <span class="stat-label">Relevant Skills:</span>
                <span class="stat-value">{{ member.skills_count }}/{{ member.required_skills_count }}</span>
              </div>
            </div>

            {% if member.skills %}
              <div class="member-skills">
                <h5>Skills:</h5>
                <div class="skills-tags">
                  {% for skill in member.skills %}
                    <span class="skill-tag">{{ skill.name }} ({{ skill.proficiency }})</span>
                  {% endfor %}
                </div>
              </div>
            {% endif %}
          </div>
        {% empty %}
          <div class="no-data">
            <i class="fas fa-info-circle"></i>
            <p>No team member data available for analysis.</p>
          </div>
        {% endfor %}
      </div>
    </div>

    <!-- Improvement Suggestions Section -->
    <div class="dashboard-section">
      <h2><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h2>

      {% if skill_gaps.suggestions %}
        <div class="suggestions-grid">
          {% for suggestion in skill_gaps.suggestions %}
            <div class="suggestion-card {{ suggestion.priority }}">
              <div class="suggestion-header">
                <i class="fas fa-{% if suggestion.priority == 'high' %}exclamation-triangle{% elif suggestion.priority == 'medium' %}info-circle{% else %}check-circle{% endif %}"></i>
                <span class="priority-label">{{ suggestion.priority|title }} Priority</span>
              </div>
              <p class="suggestion-text">{{ suggestion.message }}</p>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="status-alert success">
          <i class="fas fa-check-circle"></i>
          <div>
            <strong>Excellent Team Balance!</strong>
            <p>No significant improvements needed. This team looks well-balanced!</p>
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <a href="{% url 'collaborate:project_detail' project.hash %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>Back to Project
      </a>
      <a href="{% url 'collaborate:match_users' project.hash %}" class="btn btn-primary">
        <i class="fas fa-users me-2"></i>Find More Team Members
      </a>
    </div>
  </div>
  </div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Initialize particles.js
  particlesJS('particles-js', {
    "particles": {
      "number": {
        "value": 80,
        "density": {
          "enable": true,
          "value_area": 800
        }
      },
      "color": {
        "value": ["#C0ff6b", "#a0e066", "#80c055", "#60a044"]
      },
      "shape": {
        "type": ["circle", "triangle"],
        "stroke": {
          "width": 0,
          "color": "#000000"
        }
      },
      "opacity": {
        "value": 0.5,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 1,
          "opacity_min": 0.1,
          "sync": false
        }
      },
      "size": {
        "value": 3,
        "random": true,
        "anim": {
          "enable": true,
          "speed": 2,
          "size_min": 0.1,
          "sync": false
        }
      },
      "line_linked": {
        "enable": true,
        "distance": 150,
        "color": "#C0ff6b",
        "opacity": 0.4,
        "width": 1
      },
      "move": {
        "enable": true,
        "speed": 1,
        "direction": "none",
        "random": true,
        "straight": false,
        "out_mode": "out",
        "bounce": false
      }
    },
    "interactivity": {
      "detect_on": "canvas",
      "events": {
        "onhover": {
          "enable": true,
          "mode": "grab"
        },
        "onclick": {
          "enable": true,
          "mode": "push"
        },
        "resize": true
      }
    },
    "retina_detect": true
  });

  // Animation for elements
  function revealElements() {
    const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
    elements.forEach(element => {
      element.classList.add('visible');
    });
  }

  // Run animations
  setTimeout(revealElements, 100);
});
</script>
{% endblock %}
