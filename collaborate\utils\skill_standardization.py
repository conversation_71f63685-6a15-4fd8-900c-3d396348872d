"""
Skill standardization utilities for the matching engine.
This module provides functions to standardize skills, handle aliases,
and create skill hierarchies.
"""
from django.db.models import Q
from ..models import Skill

# Define skill aliases (variations of the same skill)
SKILL_ALIASES = {
    # Programming Languages
    'javascript': ['js', 'ecmascript', 'javascript'],
    'typescript': ['ts', 'typescript'],
    'python': ['python', 'py'],
    'java': ['java', 'jvm'],
    'c#': ['c#', 'csharp', 'c sharp'],
    'c++': ['c++', 'cpp', 'cplusplus'],
    'ruby': ['ruby', 'rb'],
    'go': ['go', 'golang'],
    'rust': ['rust', 'rs'],
    'php': ['php'],
    'swift': ['swift'],
    'kotlin': ['kotlin'],
    'scala': ['scala'],
    'r': ['r', 'rlang'],
    'perl': ['perl'],
    'haskell': ['haskell'],
    'lua': ['lua'],
    'dart': ['dart'],

    # Web Development
    'html': ['html', 'html5'],
    'css': ['css', 'css3', 'scss', 'sass', 'less'],
    'react': ['react', 'reactjs', 'react.js'],
    'angular': ['angular', 'angularjs', 'angular.js'],
    'vue': ['vue', 'vuejs', 'vue.js'],
    'svelte': ['svelte'],
    'node': ['node', 'nodejs', 'node.js'],
    'express': ['express', 'expressjs'],
    'django': ['django'],
    'flask': ['flask'],
    'laravel': ['laravel'],
    'spring boot': ['spring boot', 'spring'],
    'asp.net': ['asp.net', 'aspnet'],
    'jquery': ['jquery'],
    'bootstrap': ['bootstrap'],
    'tailwind css': ['tailwind', 'tailwind css'],
    'graphql': ['graphql'],
    'rest api': ['rest', 'rest api', 'restful'],
    'websockets': ['websockets', 'websocket'],

    # Databases
    'sql': ['sql', 'structured query language'],
    'mysql': ['mysql'],
    'postgresql': ['postgresql', 'postgres'],
    'sqlite': ['sqlite'],
    'oracle': ['oracle', 'oracle db'],
    'nosql': ['nosql', 'no-sql'],
    'mongodb': ['mongodb', 'mongo'],
    'dynamodb': ['dynamodb'],
    'cassandra': ['cassandra'],
    'redis': ['redis'],
    'firebase': ['firebase'],

    # Cloud & DevOps
    'aws': ['aws', 'amazon web services'],
    'azure': ['azure', 'microsoft azure'],
    'gcp': ['gcp', 'google cloud', 'google cloud platform'],
    'docker': ['docker', 'containerization'],
    'kubernetes': ['kubernetes', 'k8s'],
    'jenkins': ['jenkins'],
    'github actions': ['github actions', 'gh actions'],
    'gitlab ci': ['gitlab ci', 'gitlab'],
    'terraform': ['terraform'],
    'ansible': ['ansible'],
    'ci/cd': ['ci/cd', 'cicd', 'continuous integration', 'continuous deployment'],

    # Data Science & AI
    'machine learning': ['machine learning', 'ml'],
    'artificial intelligence': ['artificial intelligence', 'ai'],
    'deep learning': ['deep learning', 'dl'],
    'natural language processing': ['natural language processing', 'nlp'],
    'computer vision': ['computer vision', 'cv'],
    'data analysis': ['data analysis'],
    'data visualization': ['data visualization', 'data viz'],
    'tensorflow': ['tensorflow', 'tf'],
    'pytorch': ['pytorch', 'torch'],
    'pandas': ['pandas'],
    'numpy': ['numpy', 'np'],
    'scikit-learn': ['scikit-learn', 'sklearn'],

    # Mobile Development
    'android': ['android'],
    'ios': ['ios'],
    'react native': ['react native'],
    'flutter': ['flutter'],
    'xamarin': ['xamarin'],
    'swift ui': ['swift ui', 'swiftui'],

    # Other Categories
    'devops': ['devops', 'devsecops'],
    'frontend': ['frontend', 'front-end', 'front end'],
    'backend': ['backend', 'back-end', 'back end'],
    'fullstack': ['fullstack', 'full-stack', 'full stack'],
    'ui/ux design': ['ui/ux', 'ui/ux design', 'user interface', 'user experience'],
    'agile': ['agile', 'scrum', 'kanban'],
    'project management': ['project management', 'pm'],
    'testing': ['testing', 'qa', 'quality assurance'],
    'security': ['security', 'cybersecurity', 'infosec'],
}

# Define skill hierarchies (parent-child relationships)
SKILL_HIERARCHY = {
    # High-level categories
    'frontend': ['html', 'css', 'javascript', 'typescript', 'react', 'angular', 'vue', 'svelte', 'jquery', 'bootstrap', 'tailwind css'],
    'backend': ['python', 'java', 'c#', 'node', 'php', 'ruby', 'go', 'rust', 'django', 'flask', 'express', 'spring boot', 'asp.net', 'laravel'],
    'database': ['sql', 'nosql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'sqlite', 'oracle', 'dynamodb', 'cassandra', 'firebase'],
    'devops': ['docker', 'kubernetes', 'jenkins', 'github actions', 'gitlab ci', 'terraform', 'ansible', 'ci/cd'],
    'cloud': ['aws', 'azure', 'gcp', 'heroku', 'digitalocean'],
    'mobile': ['android', 'ios', 'react native', 'flutter', 'swift', 'kotlin', 'xamarin', 'swift ui'],
    'testing': ['unit testing', 'integration testing', 'e2e testing', 'jest', 'pytest', 'selenium'],
    'data science': ['machine learning', 'artificial intelligence', 'deep learning', 'natural language processing', 'computer vision', 'data analysis', 'data visualization'],

    # Programming language ecosystems
    'javascript': ['react', 'angular', 'vue', 'svelte', 'jquery', 'node', 'express', 'typescript', 'graphql', 'websockets'],
    'python': ['django', 'flask', 'fastapi', 'pandas', 'numpy', 'tensorflow', 'pytorch', 'scikit-learn'],
    'java': ['spring', 'spring boot', 'hibernate', 'maven', 'gradle', 'junit'],
    'c#': ['.net', 'asp.net', 'xamarin', 'unity'],
    'php': ['laravel', 'symfony', 'wordpress'],
    'ruby': ['rails', 'sinatra'],

    # Specialized areas
    'machine learning': ['tensorflow', 'pytorch', 'scikit-learn', 'deep learning', 'natural language processing', 'computer vision'],
    'web apis': ['rest api', 'graphql', 'websockets'],
    'ui/ux': ['ui/ux design', 'figma', 'sketch', 'adobe xd'],
    'project management': ['agile', 'scrum', 'kanban', 'jira'],
    'security': ['penetration testing', 'encryption', 'authentication', 'authorization'],
}

def standardize_skill_name(skill_name):
    """
    Standardize a skill name by converting to lowercase and checking aliases.

    Args:
        skill_name: Name of the skill to standardize

    Returns:
        str: Standardized skill name
    """
    if not skill_name:
        return None

    # Convert to lowercase
    normalized = skill_name.lower().strip()

    # Check aliases
    for standard, aliases in SKILL_ALIASES.items():
        if normalized in aliases:
            return standard

    return normalized

def get_skill_by_name_or_alias(skill_name):
    """
    Get a skill by its name or any of its aliases.

    Args:
        skill_name: Name or alias of the skill

    Returns:
        Skill: Skill object or None if not found
    """
    if not skill_name:
        return None

    # Standardize the name
    standard_name = standardize_skill_name(skill_name)

    # Try to find the skill by its standard name
    skill = Skill.objects.filter(name__iexact=standard_name).first()
    if skill:
        return skill

    # If not found, check aliases
    for standard, aliases in SKILL_ALIASES.items():
        if standard_name in aliases:
            # Try to find the skill by the standard name
            skill = Skill.objects.filter(name__iexact=standard).first()
            if skill:
                return skill

    # If still not found, try a more flexible search
    return Skill.objects.filter(
        Q(name__iexact=skill_name) |
        Q(name__icontains=skill_name)
    ).first()

def get_parent_skills(skill_name):
    """
    Get parent skills for a given skill.

    Args:
        skill_name: Name of the skill

    Returns:
        list: List of parent skill names
    """
    standard_name = standardize_skill_name(skill_name)
    parents = []

    for parent, children in SKILL_HIERARCHY.items():
        if standard_name in children:
            parents.append(parent)

    return parents

def get_child_skills(skill_name):
    """
    Get child skills for a given skill.

    Args:
        skill_name: Name of the skill

    Returns:
        list: List of child skill names
    """
    standard_name = standardize_skill_name(skill_name)

    # Check if this skill is a parent in the hierarchy
    if standard_name in SKILL_HIERARCHY:
        return SKILL_HIERARCHY[standard_name]

    return []

def get_related_skills(skill_name):
    """
    Get related skills (siblings, parents, and children).

    Args:
        skill_name: Name of the skill

    Returns:
        dict: Dictionary of related skills by relationship type
    """
    standard_name = standardize_skill_name(skill_name)

    # Get parent skills
    parents = get_parent_skills(standard_name)

    # Get child skills
    children = get_child_skills(standard_name)

    # Get sibling skills (skills with the same parent)
    siblings = []
    for parent in parents:
        for child in SKILL_HIERARCHY.get(parent, []):
            if child != standard_name and child not in siblings:
                siblings.append(child)

    return {
        'parents': parents,
        'children': children,
        'siblings': siblings
    }

def get_skill_with_related(skill_name):
    """
    Get a skill with its related skills.

    Args:
        skill_name: Name of the skill

    Returns:
        dict: Skill data with related skills
    """
    skill = get_skill_by_name_or_alias(skill_name)
    if not skill:
        return None

    related = get_related_skills(skill.name)

    # Get actual skill objects for related skills
    related_skills = {
        'parents': Skill.objects.filter(name__in=related['parents']),
        'children': Skill.objects.filter(name__in=related['children']),
        'siblings': Skill.objects.filter(name__in=related['siblings'])
    }

    return {
        'skill': skill,
        'related': related_skills
    }
