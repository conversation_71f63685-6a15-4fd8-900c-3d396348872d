from collaborate.models import Notification
from asgiref.sync import async_to_sync
from channels.layers import get_channel_layer

def send_notification(user, title, message):
    # Save to database
    Notification.objects.create(user=user, title=title, message=message)
    
    # Send via WebSocket
    channel_layer = get_channel_layer()
    async_to_sync(channel_layer.group_send)(
        f"user_{user.id}",
        {
            "type": "send_notification",
            "title": title,
            "message": message,
        }
    )