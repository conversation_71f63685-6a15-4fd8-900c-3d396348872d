{% extends "base.html" %} 
{% load static %}
{% block title %}Manage Course Approvals - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="course-form-container fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">📚 Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active" aria-current="page">Manage Approvals</li>
    </ol>
  </nav>

  <!-- Header -->
  <div class="admin-form-header">
    <h1>📋 Course Approval Management</h1>
    <p>Review and approve courses submitted by verified mentors</p>
  </div>

  <!-- Messages -->
  {% if messages %}
  <div class="messages-container">
    {% for message in messages %}
      <div class="alert alert-{{ message.tags }} slide-down">
        <span class="alert-icon">
          {% if message.tags == 'success' %}✅{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
        </span>
        {{ message }}
        <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
      </div>
    {% endfor %}
  </div>
  {% endif %}

  <!-- Pending Courses -->
  {% if pending_courses %}
  <div class="approval-section">
    <h3 class="section-title">📝 Courses Pending Approval ({{ pending_courses|length }})</h3>
    
    <div class="approval-grid">
      {% for course in pending_courses %}
      <div class="approval-card slide-up" style="animation-delay: {{ forloop.counter0|add:1 }}00ms;">
        <div class="approval-header">
          <h4 class="course-title">{{ course.name }}</h4>
          <div class="course-meta">
            <span class="creator-info">
              <i class="icon">🎓</i> By {{ course.get_creator_name }}
            </span>
            <span class="difficulty-badge difficulty-{{ course.difficulty }}">
              {{ course.get_difficulty_display }}
            </span>
          </div>
        </div>

        <div class="approval-content">
          <p class="course-description">{{ course.description|truncatewords:30 }}</p>
          
          <div class="course-details">
            <div class="detail-item">
              <strong>Duration:</strong> {{ course.estimated_duration }}
            </div>
            <div class="detail-item">
              <strong>Prerequisites:</strong> 
              {% if course.prerequisites %}
                {{ course.prerequisites|truncatewords:15 }}
              {% else %}
                None specified
              {% endif %}
            </div>
            <div class="detail-item">
              <strong>Learning Objectives:</strong> 
              {% if course.learning_objectives %}
                {{ course.learning_objectives|truncatewords:15 }}
              {% else %}
                None specified
              {% endif %}
            </div>
            <div class="detail-item">
              <strong>Submitted:</strong> {{ course.created_at|date:"M d, Y" }}
            </div>
          </div>
        </div>

        <div class="approval-actions">
          <a href="{% url 'learn:course_details' course.id %}" class="btn btn-secondary btn-sm">
            <i class="icon">👁️</i> Preview
          </a>
          <a href="{% url 'learn:approve_course' course.id %}" 
             onclick="return confirm('Approve this course? It will become visible to all students.')"
             class="btn btn-success btn-sm">
            <i class="icon">✅</i> Approve
          </a>
          <a href="{% url 'learn:reject_course' course.id %}"
             onclick="return confirm('Reject this course? It will be moved back to draft status.')"
             class="btn btn-danger btn-sm">
            <i class="icon">❌</i> Reject
          </a>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% else %}
  <div class="empty-state">
    <div class="empty-icon">✅</div>
    <h3>No Courses Pending Approval</h3>
    <p>All mentor-submitted courses have been reviewed!</p>
    <a href="{% url 'learn:course_list' %}" class="btn btn-primary">
      Back to Courses
    </a>
  </div>
  {% endif %}

  <!-- Quick Actions -->
  <div class="quick-actions">
    <a href="{% url 'learn:course_list' %}" class="btn btn-secondary">
      <i class="icon">📚</i> Back to Courses
    </a>
    <a href="/admin/learn/course/" class="btn btn-primary" target="_blank">
      <i class="icon">⚙️</i> Django Admin
    </a>
  </div>
</div>
</section>

<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 25,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.1,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.05,
            "sync": false
          }
        },
        "size": {
          "value": 2,
          "random": true
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.05,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 0.5,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-up');
      elements.forEach((element, index) => {
        setTimeout(() => {
          element.classList.add('visible');
        }, index * 100);
      });
    }

    // Run animations
    setTimeout(revealElements, 200);

    // Auto-hide messages after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
      setTimeout(() => {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(() => {
          alert.style.display = 'none';
        }, 300);
      }, 5000);
    });
  });
</script>
{% endblock %}
