{% extends "base.html" %} {% block content %}
<style>
  /* .delete-project-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    padding: 1rem;
  }

  .delete-confirmation-card {
    background-color: #fff;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 100%;
    animation: fadeIn 0.3s ease-in-out;
  }

  .delete-confirmation-card h1 {
    font-size: 1.8rem;
    color: #c0392b;
    margin-bottom: 1rem;
  }

  .warning-message {
    background-color: #fff0e6;
    border-left: 6px solid #e67e22;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
  }

  .warning-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }

  .warning-detail {
    font-size: 0.95rem;
    color: #7f8c8d;
  }

  .project-info {
    margin: 1rem 0;
  }

  .project-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }

  .project-info ul {
    list-style: none;
    padding: 0;
    font-size: 0.95rem;
  }

  .project-info li {
    margin-bottom: 0.3rem;
  }

  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .btn-danger {
    background-color: #e74c3c;
    color: white;
    border: none;
  }

  .btn-danger:hover {
    background-color: #c0392b;
  }

  .btn-secondary {
    background-color: #bdc3c7;
    color: #2c3e50;
  }

  .btn-secondary:hover {
    background-color: #95a5a6;
  } */
</style>
<div class="delete-project-container">
  <div class="delete-confirmation-card">
    <h1>Delete Project</h1>

    <div class="warning-message">
      <i class="warning-icon">⚠️</i>
      <p>
        Are you sure you want to delete the project "<strong
          >{{ project.title }}</strong
        >"?
      </p>
      <p class="warning-detail">
        This action cannot be undone. All project data, including files and team
        memberships, will be permanently deleted.
      </p>
    </div>

    <div class="project-info">
      <h3>Project Details:</h3>
      <ul>
        <li><strong>Title:</strong> {{ project.title }}</li>
        <li>
          <strong>Created on:</strong> {{ project.created_at|date:"F j, Y" }}
        </li>
        <li><strong>Team Members:</strong> {{ project.memberships.count }}</li>
      </ul>
    </div>

    <div class="action-buttons">
      <form method="post" class="delete-form">
        {% csrf_token %}
        <button type="submit" class="btn btn-danger">
          Yes, Delete Project
        </button>
      </form>
      <a
        href="{% url 'collaborate:project_detail' project.id %}"
        class="btn btn-secondary"
        >No, Keep Project</a
      >
    </div>
  </div>
</div>
{% endblock %}
