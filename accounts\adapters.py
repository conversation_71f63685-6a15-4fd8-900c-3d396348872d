from allauth.account.adapter import DefaultAccountAdapter
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.account.models import EmailAddress
from allauth.socialaccount.models import SocialAccount
from django.urls import reverse
from django.contrib import messages
from django.shortcuts import redirect
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.conf import settings
import logging
import re

# Set up logging for authentication events
logger = logging.getLogger(__name__)


class CustomAccountAdapter(DefaultAccountAdapter):
    """Professional account adapter with enhanced security and user experience"""

    def get_login_redirect_url(self, request):
        """
        Intelligent login redirect based on user context
        """
        user = request.user

        # Log successful login
        logger.info(f"User {user.username} logged in successfully from IP: {self.get_client_ip(request)}")

        # Check if this is a social login
        if hasattr(request, 'session') and request.session.get('socialaccount_login'):
            # Clear the session flag
            del request.session['socialaccount_login']
            return reverse('accounts:dashboard') + '?welcome=google'

        # Check if user needs to complete profile
        if hasattr(user, 'profile'):
            try:
                profile = user.profile
                if not profile.bio and not profile.skills.exists():
                    messages.info(request, "Please complete your profile to get the best experience!")
                    return reverse('accounts:profile_setup')
            except:
                pass

        # Default redirect for regular login
        return reverse('accounts:dashboard') + '?welcome=login'

    def clean_email(self, email):
        """
        Enhanced email validation with domain restrictions
        """
        email = super().clean_email(email)

        # Check against allowed domains if configured
        if hasattr(settings, 'ALLOWED_EMAIL_DOMAINS'):
            domain = email.split('@')[-1].lower()
            allowed_domains = [d.strip().lower() for d in settings.ALLOWED_EMAIL_DOMAINS]
            if domain not in allowed_domains:
                raise ValidationError(
                    _("Email domain '{}' is not allowed. Please use an email from: {}").format(
                        domain, ', '.join(allowed_domains)
                    )
                )

        return email

    def clean_username(self, username, shallow=False):
        """
        Enhanced username validation
        """
        username = super().clean_username(username, shallow=shallow)

        # Skip additional validation if shallow=True (used during username generation)
        if shallow:
            return username

        # Additional username validation for full validation
        if len(username) < 3:
            raise ValidationError(_("Username must be at least 3 characters long."))

        if not re.match(r'^[a-zA-Z0-9_-]+$', username):
            raise ValidationError(_("Username can only contain letters, numbers, hyphens, and underscores."))

        # Check for reserved usernames
        reserved_usernames = ['admin', 'root', 'api', 'www', 'mail', 'support', 'help']
        if username.lower() in reserved_usernames:
            raise ValidationError(_("This username is reserved. Please choose another."))

        return username

    def send_confirmation_mail(self, request, emailconfirmation, signup):
        """
        Enhanced email confirmation with better templates and logging
        """
        logger.info(f"Sending confirmation email to {emailconfirmation.email_address.email}")
        super().send_confirmation_mail(request, emailconfirmation, signup)

    def get_client_ip(self, request):
        """Get client IP address for logging"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class CustomSocialAccountAdapter(DefaultSocialAccountAdapter):
    """Professional social account adapter with comprehensive features"""

    def pre_social_login(self, request, sociallogin):
        """
        Enhanced pre-login processing with account linking and validation
        """
        # Set session flag for social login tracking
        request.session['socialaccount_login'] = True

        # Log social login attempt
        provider = sociallogin.account.provider
        email = sociallogin.account.extra_data.get('email', 'No email')
        logger.info(f"Social login attempt via {provider} for email: {email} from IP: {self.get_client_ip(request)}")

        # Handle account linking for existing users
        if sociallogin.is_existing:
            return

        # Check if user with this email already exists
        if email and email != 'No email':
            try:
                existing_user = User.objects.get(email=email)
                # Link the social account to existing user
                sociallogin.connect(request, existing_user)
                logger.info(f"Linked {provider} account to existing user: {existing_user.username}")
                messages.success(
                    request,
                    f"Your {provider.title()} account has been linked to your existing account."
                )
            except User.DoesNotExist:
                pass

        super().pre_social_login(request, sociallogin)

    def save_user(self, request, sociallogin, form=None):
        """
        Enhanced user creation with comprehensive profile setup
        """
        user = super().save_user(request, sociallogin, form)

        # Extract additional data from social login
        extra_data = sociallogin.account.extra_data
        provider = sociallogin.account.provider

        # Update user with social data
        if not user.first_name and extra_data.get('given_name'):
            user.first_name = extra_data.get('given_name', '')

        if not user.last_name and extra_data.get('family_name'):
            user.last_name = extra_data.get('family_name', '')

        # Ensure email is verified for social accounts
        if user.email:
            email_address, created = EmailAddress.objects.get_or_create(
                user=user,
                email=user.email,
                defaults={'verified': True, 'primary': True}
            )
            if not email_address.verified:
                email_address.verified = True
                email_address.save()

        user.save()

        # Create or update user profile with social data
        self.setup_user_profile(user, extra_data, provider)

        # Log successful social account creation
        logger.info(f"Created new user {user.username} via {provider} social login")

        return user

    def setup_user_profile(self, user, extra_data, provider):
        """
        Setup user profile with data from social provider
        """
        try:
            from .models import UserProfile

            profile, created = UserProfile.objects.get_or_create(user=user)

            # Extract profile picture URL
            if provider == 'google' and extra_data.get('picture'):
                # Store the profile picture URL (you might want to download and save it locally)
                profile.social_profile_picture_url = extra_data.get('picture')

            # Set default bio if not exists
            if not profile.bio and extra_data.get('name'):
                profile.bio = f"Hello! I'm {extra_data.get('name')} and I joined via {provider.title()}."

            profile.save()

        except Exception as e:
            logger.error(f"Error setting up user profile for {user.username}: {str(e)}")

    def get_login_redirect_url(self, request):
        """
        Intelligent redirect based on user status and profile completion
        """
        user = getattr(request, 'user', None)

        if user and user.is_authenticated:
            # Check if this is a new user
            is_new_user = self.is_new_social_user(user)

            if is_new_user:
                # New user - redirect to profile setup
                messages.success(
                    request,
                    f"🎉 Welcome to Forge X, {user.first_name or user.username}! "
                    "Let's set up your profile to get started."
                )
                return reverse('accounts:profile_setup') + '?welcome=google&new=true'
            else:
                # Existing user - redirect to dashboard
                messages.info(
                    request,
                    f"Welcome back, {user.first_name or user.username}! 👋"
                )
                return reverse('accounts:dashboard') + '?welcome=google'

        return reverse('accounts:dashboard') + '?welcome=google'

    def is_new_social_user(self, user):
        """
        Determine if this is a new user based on profile completion
        """
        try:
            from .models import UserProfile
            profile = UserProfile.objects.get(user=user)

            # Consider user new if profile is incomplete
            return not profile.bio and not profile.skills.exists()

        except UserProfile.DoesNotExist:
            return True

    def get_client_ip(self, request):
        """Get client IP address for logging"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def authentication_error(self, request, provider_id, error=None, exception=None, extra_context=None):
        """
        Handle authentication errors gracefully
        """
        logger.error(f"Social authentication error for provider {provider_id}: {error}")

        # Add user-friendly error message
        if 'access_denied' in str(error):
            messages.error(
                request,
                "Authentication was cancelled. Please try again if you want to sign in with Google."
            )
        else:
            messages.error(
                request,
                "There was an error signing in with Google. Please try again or use email/password login."
            )

        return reverse('accounts:login')
