{% extends "base.html" %}
{% load static %}
{% block title %}AI Learning Dashboard - Forge X{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
<div class="collaborate-card fade-in visible">
  <!-- Dashboard Header -->
  <div class="dashboard-hero">
    <h1>🤖 AI-Powered Learning Dashboard</h1>
    <p>Your personalized learning journey with intelligent insights and recommendations</p>
    {% if setup_required %}
    <div class="setup-notice">
      <p>⚠️ AI features are being set up. Please run migrations to enable full functionality.</p>
    </div>
    {% endif %}
  </div>

  <!-- Learning Analytics Overview -->
  <div class="analytics-overview">
    <h2>📊 Your Learning Analytics</h2>
    <div class="analytics-grid">
      <div class="analytics-card">
        <div class="analytics-icon">📚</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.total_lessons }}</div>
          <div class="analytics-label">Total Lessons</div>
        </div>
      </div>
      
      <div class="analytics-card">
        <div class="analytics-icon">✅</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.completed_lessons }}</div>
          <div class="analytics-label">Completed</div>
        </div>
      </div>
      
      <div class="analytics-card">
        <div class="analytics-icon">🏆</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.mastered_lessons }}</div>
          <div class="analytics-label">Mastered</div>
        </div>
      </div>
      
      <div class="analytics-card">
        <div class="analytics-icon">📈</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.completion_rate }}%</div>
          <div class="analytics-label">Completion Rate</div>
        </div>
      </div>
      
      <div class="analytics-card">
        <div class="analytics-icon">⭐</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.average_score }}</div>
          <div class="analytics-label">Avg Score</div>
        </div>
      </div>
      
      <div class="analytics-card">
        <div class="analytics-icon">⏱️</div>
        <div class="analytics-content">
          <div class="analytics-value">{{ progress_analytics.total_study_time }}m</div>
          <div class="analytics-label">Study Time</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Learning Profile -->
  <div class="learning-profile-section">
    <h2>👤 Your Learning Profile</h2>
    <div class="profile-content">
      <div class="profile-info">
        <div class="profile-item">
          <span class="profile-label">Learning Style:</span>
          <span class="profile-value">{{ learning_profile.learning_style|title|default:"Visual" }}</span>
        </div>
        <div class="profile-item">
          <span class="profile-label">Current Level:</span>
          <span class="profile-value">{{ learning_profile.current_skill_level|title|default:"Beginner" }}</span>
        </div>
        <div class="profile-item">
          <span class="profile-label">Preferred Difficulty:</span>
          <span class="profile-value">{{ learning_profile.preferred_difficulty|title|default:"Beginner" }}</span>
        </div>
      </div>
      <div class="profile-goals">
        <h4>🎯 Learning Goals</h4>
        {% if learning_profile %}
        <p>{{ learning_profile.learning_goals|default:"Set your learning goals to get personalized recommendations!" }}</p>
        {% else %}
        <p>Complete the setup to set your learning goals and get personalized recommendations!</p>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- AI Recommendations -->
  {% if recommendations %}
  <div class="ai-recommendations-section">
    <h2>🎯 AI-Powered Recommendations</h2>
    <div class="recommendations-grid">
      {% for rec in recommendations %}
      <div class="recommendation-card">
        <div class="rec-header">
          <div class="rec-type">{{ rec.type|title }}</div>
          <div class="rec-confidence">{{ rec.confidence|floatformat:0 }}% match</div>
        </div>
        <h3>{{ rec.title }}</h3>
        <p>{{ rec.description }}</p>
        <div class="rec-actions">
          {% if rec.lesson %}
          <a href="{% url 'learn:lesson_details_ai' rec.lesson.id %}" class="rec-action-btn primary">
            Start Learning →
          </a>
          {% elif rec.course %}
          <a href="{% url 'learn:course_details' rec.course.id %}" class="rec-action-btn primary">
            Explore Course →
          </a>
          {% endif %}
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <!-- Recent Activity -->
  <div class="recent-activity-section">
    <h2>📈 Recent Learning Activity</h2>
    <div class="activity-content">
      <div class="activity-lessons">
        <h3>Recent Lessons</h3>
        {% if recent_progress %}
        <div class="lessons-list">
          {% for progress in recent_progress %}
          <div class="lesson-item">
            <div class="lesson-info">
              <div class="lesson-title">
                <a href="{% url 'learn:lesson_details_ai' progress.lesson.id %}">
                  {{ progress.lesson.name }}
                </a>
              </div>
              <div class="lesson-meta">
                {{ progress.lesson.chapter.course.name }} • {{ progress.lesson.chapter.name }}
              </div>
            </div>
            <div class="lesson-progress">
              <div class="progress-status status-{{ progress.status }}">
                {% if progress.status == 'completed' %}✅
                {% elif progress.status == 'in_progress' %}🔄
                {% elif progress.status == 'mastered' %}🏆
                {% else %}📝{% endif %}
              </div>
              {% if progress.score %}
              <div class="progress-score">{{ progress.score|floatformat:0 }}%</div>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <p class="no-activity">No recent lesson activity. Start learning to see your progress here!</p>
        {% endif %}
      </div>
      
      <div class="activity-conversations">
        <h3>Recent AI Conversations</h3>
        {% if recent_conversations %}
        <div class="conversations-list">
          {% for conversation in recent_conversations %}
          <div class="conversation-item">
            <div class="conversation-type">{{ conversation.interaction_type|title }}</div>
            <div class="conversation-preview">{{ conversation.user_message|truncatechars:100 }}</div>
            <div class="conversation-time">{{ conversation.created_at|timesince }} ago</div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <p class="no-activity">No recent AI conversations. Try asking the AI assistant for help!</p>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="quick-actions-section">
    <h2>🚀 Quick Actions</h2>
    <div class="actions-grid">
      <a href="{% url 'learn:course_list' %}" class="action-card">
        <div class="action-icon">📚</div>
        <div class="action-title">Browse Courses</div>
        <div class="action-description">Explore available courses and start learning</div>
      </a>
      
      <a href="#" onclick="openAIChat()" class="action-card">
        <div class="action-icon">🤖</div>
        <div class="action-title">Ask AI Assistant</div>
        <div class="action-description">Get help with any programming question</div>
      </a>
      
      <a href="#" onclick="openCodeAnalyzer()" class="action-card">
        <div class="action-icon">🔍</div>
        <div class="action-title">Analyze Code</div>
        <div class="action-description">Get AI feedback on your code</div>
      </a>
      
      <a href="{% url 'accounts:profile' %}" class="action-card">
        <div class="action-icon">⚙️</div>
        <div class="action-title">Update Profile</div>
        <div class="action-description">Customize your learning preferences</div>
      </a>
    </div>
  </div>

  <!-- AI Chat Modal -->
  <div id="ai-chat-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>🤖 AI Learning Assistant</h3>
        <span class="close" onclick="closeAIChat()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="chat-container">
          <div class="chat-messages" id="modal-chat-messages"></div>
          <div class="chat-input-container">
            <textarea id="modal-chat-input" placeholder="Ask me anything about programming..." rows="3"></textarea>
            <button onclick="sendModalMessage()" class="send-btn">Send 🚀</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Code Analyzer Modal -->
  <div id="code-analyzer-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>🔍 AI Code Analyzer</h3>
        <span class="close" onclick="closeCodeAnalyzer()">&times;</span>
      </div>
      <div class="modal-body">
        <div class="code-analyzer-container">
          <div class="analyzer-controls">
            <select id="modal-code-language">
              <option value="python">Python</option>
              <option value="javascript">JavaScript</option>
              <option value="java">Java</option>
              <option value="cpp">C++</option>
            </select>
            <button onclick="analyzeModalCode()" class="analyze-btn">Analyze 🔍</button>
          </div>
          <textarea id="modal-code-input" placeholder="Paste your code here..." rows="15"></textarea>
          <div id="modal-analysis-results" class="analysis-results" style="display: none;"></div>
        </div>
      </div>
    </div>
  </div>
</div>
</section>

<!-- Include CSS and JavaScript -->
<link rel="stylesheet" href="{% static 'css/ai_learning.css' %}">
<link rel="stylesheet" href="{% static 'css/learning_dashboard.css' %}">
<script src="{% static 'js/ai_learning.js' %}"></script>
<script src="{% static 'js/learning_dashboard.js' %}"></script>

<!-- Particles.js -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

<script>
// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});
</script>

{% endblock %}
