from django.test import TestCase
from accounts.models import UserProfile, Skill
from collaborate.models import Project
from django.contrib.auth.models import User

class PairingModelTest(TestCase):
    def setUp(self):
        # Create skills
        skill1 = Skill.objects.create(name="Python")
        skill2 = Skill.objects.create(name="Django")
        skill3 = Skill.objects.create(name="JavaScript")

        # Create users with skills
        user1 = User.objects.create(username="user1")
        user2 = User.objects.create(username="user2")
        user3 = User.objects.create(username="user3")

        # Create profiles for users
        UserProfile.objects.create(user=user1)
        UserProfile.objects.create(user=user2)
        UserProfile.objects.create(user=user3)

        # Add skills to user profiles
        user1.profile.skills.add(skill1, skill2)
        user2.profile.skills.add(skill1)
        user3.profile.skills.add(skill3)

        # Create a project with required skills
        self.project = Project.objects.create(
            title="Test Project",
            description="A project for testing",
            owner=user1,
            type="user",
            status="open"
        )
        self.project.required_skills.add(skill1, skill2)

    def test_pairing_logic(self):
        # Run the pairing logic
        matched_users = self.project.match_users()

        # Assert the results
        self.assertEqual(len(matched_users), 3)  # Ensure all users are considered
        self.assertEqual(matched_users[0][0].username, "user1")  # Best match
        self.assertGreater(matched_users[0][1], matched_users[1][1])  # Ensure scores are sorted
