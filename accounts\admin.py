from django.contrib import admin
from .models import Skill, UserProfile, Event, SupportCategory, SupportTicket, SupportMessage, SupportAttachment

@admin.register(Skill)
class SkillAdmin(admin.ModelAdmin):
    list_display = ('name',)
    search_fields = ('name',)
    ordering = ('name',)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'get_email', 'get_skills')
    search_fields = ('user__username', 'user__email', 'bio')
    filter_horizontal = ('skills',)

    def get_email(self, obj):
        return obj.user.email
    get_email.short_description = 'Email'

    def get_skills(self, obj):
        return ", ".join([skill.name for skill in obj.skills.all()])
    get_skills.short_description = 'Skills'


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = ('title', 'event_date', 'location', 'created_by', 'is_active', 'created_at')
    list_filter = ('is_active', 'event_date', 'created_at')
    search_fields = ('title', 'description', 'location', 'created_by__username')
    readonly_fields = ('created_at', 'updated_at')
    ordering = ('-event_date',)

    fieldsets = (
        ('Event Information', {
            'fields': ('title', 'description', 'event_date', 'location', 'is_active')
        }),
        ('Meta Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # If creating a new event
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SupportCategory)
class SupportCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'description')
    readonly_fields = ('created_at',)


class SupportMessageInline(admin.TabularInline):
    model = SupportMessage
    extra = 0
    readonly_fields = ('sender', 'created_at')
    fields = ('sender', 'message', 'is_internal', 'created_at')


class SupportAttachmentInline(admin.TabularInline):
    model = SupportAttachment
    extra = 0
    readonly_fields = ('uploaded_by', 'uploaded_at')
    fields = ('file', 'filename', 'uploaded_by', 'uploaded_at')


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = ('ticket_id', 'subject', 'user', 'status', 'priority', 'assigned_to', 'created_at')
    list_filter = ('status', 'priority', 'category', 'assigned_to', 'created_at')
    search_fields = ('ticket_id', 'subject', 'description', 'user__username', 'user__email')
    readonly_fields = ('ticket_id', 'created_at', 'updated_at', 'resolved_at')
    inlines = [SupportMessageInline, SupportAttachmentInline]

    fieldsets = (
        ('Ticket Information', {
            'fields': ('ticket_id', 'user', 'subject', 'description', 'category')
        }),
        ('Status & Assignment', {
            'fields': ('status', 'priority', 'assigned_to')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'resolved_at'),
            'classes': ('collapse',)
        }),
    )

    def save_model(self, request, obj, form, change):
        if 'status' in form.changed_data and obj.status == 'resolved' and not obj.resolved_at:
            from django.utils import timezone
            obj.resolved_at = timezone.now()
        super().save_model(request, obj, form, change)


@admin.register(SupportMessage)
class SupportMessageAdmin(admin.ModelAdmin):
    list_display = ('ticket', 'sender', 'is_internal', 'created_at')
    list_filter = ('is_internal', 'created_at', 'sender__is_staff')
    search_fields = ('ticket__ticket_id', 'sender__username', 'message')
    readonly_fields = ('created_at',)


@admin.register(SupportAttachment)
class SupportAttachmentAdmin(admin.ModelAdmin):
    list_display = ('filename', 'ticket', 'uploaded_by', 'uploaded_at')
    list_filter = ('uploaded_at',)
    search_fields = ('filename', 'ticket__ticket_id', 'uploaded_by__username')
    readonly_fields = ('uploaded_at',)
