// Enhanced Y-WebSocket implementation for real collaboration
(function(global) {
  class WebsocketProvider {
    constructor(url, roomName, doc) {
      this.url = url;
      this.roomName = roomName;
      this.doc = doc;
      this.connected = false;
      this.awareness = new Awareness(this);
      this.ws = null;
      this.messageQueue = [];
      this.updateHandler = null;

      console.log(`<PERSON>socket<PERSON>rovider initialized with URL: ${url}, room: ${roomName}`);

      // Set up update handler to send changes to the server
      this.updateHandler = (update, origin) => {
        if (origin !== this) {
          this.sendUpdate(update);
        }
      };

      // Listen for document updates
      this.doc.on('update', this.updateHandler);

      // Connect to the WebSocket server
      this.connect();
    }

    connect() {
      try {
        // Make sure the URL uses port 8001 if it's connecting to localhost or 127.0.0.1
        let url = this.url;
        if (url.includes('localhost') || url.includes('127.0.0.1')) {
          // Replace the port if it's not already 8001
          const urlObj = new URL(url);
          if (urlObj.port !== '8001') {
            urlObj.port = '8001';
            url = urlObj.toString();
            console.log(`Adjusted WebSocket URL to use port 8001: ${url}`);
          }
        }

        // Create WebSocket connection
        this.ws = new WebSocket(url);

        // Connection opened
        this.ws.addEventListener('open', () => {
          console.log(`WebsocketProvider connected to ${url}`);
          this.connected = true;

          // Send any queued messages
          while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.ws.send(message);
          }

          // Send join room message
          this.ws.send(JSON.stringify({
            type: 'join',
            room: this.roomName
          }));
        });

        // Listen for messages
        this.ws.addEventListener('message', (event) => {
          try {
            const message = JSON.parse(event.data);

            if (message.type === 'update') {
              // Apply update to the document
              if (message.update && message.sender !== this.clientId) {
                const update = new Uint8Array(message.update);
                this.doc.applyUpdate(update, this);
              }
            } else if (message.type === 'awareness') {
              // Update awareness states
              if (message.states && message.sender !== this.clientId) {
                for (const [clientId, state] of Object.entries(message.states)) {
                  this.awareness.states.set(clientId, state);
                }
                // Notify awareness observers
                this.awareness.emit('update', [{ added: [], updated: Object.keys(message.states), removed: [] }]);
              }
            }
          } catch (error) {
            console.error('Error processing WebSocket message:', error);
          }
        });

        // Handle errors
        this.ws.addEventListener('error', (error) => {
          console.error('WebSocket error:', error);
        });

        // Handle disconnection
        this.ws.addEventListener('close', () => {
          console.log('WebSocket connection closed');
          this.connected = false;

          // Try to reconnect after a delay
          setTimeout(() => this.connect(), 5000);
        });

        // Generate a unique client ID
        this.clientId = Math.random().toString(36).substring(2, 15);
      } catch (error) {
        console.error('Error connecting to WebSocket:', error);
      }
    }

    disconnect() {
      if (this.ws) {
        this.ws.close();
      }
      this.connected = false;
      console.log(`WebsocketProvider disconnected from ${this.url}`);
    }

    sendUpdate(update) {
      const message = JSON.stringify({
        type: 'update',
        room: this.roomName,
        sender: this.clientId,
        update: Array.from(update)
      });

      console.log(`Sending update to room ${this.roomName}`);

      if (this.connected && this.ws && this.ws.readyState === WebSocket.OPEN) {
        console.log(`WebSocket is open, sending update immediately`);
        this.ws.send(message);
      } else {
        console.log(`WebSocket not ready (connected: ${this.connected}), queuing message`);
        // Queue message to send when connected
        this.messageQueue.push(message);
      }
    }

    destroy() {
      // Clean up
      this.doc.off('update', this.updateHandler);
      this.disconnect();
    }
  }

  class Awareness {
    constructor(provider) {
      this.provider = provider;
      this.states = new Map();
      this.observers = [];
    }

    setLocalState(state) {
      const clientId = this.provider.clientId;
      this.states.set(clientId, state);

      // Notify local observers
      this.emit('update', [{ added: [], updated: [clientId], removed: [] }]);

      // Send to other clients
      if (this.provider.connected && this.provider.ws) {
        const message = JSON.stringify({
          type: 'awareness',
          room: this.provider.roomName,
          sender: this.provider.clientId,
          states: { [clientId]: state }
        });
        this.provider.ws.send(message);
      }
    }

    getStates() {
      return this.states;
    }

    on(event, callback) {
      this.observers.push({ event, callback });
    }

    off(event, callback) {
      this.observers = this.observers.filter(
        observer => observer.event !== event || observer.callback !== callback
      );
    }

    emit(event, args) {
      this.observers
        .filter(observer => observer.event === event)
        .forEach(observer => observer.callback(...args));
    }
  }

  // Export to global scope
  global.WebsocketProvider = WebsocketProvider;
})(typeof window !== 'undefined' ? window : global);

console.log("Enhanced Y-WebSocket library loaded");