{% extends "base.html" %} {% block content %} {% load static %}
<div class="collaborate-card fade-in visible">
  <!-- Breadcrumb navigation -->
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_list' %}">Courses</a>
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item">
        <a href="{% url 'learn:course_details' course.id %}"
          >{{ course.name }}</a
        >
      </li>
      <li class="breadcrumb-separator">/</li>
      <li class="breadcrumb-item active" aria-current="page">
        {{ chapter.name }}
      </li>
    </ol>
  </nav>

  <div class="course-header">
    <h2>{{ chapter.name }}</h2>
  </div>
  {% if can_manage_course %}
  <div class="course-actions">
    <a href="{% url 'learn:create_lesson' chapter.id %}" class="btn btn-success"
      >📝 Create a Lesson</a
    >
    <a href="{% url 'learn:edit_chapter' chapter.id %}" class="btn btn-primary"
      >✏️ Edit Chapter</a
    >
  </div>
  {% endif %}
  <div class="course-description">
    <h3>Lessons</h3>
  </div>
  <ul class="chapter-list">
    {% for lesson in lessons %}
    <li class="chapter-box">
      <div class="course-header">
        <a
          href="{% url 'learn:lesson_details' lesson.id %}"
          class="lesson-link"
          >{{ lesson.name }}</a
        >
      </div>
      {% if can_manage_course %}
      <div class="project-actions">
        <a
          href="{% url 'learn:edit_lesson' lesson.id %}"
          class="btn btn-secondary btn-sm"
          >✏️ Edit</a
        >
        <a
          href="{% url 'learn:delete_lesson' lesson.id %}"
          onclick="return confirm('Delete this lesson and all its content?')"
          class="btn btn-danger btn-sm"
        >
          🗑️ Delete
        </a>
      </div>
      {% endif %}
    </li>
    {% endfor %}
  </ul>
</div>
{% endblock %}
