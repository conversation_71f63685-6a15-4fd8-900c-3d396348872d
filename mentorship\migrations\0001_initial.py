# Generated by Django 5.2.1 on 2025-05-28 07:04

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("collaborate", "0017_rename_team_match_log"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="MentorProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        help_text="Tell learners about your experience and expertise",
                        max_length=1000,
                    ),
                ),
                (
                    "hourly_rate",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Your hourly rate in USD",
                        max_digits=6,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("5.00")),
                            django.core.validators.MaxValueValidator(Decimal("500.00")),
                        ],
                    ),
                ),
                (
                    "available_slots",
                    models.JSONField(
                        default=list,
                        help_text="Available time slots for mentorship sessions",
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("total_sessions", models.PositiveIntegerField(default=0)),
                (
                    "average_rating",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        max_digits=3,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0.00")),
                            django.core.validators.MaxValueValidator(Decimal("5.00")),
                        ],
                    ),
                ),
                (
                    "total_earnings",
                    models.DecimalField(
                        decimal_places=2, default=Decimal("0.00"), max_digits=10
                    ),
                ),
                ("profile_completed", models.BooleanField(default=False)),
                ("verified", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "specializations",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Skills you can mentor in",
                        to="collaborate.skill",
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentor_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-average_rating", "-total_sessions"],
            },
        ),
        migrations.CreateModel(
            name="MentorshipSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "scheduled_time",
                    models.DateTimeField(
                        help_text="When the session is scheduled to start"
                    ),
                ),
                (
                    "duration_minutes",
                    models.PositiveIntegerField(
                        choices=[(60, "1 Hour"), (120, "2 Hours")],
                        default=60,
                        help_text="Duration of the session in minutes",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("no_show", "No Show"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                (
                    "room_id",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "hourly_rate",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Hourly rate at the time of booking",
                        max_digits=6,
                    ),
                ),
                (
                    "total_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Total amount for the session",
                        max_digits=8,
                    ),
                ),
                (
                    "commission_amount",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="ForgeX commission amount",
                        max_digits=8,
                    ),
                ),
                (
                    "mentor_earnings",
                    models.DecimalField(
                        decimal_places=2,
                        default=Decimal("0.00"),
                        help_text="Mentor's net earnings",
                        max_digits=8,
                    ),
                ),
                ("is_paid", models.BooleanField(default=False)),
                (
                    "payment_intent_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                (
                    "session_notes",
                    models.TextField(blank=True, help_text="Notes about the session"),
                ),
                (
                    "learner_feedback",
                    models.TextField(
                        blank=True, help_text="Learner's feedback after session"
                    ),
                ),
                (
                    "mentor_feedback",
                    models.TextField(
                        blank=True, help_text="Mentor's feedback after session"
                    ),
                ),
                (
                    "rating",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Learner's rating of the session (1-5)",
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                (
                    "learner",
                    models.ForeignKey(
                        help_text="The learner booking the session",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "mentor",
                    models.ForeignKey(
                        help_text="The mentor providing the session",
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentor_sessions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_time"],
            },
        ),
        migrations.CreateModel(
            name="MentorAvailability",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("date", models.DateField(help_text="Date for this availability")),
                (
                    "start_time",
                    models.TimeField(help_text="Start time for availability"),
                ),
                ("end_time", models.TimeField(help_text="End time for availability")),
                ("is_booked", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentor_availability",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        blank=True,
                        help_text="Session that booked this slot",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="mentorship.mentorshipsession",
                    ),
                ),
            ],
            options={
                "ordering": ["date", "start_time"],
            },
        ),
        migrations.CreateModel(
            name="SessionFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "communication_rating",
                    models.PositiveIntegerField(
                        help_text="How well did the mentor communicate?",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "knowledge_rating",
                    models.PositiveIntegerField(
                        help_text="How knowledgeable was the mentor?",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "helpfulness_rating",
                    models.PositiveIntegerField(
                        help_text="How helpful was the session?",
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                (
                    "would_recommend",
                    models.BooleanField(
                        help_text="Would you recommend this mentor to others?"
                    ),
                ),
                (
                    "what_went_well",
                    models.TextField(
                        blank=True, help_text="What went well in this session?"
                    ),
                ),
                (
                    "areas_for_improvement",
                    models.TextField(blank=True, help_text="What could be improved?"),
                ),
                (
                    "additional_comments",
                    models.TextField(blank=True, help_text="Any additional comments?"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "session",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="detailed_feedback",
                        to="mentorship.mentorshipsession",
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name="mentorshipsession",
            index=models.Index(
                fields=["learner", "status"], name="mentorship__learner_4ec1fc_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentorshipsession",
            index=models.Index(
                fields=["mentor", "status"], name="mentorship__mentor__5dfe8b_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentorshipsession",
            index=models.Index(
                fields=["scheduled_time"], name="mentorship__schedul_b5ef6e_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="mentorshipsession",
            index=models.Index(
                fields=["room_id"], name="mentorship__room_id_ad7d71_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="mentoravailability",
            unique_together={("mentor", "date", "start_time")},
        ),
    ]
