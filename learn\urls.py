from django.urls import path
from . import views
# for image
from django.conf import settings
from django.conf.urls.static import static
app_name = "learn"
urlpatterns = [
    path('', views.course_list, name='course_list'),
    path('course/<int:id>/', views.course_details, name='course_details'),
    path('chapter/<int:id>/', views.chapter_details, name='chapter_details'),
    path('lesson/<int:id>/', views.lesson_details, name='lesson_details'),
    path('lesson/<int:id>/content/', views.lesson_content, name='lesson_content'),
    path('create_course/', views.create_course, name='create_course'),
    path('create_chapter/<int:course_id>/', views.create_chapter, name='create_chapter'),
    path('create_lesson/<int:chapter_id>/', views.create_lesson, name='create_lesson'),
    path('lesson/<int:lesson_id>/content/create/', views.create_lesson_content, name='create_lesson_content'),
    path('course/<int:id>/delete/', views.delete_course, name='delete_course'),
    path('chapter/<int:id>/delete/', views.delete_chapter, name='delete_chapter'),
    path('lesson/<int:id>/delete/', views.delete_lesson, name='delete_lesson'),
    path('lessoncontent/<int:id>/delete/', views.delete_lesson_content, name='delete_lesson_content'),
    path('course/<int:id>/edit/', views.edit_course, name='edit_course'),
    path('chapter/<int:id>/edit/', views.edit_chapter, name='edit_chapter'),
    path('lesson/<int:id>/edit/', views.edit_lesson, name='edit_lesson'),
    path('lessoncontent/<int:id>/edit/', views.edit_lesson_content, name='edit_lesson_content'),

    # Course approval management
    path('admin/approvals/', views.manage_course_approvals, name='manage_course_approvals'),
    path('admin/approve/<int:course_id>/', views.approve_course, name='approve_course'),
    path('admin/reject/<int:course_id>/', views.reject_course, name='reject_course'),

    # AI-Powered Learning URLs
    path('lesson/<int:id>/ai/', views.lesson_details_ai, name='lesson_details_ai'),
    path('ai/chat/', views.ai_chat, name='ai_chat'),
    path('ai/analyze-code/', views.analyze_code, name='analyze_code'),
    path('ai/update-progress/', views.update_lesson_progress, name='update_lesson_progress'),
    path('dashboard/', views.learning_dashboard, name='learning_dashboard'),
    path('unified-dashboard/', views.unified_dashboard, name='unified_dashboard'),
]
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)