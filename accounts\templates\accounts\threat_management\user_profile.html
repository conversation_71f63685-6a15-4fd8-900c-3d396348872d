{% extends 'base.html' %}
{% load static %}

{% block title %}User Security Profile - {{ target_user.username }} - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Page Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">👤 User Security Profile</h1>
      <p class="welcome-subtitle">Security information and threat assessment for {{ target_user.username }}</p>
    </div>

    <!-- User Information -->
    <div class="dashboard-section">
      <h2>📋 User Information</h2>
      <div class="user-info-grid">
        <div class="info-card">
          <div class="info-header">
            <h3>Basic Information</h3>
          </div>
          <div class="info-content">
            <div class="info-item">
              <span class="info-label">Username:</span>
              <span class="info-value">{{ target_user.username }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Email:</span>
              <span class="info-value">{{ target_user.email }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Date Joined:</span>
              <span class="info-value">{{ target_user.date_joined|date:"M d, Y" }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Last Login:</span>
              <span class="info-value">
                {% if target_user.last_login %}
                  {{ target_user.last_login|date:"M d, Y H:i" }}
                {% else %}
                  Never
                {% endif %}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">Account Status:</span>
              <span class="info-value">
                {% if target_user.is_active %}
                  <span class="status-badge status-active">Active</span>
                {% else %}
                  <span class="status-badge status-inactive">Inactive</span>
                {% endif %}
              </span>
            </div>
          </div>
        </div>

        <div class="info-card">
          <div class="info-header">
            <h3>Threat Assessment</h3>
          </div>
          <div class="info-content">
            {% if threat_level %}
            <div class="info-item">
              <span class="info-label">Threat Level:</span>
              <span class="info-value">
                <span class="threat-badge threat-level-{{ threat_level.level }}">{{ threat_level.get_level_display }}</span>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">Total Violations:</span>
              <span class="info-value">{{ threat_level.total_violations }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Warnings Received:</span>
              <span class="info-value">{{ threat_level.warnings_received }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Last Violation:</span>
              <span class="info-value">
                {% if threat_level.last_violation %}
                  {{ threat_level.last_violation|date:"M d, Y H:i" }}
                {% else %}
                  None
                {% endif %}
              </span>
            </div>
            {% else %}
            <div class="no-threat-data">
              <p>No threat assessment data available for this user.</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Security Events -->
    <div class="dashboard-section">
      <h2>🚨 Recent Security Events</h2>
      {% if security_events %}
      <div class="events-table-container">
        <table class="events-table">
          <thead>
            <tr>
              <th>Date/Time</th>
              <th>Event Type</th>
              <th>Severity</th>
              <th>IP Address</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for event in security_events %}
            <tr class="event-row severity-{{ event.severity }}">
              <td class="event-time">{{ event.timestamp|date:"M d, Y H:i:s" }}</td>
              <td class="event-type">{{ event.get_event_type_display }}</td>
              <td class="event-severity">
                <span class="severity-badge severity-{{ event.severity }}">{{ event.severity|title }}</span>
              </td>
              <td class="event-ip">{{ event.ip_address }}</td>
              <td class="event-actions">
                <button class="btn btn-sm btn-warning" onclick="showWarningModal({{ target_user.id }}, {{ event.id }})">
                  <i class="fas fa-exclamation-triangle"></i> Warn
                </button>
                <button class="btn btn-sm btn-danger" onclick="showBlockModal('{{ event.ip_address }}', {{ target_user.id }}, [{{ event.id }}])">
                  <i class="fas fa-ban"></i> Block
                </button>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% else %}
      <div class="no-events-message">
        <div class="no-events-icon">🛡️</div>
        <h3>No Security Events</h3>
        <p>This user has no recorded security events.</p>
      </div>
      {% endif %}
    </div>

    <!-- Warnings History -->
    <div class="dashboard-section">
      <h2>⚠️ Warnings History</h2>
      {% if warnings %}
      <div class="warnings-list">
        {% for warning in warnings %}
        <div class="warning-card">
          <div class="warning-header">
            <h4>{{ warning.title }}</h4>
            <span class="warning-date">{{ warning.created_at|date:"M d, Y H:i" }}</span>
          </div>
          <div class="warning-content">
            <p>{{ warning.message|truncatewords:30 }}</p>
            <div class="warning-meta">
              <span class="warning-severity">
                <span class="severity-badge severity-{{ warning.severity }}">{{ warning.severity|title }}</span>
              </span>
              <span class="warning-admin">by {{ warning.admin.username }}</span>
              <span class="warning-status">
                {% if warning.is_delivered %}
                  <span class="status-badge status-delivered">Delivered</span>
                {% else %}
                  <span class="status-badge status-pending">Pending</span>
                {% endif %}
              </span>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
      {% else %}
      <div class="no-warnings-message">
        <div class="no-warnings-icon">📝</div>
        <h3>No Warnings</h3>
        <p>This user has not received any security warnings.</p>
      </div>
      {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="dashboard-section">
      <h2>⚡ Quick Actions</h2>
      <div class="action-grid">
        <button class="action-card action-button" onclick="showWarningModal({{ target_user.id }})">
          <div class="action-icon">⚠️</div>
          <h3>Send Warning</h3>
          <p>Send a security warning to this user</p>
        </button>
        <button class="action-card action-button" onclick="showThreatLevelModal({{ target_user.id }})">
          <div class="action-icon">🎯</div>
          <h3>Update Threat Level</h3>
          <p>Modify the user's threat assessment level</p>
        </button>
        <a href="{% url 'accounts:security_events_list' %}?user_filter={{ target_user.username }}" class="action-card">
          <div class="action-icon">📋</div>
          <h3>View All Events</h3>
          <p>See all security events for this user</p>
        </a>
        {% if target_user.is_active %}
        <button class="action-card action-button danger" onclick="suspendUser({{ target_user.id }})">
          <div class="action-icon">🚫</div>
          <h3>Suspend Account</h3>
          <p>Temporarily suspend this user account</p>
        </button>
        {% endif %}
      </div>
    </div>

    <!-- Back to Dashboard -->
    <div class="dashboard-section">
      <div class="back-to-dashboard">
        <a href="{% url 'accounts:threat_management_dashboard' %}" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Security Dashboard
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Security Warning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="warningForm">
                    <input type="hidden" id="warning-user-id">
                    <input type="hidden" id="warning-security-log-id">
                    
                    <div class="mb-3">
                        <label for="warning-template" class="form-label">Warning Template</label>
                        <select class="form-select" id="warning-template" onchange="loadTemplate()">
                            <option value="">Custom Message</option>
                            {% for key, template in warning_templates.items %}
                            <option value="{{ key }}">{{ template.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="warning-title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-message" class="form-label">Message</label>
                        <textarea class="form-control" id="warning-message" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-severity" class="form-label">Severity</label>
                        <select class="form-select" id="warning-severity" required>
                            <option value="warning">Warning</option>
                            <option value="final_warning">Final Warning</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="sendWarning()">Send Warning</button>
            </div>
        </div>
    </div>
</div>

<!-- Threat Level Modal -->
<div class="modal fade" id="threatLevelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Threat Level</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="threatLevelForm">
                    <input type="hidden" id="threat-user-id">
                    
                    <div class="mb-3">
                        <label for="threat-level" class="form-label">Threat Level</label>
                        <select class="form-select" id="threat-level" required>
                            <option value="green">Green - Low Risk</option>
                            <option value="yellow">Yellow - Medium Risk</option>
                            <option value="red">Red - High Risk</option>
                            <option value="critical">Critical - Immediate Threat</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="threat-reason" class="form-label">Reason for Change</label>
                        <textarea class="form-control" id="threat-reason" rows="3" required placeholder="Explain why you are changing the threat level..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateThreatLevel()">Update Level</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Section Styling - Match Main Dashboard */
    .dashboard-section {
      margin-bottom: 40px;
    }

    .dashboard-section h2 {
      color: #ffffff;
      margin-bottom: 30px;
      font-size: 1.8rem;
      font-weight: 600;
    }

    /* User Info Grid */
    .user-info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .info-card {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      overflow: hidden;
    }

    .info-header {
      background-color: rgba(192, 255, 107, 0.1);
      padding: 15px 20px;
      border-bottom: 1px solid rgba(192, 255, 107, 0.3);
    }

    .info-header h3 {
      color: var(--color-border);
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .info-content {
      padding: 20px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .info-item:last-child {
      border-bottom: none;
    }

    .info-label {
      color: #b0b0b0;
      font-weight: 500;
    }

    .info-value {
      color: #ffffff;
      font-weight: 600;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .status-active {
      background: rgba(40, 167, 69, 0.2);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .status-inactive {
      background: rgba(108, 117, 125, 0.2);
      color: #6c757d;
      border: 1px solid rgba(108, 117, 125, 0.3);
    }

    .status-delivered {
      background: rgba(40, 167, 69, 0.2);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .status-pending {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .threat-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .threat-level-green {
      background: rgba(40, 167, 69, 0.2);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .threat-level-yellow {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .threat-level-red {
      background: rgba(255, 107, 107, 0.2);
      color: #ff6b6b;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }

    .threat-level-critical {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
      animation: pulse-critical 2s infinite;
    }

    @keyframes pulse-critical {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }

    .no-threat-data {
      text-align: center;
      padding: 20px;
      color: #b0b0b0;
    }

    /* Events Table Styling */
    .events-table-container {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      overflow: hidden;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .events-table {
      width: 100%;
      border-collapse: collapse;
    }

    .events-table th {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      padding: 15px;
      text-align: left;
      font-weight: 600;
      border-bottom: 1px solid rgba(192, 255, 107, 0.3);
    }

    .events-table td {
      padding: 15px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      color: #ffffff;
    }

    .event-row:hover {
      background-color: rgba(192, 255, 107, 0.05);
    }

    .severity-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .severity-warning {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .severity-final_warning {
      background: rgba(255, 107, 107, 0.2);
      color: #ff6b6b;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }

    .severity-critical {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
      animation: pulse-critical 2s infinite;
    }

    /* Warnings List */
    .warnings-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .warning-card {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      transition: all 0.3s ease;
    }

    .warning-card:hover {
      border-color: rgba(192, 255, 107, 0.4);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .warning-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .warning-header h4 {
      color: var(--color-border);
      margin: 0;
      font-size: 1.1rem;
    }

    .warning-date {
      color: #b0b0b0;
      font-size: 0.9rem;
    }

    .warning-content p {
      color: #ffffff;
      margin-bottom: 15px;
    }

    .warning-meta {
      display: flex;
      gap: 15px;
      align-items: center;
      flex-wrap: wrap;
    }

    .warning-admin {
      color: #b0b0b0;
      font-size: 0.9rem;
    }

    /* Action Grid - Match Main Dashboard */
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .action-card {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      text-align: center;
      border: 1px solid rgba(192, 255, 107, 0.2);
      transition: all 0.3s ease;
      text-decoration: none;
      color: inherit;
      display: block;
    }

    .action-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
      border-color: rgba(192, 255, 107, 0.4);
      text-decoration: none;
      color: inherit;
    }

    .action-card.danger {
      border-color: rgba(220, 53, 69, 0.3);
    }

    .action-card.danger:hover {
      border-color: rgba(220, 53, 69, 0.5);
    }

    .action-icon {
      font-size: 2.5rem;
      margin-bottom: 15px;
    }

    .action-card h3 {
      font-size: 1.2rem;
      color: var(--color-border);
      margin-bottom: 10px;
    }

    .action-card p {
      color: #b0b0b0;
      margin: 0;
      font-size: 0.9rem;
    }

    .action-button {
      background: none;
      border: none;
      cursor: pointer;
      text-align: center;
      width: 100%;
      padding: 25px;
    }

    /* No Messages */
    .no-events-message,
    .no-warnings-message {
      text-align: center;
      padding: 60px 20px;
      background-color: rgba(40, 40, 40, 0.3);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .no-events-icon,
    .no-warnings-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    .no-events-message h3,
    .no-warnings-message h3 {
      color: var(--color-border);
      margin-bottom: 10px;
    }

    .no-events-message p,
    .no-warnings-message p {
      color: #b0b0b0;
    }

    /* Buttons */
    .btn {
      padding: 8px 16px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-block;
      border: none;
      cursor: pointer;
      font-size: 0.9rem;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.8rem;
    }

    .btn-primary {
      background-color: var(--color-border);
      color: #000;
    }

    .btn-secondary {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
    }

    .btn-warning {
      background-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .btn-danger {
      background-color: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Form Controls */
    .form-control,
    .form-select {
      background-color: rgba(60, 60, 60, 0.8);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      padding: 10px 15px;
      color: #ffffff;
      font-size: 0.9rem;
    }

    .form-control:focus,
    .form-select:focus {
      border-color: var(--color-border);
      box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
      outline: none;
    }

    .form-label {
      color: #ffffff;
      margin-bottom: 8px;
      font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .user-info-grid {
        grid-template-columns: 1fr;
      }

      .action-grid {
        grid-template-columns: 1fr;
      }

      .events-table-container {
        overflow-x: auto;
      }

      .events-table {
        min-width: 700px;
      }

      .warning-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .warning-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .dashboard-section h2 {
        font-size: 1.5rem;
      }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<script>
// Warning templates data
const warningTemplates = {{ warning_templates|safe }};

function showWarningModal(userId, securityLogId = null) {
    document.getElementById('warning-user-id').value = userId;
    document.getElementById('warning-security-log-id').value = securityLogId || '';
    document.getElementById('warning-template').value = '';
    document.getElementById('warning-title').value = '';
    document.getElementById('warning-message').value = '';
    document.getElementById('warning-severity').value = 'warning';

    new bootstrap.Modal(document.getElementById('warningModal')).show();
}

function showBlockModal(ipAddress, userId = null, securityLogIds = []) {
    document.getElementById('block-ip').value = ipAddress;
    document.getElementById('block-user-id').value = userId || '';
    document.getElementById('block-security-log-ids').value = JSON.stringify(securityLogIds);
    document.getElementById('block-type').value = 'temporary';
    document.getElementById('block-duration').value = '24';
    document.getElementById('block-reason').value = 'security_violation';
    document.getElementById('block-custom-reason').value = '';

    new bootstrap.Modal(document.getElementById('blockModal')).show();
}

function showThreatLevelModal(userId) {
    document.getElementById('threat-user-id').value = userId;
    document.getElementById('threat-level').value = '{% if threat_level %}{{ threat_level.level }}{% else %}green{% endif %}';
    document.getElementById('threat-reason').value = '';

    new bootstrap.Modal(document.getElementById('threatLevelModal')).show();
}

function loadTemplate() {
    const templateKey = document.getElementById('warning-template').value;
    if (templateKey && warningTemplates[templateKey]) {
        const template = warningTemplates[templateKey];
        document.getElementById('warning-title').value = template.title;
        document.getElementById('warning-message').value = template.message;
        document.getElementById('warning-severity').value = template.severity;
    }
}

function sendWarning() {
    const formData = {
        user_id: document.getElementById('warning-user-id').value,
        template: document.getElementById('warning-template').value,
        custom_title: document.getElementById('warning-title').value,
        custom_message: document.getElementById('warning-message').value,
        severity: document.getElementById('warning-severity').value,
        security_log_id: document.getElementById('warning-security-log-id').value || null
    };

    fetch('{% url "accounts:api_send_warning" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Warning sent successfully!');
            bootstrap.Modal.getInstance(document.getElementById('warningModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error sending warning: ' + error.message);
    });
}

function updateThreatLevel() {
    const formData = {
        user_id: document.getElementById('threat-user-id').value,
        level: document.getElementById('threat-level').value,
        reason: document.getElementById('threat-reason').value
    };

    fetch('{% url "accounts:api_update_threat_level" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Threat level updated successfully!');
            bootstrap.Modal.getInstance(document.getElementById('threatLevelModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error updating threat level: ' + error.message);
    });
}

function suspendUser(userId) {
    if (!confirm('Are you sure you want to suspend this user account? This action can be reversed later.')) {
        return;
    }

    fetch('{% url "accounts:api_suspend_user" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify({
            user_id: userId,
            reason: 'Suspended via security dashboard'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('User account suspended successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error suspending user: ' + error.message);
    });
}
</script>
{% endblock %}
