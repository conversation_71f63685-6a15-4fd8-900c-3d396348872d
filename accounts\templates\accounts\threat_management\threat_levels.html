{% extends 'base.html' %}
{% load static %}

{% block title %}User Threat Levels - ForgeX{% endblock %}

{% block content %}
<!-- Particle background -->
<div id="particles-js" class="particles-container"></div>

<section class="collaborate-page">
  <div class="collaborate-card fade-in">
    <!-- Messages -->
    {% if messages %}
    <div class="messages-container">
      {% for message in messages %}
        <div class="alert alert-{{ message.tags }} slide-down">
          <span class="alert-icon">
            {% if message.tags == 'success' %}🎉{% elif message.tags == 'info' %}ℹ️{% elif message.tags == 'warning' %}⚠️{% else %}❌{% endif %}
          </span>
          {{ message }}
          <button class="alert-close" onclick="this.parentElement.style.display='none'">×</button>
        </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Page Header -->
    <div class="dashboard-welcome">
      <h1 class="nunito-sans-header">🎯 User Threat Levels</h1>
      <p class="welcome-subtitle">Monitor and manage user threat assessments and security classifications</p>
    </div>

    <!-- Filters -->
    <div class="dashboard-section">
      <h2>🔍 Filter Users</h2>
      <form method="get" class="filter-form">
        <div class="filter-grid">
          <div class="filter-item">
            <label for="level">Threat Level:</label>
            <select name="level" id="level" class="form-control">
              <option value="">All Levels</option>
              <option value="green" {% if request.GET.level == 'green' %}selected{% endif %}>Green - Low Risk</option>
              <option value="yellow" {% if request.GET.level == 'yellow' %}selected{% endif %}>Yellow - Medium Risk</option>
              <option value="red" {% if request.GET.level == 'red' %}selected{% endif %}>Red - High Risk</option>
              <option value="critical" {% if request.GET.level == 'critical' %}selected{% endif %}>Critical - Immediate Threat</option>
            </select>
          </div>
          <div class="filter-item">
            <label for="user_filter">Username:</label>
            <input type="text" name="user_filter" id="user_filter" value="{{ request.GET.user_filter }}" placeholder="Search username" class="form-control">
          </div>
          <div class="filter-item">
            <label for="min_violations">Min Violations:</label>
            <input type="number" name="min_violations" id="min_violations" value="{{ request.GET.min_violations }}" placeholder="0" class="form-control">
          </div>
          <div class="filter-item">
            <button type="submit" class="btn btn-primary">Apply Filters</button>
            <a href="{% url 'accounts:user_threat_levels' %}" class="btn btn-secondary">Clear</a>
          </div>
        </div>
      </form>
    </div>

    <!-- Threat Level Statistics -->
    <div class="dashboard-section">
      <h2>📊 Threat Level Overview</h2>
      <div class="stats-grid">
        <div class="stat-card stat-green">
          <div class="stat-icon">🟢</div>
          <div class="stat-content">
            <span class="stat-number">{{ stats.low_threat }}</span>
            <span class="stat-label">Green - Low Risk</span>
          </div>
        </div>
        <div class="stat-card stat-yellow">
          <div class="stat-icon">🟡</div>
          <div class="stat-content">
            <span class="stat-number">{{ stats.medium_threat }}</span>
            <span class="stat-label">Yellow - Medium Risk</span>
          </div>
        </div>
        <div class="stat-card stat-red">
          <div class="stat-icon">🟠</div>
          <div class="stat-content">
            <span class="stat-number">{{ stats.high_threat }}</span>
            <span class="stat-label">Red - High Risk</span>
          </div>
        </div>
        <div class="stat-card stat-critical">
          <div class="stat-icon">🔴</div>
          <div class="stat-content">
            <span class="stat-number">{{ stats.critical_threat }}</span>
            <span class="stat-label">Critical - Immediate Threat</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Threat Levels List -->
    <div class="dashboard-section">
      <h2>👥 User Threat Levels ({{ page_obj.object_list|length }} found)</h2>
      
      {% if page_obj.object_list %}
      <div class="threat-levels-grid">
        {% for threat_level in page_obj.object_list %}
        <div class="threat-level-card threat-level-{{ threat_level.level }}">
          <div class="threat-header">
            <div class="user-info">
              <h3>{{ threat_level.user.username }}</h3>
              <p class="user-email">{{ threat_level.user.email }}</p>
            </div>
            <span class="threat-badge threat-level-{{ threat_level.level }}">{{ threat_level.get_level_display }}</span>
          </div>
          
          <div class="threat-stats">
            <div class="stat-item">
              <span class="stat-label">Violations</span>
              <span class="stat-value">{{ threat_level.total_violations }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Warnings</span>
              <span class="stat-value">{{ threat_level.warnings_received }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Last Violation</span>
              <span class="stat-value">
                {% if threat_level.last_violation %}
                  {{ threat_level.last_violation|date:"M d, Y" }}
                {% else %}
                  None
                {% endif %}
              </span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Updated</span>
              <span class="stat-value">{{ threat_level.updated_at|date:"M d, Y" }}</span>
            </div>
          </div>
          
          <div class="threat-actions">
            <button class="btn btn-sm btn-warning" onclick="showWarningModal({{ threat_level.user.id }})">
              <i class="fas fa-exclamation-triangle"></i> Send Warning
            </button>
            <button class="btn btn-sm btn-primary" onclick="showThreatLevelModal({{ threat_level.user.id }}, '{{ threat_level.level }}')">
              <i class="fas fa-edit"></i> Update Level
            </button>
            <a href="{% url 'accounts:user_security_profile' threat_level.user.id %}" class="btn btn-sm btn-info">
              <i class="fas fa-user"></i> View Profile
            </a>
          </div>
        </div>
        {% endfor %}
      </div>

      <!-- Pagination -->
      {% if is_paginated %}
      <div class="pagination-container">
        <div class="pagination">
          {% if page_obj.has_previous %}
            <a href="?page=1{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}{% if request.GET.min_violations %}&min_violations={{ request.GET.min_violations }}{% endif %}" class="page-link">&laquo; First</a>
            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}{% if request.GET.min_violations %}&min_violations={{ request.GET.min_violations }}{% endif %}" class="page-link">Previous</a>
          {% endif %}
          
          <span class="page-info">
            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
          </span>
          
          {% if page_obj.has_next %}
            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}{% if request.GET.min_violations %}&min_violations={{ request.GET.min_violations }}{% endif %}" class="page-link">Next</a>
            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.level %}&level={{ request.GET.level }}{% endif %}{% if request.GET.user_filter %}&user_filter={{ request.GET.user_filter }}{% endif %}{% if request.GET.min_violations %}&min_violations={{ request.GET.min_violations }}{% endif %}" class="page-link">Last &raquo;</a>
          {% endif %}
        </div>
      </div>
      {% endif %}

      {% else %}
      <div class="no-threats-message">
        <div class="no-threats-icon">🎯</div>
        <h3>No Threat Levels Found</h3>
        <p>No users match your current filters. Try adjusting your search criteria.</p>
      </div>
      {% endif %}
    </div>

    <!-- Back to Dashboard -->
    <div class="dashboard-section">
      <div class="back-to-dashboard">
        <a href="{% url 'accounts:threat_management_dashboard' %}" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i> Back to Security Dashboard
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Security Warning</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="warningForm">
                    <input type="hidden" id="warning-user-id">
                    
                    <div class="mb-3">
                        <label for="warning-template" class="form-label">Warning Template</label>
                        <select class="form-select" id="warning-template" onchange="loadTemplate()">
                            <option value="">Custom Message</option>
                            {% for key, template in warning_templates.items %}
                            <option value="{{ key }}">{{ template.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="warning-title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-message" class="form-label">Message</label>
                        <textarea class="form-control" id="warning-message" rows="4" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="warning-severity" class="form-label">Severity</label>
                        <select class="form-select" id="warning-severity" required>
                            <option value="warning">Warning</option>
                            <option value="final_warning">Final Warning</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="sendWarning()">Send Warning</button>
            </div>
        </div>
    </div>
</div>

<!-- Threat Level Modal -->
<div class="modal fade" id="threatLevelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Threat Level</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="threatLevelForm">
                    <input type="hidden" id="threat-user-id">
                    
                    <div class="mb-3">
                        <label for="threat-level" class="form-label">Threat Level</label>
                        <select class="form-select" id="threat-level" required>
                            <option value="green">Green - Low Risk</option>
                            <option value="yellow">Yellow - Medium Risk</option>
                            <option value="red">Red - High Risk</option>
                            <option value="critical">Critical - Immediate Threat</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="threat-reason" class="form-label">Reason for Change</label>
                        <textarea class="form-control" id="threat-reason" rows="3" required placeholder="Explain why you are changing the threat level..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateThreatLevel()">Update Level</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Dashboard Section Styling - Match Main Dashboard */
    .dashboard-section {
      margin-bottom: 40px;
    }

    .dashboard-section h2 {
      color: #ffffff;
      margin-bottom: 30px;
      font-size: 1.8rem;
      font-weight: 600;
    }

    /* Filter Form Styling */
    .filter-form {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      margin-bottom: 30px;
    }

    .filter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      align-items: end;
    }

    .filter-item label {
      display: block;
      color: #ffffff;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-control {
      background-color: rgba(60, 60, 60, 0.8);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      padding: 10px 15px;
      color: #ffffff;
      font-size: 0.9rem;
    }

    .form-control:focus {
      border-color: var(--color-border);
      box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
      outline: none;
    }

    /* Stats Grid */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .stat-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }

    .stat-icon {
      font-size: 2.5rem;
    }

    .stat-content {
      display: flex;
      flex-direction: column;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #ffffff;
    }

    .stat-label {
      color: #b0b0b0;
      font-size: 0.9rem;
      font-weight: 500;
    }

    .stat-green {
      border-color: rgba(40, 167, 69, 0.3);
    }

    .stat-yellow {
      border-color: rgba(255, 193, 7, 0.3);
    }

    .stat-red {
      border-color: rgba(255, 107, 107, 0.3);
    }

    .stat-critical {
      border-color: rgba(220, 53, 69, 0.3);
    }

    /* Threat Levels Grid */
    .threat-levels-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .threat-level-card {
      background-color: rgba(40, 40, 40, 0.5);
      border-radius: 12px;
      padding: 25px;
      border: 1px solid rgba(192, 255, 107, 0.2);
      transition: all 0.3s ease;
      position: relative;
    }

    .threat-level-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }

    .threat-level-card.threat-level-green {
      border-left: 4px solid #28a745;
    }

    .threat-level-card.threat-level-yellow {
      border-left: 4px solid #ffc107;
    }

    .threat-level-card.threat-level-red {
      border-left: 4px solid #ff6b6b;
    }

    .threat-level-card.threat-level-critical {
      border-left: 4px solid #dc3545;
      animation: pulse-critical-card 3s infinite;
    }

    @keyframes pulse-critical-card {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.2); }
      70% { box-shadow: 0 0 0 15px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }

    .threat-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 20px;
    }

    .user-info h3 {
      color: var(--color-border);
      margin: 0 0 5px 0;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .user-email {
      color: #b0b0b0;
      margin: 0;
      font-size: 0.9rem;
    }

    .threat-badge {
      padding: 6px 15px;
      border-radius: 20px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .threat-level-green {
      background: rgba(40, 167, 69, 0.2);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .threat-level-yellow {
      background: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .threat-level-red {
      background: rgba(255, 107, 107, 0.2);
      color: #ff6b6b;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }

    .threat-level-critical {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
      animation: pulse-critical 2s infinite;
    }

    @keyframes pulse-critical {
      0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4); }
      70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
      100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
    }

    .threat-stats {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-bottom: 20px;
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .stat-item .stat-label {
      color: #b0b0b0;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .stat-item .stat-value {
      color: #ffffff;
      font-weight: 600;
      font-size: 1rem;
    }

    .threat-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 8px 16px;
      border-radius: 8px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
      display: inline-block;
      border: none;
      cursor: pointer;
      font-size: 0.9rem;
    }

    .btn-sm {
      padding: 6px 12px;
      font-size: 0.8rem;
    }

    .btn-primary {
      background-color: var(--color-border);
      color: #000;
    }

    .btn-secondary {
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
    }

    .btn-warning {
      background-color: rgba(255, 193, 7, 0.2);
      color: #ffc107;
      border: 1px solid rgba(255, 193, 7, 0.3);
    }

    .btn-info {
      background-color: rgba(23, 162, 184, 0.2);
      color: #17a2b8;
      border: 1px solid rgba(23, 162, 184, 0.3);
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Pagination Styling */
    .pagination-container {
      text-align: center;
      margin-top: 30px;
    }

    .pagination {
      display: inline-flex;
      gap: 10px;
      align-items: center;
    }

    .page-link {
      padding: 8px 16px;
      background-color: rgba(192, 255, 107, 0.1);
      color: var(--color-border);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .page-link:hover {
      background-color: rgba(192, 255, 107, 0.2);
      transform: translateY(-2px);
    }

    .page-info {
      color: #ffffff;
      font-weight: 500;
      margin: 0 15px;
    }

    /* No Threats Message */
    .no-threats-message {
      text-align: center;
      padding: 60px 20px;
      background-color: rgba(40, 40, 40, 0.3);
      border-radius: 12px;
      border: 1px solid rgba(192, 255, 107, 0.2);
    }

    .no-threats-icon {
      font-size: 4rem;
      margin-bottom: 20px;
    }

    .no-threats-message h3 {
      color: var(--color-border);
      margin-bottom: 10px;
    }

    .no-threats-message p {
      color: #b0b0b0;
    }

    /* Form Controls */
    .form-select {
      background-color: rgba(60, 60, 60, 0.8);
      border: 1px solid rgba(192, 255, 107, 0.3);
      border-radius: 8px;
      padding: 10px 15px;
      color: #ffffff;
      font-size: 0.9rem;
    }

    .form-select:focus {
      border-color: var(--color-border);
      box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
      outline: none;
    }

    .form-label {
      color: #ffffff;
      margin-bottom: 8px;
      font-weight: 500;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .filter-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .threat-levels-grid {
        grid-template-columns: 1fr;
      }

      .threat-stats {
        grid-template-columns: 1fr;
      }

      .threat-actions {
        flex-direction: column;
        gap: 10px;
      }

      .pagination {
        flex-direction: column;
        gap: 10px;
      }

      .dashboard-section h2 {
        font-size: 1.5rem;
      }
    }

    @media (max-width: 480px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Particles.js script -->
<script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize particles.js
    particlesJS('particles-js', {
      "particles": {
        "number": {
          "value": 50,
          "density": {
            "enable": true,
            "value_area": 800
          }
        },
        "color": {
          "value": ["#C0ff6b", "#a0e066"]
        },
        "shape": {
          "type": "circle"
        },
        "opacity": {
          "value": 0.5,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 1,
            "opacity_min": 0.1,
            "sync": false
          }
        },
        "size": {
          "value": 3,
          "random": true,
          "anim": {
            "enable": true,
            "speed": 2,
            "size_min": 0.1,
            "sync": false
          }
        },
        "line_linked": {
          "enable": true,
          "distance": 150,
          "color": "#C0ff6b",
          "opacity": 0.4,
          "width": 1
        },
        "move": {
          "enable": true,
          "speed": 1,
          "direction": "none",
          "random": true,
          "straight": false,
          "out_mode": "out",
          "bounce": false
        }
      },
      "interactivity": {
        "detect_on": "canvas",
        "events": {
          "onhover": {
            "enable": true,
            "mode": "grab"
          },
          "onclick": {
            "enable": true,
            "mode": "push"
          },
          "resize": true
        }
      },
      "retina_detect": true
    });

    // Animation for elements
    function revealElements() {
      const elements = document.querySelectorAll('.fade-in, .slide-right, .slide-left');
      elements.forEach(element => {
        element.classList.add('visible');
      });
    }

    // Run animations
    setTimeout(revealElements, 100);
  });
</script>

<script>
// Warning templates data
const warningTemplates = {{ warning_templates|safe }};

function showWarningModal(userId) {
    document.getElementById('warning-user-id').value = userId;
    document.getElementById('warning-template').value = '';
    document.getElementById('warning-title').value = '';
    document.getElementById('warning-message').value = '';
    document.getElementById('warning-severity').value = 'warning';

    new bootstrap.Modal(document.getElementById('warningModal')).show();
}

function showThreatLevelModal(userId, currentLevel) {
    document.getElementById('threat-user-id').value = userId;
    document.getElementById('threat-level').value = currentLevel;
    document.getElementById('threat-reason').value = '';

    new bootstrap.Modal(document.getElementById('threatLevelModal')).show();
}

function loadTemplate() {
    const templateKey = document.getElementById('warning-template').value;
    if (templateKey && warningTemplates[templateKey]) {
        const template = warningTemplates[templateKey];
        document.getElementById('warning-title').value = template.title;
        document.getElementById('warning-message').value = template.message;
        document.getElementById('warning-severity').value = template.severity;
    }
}

function sendWarning() {
    const formData = {
        user_id: document.getElementById('warning-user-id').value,
        template: document.getElementById('warning-template').value,
        custom_title: document.getElementById('warning-title').value,
        custom_message: document.getElementById('warning-message').value,
        severity: document.getElementById('warning-severity').value
    };

    fetch('{% url "accounts:api_send_warning" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Warning sent successfully!');
            bootstrap.Modal.getInstance(document.getElementById('warningModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error sending warning: ' + error.message);
    });
}

function updateThreatLevel() {
    const formData = {
        user_id: document.getElementById('threat-user-id').value,
        level: document.getElementById('threat-level').value,
        reason: document.getElementById('threat-reason').value
    };

    fetch('{% url "accounts:api_update_threat_level" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Threat level updated successfully!');
            bootstrap.Modal.getInstance(document.getElementById('threatLevelModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error updating threat level: ' + error.message);
    });
}
</script>
{% endblock %}
