{% extends 'base.html' %}
{% load static %}

{% block title %}Security Warnings - ForgeX{% endblock %}

{% block extra_css %}
<style>
    .warnings-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    
    .warning-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
        transition: transform 0.3s ease;
    }
    
    .warning-card:hover {
        transform: translateY(-2px);
    }
    
    .warning-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .warning-header.severity-warning {
        background: linear-gradient(135deg, #ffc107 0%, #ffb300 100%);
        color: #212529;
    }
    
    .warning-header.severity-final_warning {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        color: white;
    }
    
    .warning-header.severity-critical {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    
    .warning-icon {
        font-size: 2rem;
        margin-right: 1rem;
    }
    
    .warning-title {
        font-size: 1.3rem;
        font-weight: bold;
        margin: 0;
        flex: 1;
    }
    
    .warning-severity {
        background: rgba(255, 255, 255, 0.2);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .warning-body {
        padding: 1.5rem;
    }
    
    .warning-message {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 1rem;
    }
    
    .warning-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .warning-actions {
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    
    .status-delivered {
        background: #d1ecf1;
        color: #0c5460;
    }
    
    .status-acknowledged {
        background: #d4edda;
        color: #155724;
    }
    
    .no-warnings {
        text-align: center;
        padding: 3rem;
        color: #6c757d;
    }
    
    .no-warnings-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #28a745;
    }
    
    .security-tips {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-top: 2rem;
    }
    
    .tips-title {
        color: #1976d2;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    
    .tips-list {
        margin: 0;
        padding-left: 1.5rem;
    }
    
    .tips-list li {
        margin-bottom: 0.5rem;
        color: #1976d2;
    }
    
    .warning-modal .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }
    
    .warning-modal .modal-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        border-radius: 15px 15px 0 0;
    }
    
    .pulse-animation {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="warnings-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-exclamation-triangle text-warning"></i> Security Warnings</h1>
        <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
    
    {% if pending_warnings %}
    <div class="alert alert-warning">
        <i class="fas fa-bell"></i>
        <strong>New Warnings:</strong> You have {{ pending_warnings|length }} new security warning{{ pending_warnings|length|pluralize }}.
    </div>
    {% endif %}
    
    {% for warning in all_warnings %}
    <div class="warning-card {% if warning.status == 'pending' %}pulse-animation{% endif %}">
        <div class="warning-header severity-{{ warning.severity }}">
            <div class="warning-icon">
                {% if warning.severity == 'critical' %}
                    <i class="fas fa-exclamation-triangle"></i>
                {% elif warning.severity == 'final_warning' %}
                    <i class="fas fa-exclamation-circle"></i>
                {% else %}
                    <i class="fas fa-info-circle"></i>
                {% endif %}
            </div>
            <h3 class="warning-title">{{ warning.title }}</h3>
            <span class="warning-severity">{{ warning.get_severity_display }}</span>
        </div>
        
        <div class="warning-body">
            <div class="warning-message">
                {{ warning.message|linebreaks }}
            </div>
            
            <div class="warning-meta">
                <div>
                    <strong>Sent by:</strong> {{ warning.admin.get_full_name|default:warning.admin.username }}
                </div>
                <div>
                    <strong>Date:</strong> {{ warning.created_at|date:"M d, Y H:i:s" }}
                </div>
                {% if warning.delivered_at %}
                <div>
                    <strong>Delivered:</strong> {{ warning.delivered_at|date:"M d, Y H:i:s" }}
                </div>
                {% endif %}
                {% if warning.acknowledged_at %}
                <div>
                    <strong>Acknowledged:</strong> {{ warning.acknowledged_at|date:"M d, Y H:i:s" }}
                </div>
                {% endif %}
                {% if warning.expires_at %}
                <div>
                    <strong>Expires:</strong> {{ warning.expires_at|date:"M d, Y H:i:s" }}
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="warning-actions">
            <span class="status-badge status-{{ warning.status }}">
                {{ warning.get_status_display }}
            </span>
            
            {% if warning.status != 'acknowledged' %}
            <button class="btn btn-primary btn-sm" onclick="acknowledgeWarning({{ warning.id }})">
                <i class="fas fa-check"></i> Acknowledge
            </button>
            {% else %}
            <span class="text-success">
                <i class="fas fa-check-circle"></i> Acknowledged
            </span>
            {% endif %}
        </div>
    </div>
    {% empty %}
    <div class="no-warnings">
        <div class="no-warnings-icon">
            <i class="fas fa-shield-alt"></i>
        </div>
        <h3>No Security Warnings</h3>
        <p>You have no security warnings at this time. Keep up the good work!</p>
    </div>
    {% endfor %}
    
    <div class="security-tips">
        <div class="tips-title">
            <i class="fas fa-lightbulb"></i> Security Best Practices
        </div>
        <ul class="tips-list">
            <li>Do not attempt to access browser developer tools (F12, Ctrl+Shift+I)</li>
            <li>Avoid using browser console or attempting to modify page content</li>
            <li>Do not try to bypass security measures or access restricted areas</li>
            <li>Follow all platform terms of service and security policies</li>
            <li>Report any security concerns through proper channels</li>
            <li>Keep your account credentials secure and do not share them</li>
        </ul>
    </div>
    
    {% if all_warnings %}
    <div class="text-center mt-4">
        <p class="text-muted">
            <small>
                If you have questions about these warnings, please contact support at 
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </small>
        </p>
    </div>
    {% endif %}
</div>

<!-- Warning Detail Modal -->
<div class="modal fade warning-modal" id="warningDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Warning Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="warningDetailContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="acknowledgeModalBtn">
                    <i class="fas fa-check"></i> Acknowledge
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function acknowledgeWarning(warningId) {
    if (!confirm('Are you sure you want to acknowledge this warning?')) {
        return;
    }
    
    fetch(`{% url 'accounts:acknowledge_warning' 0 %}`.replace('0', warningId), {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error acknowledging warning: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}

// Auto-mark pending warnings as delivered when page loads
document.addEventListener('DOMContentLoaded', function() {
    // This happens automatically in the view when the page is loaded
    console.log('Security warnings page loaded');
});

// Show warning detail modal
function showWarningDetail(warningId) {
    // This function can be used to show detailed warning information
    // Implementation depends on specific requirements
}
</script>
{% endblock %}
