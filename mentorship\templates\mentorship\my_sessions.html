{% extends 'base.html' %}
{% load static %}

{% block title %}My Sessions - ForgeX{% endblock %}

{% block content %}
<div class="my-sessions-page">
    <!-- Header -->
    <section class="sessions-header">
        <div class="container">
            <h1>My Mentorship Sessions</h1>
            <p>Manage your learning and mentoring sessions</p>

            <!-- Filter Tabs -->
            <div class="filter-tabs">
                <a href="?status=" class="tab {% if not status_filter %}active{% endif %}">All Sessions</a>
                <a href="?status=scheduled" class="tab {% if status_filter == 'scheduled' %}active{% endif %}">Scheduled</a>
                <a href="?status=active" class="tab {% if status_filter == 'active' %}active{% endif %}">Active</a>
                <a href="?status=completed" class="tab {% if status_filter == 'completed' %}active{% endif %}">Completed</a>
                <a href="?status=cancelled" class="tab {% if status_filter == 'cancelled' %}active{% endif %}">Cancelled</a>
            </div>
        </div>
    </section>

    <!-- Sessions Content -->
    <section class="sessions-content">
        <div class="container">
            <div class="sessions-grid">
                <!-- Learner Sessions -->
                <div class="sessions-section">
                    <h2><i class="fas fa-graduation-cap"></i> As a Learner</h2>
                    {% if learner_sessions %}
                        <div class="sessions-list">
                            {% for session in learner_sessions %}
                                <div class="session-card learner-session">
                                    <div class="session-header">
                                        <div class="session-info">
                                            <h3>Session with {{ session.mentor.get_full_name }}</h3>
                                            <div class="session-meta">
                                                <span class="session-date">
                                                    <i class="fas fa-calendar"></i>
                                                    {{ session.scheduled_time|date:"M d, Y" }}
                                                </span>
                                                <span class="session-time">
                                                    <i class="fas fa-clock"></i>
                                                    {{ session.scheduled_time|time:"g:i A" }} ({{ session.duration_minutes }} min)
                                                </span>
                                            </div>
                                        </div>
                                        <div class="session-status">
                                            <span class="status-badge status-{{ session.status }}">
                                                {{ session.get_status_display }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="session-details">
                                        <div class="session-price">
                                            <span class="price-label">Total Amount:</span>
                                            <span class="price-amount">${{ session.total_amount }}</span>
                                        </div>

                                        <div class="payment-status">
                                            <span class="payment-label">Payment Status:</span>
                                            <span class="payment-badge payment-{{ session.payment_status }}">
                                                {% if session.payment_status == 'succeeded' %}
                                                    <i class="fas fa-check-circle"></i> Paid
                                                {% elif session.payment_status == 'pending' %}
                                                    <i class="fas fa-clock"></i> Pending
                                                {% elif session.payment_status == 'processing' %}
                                                    <i class="fas fa-spinner"></i> Processing
                                                {% elif session.payment_status == 'failed' %}
                                                    <i class="fas fa-times-circle"></i> Failed
                                                {% else %}
                                                    <i class="fas fa-question-circle"></i> {{ session.get_payment_status_display }}
                                                {% endif %}
                                            </span>
                                        </div>

                                        {% if session.session_notes %}
                                            <div class="session-notes">
                                                <strong>Notes:</strong> {{ session.session_notes|truncatewords:20 }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Session Timer -->
                                    {% if session.status == 'scheduled' %}
                                        <div class="session-timer"
                                             data-scheduled-time="{{ session.scheduled_time|date:'c' }}"
                                             data-session-id="{{ session.id }}">
                                            <div class="timer-display">
                                                <i class="fas fa-clock"></i>
                                                <span class="timer-text">Calculating...</span>
                                            </div>
                                        </div>
                                    {% elif session.status == 'active' %}
                                        <div class="session-timer active">
                                            <div class="timer-display">
                                                <i class="fas fa-play-circle"></i>
                                                <span class="timer-text">Session Active</span>
                                            </div>
                                        </div>
                                    {% endif %}

                                    <div class="session-actions">
                                        {% if session.is_payment_required %}
                                            <a href="{% url 'mentorship:payment_page' session.id %}"
                                               class="btn btn-warning">
                                                <i class="fas fa-credit-card"></i> Complete Payment
                                            </a>
                                        {% elif session.status == 'scheduled' and session.can_start %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-primary">Join Session</a>
                                        {% elif session.status == 'active' %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-success">Continue Session</a>
                                        {% elif session.status == 'completed' and not session.detailed_feedback %}
                                            <button class="btn btn-secondary"
                                                    onclick="openFeedbackModal({{ session.id }})">
                                                Leave Feedback
                                            </button>
                                        {% elif session.status == 'completed' %}
                                            <span class="feedback-given">
                                                <i class="fas fa-check"></i> Feedback Given
                                            </span>
                                        {% endif %}

                                        <!-- Debug Join Button (Always Visible for Testing) -->
                                        {% if session.status == 'scheduled' or session.status == 'active' %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-debug">
                                                <i class="fas fa-bug"></i> Debug Join
                                            </a>
                                        {% endif %}

                                        <a href="{% url 'mentorship:session_details' session.id %}" class="btn btn-outline"
                                           onclick="console.log('Clicking details for session:', {{ session.id }}, 'URL:', this.href); return true;">
                                            View Details (ID: {{ session.id }})
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="no-sessions">
                            <i class="fas fa-graduation-cap"></i>
                            <h3>No learning sessions yet</h3>
                            <p>Book your first mentorship session to start learning!</p>
                            <a href="{% url 'mentorship:marketplace' %}" class="btn btn-primary">
                                Find a Mentor
                            </a>
                        </div>
                    {% endif %}
                </div>

                <!-- Mentor Sessions -->
                <div class="sessions-section">
                    <h2><i class="fas fa-chalkboard-teacher"></i> As a Mentor</h2>
                    {% if mentor_sessions %}
                        <div class="sessions-list">
                            {% for session in mentor_sessions %}
                                <div class="session-card mentor-session">
                                    <div class="session-header">
                                        <div class="session-info">
                                            <h3>Session with {{ session.learner.get_full_name }}</h3>
                                            <div class="session-meta">
                                                <span class="session-date">
                                                    <i class="fas fa-calendar"></i>
                                                    {{ session.scheduled_time|date:"M d, Y" }}
                                                </span>
                                                <span class="session-time">
                                                    <i class="fas fa-clock"></i>
                                                    {{ session.scheduled_time|time:"g:i A" }} ({{ session.duration_minutes }} min)
                                                </span>
                                            </div>
                                        </div>
                                        <div class="session-status">
                                            <span class="status-badge status-{{ session.status }}">
                                                {{ session.get_status_display }}
                                            </span>
                                        </div>
                                    </div>

                                    <div class="session-details">
                                        <div class="session-earnings">
                                            <span class="earnings-label">Your Earnings:</span>
                                            <span class="earnings-amount">${{ session.mentor_earnings }}</span>
                                        </div>

                                        {% if session.rating %}
                                            <div class="session-rating">
                                                <span class="rating-label">Rating:</span>
                                                <div class="rating-stars">
                                                    {% for i in "12345" %}
                                                        <span class="star {% if session.rating >= i|add:0 %}filled{% endif %}">★</span>
                                                    {% endfor %}
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Session Timer -->
                                    {% if session.status == 'scheduled' %}
                                        <div class="session-timer"
                                             data-scheduled-time="{{ session.scheduled_time|date:'c' }}"
                                             data-session-id="{{ session.id }}">
                                            <div class="timer-display">
                                                <i class="fas fa-clock"></i>
                                                <span class="timer-text">Calculating...</span>
                                            </div>
                                        </div>
                                    {% elif session.status == 'active' %}
                                        <div class="session-timer active">
                                            <div class="timer-display">
                                                <i class="fas fa-play-circle"></i>
                                                <span class="timer-text">Session Active</span>
                                            </div>
                                        </div>
                                    {% endif %}

                                    <div class="session-actions">
                                        {% if session.status == 'scheduled' and session.can_start %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-primary">Start Session</a>
                                        {% elif session.status == 'active' %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-success">Continue Session</a>
                                        {% endif %}

                                        <!-- Debug Join Button (Always Visible for Testing) -->
                                        {% if session.status == 'scheduled' or session.status == 'active' %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-debug">
                                                <i class="fas fa-bug"></i> Debug Join
                                            </a>
                                        {% endif %}

                                        <a href="{% url 'mentorship:session_details' session.id %}" class="btn btn-outline"
                                           onclick="console.log('Clicking details for session:', {{ session.id }}, 'URL:', this.href); return true;">
                                            View Details (ID: {{ session.id }})
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="no-sessions">
                            <i class="fas fa-chalkboard-teacher"></i>
                            <h3>No mentoring sessions yet</h3>
                            <p>Start mentoring to help others and earn money!</p>
                            {% if not user.mentor_profile %}
                                <a href="{% url 'mentorship:become_mentor' %}" class="btn btn-primary">
                                    Become a Mentor
                                </a>
                            {% else %}
                                <a href="{% url 'mentorship:mentor_dashboard' %}" class="btn btn-primary">
                                    Mentor Dashboard
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Feedback Modal -->
<div id="feedbackModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Leave Feedback</h3>
            <button class="modal-close" onclick="closeFeedbackModal()">&times;</button>
        </div>
        <form id="feedbackForm">
            <div class="rating-section">
                <div class="rating-item">
                    <label>Communication</label>
                    <div class="star-rating" data-rating="communication_rating">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                </div>

                <div class="rating-item">
                    <label>Knowledge</label>
                    <div class="star-rating" data-rating="knowledge_rating">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                </div>

                <div class="rating-item">
                    <label>Helpfulness</label>
                    <div class="star-rating" data-rating="helpfulness_rating">
                        <span class="star" data-value="1">★</span>
                        <span class="star" data-value="2">★</span>
                        <span class="star" data-value="3">★</span>
                        <span class="star" data-value="4">★</span>
                        <span class="star" data-value="5">★</span>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="would_recommend">
                    I would recommend this mentor to others
                </label>
            </div>

            <div class="form-group">
                <label for="whatWentWell">What went well?</label>
                <textarea id="whatWentWell" name="what_went_well" rows="3"></textarea>
            </div>

            <div class="form-group">
                <label for="improvements">Areas for improvement</label>
                <textarea id="improvements" name="areas_for_improvement" rows="3"></textarea>
            </div>

            <div class="form-group">
                <label for="additionalComments">Additional comments</label>
                <textarea id="additionalComments" name="additional_comments" rows="3"></textarea>
            </div>

            <div class="modal-actions">
                <button type="submit" class="btn btn-primary">Submit Feedback</button>
                <button type="button" class="btn btn-secondary" onclick="closeFeedbackModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<style>
.my-sessions-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.sessions-header {
    background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
    text-align: center;
}

.sessions-header h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.sessions-header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.tab {
    padding: 0.75rem 1.5rem;
    background: rgba(26,26,26,0.2);
    color: #1a1a1a;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tab:hover, .tab.active {
    background: #1a1a1a;
    color: #C0ff6b;
}

.sessions-content {
    padding: 4rem 0;
}

.sessions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    max-width: 1400px;
    margin: 0 auto;
}

.sessions-section h2 {
    color: #C0ff6b;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.8rem;
}

.sessions-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.session-card {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(192,255,107,0.2);
    transition: all 0.3s ease;
}

.session-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.3);
    border-color: rgba(192,255,107,0.4);
}

.learner-session {
    border-left: 4px solid #3498db;
}

.mentor-session {
    border-left: 4px solid #e74c3c;
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.session-info h3 {
    margin: 0 0 0.5rem 0;
    color: #ffffff;
    font-size: 1.2rem;
}

.session-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.session-meta span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-scheduled {
    background: #ffc107;
    color: #1a1a1a;
}

.status-active {
    background: #28a745;
    color: white;
}

.status-completed {
    background: #6c757d;
    color: white;
}

.status-cancelled {
    background: #dc3545;
    color: white;
}

.session-details {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255,255,255,0.02);
    border-radius: 8px;
}

.session-price, .session-earnings, .payment-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.price-amount, .earnings-amount {
    font-size: 1.2rem;
    font-weight: 700;
    color: #C0ff6b;
}

.payment-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.payment-succeeded {
    background: #28a745;
    color: white;
}

.payment-pending {
    background: #ffc107;
    color: #1a1a1a;
}

.payment-processing {
    background: #17a2b8;
    color: white;
}

.payment-failed {
    background: #dc3545;
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #1a1a1a;
    border: none;
    box-shadow: 0 4px 15px rgba(255,193,7,0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #fd7e14, #dc3545);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255,193,7,0.4);
}

.session-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars .star {
    color: #ddd;
}

.rating-stars .star.filled {
    color: #ffd700;
}

.session-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    box-shadow: 0 8px 25px rgba(40,167,69,0.4);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-outline {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-outline:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-debug {
    background: linear-gradient(135deg, #ff6b6b, #ff5252);
    color: white;
    border: 2px solid #ff6b6b;
    font-size: 0.9rem;
    box-shadow: 0 4px 15px rgba(255,107,107,0.3);
}

.btn-debug:hover {
    background: linear-gradient(135deg, #ff5252, #e53e3e);
    border-color: #ff5252;
    box-shadow: 0 8px 25px rgba(255,107,107,0.4);
}

.btn:hover {
    transform: translateY(-2px);
}

.no-sessions {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255,255,255,0.02);
    border-radius: 15px;
    border: 2px dashed rgba(192,255,107,0.3);
}

.no-sessions i {
    font-size: 4rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.no-sessions h3 {
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.feedback-given {
    color: #28a745;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Session Timer Styles */
.session-timer {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(192,255,107,0.1);
    border: 1px solid rgba(192,255,107,0.3);
    border-radius: 8px;
    text-align: center;
}

.session-timer.active {
    background: rgba(40,167,69,0.1);
    border-color: rgba(40,167,69,0.3);
}

.session-timer.starting {
    background: rgba(255,193,7,0.1);
    border-color: rgba(255,193,7,0.3);
    animation: pulse 1.5s infinite;
}

.timer-display {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 600;
}

.session-timer .timer-text {
    color: #C0ff6b;
    font-size: 0.9rem;
}

.session-timer.active .timer-text {
    color: #28a745;
}

.session-timer.starting .timer-text {
    color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: #2d2d2d;
    padding: 0;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.modal-header h3 {
    margin: 0;
    color: #C0ff6b;
}

.modal-close {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal form {
    padding: 1.5rem;
}

.rating-section {
    margin-bottom: 1.5rem;
}

.rating-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.star-rating {
    display: flex;
    gap: 0.25rem;
}

.star-rating .star {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s ease;
}

.star-rating .star:hover,
.star-rating .star.active {
    color: #ffd700;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #C0ff6b;
    font-weight: 600;
}

.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255,255,255,0.05);
    border: 1px solid rgba(192,255,107,0.2);
    border-radius: 8px;
    color: #ffffff;
    resize: vertical;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

@media (max-width: 768px) {
    .sessions-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .filter-tabs {
        flex-direction: column;
        align-items: center;
    }

    .session-actions {
        flex-direction: column;
    }

    .btn {
        min-width: auto;
    }
}
</style>

<script>
let currentSessionId = null;
const ratings = {
    communication_rating: 0,
    knowledge_rating: 0,
    helpfulness_rating: 0
};

function openFeedbackModal(sessionId) {
    currentSessionId = sessionId;
    document.getElementById('feedbackModal').classList.add('show');
}

function closeFeedbackModal() {
    document.getElementById('feedbackModal').classList.remove('show');
    currentSessionId = null;
    // Reset form
    document.getElementById('feedbackForm').reset();
    // Reset star ratings
    document.querySelectorAll('.star-rating .star').forEach(star => {
        star.classList.remove('active');
    });
    Object.keys(ratings).forEach(key => ratings[key] = 0);
}

// Star rating functionality
document.querySelectorAll('.star-rating').forEach(ratingGroup => {
    const stars = ratingGroup.querySelectorAll('.star');
    const ratingName = ratingGroup.dataset.rating;

    stars.forEach((star, index) => {
        star.addEventListener('click', () => {
            const value = index + 1;
            ratings[ratingName] = value;

            // Update visual state
            stars.forEach((s, i) => {
                if (i < value) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });

        star.addEventListener('mouseenter', () => {
            stars.forEach((s, i) => {
                if (i <= index) {
                    s.style.color = '#ffd700';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });

    ratingGroup.addEventListener('mouseleave', () => {
        stars.forEach((s, i) => {
            if (i < ratings[ratingName]) {
                s.style.color = '#ffd700';
            } else {
                s.style.color = '#ddd';
            }
        });
    });
});

// Feedback form submission
document.getElementById('feedbackForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    if (!currentSessionId) return;

    const formData = new FormData(this);
    const data = {
        ...ratings,
        would_recommend: formData.get('would_recommend') === 'on',
        what_went_well: formData.get('what_went_well'),
        areas_for_improvement: formData.get('areas_for_improvement'),
        additional_comments: formData.get('additional_comments')
    };

    // Validate ratings
    if (Object.values(ratings).some(rating => rating === 0)) {
        alert('Please provide ratings for all categories.');
        return;
    }

    try {
        const response = await fetch(`/mentorship/session/${currentSessionId}/feedback/submit/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Feedback submitted successfully!');
            closeFeedbackModal();
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
});

// View Details functionality now handled by direct links

// Close modal when clicking outside
document.getElementById('feedbackModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeFeedbackModal();
    }
});

// Session Timer Functionality
function initializeSessionTimers() {
    const timers = document.querySelectorAll('.session-timer[data-scheduled-time]');

    timers.forEach(timer => {
        const scheduledTime = new Date(timer.dataset.scheduledTime);
        const sessionId = timer.dataset.sessionId;

        updateTimer(timer, scheduledTime);

        // Update every second
        setInterval(() => {
            updateTimer(timer, scheduledTime);
        }, 1000);
    });
}

function updateTimer(timerElement, scheduledTime) {
    const now = new Date();
    const timeDiff = scheduledTime.getTime() - now.getTime();
    const timerText = timerElement.querySelector('.timer-text');
    const timerIcon = timerElement.querySelector('i');

    // Session can start 10 minutes before scheduled time
    const canStartTime = new Date(scheduledTime.getTime() - (10 * 60 * 1000));

    if (timeDiff <= 0) {
        // Session time has passed
        timerElement.classList.add('starting');
        timerIcon.className = 'fas fa-play-circle';
        timerText.textContent = 'Session Starting Now!';
    } else if (now >= canStartTime) {
        // Can start now (within 10 minutes of start time)
        timerElement.classList.add('starting');
        timerIcon.className = 'fas fa-clock';
        timerText.textContent = 'Ready to Start';
    } else {
        // Show countdown
        timerElement.classList.remove('starting');
        timerIcon.className = 'fas fa-clock';

        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

        let timeString = '';
        if (days > 0) {
            timeString = `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            timeString = `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            timeString = `${minutes}m ${seconds}s`;
        } else {
            timeString = `${seconds}s`;
        }

        timerText.textContent = `Starts in ${timeString}`;
    }
}

// Initialize timers when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeSessionTimers();
});
</script>
{% endblock %}
