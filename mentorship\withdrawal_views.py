import json
import stripe
from decimal import Decimal
from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse
from django.utils import timezone
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction, models

from .models import MentorProfile, WithdrawalRequest
from collaborate.notification_utils import send_notification

# Configure Stripe
stripe.api_key = getattr(settings, 'STRIPE_SECRET_KEY', '')


@login_required
def withdrawal_dashboard(request):
    """Display withdrawal dashboard for mentors"""

    # Check if user is a mentor
    try:
        mentor_profile = request.user.mentor_profile
    except MentorProfile.DoesNotExist:
        messages.error(request, 'You need to be a mentor to access withdrawals.')
        return redirect('mentorship:marketplace')

    # Get withdrawal history
    withdrawal_requests = WithdrawalRequest.objects.filter(
        mentor=request.user
    ).order_by('-requested_at')[:10]

    # Calculate available balance
    available_balance = mentor_profile.get_available_balance()

    context = {
        'mentor_profile': mentor_profile,
        'available_balance': available_balance,
        'withdrawal_requests': withdrawal_requests,
        'min_withdrawal': Decimal('10.00'),
    }

    return render(request, 'mentorship/withdrawal_dashboard.html', context)


@login_required
@require_http_methods(["POST"])
def request_withdrawal(request):
    """Handle withdrawal request"""

    try:
        mentor_profile = request.user.mentor_profile
    except MentorProfile.DoesNotExist:
        return JsonResponse({'error': 'You need to be a mentor to request withdrawals.'}, status=403)

    try:
        data = json.loads(request.body)
        amount = Decimal(str(data.get('amount', 0)))

        # Validate amount
        if amount < Decimal('10.00'):
            return JsonResponse({'error': 'Minimum withdrawal amount is $10.00'}, status=400)

        if not mentor_profile.can_withdraw(amount):
            return JsonResponse({'error': 'Insufficient balance or invalid amount'}, status=400)

        # Check for pending withdrawals
        pending_withdrawals = WithdrawalRequest.objects.filter(
            mentor=request.user,
            status__in=['pending', 'processing']
        ).exists()

        if pending_withdrawals:
            return JsonResponse({'error': 'You have a pending withdrawal request. Please wait for it to complete.'}, status=400)

        # Create withdrawal request
        with transaction.atomic():
            withdrawal = WithdrawalRequest.objects.create(
                mentor=request.user,
                amount=amount
            )

            # Update mentor's withdrawn earnings
            mentor_profile.withdrawn_earnings += amount
            mentor_profile.save()

        return JsonResponse({
            'success': True,
            'message': 'Withdrawal request submitted successfully!',
            'withdrawal_id': withdrawal.id,
            'estimated_arrival': withdrawal.get_estimated_arrival()
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def withdrawal_history(request):
    """Display complete withdrawal history"""

    try:
        mentor_profile = request.user.mentor_profile
    except MentorProfile.DoesNotExist:
        messages.error(request, 'You need to be a mentor to access withdrawals.')
        return redirect('mentorship:marketplace')

    withdrawal_requests = WithdrawalRequest.objects.filter(
        mentor=request.user
    ).order_by('-requested_at')

    context = {
        'mentor_profile': mentor_profile,
        'withdrawal_requests': withdrawal_requests,
    }

    return render(request, 'mentorship/withdrawal_history.html', context)


@login_required
@require_http_methods(["POST"])
def cancel_withdrawal(request, withdrawal_id):
    """Cancel a pending withdrawal request"""

    withdrawal = get_object_or_404(
        WithdrawalRequest,
        id=withdrawal_id,
        mentor=request.user
    )

    if not withdrawal.can_cancel():
        return JsonResponse({'error': 'This withdrawal cannot be cancelled'}, status=400)

    try:
        with transaction.atomic():
            # Restore the amount to mentor's available balance
            mentor_profile = request.user.mentor_profile
            mentor_profile.withdrawn_earnings -= withdrawal.amount
            mentor_profile.save()

            # Update withdrawal status
            withdrawal.status = 'cancelled'
            withdrawal.admin_notes = f"Cancelled by mentor on {timezone.now()}"
            withdrawal.save()

        return JsonResponse({
            'success': True,
            'message': 'Withdrawal request cancelled successfully!'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


# Admin functions for processing withdrawals
@login_required
def admin_withdrawal_dashboard(request):
    """Admin dashboard for managing withdrawal requests (superuser only)"""

    if not request.user.is_superuser:
        messages.error(request, 'You need superuser privileges to access this page.')
        return redirect('mentorship:marketplace')

    try:
        # Get withdrawal requests by status
        pending_withdrawals = WithdrawalRequest.objects.filter(
            status='pending'
        ).select_related('mentor', 'mentor__mentor_profile').order_by('-requested_at')

        processing_withdrawals = WithdrawalRequest.objects.filter(
            status='processing'
        ).select_related('mentor', 'mentor__mentor_profile').order_by('-processed_at')

        recent_completed = WithdrawalRequest.objects.filter(
            status__in=['completed', 'failed', 'cancelled']
        ).select_related('mentor', 'mentor__mentor_profile').order_by('-completed_at')[:10]

        # Calculate statistics
        total_pending_amount = pending_withdrawals.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0.00')

        total_processing_amount = processing_withdrawals.aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0.00')

        context = {
            'pending_withdrawals': pending_withdrawals,
            'processing_withdrawals': processing_withdrawals,
            'recent_completed': recent_completed,
            'total_pending_amount': total_pending_amount,
            'total_processing_amount': total_processing_amount,
            'pending_count': pending_withdrawals.count(),
            'processing_count': processing_withdrawals.count(),
        }

        return render(request, 'mentorship/admin_withdrawal_dashboard.html', context)

    except Exception as e:
        messages.error(request, f'Database error: {str(e)}. Please ensure migrations are applied.')
        return redirect('mentorship:marketplace')


@login_required
@require_http_methods(["POST"])
def admin_approve_withdrawal(request, withdrawal_id):
    """Approve and complete a withdrawal request (superuser only)"""

    if not request.user.is_superuser:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    withdrawal = get_object_or_404(WithdrawalRequest, id=withdrawal_id)

    if withdrawal.status not in ['pending', 'processing']:
        return JsonResponse({'error': 'Withdrawal cannot be approved in current status'}, status=400)

    try:
        with transaction.atomic():
            # Update withdrawal status
            withdrawal.status = 'completed'
            withdrawal.processed_at = timezone.now()
            withdrawal.completed_at = timezone.now()
            withdrawal.admin_notes = f"Approved by {request.user.username} on {timezone.now()}"
            withdrawal.save()

            # Send notification to mentor
            send_notification(
                user=withdrawal.mentor,
                title="Withdrawal Approved",
                message=f"Your withdrawal request of ${withdrawal.amount} has been approved and processed. You should receive the funds within 1-3 business days.",
                notification_type="success"
            )

        return JsonResponse({
            'success': True,
            'message': f'Withdrawal of ${withdrawal.amount} approved successfully!'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
def admin_reject_withdrawal(request, withdrawal_id):
    """Reject a withdrawal request (superuser only)"""

    if not request.user.is_superuser:
        return JsonResponse({'error': 'Unauthorized'}, status=403)

    withdrawal = get_object_or_404(WithdrawalRequest, id=withdrawal_id)

    if withdrawal.status not in ['pending', 'processing']:
        return JsonResponse({'error': 'Withdrawal cannot be rejected in current status'}, status=400)

    try:
        data = json.loads(request.body)
        rejection_reason = data.get('reason', '').strip()

        if not rejection_reason:
            return JsonResponse({'error': 'Rejection reason is required'}, status=400)

        with transaction.atomic():
            # Restore the amount to mentor's available balance
            mentor_profile = withdrawal.mentor.mentor_profile
            mentor_profile.withdrawn_earnings -= withdrawal.amount
            mentor_profile.save()

            # Update withdrawal status
            withdrawal.status = 'failed'
            withdrawal.processed_at = timezone.now()
            withdrawal.failure_reason = rejection_reason
            withdrawal.admin_notes = f"Rejected by {request.user.username} on {timezone.now()}: {rejection_reason}"
            withdrawal.save()

            # Send notification to mentor
            send_notification(
                user=withdrawal.mentor,
                title="Withdrawal Rejected",
                message=f"Your withdrawal request of ${withdrawal.amount} has been rejected. Reason: {rejection_reason}. The amount has been restored to your available balance.",
                notification_type="error"
            )

        return JsonResponse({
            'success': True,
            'message': f'Withdrawal of ${withdrawal.amount} rejected successfully!'
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def admin_withdrawal_details(request, withdrawal_id):
    """View detailed information about a withdrawal request (superuser only)"""

    if not request.user.is_superuser:
        messages.error(request, 'You need superuser privileges to access this page.')
        return redirect('mentorship:marketplace')

    withdrawal = get_object_or_404(
        WithdrawalRequest.objects.select_related('mentor', 'mentor__mentor_profile'),
        id=withdrawal_id
    )

    # Get mentor's withdrawal history
    mentor_withdrawals = WithdrawalRequest.objects.filter(
        mentor=withdrawal.mentor
    ).order_by('-requested_at')[:5]

    # Calculate mentor statistics
    mentor_stats = {
        'total_withdrawals': WithdrawalRequest.objects.filter(mentor=withdrawal.mentor).count(),
        'completed_withdrawals': WithdrawalRequest.objects.filter(
            mentor=withdrawal.mentor, status='completed'
        ).count(),
        'total_withdrawn': WithdrawalRequest.objects.filter(
            mentor=withdrawal.mentor, status='completed'
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00'),
    }

    context = {
        'withdrawal': withdrawal,
        'mentor_withdrawals': mentor_withdrawals,
        'mentor_stats': mentor_stats,
    }

    return render(request, 'mentorship/admin_withdrawal_details.html', context)
