from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from learn.ai_services import AILearningService, CodeAnalysisService


class Command(BaseCommand):
    help = 'Test AI learning services'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing AI Learning Services...'))
        
        # Test AI Learning Service
        try:
            ai_service = AILearningService()
            
            # Test with a dummy user (create if doesn't exist)
            user, created = User.objects.get_or_create(
                username='test_ai_user',
                defaults={'email': '<EMAIL>'}
            )
            
            if created:
                self.stdout.write(f'Created test user: {user.username}')
            
            # Test learning profile creation
            profile = ai_service.get_or_create_learning_profile(user)
            self.stdout.write(f'✅ Learning profile created: {profile.learning_style}')
            
            # Test progress analytics
            analytics = ai_service.analyze_user_progress(user)
            self.stdout.write(f'✅ Progress analytics: {analytics}')
            
            # Test AI response generation
            response = ai_service.generate_ai_response("How do I learn Python?")
            self.stdout.write(f'✅ AI Response generated: {len(response)} characters')
            
            # Test recommendations
            recommendations = ai_service.generate_personalized_recommendations(user)
            self.stdout.write(f'✅ Recommendations generated: {len(recommendations)} items')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ AI Learning Service error: {str(e)}'))
        
        # Test Code Analysis Service
        try:
            code_service = CodeAnalysisService()
            
            test_code = """
def hello_world():
    print("Hello, World!")
    return "success"
"""
            
            analysis = code_service.analyze_code(test_code, 'python', 'comprehensive')
            self.stdout.write(f'✅ Code analysis completed: Score {analysis["overall_score"]}')
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Code Analysis Service error: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS('AI Services test completed!'))
