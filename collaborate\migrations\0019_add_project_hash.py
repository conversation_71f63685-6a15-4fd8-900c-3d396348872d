# Generated by Django 5.2 on 2025-05-31 16:29

from django.db import migrations, models
import hashlib
import uuid
from django.utils import timezone


def generate_hash_for_existing_projects(apps, schema_editor):
    """Generate hash values for existing projects"""
    Project = apps.get_model('collaborate', 'Project')

    for project in Project.objects.all():
        if not project.hash:
            # Generate a unique hash for existing projects
            unique_string = f"{uuid.uuid4()}-{project.title}-{timezone.now().isoformat()}"
            hash_object = hashlib.sha256(unique_string.encode())
            project.hash = hash_object.hexdigest()[:32]
            project.save()


def reverse_hash_generation(apps, schema_editor):
    """Reverse operation - no action needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ("collaborate", "0018_add_project_chat_message"),
    ]

    operations = [
        # First add the field as nullable
        migrations.AddField(
            model_name="project",
            name="hash",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="Unique hash for secure URL access",
                max_length=64,
                null=True,
            ),
        ),
        # Populate hash values for existing projects
        migrations.RunPython(generate_hash_for_existing_projects, reverse_hash_generation),
        # Make the field unique after populating values
        migrations.AlterField(
            model_name="project",
            name="hash",
            field=models.CharField(
                blank=True,
                help_text="Unique hash for secure URL access",
                max_length=64,
                unique=True,
            ),
        ),
    ]
