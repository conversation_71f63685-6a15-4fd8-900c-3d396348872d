.feedback-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
  }

  .feedback-header {
    background: linear-gradient(135deg, #c0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
  
  }


  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
  }

  .title-wrap {
    max-width: 1536px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    position: relative;
  }

  .session-summary h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
  }

  .session-summary p {
    font-size: 1.2rem;
    margin-bottom: 0;
    opacity: 0.8;
  }

  .session-details {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
  }

  .detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
  }

  .detail-item i {
    color: #1a1a1a;
    opacity: 0.7;
  }

  .mentor-avatar {
    flex-shrink: 0;
  }

  .avatar-image {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(26, 26, 26, 0.1);
  }

  .avatar-initials {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: rgba(26, 26, 26, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 800;
    color: #1a1a1a;
    border: 4px solid rgba(26, 26, 26, 0.1);
  }

  .feedback-content {
    padding: 3rem 0;
  }

  .feedback-form {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  .rating-section h3,
  .written-feedback-section h3 {
    color: #c0ff6b;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .section-subtitle {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    text-align: center;
  }

  .progress-indicator {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .progress-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    opacity: 0.5;
  }

  .progress-item.completed {
    opacity: 1;
    background: rgba(192, 255, 107, 0.1);
    border: 1px solid rgba(192, 255, 107, 0.3);
    transform: scale(1.05);
  }

  .progress-item i {
    font-size: 1.2rem;
    color: #c0ff6b;
  }

  .progress-item span {
    font-size: 0.8rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
  }

  .rating-categories {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  .rating-item {
    background: rgba(255, 255, 255, 0.03);
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .rating-item:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(192, 255, 107, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  .rating-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .rating-icon {
    color: #c0ff6b;
    font-size: 1.5rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
  }

  .rating-item label {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #c0ff6b;
  }

  .rating-description {
    font-size: 0.95rem;
    opacity: 0.8;
    margin: 0;
    line-height: 1.4;
  }

  .rating-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.6;
  }

  .star-rating {
    display: flex;
    gap: 0.25rem;
  }

  .star {
    font-size: 2.5rem;
    color: #555;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    margin: 0 0.1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .star:hover {
    color: #ffd700;
    transform: scale(1.15);
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
  }

  .star.active {
    color: #ffd700;
    transform: scale(1.1);
    text-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
  }

  .star.active:hover {
    transform: scale(1.2);
  }

  .star.hover-preview {
    color: #ffed4e;
    transform: scale(1.1);
    text-shadow: 0 0 8px rgba(255, 237, 78, 0.6);
  }

  .recommendation-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: rgba(192, 255, 107, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(192, 255, 107, 0.2);
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .checkbox-item input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #c0ff6b;
  }

  .checkbox-item label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
  }

  .checkbox-item i {
    color: #c0ff6b;
  }

  .written-feedback-section {
    margin-top: 2rem;
  }

  .feedback-field {
    margin-bottom: 1.5rem;
  }

  .feedback-field label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #c0ff6b;
    font-size: 1.1rem;
  }

  .feedback-field label i {
    color: #c0ff6b;
    opacity: 0.8;
  }

  .feedback-field textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #ffffff;
    font-family: inherit;
    resize: vertical;
    min-height: 80px;
  }

  .feedback-field textarea:focus {
    outline: none;
    border-color: #c0ff6b;
    box-shadow: 0 0 0 2px rgba(192, 255, 107, 0.2);
  }

  .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #c0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(192, 255, 107, 0.3);
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      text-align: center;
    }

    .session-details {
      justify-content: center;
    }

    .feedback-form {
      margin: 0 1rem;
      padding: 1.5rem;
    }

    .form-actions {
      flex-direction: column;
    }

    .rating-categories {
      gap: 1.5rem;
    }

    .star {
      font-size: 1.5rem;
    }

    .progress-indicator {
      gap: 1rem;
      flex-wrap: wrap;
    }

    .progress-item {
      padding: 0.5rem;
    }

    .rating-header {
      flex-direction: column;
      text-align: center;
      gap: 0.5rem;
    }
  }




  
/* breakpoints  */

@media (max-width: 1536px) {

  
    .tile-wrap {
      max-width: 1024px;
    }
  
  }
  
  
  @media (max-width: 1280px) {
  
    .tile-wrap {
      max-width: 1024px;
    }
  
  }
  
  
  @media (max-width: 1024px) {
  
    .tile-wrap {
      max-width: 768px;
    }
  
  }
  
  @media (max-width: 768px) {
  .tile-wrap {
      max-width: 640px;
    }
  }
  
  @media (max-width: 640px) {
  .tile-wrap {
      max-width: 475px;
    }
  }
  
  
  @media (max-width: 475px) {
  .tile-wrap {
      max-width: 400px;
    }
  }
  
  