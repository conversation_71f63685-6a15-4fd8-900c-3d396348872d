{% extends 'base.html' %}
{% load static %}

{% block title %}Session Room - ForgeX{% endblock %}

{% block content %}
{% csrf_token %}
<div class="session-room">
    <!-- Session Header -->
    <div class="session-header">
        <div class="session-info">
            <h2>Mentorship Session</h2>
            <div class="participants">
                <span class="participant mentor">
                    <i class="fas fa-user-graduate"></i>
                    {{ session.mentor.get_full_name }}
                </span>
                <span class="participant learner">
                    <i class="fas fa-user"></i>
                    {{ session.learner.get_full_name }}
                </span>
            </div>
        </div>

        <div class="session-controls">
            <div class="timer" id="sessionTimer">
                <i class="fas fa-clock"></i>
                <span id="timeRemaining">{{ session.duration_minutes }}:00</span>
            </div>

            <div class="control-buttons">
                <button id="videoToggle" class="control-btn video-btn">
                    <i class="fas fa-video"></i>
                </button>
                <button id="voiceToggle" class="control-btn voice-btn">
                    <i class="fas fa-microphone"></i>
                </button>
                <button id="screenShare" class="control-btn screen-btn">
                    <i class="fas fa-desktop"></i>
                </button>
                <button id="endSession" class="control-btn end-btn">
                    <i class="fas fa-phone-slash"></i>
                    End Session
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="session-content">
        <!-- Video Section -->
        <div class="video-section">
            <div class="video-container">
                <div class="video-grid">
                    <div class="video-participant mentor-video">
                        <video id="mentorVideo" autoplay muted></video>

                        <!-- Profile Picture Placeholder (shown when video is off) -->
                        <div class="profile-placeholder" id="mentorProfilePlaceholder">
                            <div class="profile-avatar">
                                {% if session.mentor.profile and session.mentor.profile.profile_picture %}
                                    <img src="{{ session.mentor.profile.profile_picture.url }}"
                                         alt="{{ session.mentor.get_full_name }}"
                                         class="profile-image">
                                {% else %}
                                    <div class="avatar-initials">
                                        {{ session.mentor.first_name|first|upper }}{{ session.mentor.last_name|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="profile-name">{{ session.mentor.get_full_name }}</div>
                        </div>

                        <div class="video-label">{{ session.mentor.get_full_name }} (Mentor)</div>
                        <div class="video-controls">
                            <span class="video-status" id="mentorVideoStatus">
                                <i class="fas fa-video-slash"></i>
                            </span>
                            <span class="audio-status" id="mentorAudioStatus">
                                <i class="fas fa-microphone-slash"></i>
                            </span>
                            <button class="video-fullscreen-btn" id="mentorFullscreenBtn" title="Fullscreen Video">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    <div class="video-participant learner-video">
                        <video id="learnerVideo" autoplay muted></video>

                        <!-- Profile Picture Placeholder (shown when video is off) -->
                        <div class="profile-placeholder" id="learnerProfilePlaceholder">
                            <div class="profile-avatar">
                                {% if session.learner.profile and session.learner.profile.profile_picture %}
                                    <img src="{{ session.learner.profile.profile_picture.url }}"
                                         alt="{{ session.learner.get_full_name }}"
                                         class="profile-image">
                                {% else %}
                                    <div class="avatar-initials">
                                        {{ session.learner.first_name|first|upper }}{{ session.learner.last_name|first|upper }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="profile-name">{{ session.learner.get_full_name }}</div>
                        </div>

                        <div class="video-label">{{ session.learner.get_full_name }} (Learner)</div>
                        <div class="video-controls">
                            <span class="video-status" id="learnerVideoStatus">
                                <i class="fas fa-video-slash"></i>
                            </span>
                            <span class="audio-status" id="learnerAudioStatus">
                                <i class="fas fa-microphone-slash"></i>
                            </span>
                            <button class="video-fullscreen-btn" id="learnerFullscreenBtn" title="Fullscreen Video">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="screen-share-container" id="screenShareContainer" style="display: none;">
                    <video id="screenShareVideo" autoplay></video>
                    <div class="screen-share-label">Screen Share - {{ session.mentor.get_full_name }}</div>
                    <div class="screen-share-controls">
                        <button id="scaleToggleBtn" class="scale-toggle-btn" title="Toggle Fit Mode" style="display: none;">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="fullscreenBtn" class="fullscreen-btn" title="Enter Fullscreen">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Sidebar -->
        <div class="chat-section">
            <div class="chat-header">
                <h3>Session Chat</h3>
                <div class="connection-status">
                    <div class="status-item" id="videoStatus">
                        <i class="fas fa-video-slash"></i>
                        <span>Video Off</span>
                    </div>
                    <div class="status-item" id="audioStatus">
                        <i class="fas fa-microphone-slash"></i>
                        <span>Audio Off</span>
                    </div>
                    <div class="status-item" id="screenStatus">
                        <i class="fas fa-desktop"></i>
                        <span>Screen Share Off</span>
                    </div>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="system-message">
                    Session started. Use video, voice, and screen sharing to collaborate!
                </div>
            </div>

            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="Type a message..." maxlength="500">
                <button id="sendMessage" class="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.session-room {
    height: 100vh;
    background: #1a1a1a;
    color: #ffffff;
    display: flex;
    flex-direction: column;
}

.session-header {
    background: rgba(192,255,107,0.1);
    padding: 1rem 2rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.session-info h2 {
    margin: 0 0 0.5rem 0;
    color: #C0ff6b;
}

.participants {
    display: flex;
    gap: 2rem;
}

.participant {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.8;
}

.participant.mentor i {
    color: #C0ff6b;
}

.participant.learner i {
    color: #ffc107;
}

.session-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.timer {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: #C0ff6b;
}

.control-buttons {
    display: flex;
    gap: 1rem;
}

.control-btn {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255,255,255,0.15);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 2px solid rgba(192,255,107,0.3);
    font-weight: 600;
}

.control-btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(192,255,107,0.5);
    transform: translateY(-1px);
}

.voice-btn.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.voice-btn.muted {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.video-btn.active {
    background: linear-gradient(135deg, #28a745, #20c997);
    border-color: #28a745;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.video-btn.disabled {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.screen-btn.active {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
    box-shadow: 0 4px 15px rgba(23,162,184,0.3);
}

.end-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.end-btn:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    box-shadow: 0 8px 25px rgba(220,53,69,0.4);
}

.session-content {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 350px;
    grid-template-areas: "video chat";
    overflow: hidden;
    gap: 1px;
    height: calc(100vh - 200px); /* Ensure fixed height */
    min-height: 600px;
}

.video-section {
    grid-area: video;
    background: rgba(255,255,255,0.02);
    border-right: 1px solid rgba(192,255,107,0.2);
    display: flex;
    flex-direction: column;
    position: relative;
}

.video-container {
    flex: 1;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.video-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    flex: 1;
}

.video-participant {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 4/3;
    border: 2px solid rgba(192,255,107,0.2);
}

.video-participant video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Profile Picture Placeholder Styles */
.profile-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 5;
    transition: opacity 0.3s ease;
}

.profile-placeholder.hidden {
    opacity: 0;
    pointer-events: none;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 1rem;
    border: 3px solid rgba(192,255,107,0.3);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-avatar .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-avatar .avatar-initials {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.8rem;
}

.profile-name {
    color: #C0ff6b;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    opacity: 0.9;
}

.video-label {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background: rgba(0,0,0,0.7);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.video-controls {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 4px;
}

.video-status, .audio-status {
    background: rgba(0,0,0,0.7);
    color: #fff;
    padding: 4px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.video-status.active {
    color: #28a745;
}

.audio-status.active {
    color: #28a745;
}

.video-fullscreen-btn {
    background: rgba(0,0,0,0.7);
    color: #fff;
    border: none;
    padding: 4px 6px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.7rem;
    transition: all 0.3s ease;
    margin-left: 2px;
}

.video-fullscreen-btn:hover {
    background: rgba(0,0,0,0.9);
    color: #C0ff6b;
    transform: scale(1.1);
}

.video-fullscreen-btn:active {
    transform: scale(0.9);
}

/* Fullscreen styles for video participants */
.video-participant.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #000 !important;
    border: none !important;
    border-radius: 0 !important;
    aspect-ratio: unset !important;
}

.video-participant.fullscreen video {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
}

.video-participant.fullscreen .profile-placeholder {
    width: 100% !important;
    height: 100% !important;
}

.video-participant.fullscreen .profile-avatar {
    width: 120px !important;
    height: 120px !important;
}

.video-participant.fullscreen .profile-name {
    font-size: 1.5rem !important;
}

.video-participant.fullscreen .video-label {
    top: 20px;
    left: 20px;
    font-size: 1rem;
    padding: 8px 12px;
}

.video-participant.fullscreen .video-controls {
    top: 20px;
    right: 20px;
}

.video-participant.fullscreen .video-fullscreen-btn {
    padding: 8px 10px;
    font-size: 0.9rem;
}

.screen-share-container {
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    border: 2px solid rgba(23,162,184,0.5);
    position: absolute;
    top: 1rem;
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
    z-index: 10;
}

.screen-share-container video {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.screen-share-label {
    position: absolute;
    top: 8px;
    left: 8px;
    background: rgba(23,162,184,0.8);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 15;
}

.screen-share-controls {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 15;
}

.fullscreen-btn, .scale-toggle-btn {
    background: rgba(0,0,0,0.7);
    color: #fff;
    border: none;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 5px;
}

.fullscreen-btn:hover, .scale-toggle-btn:hover {
    background: rgba(0,0,0,0.9);
    transform: scale(1.05);
}

.fullscreen-btn:active, .scale-toggle-btn:active {
    transform: scale(0.95);
}

/* Fullscreen styles */
.screen-share-container.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.screen-share-container.fullscreen video {
    width: 100vw !important;
    height: 100vh !important;
    object-fit: contain !important;
    max-width: none !important;
    max-height: none !important;
}

.screen-share-container.fullscreen.scale-fill video {
    object-fit: cover !important;
}

.screen-share-container.fullscreen.scale-stretch video {
    object-fit: fill !important;
}

.screen-share-container.fullscreen .screen-share-label {
    top: 20px;
    left: 20px;
    font-size: 1rem;
    padding: 8px 12px;
}

.screen-share-container.fullscreen .screen-share-controls {
    top: 20px;
    right: 20px;
}

.screen-share-container.fullscreen .fullscreen-btn {
    padding: 12px 15px;
    font-size: 1.1rem;
}



.chat-section {
    grid-area: chat;
    display: flex;
    flex-direction: column;
    background: rgba(255,255,255,0.02);
    height: 100%;
    max-height: 100%;
    overflow: hidden;
}

.chat-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.chat-header h3 {
    margin: 0 0 1rem 0;
    color: #C0ff6b;
}

.connection-status {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.8;
}

.status-item.connected {
    color: #28a745;
}

.status-item.disconnected {
    color: #dc3545;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    height: 0; /* Force flex item to respect container height */
    min-height: 0; /* Allow shrinking */
    max-height: 100%;
}

.message {
    padding: 0.75rem;
    border-radius: 8px;
    max-width: 90%;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    flex-shrink: 0;
}

.message.own {
    background: rgba(192,255,107,0.2);
    align-self: flex-end;
    color: #C0ff6b;
}

.message.other {
    background: rgba(255,255,255,0.1);
    align-self: flex-start;
}

.system-message {
    background: rgba(255,193,7,0.2);
    color: #ffc107;
    padding: 0.5rem;
    border-radius: 6px;
    text-align: center;
    font-size: 0.9rem;
    opacity: 0.8;
    word-wrap: break-word;
    word-break: break-word;
    overflow-wrap: break-word;
    flex-shrink: 0;
    max-width: 100%;
}

.message-sender {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-bottom: 0.25rem;
}

.chat-input {
    padding: 1rem;
    border-top: 1px solid rgba(192,255,107,0.2);
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    background: rgba(255,255,255,0.02);
}

.chat-input input {
    flex: 1;
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 6px;
    color: #ffffff;
    transition: all 0.3s ease;
}

.chat-input input:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 15px rgba(192,255,107,0.4);
    background: rgba(255,255,255,0.15);
}

.send-btn {
    padding: 0.75rem;
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
    font-weight: 600;
}

.send-btn:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: rgba(192,255,107,0.3);
    color: #C0ff6b;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    border: 2px solid rgba(192,255,107,0.5);
}

.btn:hover {
    background: rgba(192,255,107,0.4);
    border-color: #C0ff6b;
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

@media (max-width: 1024px) {
    .session-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "video"
            "chat";
        grid-template-rows: 1fr 350px;
        height: calc(100vh - 150px);
    }

    .chat-section {
        max-height: 350px;
    }

    .video-grid {
        grid-template-columns: 1fr 1fr;
    }

    .video-participant {
        aspect-ratio: 16/9;
    }
}

@media (max-width: 768px) {
    .session-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "video"
            "chat";
        grid-template-rows: 1fr 300px;
        height: calc(100vh - 120px);
    }

    .chat-section {
        max-height: 300px;
    }

    .chat-messages {
        padding: 0.5rem;
        gap: 0.25rem;
    }

    .message {
        padding: 0.5rem;
        font-size: 0.9rem;
        max-width: 95%;
    }

    .system-message {
        padding: 0.375rem;
        font-size: 0.8rem;
    }

    .session-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .participants {
        justify-content: center;
    }

    .video-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .video-participant {
        aspect-ratio: 16/9;
    }

    .control-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }

    .video-container {
        padding: 0.5rem;
    }
}

/* Enhanced chat message styles */
.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

.system-message {
    text-align: center;
    color: #888;
    font-style: italic;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.system-time {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Remote screen share notification */
.remote-screen-share-notification {
    background: rgba(192, 255, 107, 0.1);
    border: 2px solid #C0ff6b;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    display: none;
    animation: slideIn 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #C0ff6b;
}

.notification-content i {
    font-size: 1.2rem;
}

.notification-content span {
    flex: 1;
    font-weight: 500;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
// Session configuration - Using centralized WebSocket URLs from Django settings
const sessionConfig = {
    roomId: '{{ room_id }}',
    userRole: '{{ user_role }}',
    duration: {{ session_duration_ms }},
    // WebSocket URLs from centralized configuration
    videoWsUrl: `{{ WEBSOCKET_URLS.mentorship_video }}{{ room_id }}/`,
    audioWsUrl: `{{ WEBSOCKET_URLS.mentorship_audio }}{{ room_id }}/`,
    chatWsUrl: `{{ WEBSOCKET_URLS.mentorship_chat }}{{ room_id }}/`,
    // Keep old WebSocket for existing functionality
    oldWsUrl: `{{ WEBSOCKET_URLS.mentorship_session }}{{ room_id }}/`
};

// WebSocket connections
let videoWS = null;
let audioWS = null;
let chatWS = null;

// WebRTC variables
let localStream = null;
let screenStream = null;
let peerConnections = new Map(); // Map of user_id -> RTCPeerConnection
let remoteStreams = new Map(); // Map of user_id -> MediaStream

// WebRTC configuration
const rtcConfig = {
    iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' },
        { urls: 'stun:stun3.l.google.com:19302' }
    ],
    iceCandidatePoolSize: 10,
    bundlePolicy: 'max-bundle',
    rtcpMuxPolicy: 'require'
};

// Initialize profile placeholders
function initializeProfilePlaceholders() {
    // Show profile placeholders by default since video starts disabled
    showProfilePlaceholder('mentor');
    showProfilePlaceholder('learner');
}

// Debug function for audio and video troubleshooting
function debugAudioState() {
    console.log('=== WEBRTC DEBUG STATE ===');
    console.log('Local stream:', localStream);
    if (localStream) {
        console.log('Local stream tracks:', localStream.getTracks().map(t => `${t.kind}: ${t.id} (enabled: ${t.enabled}, readyState: ${t.readyState})`));
        console.log('Local video tracks:', localStream.getVideoTracks().map(t => `${t.id} (enabled: ${t.enabled}, muted: ${t.muted})`));
        console.log('Local audio tracks:', localStream.getAudioTracks().map(t => `${t.id} (enabled: ${t.enabled}, muted: ${t.muted})`));
    }

    console.log('Screen stream:', screenStream);
    if (screenStream) {
        console.log('Screen stream tracks:', screenStream.getTracks().map(t => `${t.kind}: ${t.id} (enabled: ${t.enabled})`));
    }

    console.log('Original local stream:', window.originalLocalStream);
    if (window.originalLocalStream) {
        console.log('Original stream tracks:', window.originalLocalStream.getTracks().map(t => `${t.kind}: ${t.id} (enabled: ${t.enabled})`));
    }

    console.log('Peer connections:', peerConnections.size);
    peerConnections.forEach((pc, userId) => {
        console.log(`Peer ${userId}:`, pc.connectionState, 'ICE:', pc.iceConnectionState);
        console.log(`  Senders:`, pc.getSenders().map(s => s.track ? `${s.track.kind}: ${s.track.id} (enabled: ${s.track.enabled})` : 'no track'));
        console.log(`  Receivers:`, pc.getReceivers().map(r => r.track ? `${r.track.kind}: ${r.track.id} (enabled: ${r.track.enabled})` : 'no track'));
    });

    console.log('Remote streams:', remoteStreams.size);
    remoteStreams.forEach((stream, userId) => {
        console.log(`Remote stream ${userId}:`, stream.getTracks().map(t => `${t.kind}: ${t.id} (enabled: ${t.enabled}, readyState: ${t.readyState})`));
    });

    console.log('Media states:');
    console.log('  Video enabled:', isVideoEnabled);
    console.log('  Audio enabled:', isAudioEnabled);
    console.log('  Screen sharing:', isScreenSharing);

    console.log('WebSocket states:');
    console.log('  Video WS:', videoWS ? `${videoWS.readyState} (${videoWS.readyState === 1 ? 'OPEN' : 'CLOSED'})` : 'null');
    console.log('  Audio WS:', audioWS ? `${audioWS.readyState} (${audioWS.readyState === 1 ? 'OPEN' : 'CLOSED'})` : 'null');
    console.log('  Chat WS:', chatWS ? `${chatWS.readyState} (${chatWS.readyState === 1 ? 'OPEN' : 'CLOSED'})` : 'null');

    // Check video elements
    const mentorVideo = document.getElementById('mentorVideo');
    const learnerVideo = document.getElementById('learnerVideo');
    console.log('Video elements:');
    console.log('  Mentor video:', mentorVideo ? `srcObject: ${!!mentorVideo.srcObject}, muted: ${mentorVideo.muted}` : 'not found');
    console.log('  Learner video:', learnerVideo ? `srcObject: ${!!learnerVideo.srcObject}, muted: ${learnerVideo.muted}` : 'not found');

    console.log('========================');
}

// Debug function for placeholder states
function debugPlaceholderState() {
    console.log('=== PLACEHOLDER DEBUG STATE ===');

    const mentorPlaceholder = document.getElementById('mentorProfilePlaceholder');
    const learnerPlaceholder = document.getElementById('learnerProfilePlaceholder');
    const mentorVideo = document.getElementById('mentorVideo');
    const learnerVideo = document.getElementById('learnerVideo');

    console.log('Mentor placeholder:', {
        element: !!mentorPlaceholder,
        display: mentorPlaceholder ? mentorPlaceholder.style.display : 'N/A',
        hidden: mentorPlaceholder ? mentorPlaceholder.classList.contains('hidden') : 'N/A'
    });

    console.log('Learner placeholder:', {
        element: !!learnerPlaceholder,
        display: learnerPlaceholder ? learnerPlaceholder.style.display : 'N/A',
        hidden: learnerPlaceholder ? learnerPlaceholder.classList.contains('hidden') : 'N/A'
    });

    console.log('Mentor video:', {
        element: !!mentorVideo,
        srcObject: mentorVideo ? !!mentorVideo.srcObject : 'N/A',
        display: mentorVideo ? mentorVideo.style.display : 'N/A'
    });

    console.log('Learner video:', {
        element: !!learnerVideo,
        srcObject: learnerVideo ? !!learnerVideo.srcObject : 'N/A',
        display: learnerVideo ? learnerVideo.style.display : 'N/A'
    });

    console.log('Current user role:', sessionConfig.userRole);
    console.log('===============================');
}

// Make debug functions available globally for console access
window.debugAudioState = debugAudioState;
window.debugPlaceholderState = debugPlaceholderState;

// Check microphone permissions and availability
async function checkMicrophonePermissions() {
    try {
        console.log('Checking microphone permissions...');

        // Check if getUserMedia is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            console.error('getUserMedia is not supported in this browser');
            return false;
        }

        // Check permissions API if available
        if (navigator.permissions) {
            try {
                const permission = await navigator.permissions.query({ name: 'microphone' });
                console.log('Microphone permission state:', permission.state);

                if (permission.state === 'denied') {
                    console.warn('Microphone permission is denied');
                    return false;
                }
            } catch (error) {
                console.log('Permissions API not fully supported, continuing...');
            }
        }

        // Try to enumerate devices to check if microphone is available
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const audioInputs = devices.filter(device => device.kind === 'audioinput');
            console.log(`Found ${audioInputs.length} audio input devices`);

            if (audioInputs.length === 0) {
                console.warn('No microphone devices found');
                return false;
            }
        } catch (error) {
            console.log('Could not enumerate devices:', error);
        }

        return true;
    } catch (error) {
        console.error('Error checking microphone permissions:', error);
        return false;
    }
}

// Fullscreen functionality for screen sharing and video participants
function initializeFullscreenControls() {
    // Screen share fullscreen button
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    const scaleToggleBtn = document.getElementById('scaleToggleBtn');
    const screenShareContainer = document.getElementById('screenShareContainer');

    // Video participant fullscreen buttons
    const mentorFullscreenBtn = document.getElementById('mentorFullscreenBtn');
    const learnerFullscreenBtn = document.getElementById('learnerFullscreenBtn');

    if (fullscreenBtn && screenShareContainer) {
        fullscreenBtn.addEventListener('click', () => toggleFullscreen('screen-share'));
    }

    if (scaleToggleBtn && screenShareContainer) {
        scaleToggleBtn.addEventListener('click', () => toggleScaleMode());
    }

    if (mentorFullscreenBtn) {
        mentorFullscreenBtn.addEventListener('click', () => toggleFullscreen('mentor'));
    }

    if (learnerFullscreenBtn) {
        learnerFullscreenBtn.addEventListener('click', () => toggleFullscreen('learner'));
    }

    // Listen for fullscreen change events
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // Listen for escape key to exit fullscreen
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && isInFullscreen()) {
            exitFullscreen();
        }
    });
}

// Global variable to track current fullscreen type
let currentFullscreenType = null;
let currentScaleMode = 'contain'; // 'contain', 'cover', 'fill'

function toggleScaleMode() {
    if (currentFullscreenType !== 'screen-share') return;

    const screenShareContainer = document.getElementById('screenShareContainer');
    const scaleToggleBtn = document.getElementById('scaleToggleBtn');

    if (!screenShareContainer || !scaleToggleBtn) return;

    // Cycle through scale modes: contain -> cover -> fill -> contain
    switch(currentScaleMode) {
        case 'contain':
            currentScaleMode = 'cover';
            screenShareContainer.classList.remove('scale-stretch');
            screenShareContainer.classList.add('scale-fill');
            scaleToggleBtn.innerHTML = '<i class="fas fa-expand"></i>';
            scaleToggleBtn.title = 'Fit Mode: Fill (crop to fit)';
            addNewSystemMessage('Scale mode: Fill (crop to fit screen)');
            break;
        case 'cover':
            currentScaleMode = 'fill';
            screenShareContainer.classList.remove('scale-fill');
            screenShareContainer.classList.add('scale-stretch');
            scaleToggleBtn.innerHTML = '<i class="fas fa-arrows-alt"></i>';
            scaleToggleBtn.title = 'Fit Mode: Stretch (may distort)';
            addNewSystemMessage('Scale mode: Stretch (may distort image)');
            break;
        case 'fill':
            currentScaleMode = 'contain';
            screenShareContainer.classList.remove('scale-stretch');
            scaleToggleBtn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
            scaleToggleBtn.title = 'Fit Mode: Contain (show all)';
            addNewSystemMessage('Scale mode: Contain (show all content)');
            break;
    }
}

function toggleFullscreen(type) {
    if (isInFullscreen() && currentFullscreenType === type) {
        exitFullscreen();
    } else {
        enterFullscreen(type);
    }
}

function enterFullscreen(type) {
    let element, button, label;

    switch(type) {
        case 'screen-share':
            element = document.getElementById('screenShareContainer');
            button = document.getElementById('fullscreenBtn');
            label = 'screen share';
            break;
        case 'mentor':
            element = document.querySelector('.mentor-video');
            button = document.getElementById('mentorFullscreenBtn');
            label = 'mentor video';
            break;
        case 'learner':
            element = document.querySelector('.learner-video');
            button = document.getElementById('learnerFullscreenBtn');
            label = 'learner video';
            break;
        default:
            return;
    }

    if (!element) return;

    console.log(`Entering fullscreen mode for ${label}`);

    // Exit any existing fullscreen first
    if (isInFullscreen()) {
        exitFullscreen();
    }

    // Add fullscreen class for styling
    element.classList.add('fullscreen');
    currentFullscreenType = type;

    // Update button icon
    if (button) {
        button.innerHTML = '<i class="fas fa-compress"></i>';
        button.title = 'Exit Fullscreen';
    }

    // Show scale toggle button for screen share
    if (type === 'screen-share') {
        const scaleToggleBtn = document.getElementById('scaleToggleBtn');
        if (scaleToggleBtn) {
            scaleToggleBtn.style.display = 'flex';
            scaleToggleBtn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
            scaleToggleBtn.title = 'Fit Mode: Contain (show all)';
        }
    }

    // Request fullscreen using the Fullscreen API
    if (element.requestFullscreen) {
        element.requestFullscreen();
    } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen();
    } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen();
    } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen();
    }

    // Add system message
    addNewSystemMessage(`Entered fullscreen mode for ${label}. Press ESC to exit.`);
}

function exitFullscreen() {
    if (!currentFullscreenType) return;

    let element, button, label;

    switch(currentFullscreenType) {
        case 'screen-share':
            element = document.getElementById('screenShareContainer');
            button = document.getElementById('fullscreenBtn');
            label = 'screen share';
            break;
        case 'mentor':
            element = document.querySelector('.mentor-video');
            button = document.getElementById('mentorFullscreenBtn');
            label = 'mentor video';
            break;
        case 'learner':
            element = document.querySelector('.learner-video');
            button = document.getElementById('learnerFullscreenBtn');
            label = 'learner video';
            break;
    }

    console.log(`Exiting fullscreen mode for ${label}`);

    // Remove fullscreen class
    if (element) {
        element.classList.remove('fullscreen');
        // Remove scale classes
        element.classList.remove('scale-fill', 'scale-stretch');
    }

    // Update button icon
    if (button) {
        button.innerHTML = '<i class="fas fa-expand"></i>';
        button.title = 'Enter Fullscreen';
    }

    // Hide scale toggle button and reset scale mode
    if (currentFullscreenType === 'screen-share') {
        const scaleToggleBtn = document.getElementById('scaleToggleBtn');
        if (scaleToggleBtn) {
            scaleToggleBtn.style.display = 'none';
        }
        currentScaleMode = 'contain';
    }

    // Exit fullscreen using the Fullscreen API
    if (document.exitFullscreen) {
        document.exitFullscreen();
    } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
    } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
    } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
    }

    currentFullscreenType = null;

    // Add system message
    addNewSystemMessage(`Exited fullscreen mode.`);
}

function isInFullscreen() {
    return !!(document.fullscreenElement ||
              document.webkitFullscreenElement ||
              document.mozFullScreenElement ||
              document.msFullscreenElement);
}

function handleFullscreenChange() {
    if (!isInFullscreen() && currentFullscreenType) {
        // Fullscreen was exited (possibly by ESC key)
        let element, button;

        switch(currentFullscreenType) {
            case 'screen-share':
                element = document.getElementById('screenShareContainer');
                button = document.getElementById('fullscreenBtn');
                break;
            case 'mentor':
                element = document.querySelector('.mentor-video');
                button = document.getElementById('mentorFullscreenBtn');
                break;
            case 'learner':
                element = document.querySelector('.learner-video');
                button = document.getElementById('learnerFullscreenBtn');
                break;
        }

        if (element) {
            element.classList.remove('fullscreen');
            // Remove scale classes
            element.classList.remove('scale-fill', 'scale-stretch');
        }
        if (button) {
            button.innerHTML = '<i class="fas fa-expand"></i>';
            button.title = 'Enter Fullscreen';
        }

        // Hide scale toggle button and reset scale mode
        if (currentFullscreenType === 'screen-share') {
            const scaleToggleBtn = document.getElementById('scaleToggleBtn');
            if (scaleToggleBtn) {
                scaleToggleBtn.style.display = 'none';
            }
            currentScaleMode = 'contain';
        }

        console.log(`Fullscreen mode exited for ${currentFullscreenType}`);
        currentFullscreenType = null;
    }
}

// Helper function to get CSRF token
function getCsrfToken() {
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    return csrfToken ? csrfToken.value : '';
}

// Initialize session room
document.addEventListener('DOMContentLoaded', async function() {
    initializeNewWebSockets();
    initializeTimer();
    initializeMediaControls();
    initializeNewChat();
    initializeProfilePlaceholders();
    initializeFullscreenControls();

    // Check microphone availability
    const micAvailable = await checkMicrophonePermissions();
    if (!micAvailable) {
        console.warn('Microphone may not be available or permissions denied');
        // You could show a warning to the user here
    }

    // Add debug functions (remove in production)
    console.log('Debug functions available:');
    console.log('  window.debugAudioState() - WebRTC and audio state');
    console.log('  window.debugPlaceholderState() - Placeholder visibility state');
});



// WebSocket for real-time collaboration
function initializeWebSocket() {
    window.sessionWS = new WebSocket(sessionConfig.wsUrl);

    window.sessionWS.onopen = function(e) {
        console.log('Connected to session');
        addSystemMessage('Connected to session');
    };

    window.sessionWS.onmessage = function(e) {
        const data = JSON.parse(e.data);

        switch(data.type) {
            case 'chat_message':
                addChatMessage(data.sender, data.message, data.sender !== '{{ user.username }}');
                break;
            case 'user_joined':
                addSystemMessage(`${data.user} joined the session`);
                break;
            case 'user_left':
                addSystemMessage(`${data.user} left the session`);
                break;
            case 'screen_share_started':
                addSystemMessage(`${data.user} started screen sharing`);
                break;
            case 'screen_share_stopped':
                addSystemMessage(`${data.user} stopped screen sharing`);
                break;
        }
    };

    window.sessionWS.onclose = function(e) {
        console.log('Disconnected from session');
        addSystemMessage('Disconnected from session');
    };
}

// New WebSocket connections for video, audio, and chat
function initializeNewWebSockets() {
    // Initialize Video WebSocket
    initializeVideoWebSocket();

    // Initialize Audio WebSocket
    initializeAudioWebSocket();

    // Initialize Chat WebSocket
    initializeChatWebSocket();
}

function initializeVideoWebSocket() {
    videoWS = new WebSocket(sessionConfig.videoWsUrl);

    videoWS.onopen = function(event) {
        console.log('Connected to video WebSocket');
        updateConnectionStatus('video', true);
    };

    videoWS.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleVideoMessage(data);
    };

    videoWS.onclose = function(event) {
        console.log('Disconnected from video WebSocket');
        updateConnectionStatus('video', false);
    };

    videoWS.onerror = function(error) {
        console.error('Video WebSocket error:', error);
        updateConnectionStatus('video', false);
    };
}

function initializeAudioWebSocket() {
    audioWS = new WebSocket(sessionConfig.audioWsUrl);

    audioWS.onopen = function(event) {
        console.log('Connected to audio WebSocket');
        updateConnectionStatus('audio', true);
    };

    audioWS.onmessage = function(event) {
        if (event.data instanceof Blob) {
            // Handle binary audio data
            handleAudioData(event.data);
        } else {
            // Handle text audio control messages
            const data = JSON.parse(event.data);
            handleAudioMessage(data);
        }
    };

    audioWS.onclose = function(event) {
        console.log('Disconnected from audio WebSocket');
        updateConnectionStatus('audio', false);
    };

    audioWS.onerror = function(error) {
        console.error('Audio WebSocket error:', error);
        updateConnectionStatus('audio', false);
    };
}

function initializeChatWebSocket() {
    chatWS = new WebSocket(sessionConfig.chatWsUrl);

    chatWS.onopen = function(event) {
        console.log('Connected to chat WebSocket');
        updateConnectionStatus('chat', true);
        // Chat history will be sent automatically by the WebSocket consumer
    };

    chatWS.onmessage = function(event) {
        const data = JSON.parse(event.data);
        handleChatMessage(data);
    };

    chatWS.onclose = function(event) {
        console.log('Disconnected from chat WebSocket');
        updateConnectionStatus('chat', false);
    };

    chatWS.onerror = function(error) {
        console.error('Chat WebSocket error:', error);
        updateConnectionStatus('chat', false);
    };
}

// Handle different types of messages
async function handleVideoMessage(data) {
    switch(data.type) {
        case 'participant_joined':
            console.log('Participant joined video:', data.user_id);
            addNewSystemMessage(`${data.username} joined video call`);
            // Create WebRTC offer if we have local stream
            await handleParticipantJoined(data.user_id);
            break;
        case 'participant_left':
            console.log('Participant left video:', data.user_id);
            addNewSystemMessage(`${data.username} left video call`);
            // Cleanup peer connection
            cleanupPeerConnection(data.user_id);
            break;
        case 'offer':
        case 'answer':
        case 'ice-candidate':
            // Handle WebRTC signaling
            handleWebRTCSignaling(data);
            break;
        case 'video-toggle':
            handleRemoteVideoToggle(data);
            break;
        case 'screen-share':
            handleRemoteScreenShare(data);
            break;
    }
}

function handleAudioMessage(data) {
    console.log('Audio message received:', data);
    switch(data.type) {
        case 'audio_participant_joined':
            console.log('Participant joined audio:', data.user_id || data.client_id);
            addNewSystemMessage(`${data.username || 'Participant'} joined voice chat`);
            break;
        case 'audio_participant_left':
            console.log('Participant left audio:', data.user_id || data.client_id);
            addNewSystemMessage(`${data.username || 'Participant'} left voice chat`);
            break;
        case 'audio-toggle':
            handleRemoteAudioToggle(data);
            break;
    }
}

function handleChatMessage(data) {
    switch(data.type) {
        case 'chat_message':
            const isOwn = data.user_id === {{ user.id }};
            addNewChatMessage(data.message, data.username, isOwn);
            break;
        case 'chat_history':
            // Handle chat history messages from WebSocket
            const isOwnHistory = data.user_id === {{ user.id }};
            const messageTimestamp = new Date(data.timestamp * 1000);
            addNewChatMessage(data.message, data.username, isOwnHistory, messageTimestamp);
            break;
        case 'system_message':
            addNewSystemMessage(data.message);
            break;
        case 'user_joined':
            addNewSystemMessage(data.message);
            break;
        case 'user_left':
            addNewSystemMessage(data.message);
            break;
        case 'typing':
            handleTypingIndicator(data);
            break;
    }
}

function updateConnectionStatus(type, connected) {
    const statusElement = document.getElementById(`${type}Status`);
    if (statusElement) {
        if (connected) {
            statusElement.classList.add('connected');
            statusElement.classList.remove('disconnected');
        } else {
            statusElement.classList.add('disconnected');
            statusElement.classList.remove('connected');
        }
    }
}

// WebRTC signaling handler
async function handleWebRTCSignaling(data) {
    console.log('WebRTC signaling received:', data);
    const { sender_id, type: signalType } = data;

    // Skip our own messages
    if (sender_id == '{{ user.id }}') {
        console.log('Skipping own signaling message');
        return;
    }

    try {
        let peerConnection = peerConnections.get(sender_id);

        if (!peerConnection && signalType === 'offer') {
            console.log(`Creating peer connection for incoming offer from ${sender_id}`);
            peerConnection = await createPeerConnectionForAnswer(sender_id);
        }

        if (!peerConnection) {
            console.error(`No peer connection found for user ${sender_id}`);
            return;
        }

        switch (signalType) {
            case 'offer':
                console.log(`Processing offer from ${sender_id}`);
                await peerConnection.setRemoteDescription(new RTCSessionDescription(data.offer));
                const answer = await peerConnection.createAnswer();
                await peerConnection.setLocalDescription(answer);

                // Send answer back
                if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                    console.log(`Sending answer to ${sender_id}`);
                    videoWS.send(JSON.stringify({
                        type: 'answer',
                        answer: answer,
                        target_user: sender_id
                    }));
                }
                break;

            case 'answer':
                console.log(`Processing answer from ${sender_id}`);
                await peerConnection.setRemoteDescription(new RTCSessionDescription(data.answer));
                break;

            case 'ice-candidate':
                console.log(`Processing ICE candidate from ${sender_id}`);
                if (data.candidate) {
                    await peerConnection.addIceCandidate(new RTCIceCandidate(data.candidate));
                }
                break;
        }
    } catch (error) {
        console.error('WebRTC signaling error:', error, data);
    }
}

// Create WebRTC peer connection
async function createPeerConnection(userId) {
    console.log(`Creating new peer connection for user ${userId}`);
    const peerConnection = new RTCPeerConnection(rtcConfig);
    peerConnections.set(userId, peerConnection);

    // Add local stream tracks if available
    if (localStream) {
        console.log(`Adding ${localStream.getTracks().length} tracks to peer connection`);
        localStream.getTracks().forEach(track => {
            console.log(`Adding track: ${track.kind}`);
            peerConnection.addTrack(track, localStream);
        });
    }

    // Handle remote stream
    peerConnection.ontrack = (event) => {
        console.log('Received remote stream from user:', userId);
        const [remoteStream] = event.streams;
        remoteStreams.set(userId, remoteStream);
        displayRemoteVideo(userId, remoteStream);
    };

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
        if (event.candidate && videoWS && videoWS.readyState === WebSocket.OPEN) {
            console.log(`Sending ICE candidate to user ${userId}`);
            videoWS.send(JSON.stringify({
                type: 'ice-candidate',
                candidate: event.candidate,
                target_user: userId
            }));
        }
    };

    // Handle connection state changes
    peerConnection.onconnectionstatechange = () => {
        console.log(`Peer connection with ${userId} state:`, peerConnection.connectionState);
        if (peerConnection.connectionState === 'disconnected' ||
            peerConnection.connectionState === 'failed') {
            cleanupPeerConnection(userId);
        }
    };

    // Create and send offer if we have local stream
    if (localStream) {
        try {
            console.log(`Creating offer for user ${userId}`);

            // Set audio codec preferences for better quality
            const transceivers = peerConnection.getTransceivers();
            transceivers.forEach(transceiver => {
                if (transceiver.sender && transceiver.sender.track && transceiver.sender.track.kind === 'audio') {
                    const params = transceiver.sender.getParameters();
                    if (params.codecs) {
                        // Prefer Opus codec for audio
                        params.codecs = params.codecs.sort((a, b) => {
                            if (a.mimeType.includes('opus')) return -1;
                            if (b.mimeType.includes('opus')) return 1;
                            return 0;
                        });
                        transceiver.sender.setParameters(params);
                    }
                }
            });

            const offer = await peerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });
            await peerConnection.setLocalDescription(offer);

            if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                console.log(`Sending offer to user ${userId}`);
                videoWS.send(JSON.stringify({
                    type: 'offer',
                    offer: offer,
                    target_user: userId
                }));
            }
        } catch (error) {
            console.error(`Error creating offer for user ${userId}:`, error);
        }
    }

    return peerConnection;
}

// Create peer connection for answering (without creating offer)
async function createPeerConnectionForAnswer(userId) {
    console.log(`Creating peer connection for answer to user ${userId}`);
    const peerConnection = new RTCPeerConnection(rtcConfig);
    peerConnections.set(userId, peerConnection);

    // Add local stream tracks if available
    if (localStream) {
        console.log(`Adding ${localStream.getTracks().length} tracks to peer connection for answer`);
        localStream.getTracks().forEach(track => {
            console.log(`Adding track: ${track.kind}`);
            peerConnection.addTrack(track, localStream);
        });
    }

    // Handle remote stream
    peerConnection.ontrack = (event) => {
        console.log('Received remote stream from user:', userId);
        const [remoteStream] = event.streams;
        remoteStreams.set(userId, remoteStream);
        displayRemoteVideo(userId, remoteStream);
    };

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
        if (event.candidate && videoWS && videoWS.readyState === WebSocket.OPEN) {
            console.log(`Sending ICE candidate to user ${userId}`);
            videoWS.send(JSON.stringify({
                type: 'ice-candidate',
                candidate: event.candidate,
                target_user: userId
            }));
        }
    };

    // Handle connection state changes
    peerConnection.onconnectionstatechange = () => {
        console.log(`Peer connection with ${userId} state:`, peerConnection.connectionState);
        if (peerConnection.connectionState === 'disconnected' ||
            peerConnection.connectionState === 'failed') {
            cleanupPeerConnection(userId);
        }
    };

    return peerConnection;
}

// Display remote video stream
function displayRemoteVideo(userId, stream) {
    // Determine which video element to use based on user role
    // For now, we'll use the opposite role's video element
    const currentUserRole = sessionConfig.userRole;
    let videoElement;
    let placeholderRole;

    if (currentUserRole === 'mentor') {
        videoElement = document.getElementById('learnerVideo');
        placeholderRole = 'learner';
    } else {
        videoElement = document.getElementById('mentorVideo');
        placeholderRole = 'mentor';
    }

    if (videoElement) {
        videoElement.srcObject = stream;
        videoElement.style.display = 'block';

        // Ensure audio is enabled for remote streams
        videoElement.muted = false;
        videoElement.autoplay = true;

        console.log(`Displaying remote stream from user ${userId}`);
        console.log('Remote stream tracks:', stream.getTracks().map(t => `${t.kind}: ${t.id}`));

        // Check for audio tracks in the remote stream
        const audioTracks = stream.getAudioTracks();
        const videoTracks = stream.getVideoTracks();

        console.log(`Remote stream has ${videoTracks.length} video tracks and ${audioTracks.length} audio tracks`);

        // CRITICAL FIX: Hide profile placeholder when remote video/screen content is received
        if (videoTracks.length > 0) {
            console.log(`Hiding profile placeholder for ${placeholderRole} - remote video content received`);
            hideRemoteProfilePlaceholder(placeholderRole);
        }

        // Update status to show remote video is active
        const videoStatusElement = currentUserRole === 'mentor' ?
            document.getElementById('learnerVideoStatus') :
            document.getElementById('mentorVideoStatus');

        if (videoStatusElement && videoTracks.length > 0) {
            videoStatusElement.innerHTML = '<i class="fas fa-video"></i>';
            videoStatusElement.classList.add('active');
        }

        // Update status to show remote audio is active
        const audioStatusElement = currentUserRole === 'mentor' ?
            document.getElementById('learnerAudioStatus') :
            document.getElementById('mentorAudioStatus');

        if (audioStatusElement && audioTracks.length > 0) {
            audioStatusElement.innerHTML = '<i class="fas fa-microphone"></i>';
            audioStatusElement.classList.add('active');
            console.log('Remote audio track detected and status updated');
        }

        // Add event listeners for track events
        stream.addEventListener('addtrack', (event) => {
            console.log('Remote track added:', event.track.kind, event.track.id);
            if (event.track.kind === 'video') {
                console.log('Remote video track added - hiding placeholder');
                hideRemoteProfilePlaceholder(placeholderRole);
            }
            if (event.track.kind === 'audio') {
                console.log('Remote audio track added - updating status');
                if (audioStatusElement) {
                    audioStatusElement.innerHTML = '<i class="fas fa-microphone"></i>';
                    audioStatusElement.classList.add('active');
                }
            }
        });

        stream.addEventListener('removetrack', (event) => {
            console.log('Remote track removed:', event.track.kind, event.track.id);
            if (event.track.kind === 'video') {
                console.log('Remote video track removed - showing placeholder');
                showRemoteProfilePlaceholder(placeholderRole);
            }
            if (event.track.kind === 'audio') {
                console.log('Remote audio track removed - updating status');
                if (audioStatusElement) {
                    audioStatusElement.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                    audioStatusElement.classList.remove('active');
                }
            }
        });

        // Force video element to play (important for some browsers)
        videoElement.play().catch(error => {
            console.log('Video autoplay prevented:', error);
        });
    }
}

// Cleanup peer connection
function cleanupPeerConnection(userId) {
    const peerConnection = peerConnections.get(userId);
    if (peerConnection) {
        peerConnection.close();
        peerConnections.delete(userId);
    }

    remoteStreams.delete(userId);

    // Clear remote video display and show placeholder
    const currentUserRole = sessionConfig.userRole;
    const videoElement = currentUserRole === 'mentor' ?
        document.getElementById('learnerVideo') :
        document.getElementById('mentorVideo');

    const placeholderRole = currentUserRole === 'mentor' ? 'learner' : 'mentor';

    if (videoElement) {
        videoElement.srcObject = null;
        videoElement.style.display = 'none';

        // Show profile placeholder when connection is lost
        console.log(`Connection lost - showing profile placeholder for ${placeholderRole}`);
        showRemoteProfilePlaceholder(placeholderRole);
    }
}

function handleRemoteVideoToggle(data) {
    console.log('Remote video toggle:', data);
    const message = `${data.sender_username} ${data.enabled ? 'enabled' : 'disabled'} video`;
    addNewSystemMessage(message);

    // Update remote video status indicator and placeholder visibility
    updateRemoteVideoStatus(data.sender_username, data.user_role, data.enabled);

    // Additional placeholder management based on video state
    const currentUserRole = sessionConfig.userRole;
    const isRemoteMentor = data.user_role === 'mentor' && currentUserRole !== 'mentor';
    const isRemoteLearner = data.user_role === 'learner' && currentUserRole !== 'learner';

    if (isRemoteMentor) {
        if (data.enabled) {
            console.log('Remote mentor enabled video - will hide placeholder when stream arrives');
        } else {
            console.log('Remote mentor disabled video - showing placeholder');
            showRemoteProfilePlaceholder('mentor');
        }
    } else if (isRemoteLearner) {
        if (data.enabled) {
            console.log('Remote learner enabled video - will hide placeholder when stream arrives');
        } else {
            console.log('Remote learner disabled video - showing placeholder');
            showRemoteProfilePlaceholder('learner');
        }
    }
}

function handleRemoteScreenShare(data) {
    console.log('Remote screen share:', data);
    const message = `${data.sender_username} ${data.sharing ? 'started' : 'stopped'} screen sharing`;
    addNewSystemMessage(message);

    const currentUserRole = sessionConfig.userRole;
    const isRemoteMentor = data.user_role === 'mentor' && currentUserRole !== 'mentor';
    const isRemoteLearner = data.user_role === 'learner' && currentUserRole !== 'learner';
    const placeholderRole = isRemoteMentor ? 'mentor' : 'learner';

    if (data.sharing) {
        // Show remote screen share notification and prepare for screen stream
        showRemoteScreenShareNotification(data.sender_username);
        console.log(`${data.sender_username} started screen sharing - hiding placeholder, screen content will appear`);
        // Hide placeholder when screen sharing starts (screen content will appear)
        hideRemoteProfilePlaceholder(placeholderRole);
    } else {
        // Hide remote screen share notification
        hideRemoteScreenShareNotification(data.sender_username);
        console.log(`${data.sender_username} stopped screen sharing - will show camera or placeholder`);
        // Note: Don't show placeholder here immediately, let the video stream handler decide
        // based on whether camera is still enabled
    }
}

function handleRemoteAudioToggle(data) {
    console.log('Remote audio toggle:', data);
    const message = `${data.sender_username} ${data.enabled ? 'unmuted' : 'muted'} microphone`;
    addNewSystemMessage(message);

    // Update remote audio status indicator
    updateRemoteAudioStatus(data.sender_username, data.user_role, data.enabled);
}

function updateRemoteAudioStatus(username, userRole, enabled) {
    // Determine which audio element to update based on user role
    const isRemoteMentor = userRole === 'mentor' && sessionConfig.userRole !== 'mentor';
    const isRemoteLearner = userRole === 'learner' && sessionConfig.userRole !== 'learner';

    if (isRemoteMentor) {
        const mentorAudioStatus = document.getElementById('mentorAudioStatus');
        if (mentorAudioStatus) {
            if (enabled) {
                mentorAudioStatus.innerHTML = '<i class="fas fa-microphone"></i>';
                mentorAudioStatus.classList.add('active');
            } else {
                mentorAudioStatus.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                mentorAudioStatus.classList.remove('active');
            }
        }
    } else if (isRemoteLearner) {
        const learnerAudioStatus = document.getElementById('learnerAudioStatus');
        if (learnerAudioStatus) {
            if (enabled) {
                learnerAudioStatus.innerHTML = '<i class="fas fa-microphone"></i>';
                learnerAudioStatus.classList.add('active');
            } else {
                learnerAudioStatus.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                learnerAudioStatus.classList.remove('active');
            }
        }
    }
}

function handleAudioData(audioBlob) {
    console.log('Received audio data:', audioBlob);
    // TODO: Implement audio playback
}

function handleTypingIndicator(data) {
    console.log('Typing indicator:', data);
    // TODO: Implement typing indicator display
}

// Helper functions for remote video and screen sharing
function updateRemoteVideoStatus(username, userRole, enabled) {
    // Determine which video element to update based on user role
    const isRemoteMentor = userRole === 'mentor' && sessionConfig.userRole !== 'mentor';
    const isRemoteLearner = userRole === 'learner' && sessionConfig.userRole !== 'learner';

    if (isRemoteMentor) {
        const mentorVideoStatus = document.getElementById('mentorVideoStatus');
        if (mentorVideoStatus) {
            if (enabled) {
                mentorVideoStatus.innerHTML = '<i class="fas fa-video"></i>';
                mentorVideoStatus.classList.add('active');
                // Hide profile placeholder when remote video is enabled
                hideRemoteProfilePlaceholder('mentor');
            } else {
                mentorVideoStatus.innerHTML = '<i class="fas fa-video-slash"></i>';
                mentorVideoStatus.classList.remove('active');
                // Show profile placeholder when remote video is disabled
                showRemoteProfilePlaceholder('mentor');
            }
        }
    } else if (isRemoteLearner) {
        const learnerVideoStatus = document.getElementById('learnerVideoStatus');
        if (learnerVideoStatus) {
            if (enabled) {
                learnerVideoStatus.innerHTML = '<i class="fas fa-video"></i>';
                learnerVideoStatus.classList.add('active');
                // Hide profile placeholder when remote video is enabled
                hideRemoteProfilePlaceholder('learner');
            } else {
                learnerVideoStatus.innerHTML = '<i class="fas fa-video-slash"></i>';
                learnerVideoStatus.classList.remove('active');
                // Show profile placeholder when remote video is disabled
                showRemoteProfilePlaceholder('learner');
            }
        }
    }
}

function showRemoteScreenShareNotification(username) {
    // Create or update screen share notification
    let notification = document.getElementById('remoteScreenShareNotification');
    if (!notification) {
        notification = document.createElement('div');
        notification.id = 'remoteScreenShareNotification';
        notification.className = 'remote-screen-share-notification';

        // Insert at the top of the chat messages area
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.insertBefore(notification, chatMessages.firstChild);
        }
    }

    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-desktop"></i>
            <span><strong>${username}</strong> is sharing their screen</span>
            <span style="font-size: 0.9em; opacity: 0.8;">Screen content will appear in the video window</span>
        </div>
    `;
    notification.style.display = 'block';

    // Also add a visual indicator to the video element
    addScreenShareIndicator(username);
}

function addScreenShareIndicator(username) {
    // Add indicator to the video element showing screen share
    const currentUserRole = sessionConfig.userRole;
    const videoElement = currentUserRole === 'mentor' ?
        document.getElementById('learnerVideo') :
        document.getElementById('mentorVideo');

    if (videoElement) {
        let indicator = videoElement.parentElement.querySelector('.screen-share-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'screen-share-indicator';
            indicator.style.cssText = `
                position: absolute;
                top: 10px;
                left: 10px;
                background: rgba(192, 255, 107, 0.9);
                color: #000;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: bold;
                z-index: 10;
            `;
            videoElement.parentElement.style.position = 'relative';
            videoElement.parentElement.appendChild(indicator);
        }
        indicator.textContent = `${username}'s Screen`;
        indicator.style.display = 'block';
    }
}

function hideRemoteScreenShareNotification(username) {
    const notification = document.getElementById('remoteScreenShareNotification');
    if (notification) {
        notification.style.display = 'none';
    }

    // Remove screen share indicator
    const currentUserRole = sessionConfig.userRole;
    const videoElement = currentUserRole === 'mentor' ?
        document.getElementById('learnerVideo') :
        document.getElementById('mentorVideo');

    if (videoElement) {
        const indicator = videoElement.parentElement.querySelector('.screen-share-indicator');
        if (indicator) {
            indicator.style.display = 'none';
        }
    }
}

// Screen share is now automatically displayed in the video element via WebRTC track replacement

// Helper functions for WebRTC peer connection management
function addStreamToPeerConnections(stream) {
    console.log(`Adding stream to ${peerConnections.size} existing peer connections`);
    peerConnections.forEach((peerConnection, userId) => {
        try {
            // Get existing senders to avoid duplicates
            const existingSenders = peerConnection.getSenders();
            const existingTrackKinds = existingSenders
                .filter(sender => sender.track)
                .map(sender => sender.track.kind);

            // Add only new tracks that don't already exist
            stream.getTracks().forEach(track => {
                const trackAlreadyExists = existingSenders.some(sender =>
                    sender.track && sender.track.id === track.id
                );

                if (!trackAlreadyExists) {
                    console.log(`Adding ${track.kind} track to peer connection with ${userId}`);
                    peerConnection.addTrack(track, stream);
                } else {
                    console.log(`Track ${track.kind} already exists in peer connection with ${userId}`);
                }
            });
        } catch (error) {
            console.error(`Error adding stream to peer connection with ${userId}:`, error);
        }
    });
}

function replaceStreamInPeerConnections(newStream) {
    console.log(`Replacing stream in ${peerConnections.size} peer connections`);
    peerConnections.forEach((peerConnection, userId) => {
        try {
            // Remove all existing tracks
            const senders = peerConnection.getSenders();
            senders.forEach(sender => {
                if (sender.track) {
                    console.log(`Removing ${sender.track.kind} track from peer connection with ${userId}`);
                    peerConnection.removeTrack(sender);
                }
            });

            // Add new stream tracks
            newStream.getTracks().forEach(track => {
                console.log(`Adding ${track.kind} track to peer connection with ${userId}`);
                peerConnection.addTrack(track, newStream);
            });
        } catch (error) {
            console.error(`Error replacing stream in peer connection with ${userId}:`, error);
        }
    });
}

function updatePeerConnectionsRemoveAudio() {
    console.log(`Removing audio tracks from ${peerConnections.size} peer connections`);
    peerConnections.forEach((peerConnection, userId) => {
        try {
            const senders = peerConnection.getSenders();
            senders.forEach(sender => {
                if (sender.track && sender.track.kind === 'audio') {
                    console.log(`Removing audio track from peer connection with ${userId}`);
                    peerConnection.removeTrack(sender);
                }
            });
        } catch (error) {
            console.error(`Error removing audio track from peer connection with ${userId}:`, error);
        }
    });
}

async function createOffersForExistingParticipants() {
    console.log(`Creating offers for ${peerConnections.size} existing participants`);
    for (const [userId, peerConnection] of peerConnections) {
        try {
            console.log(`Creating new offer for existing participant ${userId}`);

            // Set audio codec preferences for better quality
            const transceivers = peerConnection.getTransceivers();
            transceivers.forEach(transceiver => {
                if (transceiver.sender && transceiver.sender.track && transceiver.sender.track.kind === 'audio') {
                    const params = transceiver.sender.getParameters();
                    if (params.codecs) {
                        // Prefer Opus codec for audio
                        params.codecs = params.codecs.sort((a, b) => {
                            if (a.mimeType.includes('opus')) return -1;
                            if (b.mimeType.includes('opus')) return 1;
                            return 0;
                        });
                        transceiver.sender.setParameters(params);
                    }
                }
            });

            const offer = await peerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });
            await peerConnection.setLocalDescription(offer);

            if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                console.log(`Sending new offer to ${userId}`);
                videoWS.send(JSON.stringify({
                    type: 'offer',
                    offer: offer,
                    target_user: userId
                }));
            }
        } catch (error) {
            console.error(`Error creating offer for existing participant ${userId}:`, error);
        }
    }
}

// Screen sharing WebRTC functions
async function addScreenStreamToPeerConnections(screenStream) {
    console.log(`Adding screen stream to ${peerConnections.size} peer connections`);

    // Use the new replace function for screen sharing
    replaceStreamInPeerConnections(screenStream);

    // Create new offers for all participants
    await createOffersForExistingParticipants();
}

// Handle new participant joining - create offer
async function handleParticipantJoined(userId) {
    if (userId != '{{ user.id }}' && !peerConnections.has(userId)) {
        try {
            console.log(`Creating peer connection for participant ${userId}`);

            if (localStream) {
                // We have local stream, create connection with offer
                const peerConnection = await createPeerConnection(userId);
                console.log(`Created offer for participant ${userId}`);
            } else {
                // No local stream yet, create connection without offer (will create offer when video is enabled)
                const peerConnection = await createPeerConnectionForAnswer(userId);
                console.log(`Created peer connection for participant ${userId} (no local stream yet)`);
            }
        } catch (error) {
            console.error('Error creating peer connection for participant:', error);
        }
    } else {
        console.log(`Skipping peer connection creation: userId=${userId}, currentUserId={{ user.id }}, existingConnection=${peerConnections.has(userId)}`);
    }
}

// Session Timer
function initializeTimer() {
    let timeLeft = sessionConfig.duration / 1000; // Convert to seconds

    function updateTimer() {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        document.getElementById('timeRemaining').textContent =
            `${minutes}:${seconds.toString().padStart(2, '0')}`;

        if (timeLeft <= 0) {
            endSession();
            return;
        }

        timeLeft--;
    }

    updateTimer();
    window.sessionTimer = setInterval(updateTimer, 1000);
}

// Profile Placeholder Functions
function showProfilePlaceholder(userRole) {
    const placeholderId = userRole === 'mentor' ? 'mentorProfilePlaceholder' : 'learnerProfilePlaceholder';
    const placeholder = document.getElementById(placeholderId);
    if (placeholder) {
        placeholder.classList.remove('hidden');
        placeholder.style.display = 'flex';
    }
}

function hideProfilePlaceholder(userRole) {
    const placeholderId = userRole === 'mentor' ? 'mentorProfilePlaceholder' : 'learnerProfilePlaceholder';
    const placeholder = document.getElementById(placeholderId);
    if (placeholder) {
        placeholder.classList.add('hidden');
        setTimeout(() => {
            placeholder.style.display = 'none';
        }, 300); // Wait for transition to complete
    }
}

function showRemoteProfilePlaceholder(userRole) {
    const placeholderId = userRole === 'mentor' ? 'mentorProfilePlaceholder' : 'learnerProfilePlaceholder';
    const placeholder = document.getElementById(placeholderId);
    if (placeholder) {
        placeholder.classList.remove('hidden');
        placeholder.style.display = 'flex';
    }
}

function hideRemoteProfilePlaceholder(userRole) {
    const placeholderId = userRole === 'mentor' ? 'mentorProfilePlaceholder' : 'learnerProfilePlaceholder';
    const placeholder = document.getElementById(placeholderId);
    if (placeholder) {
        placeholder.classList.add('hidden');
        setTimeout(() => {
            placeholder.style.display = 'none';
        }, 300);
    }
}

// Global media state variables
let isVideoEnabled = false;
let isAudioEnabled = false;
let isScreenSharing = false;

// Media Controls (Video, Audio, Screen Share)
function initializeMediaControls() {

    const videoToggle = document.getElementById('videoToggle');
    const voiceToggle = document.getElementById('voiceToggle');
    const screenShare = document.getElementById('screenShare');
    const videoStatus = document.getElementById('videoStatus');
    const audioStatus = document.getElementById('audioStatus');
    const screenStatus = document.getElementById('screenStatus');

    // Video Toggle
    videoToggle.addEventListener('click', async function() {
        try {
            if (!isVideoEnabled) {
                console.log('Requesting camera access...');
                const cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: true,
                    audio: isAudioEnabled
                });
                console.log('Camera access granted, camera stream created:', cameraStream);
                console.log('Camera stream tracks:', cameraStream.getTracks().map(t => `${t.kind}: ${t.id}`));

                // Always store camera stream as original stream
                window.originalLocalStream = cameraStream;

                // If not screen sharing, use camera stream as local stream
                if (!isScreenSharing) {
                    localStream = cameraStream;
                }

                // Display local video (always show camera in local video element)
                const userRole = sessionConfig.userRole;
                const videoElement = userRole === 'mentor' ?
                    document.getElementById('mentorVideo') :
                    document.getElementById('learnerVideo');

                videoElement.srcObject = cameraStream;
                videoElement.style.display = 'block';
                console.log(`Local camera displayed in ${userRole} video element`);

                // Hide profile placeholder when video is enabled
                hideProfilePlaceholder(userRole);

                this.classList.add('active');
                this.innerHTML = '<i class="fas fa-video"></i>';
                videoStatus.innerHTML = '<i class="fas fa-video"></i><span>Video On</span>';
                videoStatus.className = 'status-item connected';

                // Update video status indicator
                const statusElement = userRole === 'mentor' ?
                    document.getElementById('mentorVideoStatus') :
                    document.getElementById('learnerVideoStatus');
                statusElement.innerHTML = '<i class="fas fa-video"></i>';
                statusElement.classList.add('active');

                // Notify other participants via new video WebSocket
                if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                    console.log('Sending video-toggle enabled message');
                    videoWS.send(JSON.stringify({
                        type: 'video-toggle',
                        enabled: true,
                        user_role: userRole,
                        username: '{{ user.username }}'
                    }));
                }

                // Only update peer connections if not screen sharing
                if (!isScreenSharing) {
                    console.log('Video enabled, updating peer connections with camera...');
                    console.log(`Current peer connections: ${peerConnections.size}`);

                    // For video, we need to replace the stream to ensure proper video transmission
                    if (peerConnections.size > 0) {
                        replaceStreamInPeerConnections(localStream);
                        // Create offers for existing participants
                        await createOffersForExistingParticipants();
                    }
                } else {
                    console.log('Screen sharing active - keeping screen share in peer connections');
                }

                isVideoEnabled = true;
            } else {
                // Stop camera stream but keep screen share if active
                if (window.originalLocalStream) {
                    window.originalLocalStream.getVideoTracks().forEach(track => track.stop());
                    window.originalLocalStream = null;
                }

                // Hide local video element
                const userRole = sessionConfig.userRole;
                const videoElement = userRole === 'mentor' ?
                    document.getElementById('mentorVideo') :
                    document.getElementById('learnerVideo');
                videoElement.style.display = 'none';

                // Show profile placeholder when video is disabled
                showProfilePlaceholder(userRole);

                this.classList.remove('active');
                this.innerHTML = '<i class="fas fa-video-slash"></i>';
                videoStatus.innerHTML = '<i class="fas fa-video-slash"></i><span>Video Off</span>';
                videoStatus.className = 'status-item disconnected';

                // Update video status indicator
                const statusElement = userRole === 'mentor' ?
                    document.getElementById('mentorVideoStatus') :
                    document.getElementById('learnerVideoStatus');
                statusElement.innerHTML = '<i class="fas fa-video-slash"></i>';
                statusElement.classList.remove('active');

                // Notify other participants via new video WebSocket
                if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                    console.log('Sending video-toggle disabled message');
                    videoWS.send(JSON.stringify({
                        type: 'video-toggle',
                        enabled: false,
                        user_role: userRole,
                        username: '{{ user.username }}'
                    }));
                }

                // Don't update peer connections if screen sharing is active
                if (!isScreenSharing) {
                    console.log('Video disabled, no screen sharing active');
                    // Could remove video tracks here if needed
                } else {
                    console.log('Video disabled but screen sharing still active - keeping screen share');
                }

                isVideoEnabled = false;
            }
        } catch (error) {
            console.error('Error accessing camera:', error);
            alert('Could not access camera. Please check permissions.');
        }
    });

    // Audio Toggle
    voiceToggle.addEventListener('click', async function() {
        try {
            if (!isAudioEnabled) {
                console.log('Requesting microphone access...');

                // Request microphone access with specific constraints
                const audioStream = await navigator.mediaDevices.getUserMedia({
                    video: false, // Only request audio for this stream
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 44100
                    }
                });

                console.log('Microphone access granted, audio stream created:', audioStream);
                console.log('Audio stream tracks:', audioStream.getTracks().map(t => `${t.kind}: ${t.id}`));

                // Properly integrate audio with existing stream
                if (localStream) {
                    console.log('Adding audio tracks to existing local stream...');
                    // Add audio tracks to existing stream
                    audioStream.getAudioTracks().forEach(track => {
                        localStream.addTrack(track);
                        console.log('Added audio track to local stream:', track.id);
                    });
                } else {
                    // No existing stream, use audio stream as local stream
                    console.log('Setting audio stream as local stream...');
                    localStream = audioStream;
                }

                // Update UI
                this.classList.add('active');
                this.innerHTML = '<i class="fas fa-microphone"></i>';
                audioStatus.innerHTML = '<i class="fas fa-microphone"></i><span>Audio On</span>';
                audioStatus.className = 'status-item connected';

                // Update audio status indicator
                const userRole = sessionConfig.userRole;
                const statusElement = userRole === 'mentor' ?
                    document.getElementById('mentorAudioStatus') :
                    document.getElementById('learnerAudioStatus');
                statusElement.innerHTML = '<i class="fas fa-microphone"></i>';
                statusElement.classList.add('active');

                // Add audio stream to all existing peer connections
                console.log('Adding audio stream to peer connections...');
                addStreamToPeerConnections(localStream);

                // Create new offers for existing participants to include audio
                await createOffersForExistingParticipants();

                // Notify other participants via audio WebSocket
                if (audioWS && audioWS.readyState === WebSocket.OPEN) {
                    console.log('Sending audio-toggle enabled message');
                    audioWS.send(JSON.stringify({
                        type: 'audio-toggle',
                        enabled: true,
                        user_role: userRole,
                        username: '{{ user.username }}'
                    }));
                }

                isAudioEnabled = true;
                console.log('Audio enabled successfully');
            } else {
                console.log('Disabling audio...');

                // Stop and remove audio tracks
                if (localStream) {
                    const audioTracks = localStream.getAudioTracks();
                    console.log(`Stopping ${audioTracks.length} audio tracks...`);
                    audioTracks.forEach(track => {
                        track.stop();
                        localStream.removeTrack(track);
                        console.log('Stopped and removed audio track:', track.id);
                    });
                }

                // Update UI
                this.classList.remove('active');
                this.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                audioStatus.innerHTML = '<i class="fas fa-microphone-slash"></i><span>Audio Off</span>';
                audioStatus.className = 'status-item disconnected';

                // Update audio status indicator
                const userRole = sessionConfig.userRole;
                const statusElement = userRole === 'mentor' ?
                    document.getElementById('mentorAudioStatus') :
                    document.getElementById('learnerAudioStatus');
                statusElement.innerHTML = '<i class="fas fa-microphone-slash"></i>';
                statusElement.classList.remove('active');

                // Update peer connections to remove audio
                console.log('Updating peer connections to remove audio...');
                updatePeerConnectionsRemoveAudio();

                // Notify other participants via audio WebSocket
                if (audioWS && audioWS.readyState === WebSocket.OPEN) {
                    console.log('Sending audio-toggle disabled message');
                    audioWS.send(JSON.stringify({
                        type: 'audio-toggle',
                        enabled: false,
                        user_role: userRole,
                        username: '{{ user.username }}'
                    }));
                }

                isAudioEnabled = false;
                console.log('Audio disabled successfully');
            }
        } catch (error) {
            console.error('Error accessing microphone:', error);

            // Provide more specific error messages
            let errorMessage = 'Could not access microphone. ';
            if (error.name === 'NotAllowedError') {
                errorMessage += 'Please allow microphone access in your browser settings and try again.';
            } else if (error.name === 'NotFoundError') {
                errorMessage += 'No microphone found. Please connect a microphone and try again.';
            } else if (error.name === 'NotReadableError') {
                errorMessage += 'Microphone is being used by another application. Please close other applications and try again.';
            } else {
                errorMessage += 'Please check your microphone settings and permissions.';
            }

            alert(errorMessage);
            console.error('Microphone error details:', {
                name: error.name,
                message: error.message,
                constraint: error.constraint
            });
        }
    });

    // Screen Share Toggle
    screenShare.addEventListener('click', async function() {
        try {
            if (!isScreenSharing) {
                screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: true,
                    audio: true
                });

                const screenShareContainer = document.getElementById('screenShareContainer');
                const screenShareVideo = document.getElementById('screenShareVideo');

                screenShareVideo.srcObject = screenStream;
                screenShareContainer.style.display = 'block';

                // Hide profile placeholder when screen sharing starts
                const userRole = sessionConfig.userRole;
                hideProfilePlaceholder(userRole);

                this.classList.add('active');
                this.innerHTML = '<i class="fas fa-desktop"></i>';
                screenStatus.innerHTML = '<i class="fas fa-desktop"></i><span>Screen Share On</span>';
                screenStatus.className = 'status-item connected';

                // Store current local stream for restoration later
                window.originalLocalStream = localStream;

                // Replace local stream with screen stream for WebRTC
                console.log('Replacing local stream with screen share...');
                localStream = screenStream;

                // Add screen stream to all peer connections
                await addScreenStreamToPeerConnections(screenStream);

                // Notify other participants via video WebSocket
                if (videoWS && videoWS.readyState === WebSocket.OPEN) {
                    console.log('Sending screen-share started message');
                    videoWS.send(JSON.stringify({
                        type: 'screen-share',
                        sharing: true,
                        user_role: sessionConfig.userRole,
                        username: '{{ user.username }}'
                    }));
                }

                // Also notify via old WebSocket for compatibility
                if (window.sessionWS && window.sessionWS.readyState === WebSocket.OPEN) {
                    window.sessionWS.send(JSON.stringify({
                        type: 'screen_share_started',
                        user: '{{ user.username }}'
                    }));
                }

                // Listen for screen share end
                screenStream.getVideoTracks()[0].addEventListener('ended', () => {
                    stopScreenShare();
                });

                isScreenSharing = true;
            } else {
                stopScreenShare();
            }
        } catch (error) {
            console.error('Error accessing screen:', error);
            alert('Could not access screen. Please check permissions.');
        }
    });

    async function stopScreenShare() {
        if (screenStream) {
            screenStream.getTracks().forEach(track => track.stop());
        }

        const screenShareContainer = document.getElementById('screenShareContainer');
        screenShareContainer.style.display = 'none';

        screenShare.classList.remove('active');
        screenShare.innerHTML = '<i class="fas fa-desktop"></i>';
        screenStatus.innerHTML = '<i class="fas fa-desktop"></i><span>Screen Share Off</span>';
        screenStatus.className = 'status-item disconnected';

        // Restore original local stream if video was enabled
        if (window.originalLocalStream && isVideoEnabled) {
            console.log('Restoring original camera stream to peer connections...');
            localStream = window.originalLocalStream;

            // Replace screen stream with camera stream
            replaceStreamInPeerConnections(localStream);
            await createOffersForExistingParticipants();

            // Update local video element to show camera again
            const userRole = sessionConfig.userRole;
            const videoElement = userRole === 'mentor' ?
                document.getElementById('mentorVideo') :
                document.getElementById('learnerVideo');
            videoElement.srcObject = localStream;
            videoElement.style.display = 'block';
            console.log('Local camera restored in video element');
        } else if (isVideoEnabled) {
            console.log('Video was enabled but no original stream found - may need to re-enable camera');
        } else {
            console.log('Video was not enabled - no camera stream to restore');
            // Show profile placeholder if video is disabled and screen sharing stopped
            const userRole = sessionConfig.userRole;
            showProfilePlaceholder(userRole);
        }

        // Notify other participants via video WebSocket
        if (videoWS && videoWS.readyState === WebSocket.OPEN) {
            console.log('Sending screen-share stopped message');
            videoWS.send(JSON.stringify({
                type: 'screen-share',
                sharing: false,
                user_role: sessionConfig.userRole,
                username: '{{ user.username }}'
            }));
        }

        // Also notify via old WebSocket for compatibility
        if (window.sessionWS && window.sessionWS.readyState === WebSocket.OPEN) {
            window.sessionWS.send(JSON.stringify({
                type: 'screen_share_stopped',
                user: '{{ user.username }}'
            }));
        }

        isScreenSharing = false;
    }

    // End Session
    document.getElementById('endSession').addEventListener('click', async function() {
        if (confirm('Are you sure you want to end the session?')) {
            try {
                console.log('Attempting to end session with room_id:', '{{ room_id }}');

                // Call the end session API
                const response = await fetch(`/mentorship/session/{{ room_id }}/end/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value || getCsrfToken()
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('End session result:', result);

                if (result.success) {
                    console.log('Session ended successfully, cleaning up...');

                    // Stop all media streams
                    if (localStream) {
                        localStream.getTracks().forEach(track => track.stop());
                    }
                    if (screenStream) {
                        screenStream.getTracks().forEach(track => track.stop());
                    }

                    // Cleanup WebSocket connections (but don't call endSession() to avoid recursion)
                    if (window.sessionTimer) {
                        clearInterval(window.sessionTimer);
                    }
                    if (window.sessionWS) {
                        window.sessionWS.close();
                    }
                    if (videoWS) {
                        videoWS.close();
                    }
                    if (audioWS) {
                        audioWS.close();
                    }
                    if (chatWS) {
                        chatWS.close();
                    }

                    // Show success message briefly before redirect
                    console.log('Redirecting to:', result.redirect_url);

                    // Add a small delay to ensure cleanup is complete
                    setTimeout(() => {
                        window.location.href = result.redirect_url;
                    }, 500);
                } else {
                    console.error('Server returned error:', result.error);
                    alert('Error ending session: ' + result.error);

                    // Still redirect to sessions page as fallback
                    setTimeout(() => {
                        window.location.href = '/mentorship/my-sessions/';
                    }, 2000);
                }
            } catch (error) {
                console.error('Error ending session:', error);
                console.error('Error details:', error.message);

                // Try to parse error response if it's a JSON response
                if (error.response) {
                    try {
                        const errorData = await error.response.json();
                        console.error('Server error response:', errorData);
                        alert('Error ending session: ' + (errorData.error || 'Unknown server error'));
                    } catch (parseError) {
                        console.error('Could not parse error response:', parseError);
                        alert('An error occurred while ending the session. Please try again.');
                    }
                } else {
                    alert('An error occurred while ending the session. Please try again.');
                }

                // Fallback redirect after error
                setTimeout(() => {
                    window.location.href = '/mentorship/my-sessions/';
                }, 2000);
            }
        }
    });
}


// New Chat functionality using new WebSocket
function initializeNewChat() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendMessage');

    function sendMessage() {
        const message = messageInput.value.trim();
        if (message && chatWS && chatWS.readyState === WebSocket.OPEN) {
            // Send via new chat WebSocket
            chatWS.send(JSON.stringify({
                type: 'chat_message',
                message: message,
                username: '{{ user.username }}',
                user_role: sessionConfig.userRole
            }));

            // Don't add to chat immediately - wait for WebSocket broadcast
            // This prevents duplicate messages for the sender
            messageInput.value = '';
        }
    }

    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendMessage();
        }
    });

    // Add typing indicator
    let typingTimer;
    messageInput.addEventListener('input', function() {
        if (chatWS && chatWS.readyState === WebSocket.OPEN) {
            chatWS.send(JSON.stringify({
                type: 'typing',
                username: '{{ user.username }}',
                typing: true
            }));

            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                chatWS.send(JSON.stringify({
                    type: 'typing',
                    username: '{{ user.username }}',
                    typing: false
                }));
            }, 1000);
        }
    });
}

// Chat message functions
function addChatMessage(sender, message, isOther) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOther ? 'other' : 'own'}`;

    messageDiv.innerHTML = `
        <div class="message-sender">${sender}</div>
        <div class="message-content">${message}</div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addSystemMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'system-message';
    messageDiv.textContent = message;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// New chat message functions for new WebSocket
function addNewChatMessage(message, username, isOwn, messageTimestamp = null) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isOwn ? 'own' : 'other'}`;

    // Use provided timestamp or current time
    const timestamp = messageTimestamp ?
        messageTimestamp.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) :
        new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    messageDiv.innerHTML = `
        <div class="message-header">
            <span class="message-sender">${username}</span>
            <span class="message-time">${timestamp}</span>
        </div>
        <div class="message-content">${message}</div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addNewSystemMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'system-message';

    const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    messageDiv.innerHTML = `
        <i class="fas fa-info-circle"></i>
        <span>${message}</span>
        <span class="system-time">${timestamp}</span>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// Session controls
document.getElementById('endSession').addEventListener('click', function() {
    if (confirm('Are you sure you want to end this session?')) {
        endSession();
    }
});

async function endSession() {
    console.log('endSession() called - cleaning up and redirecting');

    if (window.sessionTimer) {
        clearInterval(window.sessionTimer);
    }

    // Close old WebSocket (don't touch existing functionality)
    if (window.sessionWS) {
        window.sessionWS.close();
    }

    // Close new WebSocket connections
    if (videoWS) {
        videoWS.close();
    }
    if (audioWS) {
        audioWS.close();
    }
    if (chatWS) {
        chatWS.close();
    }

    // Cleanup all peer connections
    peerConnections.forEach((peerConnection, userId) => {
        peerConnection.close();
    });
    peerConnections.clear();
    remoteStreams.clear();

    // Stop local streams
    if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
        localStream = null;
    }
    if (screenStream) {
        screenStream.getTracks().forEach(track => track.stop());
        screenStream = null;
    }

    // Call the end session API to update session status
    try {
        console.log('Calling end session API from endSession()');
        const response = await fetch(`/mentorship/session/{{ room_id }}/end/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('End session API result:', result);

        if (result.success) {
            // Redirect based on server response
            console.log('Redirecting to:', result.redirect_url);
            window.location.href = result.redirect_url;
        } else {
            console.error('Error ending session:', result.error);
            // Fallback to my sessions page
            window.location.href = '/mentorship/my-sessions/';
        }
    } catch (error) {
        console.error('Error calling end session API:', error);
        // Fallback to my sessions page
        window.location.href = '/mentorship/my-sessions/';
    }
}

// Edit permissions toggle (mentor only)
{% if user_role == 'mentor' %}
document.getElementById('toggleLearnerEdit').addEventListener('click', function() {
    const canEdit = !window.editor.getOption(monaco.editor.EditorOption.readOnly);
    window.editor.updateOptions({ readOnly: canEdit });

    const toggleText = document.getElementById('editToggleText');
    toggleText.textContent = canEdit ? 'Allow Learner Edit' : 'Disable Learner Edit';

    // Send permission change via WebSocket
    if (window.sessionWS && window.sessionWS.readyState === WebSocket.OPEN) {
        window.sessionWS.send(JSON.stringify({
            type: 'edit_permission',
            canEdit: !canEdit
        }));
    }
});
{% endif %}
</script>
{% endblock %}
