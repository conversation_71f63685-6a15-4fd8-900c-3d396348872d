from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
from datetime import datetime, timedelta
from django.utils import timezone

class MentorProfile(models.Model):
    """Extended profile for users who want to become mentors"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='mentor_profile')
    bio = models.TextField(max_length=1000, help_text="Tell learners about your experience and expertise")
    hourly_rate = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('5.00')), MaxValueValidator(Decimal('500.00'))],
        help_text="Your hourly rate in USD"
    )

    # Availability stored as JSON for flexibility
    # Format: [{'day': 'Monday', 'start': '09:00', 'end': '17:00'}, ...]
    available_slots = models.JSONField(
        default=list,
        help_text="Available time slots for mentorship sessions"
    )

    # Mentor specializations
    specializations = models.ManyToManyField(
        'collaborate.Skill',
        blank=True,
        help_text="Skills you can mentor in"
    )

    # Mentor status and ratings
    is_active = models.BooleanField(default=True)
    total_sessions = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00')), MaxValueValidator(Decimal('5.00'))]
    )
    total_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    withdrawn_earnings = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Profile completion and verification
    profile_completed = models.BooleanField(default=False)
    verified = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-average_rating', '-total_sessions']

    def __str__(self):
        return f"{self.user.get_full_name() or self.user.username} - Mentor"

    def get_specialization_names(self):
        """Get comma-separated list of specialization names"""
        return ", ".join([skill.name for skill in self.specializations.all()])

    def calculate_commission(self, amount):
        """Calculate ForgeX commission (15% default)"""
        commission_rate = Decimal('0.15')  # 15%
        return amount * commission_rate

    def get_net_earnings(self, amount):
        """Calculate mentor's net earnings after commission"""
        return amount - self.calculate_commission(amount)

    def get_available_balance(self):
        """Calculate available balance for withdrawal"""
        return self.total_earnings - self.withdrawn_earnings

    def can_withdraw(self, amount):
        """Check if mentor can withdraw the specified amount"""
        return amount <= self.get_available_balance() and amount >= Decimal('10.00')


class MentorshipSessionSave(models.Model):
    """Base class with save method for MentorshipSession"""

    def save(self, *args, **kwargs):
        """Calculate amounts before saving"""
        if hasattr(self, 'total_amount') and not self.total_amount:
            # Calculate total amount based on duration and hourly rate
            hours = Decimal(self.duration_minutes) / Decimal('60')
            self.total_amount = self.hourly_rate * hours

        if hasattr(self, 'commission_amount') and not self.commission_amount:
            # Calculate commission (15%)
            self.commission_amount = self.total_amount * Decimal('0.15')

        if hasattr(self, 'mentor_earnings') and not self.mentor_earnings:
            # Calculate mentor earnings
            self.mentor_earnings = self.total_amount - self.commission_amount

        super().save(*args, **kwargs)

    class Meta:
        abstract = True


class MentorshipSession(MentorshipSessionSave):
    """Represents a mentorship session between a learner and mentor"""

    STATUS_CHOICES = [
        ('scheduled', 'Scheduled'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('no_show', 'No Show'),
    ]

    DURATION_CHOICES = [
        (60, '1 Hour'),
        (120, '2 Hours'),
    ]

    # Session participants
    learner = models.ForeignKey(
        User,
        related_name="mentorship_sessions",
        on_delete=models.CASCADE,
        help_text="The learner booking the session"
    )
    mentor = models.ForeignKey(
        User,
        related_name="mentor_sessions",
        on_delete=models.CASCADE,
        help_text="The mentor providing the session"
    )

    # Session details
    scheduled_time = models.DateTimeField(help_text="When the session is scheduled to start")
    duration_minutes = models.PositiveIntegerField(
        choices=DURATION_CHOICES,
        default=60,
        help_text="Duration of the session in minutes"
    )

    # Session status and room
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='scheduled')
    room_id = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    started_at = models.DateTimeField(null=True, blank=True, help_text="When the session actually started")

    # Payment and pricing
    hourly_rate = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        help_text="Hourly rate at the time of booking"
    )
    total_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Total amount for the session"
    )
    commission_amount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="ForgeX commission amount"
    )
    mentor_earnings = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Mentor's net earnings"
    )

    # Payment status
    is_paid = models.BooleanField(default=False)
    payment_intent_id = models.CharField(max_length=255, blank=True, null=True)
    payment_status = models.CharField(
        max_length=20,
        choices=[
            ('pending', 'Pending'),
            ('processing', 'Processing'),
            ('succeeded', 'Succeeded'),
            ('failed', 'Failed'),
            ('canceled', 'Canceled'),
            ('refunded', 'Refunded'),
        ],
        default='pending',
        help_text="Current payment status"
    )
    stripe_session_id = models.CharField(max_length=255, blank=True, null=True)
    payment_method_id = models.CharField(max_length=255, blank=True, null=True)
    refund_id = models.CharField(max_length=255, blank=True, null=True)

    # Session metadata
    session_notes = models.TextField(blank=True, help_text="Notes about the session")
    learner_feedback = models.TextField(blank=True, help_text="Learner's feedback after session")
    mentor_feedback = models.TextField(blank=True, help_text="Mentor's feedback after session")
    rating = models.PositiveIntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Learner's rating of the session (1-5)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True)
    ended_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-scheduled_time']
        indexes = [
            models.Index(fields=['learner', 'status']),
            models.Index(fields=['mentor', 'status']),
            models.Index(fields=['scheduled_time']),
            models.Index(fields=['room_id']),
        ]

    def __str__(self):
        return f"Session: {self.learner.username} with {self.mentor.username} on {self.scheduled_time.strftime('%Y-%m-%d %H:%M')}"

    def get_duration_hours(self):
        """Get duration in hours as decimal"""
        return Decimal(self.duration_minutes) / Decimal('60')

    def is_payment_required(self):
        """Check if payment is required for this session"""
        return self.total_amount > 0 and not self.is_paid

    def can_access_session(self):
        """Check if session can be accessed (payment completed or free)"""
        return self.is_paid or self.total_amount == 0

    def can_start(self):
        """Check if session can be started"""
        if self.status != 'scheduled':
            return False

        # Payment must be completed for paid sessions
        if not self.can_access_session():
            return False

        # Allow starting 10 minutes before scheduled time
        from django.utils import timezone
        now = timezone.now()
        start_window = self.scheduled_time - timezone.timedelta(minutes=10)
        end_window = self.scheduled_time + timezone.timedelta(minutes=30)  # 30 min grace period

        return start_window <= now <= end_window

    def is_overdue(self):
        """Check if session is overdue (more than 30 minutes past scheduled time)"""
        if self.status != 'scheduled':
            return False

        now = timezone.now()
        overdue_time = self.scheduled_time + timedelta(minutes=30)
        return now > overdue_time

    def get_remaining_time_seconds(self):
        """Get remaining time in seconds based on actual start time"""
        if self.status != 'active' or not self.started_at:
            return self.duration_minutes * 60  # Return full duration if not started

        now = timezone.now()
        elapsed_seconds = (now - self.started_at).total_seconds()
        total_seconds = self.duration_minutes * 60
        remaining_seconds = max(0, total_seconds - elapsed_seconds)

        return int(remaining_seconds)

    def is_session_expired(self):
        """Check if the session time has expired"""
        return self.get_remaining_time_seconds() <= 0

    def get_session_end_time(self):
        """Get the expected end time of the session"""
        if not self.started_at:
            return self.scheduled_time + timedelta(minutes=self.duration_minutes)
        return self.started_at + timedelta(minutes=self.duration_minutes)

    @property
    def time_until_start(self):
        """Get human-readable time until session starts"""
        if self.status != 'scheduled':
            return None

        now = timezone.now()
        time_diff = self.scheduled_time - now

        if time_diff.total_seconds() <= 0:
            return "Starting now"

        days = time_diff.days
        hours, remainder = divmod(time_diff.seconds, 3600)
        minutes, _ = divmod(remainder, 60)

        if days > 0:
            return f"{days} day{'s' if days != 1 else ''}, {hours} hour{'s' if hours != 1 else ''}"
        elif hours > 0:
            return f"{hours} hour{'s' if hours != 1 else ''}, {minutes} minute{'s' if minutes != 1 else ''}"
        else:
            return f"{minutes} minute{'s' if minutes != 1 else ''}"

    @property
    def scheduled_time_iso(self):
        """Get scheduled time in ISO format for JavaScript"""
        return self.scheduled_time.isoformat()


class SessionChatMessage(models.Model):
    """Chat messages in mentorship session rooms"""
    session = models.ForeignKey(MentorshipSession, on_delete=models.CASCADE, related_name='session_chat_messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    message = models.TextField()
    message_type = models.CharField(
        max_length=20,
        choices=[
            ('chat', 'Chat Message'),
            ('system', 'System Message'),
            ('file', 'File Share'),
        ],
        default='chat'
    )
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['session', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
        ]

    def __str__(self):
        return f"Message in {self.session.room_id} by {self.sender.username}: {self.message[:50]}"


class SessionChatAttachment(models.Model):
    """File attachments for session chat messages"""
    message = models.ForeignKey(SessionChatMessage, on_delete=models.CASCADE, related_name='attachments')
    file = models.FileField(upload_to='session_chat_attachments/')
    filename = models.CharField(max_length=255)
    file_size = models.PositiveIntegerField()
    uploaded_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"Attachment: {self.filename} in session {self.message.session.room_id}"





class SessionFeedback(models.Model):
    """Detailed feedback for mentorship sessions"""

    session = models.OneToOneField(
        MentorshipSession,
        on_delete=models.CASCADE,
        related_name='detailed_feedback'
    )

    # Detailed ratings (1-5 scale)
    communication_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="How well did the mentor communicate?"
    )
    knowledge_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="How knowledgeable was the mentor?"
    )
    helpfulness_rating = models.PositiveIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="How helpful was the session?"
    )
    would_recommend = models.BooleanField(
        help_text="Would you recommend this mentor to others?"
    )

    # Detailed feedback
    what_went_well = models.TextField(
        blank=True,
        help_text="What went well in this session?"
    )
    areas_for_improvement = models.TextField(
        blank=True,
        help_text="What could be improved?"
    )
    additional_comments = models.TextField(
        blank=True,
        help_text="Any additional comments?"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Feedback for {self.session}"

    def get_average_rating(self):
        """Calculate average rating across all categories"""
        total = self.communication_rating + self.knowledge_rating + self.helpfulness_rating
        return round(total / 3, 2)


class MentorAvailability(models.Model):
    """Specific availability slots for mentors"""

    mentor = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='mentor_availability'
    )

    date = models.DateField(help_text="Date for this availability")
    start_time = models.TimeField(help_text="Start time for availability")
    end_time = models.TimeField(help_text="End time for availability")

    is_booked = models.BooleanField(default=False)
    session = models.ForeignKey(
        MentorshipSession,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        help_text="Session that booked this slot"
    )

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['date', 'start_time']
        unique_together = ['mentor', 'date', 'start_time']

    def __str__(self):
        status = "Booked" if self.is_booked else "Available"
        return f"{self.mentor.username} - {self.date} {self.start_time}-{self.end_time} ({status})"

    def is_available_for_duration(self, duration_minutes):
        """Check if this slot can accommodate a session of given duration"""
        if self.is_booked:
            return False

        # Calculate time difference in minutes
        start_datetime = datetime.combine(self.date, self.start_time)
        end_datetime = datetime.combine(self.date, self.end_time)
        available_minutes = (end_datetime - start_datetime).total_seconds() / 60

        return available_minutes >= duration_minutes

    @property
    def duration_hours(self):
        """Get the duration of this slot in hours"""
        start_datetime = datetime.combine(self.date, self.start_time)
        end_datetime = datetime.combine(self.date, self.end_time)
        duration_minutes = (end_datetime - start_datetime).total_seconds() / 60
        return int(duration_minutes / 60)


class ChatMessage(models.Model):
    """Chat messages for mentorship sessions"""

    MESSAGE_TYPE_CHOICES = [
        ('chat', 'Chat Message'),
        ('system', 'System Message'),
        ('user_joined', 'User Joined'),
        ('user_left', 'User Left'),
    ]

    session = models.ForeignKey(
        MentorshipSession,
        on_delete=models.CASCADE,
        related_name='legacy_chat_messages',
        help_text="The mentorship session this message belongs to"
    )
    sender = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sent_chat_messages',
        null=True,
        blank=True,
        help_text="User who sent the message (null for system messages)"
    )
    message_type = models.CharField(
        max_length=20,
        choices=MESSAGE_TYPE_CHOICES,
        default='chat',
        help_text="Type of message"
    )
    content = models.TextField(help_text="Message content")
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['timestamp']
        indexes = [
            models.Index(fields=['session', 'timestamp']),
            models.Index(fields=['sender', 'timestamp']),
        ]

    def __str__(self):
        if self.sender:
            return f"{self.sender.username}: {self.content[:50]}..."
        return f"System: {self.content[:50]}..."


class WithdrawalRequest(models.Model):
    """Withdrawal requests from mentors"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    mentor = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        help_text="Mentor requesting withdrawal"
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('10.00'))],
        help_text="Amount to withdraw (minimum $10)"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Current status of withdrawal"
    )

    # Payment details
    bank_account_last4 = models.CharField(
        max_length=4,
        blank=True,
        help_text="Last 4 digits of bank account"
    )
    stripe_transfer_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Stripe transfer ID"
    )

    # Processing details
    processing_fee = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Processing fee charged"
    )
    net_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Net amount transferred"
    )

    # Timestamps
    requested_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Notes
    admin_notes = models.TextField(
        blank=True,
        help_text="Internal notes for admin"
    )
    failure_reason = models.TextField(
        blank=True,
        help_text="Reason for failure if applicable"
    )

    class Meta:
        ordering = ['-requested_at']
        indexes = [
            models.Index(fields=['mentor', 'status']),
            models.Index(fields=['status', 'requested_at']),
        ]

    def __str__(self):
        return f"${self.amount} withdrawal by {self.mentor.username} - {self.status}"

    def save(self, *args, **kwargs):
        """Calculate net amount before saving"""
        if not self.net_amount:
            # Calculate processing fee (2.5% or $0.25 minimum)
            fee_percentage = self.amount * Decimal('0.025')
            self.processing_fee = max(fee_percentage, Decimal('0.25'))
            self.net_amount = self.amount - self.processing_fee
        super().save(*args, **kwargs)

    def can_cancel(self):
        """Check if withdrawal can be cancelled"""
        return self.status in ['pending', 'processing']

    def get_estimated_arrival(self):
        """Get estimated arrival time for the withdrawal"""
        if self.status == 'completed':
            return "Completed"
        elif self.status in ['pending', 'processing']:
            return "1-3 business days"
        else:
            return "N/A"
