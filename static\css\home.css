/* Home Page CSS with Parallax Effects and Visible Text */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Ensure all text is visible */
h1, h2, h3, p, button, a, span {
  color: #ffffff !important;
}

/* Background styles */
body {
  background-color: #1c1c1c;
  color: #ffffff;
}

/* Parallax base styles */
.parallax-section {
  position: relative;
  overflow: visible; /* Changed from hidden to visible to prevent content clipping */
  padding-bottom: 50px; /* Add padding to ensure content has room */
}

.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  transform: translateZ(0);
}

.parallax-content {
  position: relative;
  z-index: 1;
}

/* Particles background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 0;
  opacity: 0.7;
  pointer-events: none;
}

/* Ensure all sections have proper z-index to show particles */
section {
  position: relative;
  z-index: 1;
}

/* Add subtle gradient overlays to make particles more visible */
.hero::after,
.features::after,
.testimonials::after,
.cta-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, transparent 0%, rgba(28, 28, 28, 0.7) 100%);
  z-index: -1;
  pointer-events: none;
}

/* Hero Section with Parallax */
.hero {
  position: relative;
  min-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 64px 100px;
  /* background: linear-gradient(to bottom, #1c1c1c, #2d2d2d); */
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(28, 28, 28, 0.5) 0%, rgba(28, 28, 28, 0.9) 100%);
  z-index: 0;
}

.hero-content {
  max-width: 576px;
  z-index: 2;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.hero-content.visible {
  opacity: 1;
  transform: translateY(0);
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-content h2 {
  font-size: 24px;
  font-weight: 500;
  margin-bottom: 30px;
  color: #ffffff;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.highlight {
  color: #C0ff6b !important;
  text-shadow: 0 0 10px #C0ff6b, 0 0 20px #C0ff6b;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { text-shadow: 0 0 5px #C0ff6b, 0 0 10px #C0ff6b; }
  to { text-shadow: 0 0 10px #C0ff6b, 0 0 20px #C0ff6b, 0 0 30px #C0ff6b; }
}

.start {
  background-color: #C0ff6b;
  color: #000000 !important;
  border: none;
  padding: 12px 30px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.start::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  z-index: -1;
  transform: scale(0);
  border-radius: 50%;
  transition: transform 0.5s ease;
}

.start:hover {
  transform: scale(1.05);
  box-shadow: 0 0 15px #C0ff6b, 0 0 30px #C0ff6b;
}

.start:hover::before {
  transform: scale(2);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.hero-image {
  max-width: 844px;
  height: 634px;
  position: relative;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

.platform-interface img {
  width: 724px;
  height: 589px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
  transition: all 0.6s ease;
  border: 4px solid rgba(192, 255, 107, 0.5);
  filter: brightness(1.15) contrast(1.15);
  transform: perspective(1200px) rotateX(2deg) rotateY(-5deg);
}

.platform-interface img:hover {
  transform: perspective(1200px) rotateX(0deg) rotateY(0deg) scale(1.03);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.6), 0 0 30px rgba(192, 255, 107, 0.4);
  border-color: rgba(192, 255, 107, 0.8);
}

/* Features Section with Parallax - FIXED */
.features {
  padding: 100px 100px;
  /* background-color: #1c1c1c; */
  position: relative;
  z-index: 1;
}

.feature-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 60px;
  margin-bottom: 150px; /* Increased margin to prevent overlap */
  padding: 40px;
  border-radius: 15px;
  background: rgba(40, 40, 40, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.5s ease, box-shadow 0.5s ease;
  position: relative;
  overflow: visible; /* Changed from hidden to visible to allow content to extend outside */
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  min-height: 350px; /* Minimum height to ensure consistent spacing */
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(192, 255, 107, 0.05), transparent);
  z-index: -1;
}

.feature-content {
  max-width: 500px;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
  padding: 20px;
  background-color: rgba(28, 28, 28, 0.7);
  border-radius: 10px;
}

.feature-content.visible {
  opacity: 1;
  transform: translateY(0);
}

.feature-content h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  position: relative;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.feature-content p {
  color: #ffffff;
  line-height: 1.6;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.animated-bar {
  width: 50px;
  height: 3px;
  background: #C0ff6b;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(192, 255, 107, 0.5);
}

.animated-bar::before {
  content: '';
  position: absolute;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% { left: 100%; }
}

.feature-image {
  opacity: 0;
  transition: opacity 1s ease, transform 1s ease;
  flex: 0 0 45%;
  position: relative;
  overflow: hidden;
  height: 300px; /* Fixed height to prevent content jumping */
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-image.visible {
  opacity: 1;
}

.feature-image.slide-right {
  transform: translateX(-50px);
}

.feature-image.slide-right.visible {
  transform: translateX(0);
}

.feature-image.slide-left {
  transform: translateX(50px);
}

.feature-image.slide-left.visible {
  transform: translateX(0);
}

.feature-image img {
  border-radius: 15px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.5s ease;
  border: 3px solid rgba(192, 255, 107, 0.4);
  filter: brightness(1.1) contrast(1.1);
  transform: perspective(1000px) rotateY(0deg);
  position: relative;
  z-index: 2;
}

.feature-image img:hover {
  transform: perspective(1000px) rotateY(5deg) scale(1.05);
  border-color: rgba(192, 255, 107, 0.8);
  box-shadow: 0 0 30px rgba(192, 255, 107, 0.5);
  filter: brightness(1.2) contrast(1.2);
}

/* Testimonials Section with Parallax - FIXED */
.testimonials {
  padding: 100px 100px;
  text-align: center;
  background: linear-gradient(to bottom, #2d2d2d, #1c1c1c);
  position: relative;
  overflow: hidden;
}

.testimonials::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.05) 0%, transparent 70%);
  z-index: 0;
  animation: pulse 8s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.2); opacity: 0.7; }
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 50px;
  color: #ffffff;
  position: relative;
  display: inline-block;
  z-index: 1;
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.section-title.visible {
  opacity: 1;
  transform: translateY(0);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: #C0ff6b;
  box-shadow: 0 0 10px #C0ff6b;
}

.testimonials-grid {
  display: flex;
  gap: 32px;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 1;
  margin: 0 auto;
  max-width: 1200px;
}

.testimonial-card {
  position: relative;
  width: 350px;
  padding: 30px;
  border-radius: 15px;
  background: rgba(40, 40, 40, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  opacity: 0;
  transform: translateY(0);
  transition: opacity 1s ease, transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card.visible {
  opacity: 1;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.testimonial-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px solid transparent;
  border-radius: 15px;
  transition: border-color 0.3s ease;
  pointer-events: none;
}

.testimonial-card:hover::after {
  border-color: rgba(192, 255, 107, 0.5);
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.3);
}

.testimonial-content {
  background-color: rgba(40, 40, 40, 0.7);
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 15px;
}

.testimonial-content p {
  color: #ffffff;
  line-height: 1.6;
  position: relative;
  z-index: 1;
  font-size: 16px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.testimonial-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 16px;
}

.testimonial-author img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  margin-bottom: 10px;
  transition: all 0.5s ease;
  border: 3px solid #C0ff6b;
  box-shadow: 0 0 15px rgba(192, 255, 107, 0.4);
  filter: brightness(1.1);
  object-fit: cover;
  transform: translateY(0);
}

.testimonial-card:hover .testimonial-author img {
  transform: translateY(-5px) scale(1.1);
  border-color: #C0ff6b;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3), 0 0 20px rgba(192, 255, 107, 0.7);
  filter: brightness(1.2);
}

.testimonial-author p {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Quote styling */
.quote-open, .quote-close {
  width: 30px;
  height: auto;
  opacity: 0.7;
}

/* Call to Action Section with Parallax */
.cta-section {
  padding: 100px 0;
  text-align: center;
  background: linear-gradient(135deg, #1c1c1c 0%, #2d2d2d 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(192, 255, 107, 0.1) 0%, transparent 70%);
  z-index: 0;
  animation: pulse 4s infinite;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  /* opacity: 0; */
  transform: translateY(30px);
  transition: opacity 1s ease, transform 1s ease;
}

.cta-content.visible {
  opacity: 1;
  transform: translateY(0);
}

.cta-content h2 {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 20px;
  color: #ffffff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.cta-content p {
  color: #ffffff;
  margin-bottom: 30px;
}

.cta-button {
  background-color: transparent;
  color: #ffffff !important;
  border: 2px solid #C0ff6b;
  padding: 12px 30px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 30px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(192, 255, 107, 0.3), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover {
  background-color: #C0ff6b;
  color: #000000 !important;
  box-shadow: 0 0 15px #C0ff6b, 0 0 30px #C0ff6b;
}

.cta-button:hover::before {
  left: 100%;
}

/* Parallax Scroll Effects */
.parallax-scroll {
  transition: transform 0.5s ease-out;
  will-change: transform;
}

/* Floating Animation */
.floating {
  animation: float 6s ease-in-out infinite;
}

/* Scroll Reveal Animations */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.fade-in.exit-animation {
  opacity: 0;
  transform: translateY(-30px);
}

/* Always visible elements */
.always-visible {
  opacity: 1 !important;
  transform: translateY(0) !important;
  transform: translateX(0) !important;
}

.slide-right {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-right.exit-animation {
  opacity: 0;
  transform: translateX(50px);
}

.slide-left {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-left.exit-animation {
  opacity: 0;
  transform: translateX(-50px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .hero, .features, .testimonials {
    padding: 64px 50px;
  }

  .feature-card {
    flex-direction: column;
    gap: 40px;
  }

  .platform-interface img {
    width: 100%;
    height: auto;
  }
}

@media (max-width: 768px) {
  .hero, .features, .testimonials {
    padding: 50px 30px;
  }

  .hero {
    flex-direction: column;
  }

  .testimonials-grid {
    flex-direction: column;
    align-items: center;
  }
}
