"""
Django management command to debug session issues
Usage: python manage.py debug_sessions [username]
"""

from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.contrib.auth.models import User
from django.utils import timezone
from accounts.session_utils import get_user_sessions, get_session_info, cleanup_expired_sessions
import json


class Command(BaseCommand):
    help = 'Debug session issues and display session information'

    def add_arguments(self, parser):
        parser.add_argument(
            '--username',
            type=str,
            help='Username to check sessions for (optional)',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up expired sessions',
        )
        parser.add_argument(
            '--all-sessions',
            action='store_true',
            help='Show all active sessions',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('=== ForgeX Session Debug Tool ===\n')
        )

        # Clean up expired sessions if requested
        if options['cleanup']:
            expired_count = cleanup_expired_sessions()
            self.stdout.write(
                self.style.SUCCESS(f'Cleaned up {expired_count} expired sessions\n')
            )

        # Show sessions for specific user
        if options['username']:
            try:
                user = User.objects.get(username=options['username'])
                self.show_user_sessions(user)
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{options["username"]}" not found')
                )
                return

        # Show all active sessions
        elif options['all_sessions']:
            self.show_all_sessions()

        # Show general session statistics
        else:
            self.show_session_stats()

    def show_user_sessions(self, user):
        """Show sessions for a specific user"""
        self.stdout.write(
            self.style.HTTP_INFO(f'Sessions for user: {user.username}\n')
        )

        sessions = get_user_sessions(user)
        
        if not sessions:
            self.stdout.write(
                self.style.WARNING('No active sessions found for this user')
            )
            return

        for i, session in enumerate(sessions, 1):
            self.stdout.write(f'Session {i}:')
            self.stdout.write(f'  Session Key: {session["session_key"][:20]}...')
            self.stdout.write(f'  Expires: {session["expire_date"]}')
            self.stdout.write(f'  Remember Me: {session["remember_me"]}')
            self.stdout.write(f'  Last Activity: {session["last_activity"]}')
            self.stdout.write('')

    def show_all_sessions(self):
        """Show all active sessions"""
        self.stdout.write(
            self.style.HTTP_INFO('All Active Sessions:\n')
        )

        sessions = Session.objects.filter(expire_date__gte=timezone.now())
        
        if not sessions:
            self.stdout.write(
                self.style.WARNING('No active sessions found')
            )
            return

        user_sessions = {}
        anonymous_sessions = 0

        for session in sessions:
            data = session.get_decoded()
            user_id = data.get('_auth_user_id')
            
            if user_id:
                try:
                    user = User.objects.get(id=user_id)
                    username = user.username
                    if username not in user_sessions:
                        user_sessions[username] = []
                    user_sessions[username].append({
                        'session_key': session.session_key,
                        'expire_date': session.expire_date,
                        'remember_me': data.get('remember_me', False),
                    })
                except User.DoesNotExist:
                    anonymous_sessions += 1
            else:
                anonymous_sessions += 1

        # Display user sessions
        for username, sessions in user_sessions.items():
            self.stdout.write(f'{username}: {len(sessions)} session(s)')
            for session in sessions:
                remember_status = "Remember Me" if session['remember_me'] else "Browser Session"
                self.stdout.write(f'  - {session["session_key"][:20]}... ({remember_status})')

        if anonymous_sessions > 0:
            self.stdout.write(f'\nAnonymous sessions: {anonymous_sessions}')

    def show_session_stats(self):
        """Show general session statistics"""
        total_sessions = Session.objects.count()
        active_sessions = Session.objects.filter(expire_date__gte=timezone.now()).count()
        expired_sessions = total_sessions - active_sessions

        self.stdout.write('Session Statistics:')
        self.stdout.write(f'  Total sessions: {total_sessions}')
        self.stdout.write(f'  Active sessions: {active_sessions}')
        self.stdout.write(f'  Expired sessions: {expired_sessions}')

        # Count sessions by user
        active_session_objects = Session.objects.filter(expire_date__gte=timezone.now())
        authenticated_sessions = 0
        anonymous_sessions = 0
        remember_me_sessions = 0

        for session in active_session_objects:
            data = session.get_decoded()
            if data.get('_auth_user_id'):
                authenticated_sessions += 1
                if data.get('remember_me'):
                    remember_me_sessions += 1
            else:
                anonymous_sessions += 1

        self.stdout.write(f'  Authenticated sessions: {authenticated_sessions}')
        self.stdout.write(f'  Anonymous sessions: {anonymous_sessions}')
        self.stdout.write(f'  "Remember Me" sessions: {remember_me_sessions}')

        self.stdout.write('\nSession Configuration:')
        from django.conf import settings
        self.stdout.write(f'  SESSION_COOKIE_AGE: {settings.SESSION_COOKIE_AGE} seconds')
        self.stdout.write(f'  SESSION_EXPIRE_AT_BROWSER_CLOSE: {settings.SESSION_EXPIRE_AT_BROWSER_CLOSE}')
        self.stdout.write(f'  SESSION_SAVE_EVERY_REQUEST: {getattr(settings, "SESSION_SAVE_EVERY_REQUEST", False)}')

        self.stdout.write('\nUsage Examples:')
        self.stdout.write('  python manage.py debug_sessions --username john_doe')
        self.stdout.write('  python manage.py debug_sessions --all-sessions')
        self.stdout.write('  python manage.py debug_sessions --cleanup')
