{% extends 'base.html' %}
{% load static %}

{% block title %}Mentor Dashboard - ForgeX{% endblock %}

{% block content %}
<div class="mentor-dashboard">
    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="header-content">
                <div class="mentor-welcome">
                    <h1>Welcome back, {{ mentor_profile.user.first_name }}!</h1>
                    <p>Manage your mentorship sessions and profile</p>
                </div>

                <div class="quick-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">{{ mentor_profile.total_sessions }}</span>
                            <span class="stat-label">Total Sessions</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">{{ mentor_profile.average_rating }}</span>
                            <span class="stat-label">Average Rating</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <span class="stat-number">${{ mentor_profile.total_earnings }}</span>
                            <span class="stat-label">Total Earnings</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="dashboard-content">
        <div class="container">
            <div class="dashboard-grid">
                <!-- Profile Management -->
                <div class="dashboard-section profile-management-section">
                    <div class="section-header">
                        <h2><i class="fas fa-user-cog"></i> Profile Management</h2>
                        <span class="profile-status-badge {% if mentor_profile.is_active %}active{% else %}inactive{% endif %}">
                            {{ mentor_profile.is_active|yesno:"Live in Marketplace,Not Listed" }}
                        </span>
                    </div>

                    <form id="mentorProfileForm" class="profile-form">
                        {% csrf_token %}

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="bio">Professional Bio</label>
                                <textarea id="bio" name="bio" rows="4" required
                                          placeholder="Describe your experience, expertise, and what you can help learners with...">{{ mentor_profile.bio }}</textarea>
                                <small>Tell potential learners about your background and expertise</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="hourlyRate">Hourly Rate (USD)</label>
                                <input type="number" id="hourlyRate" name="hourly_rate"
                                       min="5" max="500" step="0.01" value="{{ mentor_profile.hourly_rate }}" required>
                                <small>Your rate per hour (platform takes 15% commission)</small>
                            </div>

                            <div class="form-group">
                                <label for="specializations">Specializations</label>
                                <select id="specializations" name="specializations" multiple required>
                                    {% for skill in all_skills %}
                                        <option value="{{ skill.id }}"
                                                {% if skill in mentor_profile.specializations.all %}selected{% endif %}>
                                            {{ skill.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <small>Hold Ctrl/Cmd to select multiple skills</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="profileStatus">Marketplace Visibility</label>
                                <select id="profileStatus" name="is_active" required>
                                    <option value="true" {% if mentor_profile.is_active %}selected{% endif %}>
                                        Active (Visible in marketplace)
                                    </option>
                                    <option value="false" {% if not mentor_profile.is_active %}selected{% endif %}>
                                        Inactive (Hidden from marketplace)
                                    </option>
                                </select>
                                <small>Control whether your profile appears in the marketplace</small>
                            </div>

                            <div class="form-group">
                                <label>Profile Status</label>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="profileCompleted" name="profile_completed"
                                               {% if mentor_profile.profile_completed %}checked{% endif %}>
                                        <span class="checkmark"></span>
                                        Profile is complete and ready for marketplace
                                    </label>
                                </div>
                                <small>Check this when your profile is ready to go live</small>
                            </div>
                        </div>

                        <!-- Availability Management -->
                        <div class="form-row">
                            <div class="form-group full-width">
                                <label>Weekly Availability</label>
                                <div class="availability-grid">
                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="monday" name="available_days" value="Monday">
                                            <span class="day-checkmark"></span>
                                            Monday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="monday_start" value="09:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="monday_end" value="17:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="tuesday" name="available_days" value="Tuesday">
                                            <span class="day-checkmark"></span>
                                            Tuesday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="tuesday_start" value="09:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="tuesday_end" value="17:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="wednesday" name="available_days" value="Wednesday">
                                            <span class="day-checkmark"></span>
                                            Wednesday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="wednesday_start" value="09:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="wednesday_end" value="17:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="thursday" name="available_days" value="Thursday">
                                            <span class="day-checkmark"></span>
                                            Thursday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="thursday_start" value="09:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="thursday_end" value="17:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="friday" name="available_days" value="Friday">
                                            <span class="day-checkmark"></span>
                                            Friday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="friday_start" value="09:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="friday_end" value="17:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="saturday" name="available_days" value="Saturday">
                                            <span class="day-checkmark"></span>
                                            Saturday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="saturday_start" value="10:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="saturday_end" value="16:00" class="time-input">
                                        </div>
                                    </div>

                                    <div class="availability-day">
                                        <label class="day-label">
                                            <input type="checkbox" id="sunday" name="available_days" value="Sunday">
                                            <span class="day-checkmark"></span>
                                            Sunday
                                        </label>
                                        <div class="time-inputs">
                                            <input type="time" name="sunday_start" value="10:00" class="time-input">
                                            <span class="time-separator">to</span>
                                            <input type="time" name="sunday_end" value="16:00" class="time-input">
                                        </div>
                                    </div>
                                </div>
                                <small>Select the days and times when you're available for mentorship sessions</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-large">
                                <i class="fas fa-save"></i>
                                Update Profile & Post to Marketplace
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="previewProfile()">
                                <i class="fas fa-eye"></i>
                                Preview Profile
                            </button>
                        </div>
                    </form>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="btn btn-outline" onclick="openAvailabilityModal()">
                                <i class="fas fa-calendar-plus"></i>
                                Manage Availability
                            </button>
                            <button class="btn btn-outline" onclick="viewMarketplaceProfile()">
                                <i class="fas fa-external-link-alt"></i>
                                View in Marketplace
                            </button>
                            <a href="{% url 'mentorship:withdrawal_dashboard' %}" class="btn btn-outline">
                                <i class="fas fa-money-bill-wave"></i>
                                Withdraw Money
                            </a>
                            {% if user.is_superuser %}
                                <a href="{% url 'mentorship:admin_withdrawal_dashboard' %}" class="btn btn-admin">
                                    <i class="fas fa-shield-alt"></i>
                                    Admin: Manage Withdrawals
                                </a>
                            {% endif %}
                            <button class="btn btn-outline" onclick="downloadEarningsReport()">
                                <i class="fas fa-download"></i>
                                Download Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Sessions -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h2><i class="fas fa-clock"></i> Upcoming Sessions</h2>
                        <a href="{% url 'mentorship:my_sessions' %}" class="view-all-link">View All</a>
                    </div>

                    {% if upcoming_sessions %}
                        <div class="sessions-list">
                            {% for session in upcoming_sessions %}
                                <div class="session-item">
                                    <div class="session-info">
                                        <h4>{{ session.learner.get_full_name }}</h4>
                                        <div class="session-details">
                                            <span class="session-date">
                                                <i class="fas fa-calendar"></i>
                                                {{ session.scheduled_time|date:"M d, Y" }}
                                            </span>
                                            <span class="session-time">
                                                <i class="fas fa-clock"></i>
                                                {{ session.scheduled_time|time:"g:i A" }}
                                            </span>
                                            <span class="session-duration">
                                                {{ session.duration_minutes }} min
                                            </span>
                                        </div>
                                    </div>

                                    <div class="session-actions">
                                        {% if session.can_start %}
                                            <a href="{% url 'mentorship:session_room' session.room_id %}"
                                               class="btn btn-primary btn-sm">Start Session</a>
                                        {% else %}
                                            <span class="session-status">Scheduled</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-calendar-plus"></i>
                            <p>No upcoming sessions</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Recent Sessions -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h2><i class="fas fa-history"></i> Recent Sessions</h2>
                        <a href="{% url 'mentorship:my_sessions' %}?status=completed" class="view-all-link">View All</a>
                    </div>

                    {% if recent_sessions %}
                        <div class="sessions-list">
                            {% for session in recent_sessions %}
                                <div class="session-item completed">
                                    <div class="session-info">
                                        <h4>{{ session.learner.get_full_name }}</h4>
                                        <div class="session-details">
                                            <span class="session-date">
                                                <i class="fas fa-calendar"></i>
                                                {{ session.ended_at|date:"M d, Y" }}
                                            </span>
                                            <span class="session-earnings">
                                                <i class="fas fa-dollar-sign"></i>
                                                ${{ session.mentor_earnings }}
                                            </span>
                                            {% if session.rating %}
                                                <span class="session-rating">
                                                    {% for i in "12345" %}
                                                        <span class="star {% if session.rating >= i|add:0 %}filled{% endif %}">★</span>
                                                    {% endfor %}
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history"></i>
                            <p>No completed sessions yet</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Monthly Earnings -->
                <div class="dashboard-section earnings-section">
                    <div class="section-header">
                        <h2><i class="fas fa-calendar-alt"></i> Monthly Earnings</h2>
                    </div>

                    <div class="earnings-content">
                        <div class="monthly-earnings-highlight">
                            <div class="monthly-amount">
                                <span class="amount-label">This Month</span>
                                <span class="amount-value">${{ monthly_earnings }}</span>
                            </div>
                        </div>

                        <div class="earnings-summary">
                            <div class="earnings-item">
                                <span class="earnings-label">Hourly Rate</span>
                                <span class="earnings-value">${{ mentor_profile.hourly_rate }}/hr</span>
                            </div>
                            <div class="earnings-item">
                                <span class="earnings-label">Sessions This Month</span>
                                <span class="earnings-value">{{ monthly_sessions_count|default:0 }}</span>
                            </div>
                        </div>

                        <div class="earnings-actions">
                            <a href="{% url 'mentorship:withdrawal_dashboard' %}" class="btn btn-primary">
                                <i class="fas fa-money-bill-wave"></i>
                                Withdraw Earnings
                            </a>
                        </div>

                        <div class="earnings-note">
                            <i class="fas fa-info-circle"></i>
                            <p>Earnings are calculated after ForgeX's 15% commission. Payments are processed weekly.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
.mentor-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #ffffff;
}

.dashboard-header {
    background: linear-gradient(135deg, #C0ff6b 0%, #a0e066 100%);
    color: #1a1a1a;
    padding: 3rem 0;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.mentor-welcome h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.mentor-welcome p {
    font-size: 1.1rem;
    opacity: 0.8;
}

.quick-stats {
    display: flex;
    gap: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(26,26,26,0.1);
    padding: 1.5rem;
    border-radius: 12px;
    min-width: 150px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: #1a1a1a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon i {
    color: #C0ff6b;
    font-size: 1.5rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a1a1a;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.dashboard-content {
    padding: 4rem 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-section {
    background: rgba(255,255,255,0.05);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(192,255,107,0.2);
    min-height: 300px;
    display: flex;
    flex-direction: column;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(192,255,107,0.2);
}

.section-header h2 {
    color: #C0ff6b;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.3rem;
}

.view-all-link {
    color: #C0ff6b;
    text-decoration: none;
    font-size: 0.9rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.view-all-link:hover {
    opacity: 1;
}

.sessions-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.session-item {
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    padding: 1.5rem;
    border: 1px solid rgba(192,255,107,0.1);
    transition: all 0.3s ease;
}

.session-item:hover {
    border-color: rgba(192,255,107,0.3);
    background: rgba(255,255,255,0.05);
    transform: translateY(-2px);
}

.session-item.completed {
    opacity: 0.8;
}

.session-info h4 {
    margin: 0 0 0.75rem 0;
    color: #ffffff;
    font-size: 1.1rem;
}

.session-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.session-details span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.8;
}

.session-details i {
    color: #C0ff6b;
    width: 16px;
}

.session-actions {
    display: flex;
    justify-content: flex-end;
}

.session-status {
    color: #C0ff6b;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    background: rgba(192,255,107,0.1);
    border-radius: 15px;
}

.empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 3rem 1rem;
    opacity: 0.6;
}

.empty-state i {
    font-size: 3rem;
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.empty-state p {
    margin: 0;
    font-size: 1rem;
}

/* Profile Management Form Styles */
.profile-management-section {
    grid-column: 1 / -1;
    margin-bottom: 2rem;
}

.profile-status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.profile-status-badge.active {
    background: rgba(40,167,69,0.2);
    color: #28a745;
}

.profile-status-badge.inactive {
    background: rgba(220,53,69,0.2);
    color: #dc3545;
}

.profile-form {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #C0ff6b;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    background: rgba(255,255,255,0.1);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 8px;
    color: #ffffff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 15px rgba(192,255,107,0.4);
    background: rgba(255,255,255,0.15);
}

.form-group small {
    margin-top: 0.5rem;
    opacity: 0.7;
    font-size: 0.9rem;
}

.checkbox-group {
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: normal;
    color: #ffffff;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 24px;
    height: 24px;
    background: rgba(255,255,255,0.1);
    border: 3px solid rgba(192,255,107,0.6);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #C0ff6b;
    border-color: #C0ff6b;
    box-shadow: 0 0 12px rgba(192,255,107,0.6);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000000;
    font-weight: 900;
    font-size: 1.1rem;
    line-height: 1;
    text-shadow: none;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
}

/* Availability Grid Styles */
.availability-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.02);
    border-radius: 8px;
    border: 1px solid rgba(192,255,107,0.1);
}

.availability-day {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255,255,255,0.02);
    border-radius: 6px;
    border: 1px solid rgba(192,255,107,0.1);
    transition: all 0.3s ease;
    min-height: 60px;
}

.availability-day:hover {
    border-color: rgba(192,255,107,0.3);
    background: rgba(255,255,255,0.05);
}

.day-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 600;
    color: #ffffff;
    min-width: 100px;
    font-size: 0.9rem;
}

.day-label input[type="checkbox"] {
    display: none;
}

.day-checkmark {
    width: 24px;
    height: 24px;
    background: rgba(255,255,255,0.1);
    border: 3px solid rgba(192,255,107,0.6);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.day-label input[type="checkbox"]:checked + .day-checkmark {
    background: #C0ff6b;
    border-color: #C0ff6b;
    box-shadow: 0 0 12px rgba(192,255,107,0.6);
}

.day-label input[type="checkbox"]:checked + .day-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #000000;
    font-weight: 900;
    font-size: 1.2rem;
    line-height: 1;
    text-shadow: none;
}

.time-inputs {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.time-input {
    padding: 0.5rem 0.6rem;
    background: rgba(255,255,255,0.15);
    border: 2px solid rgba(192,255,107,0.4);
    border-radius: 4px;
    color: #ffffff;
    font-size: 0.9rem;
    font-weight: 600;
    width: 75px;
    text-align: center;
}

.time-input:focus {
    outline: none;
    border-color: #C0ff6b;
    box-shadow: 0 0 8px rgba(192,255,107,0.5);
    background: rgba(255,255,255,0.2);
}

/* Webkit browsers (Chrome, Safari, Edge) */
.time-input::-webkit-calendar-picker-indicator {
    filter: invert(1);
    opacity: 0.8;
}

/* Firefox */
.time-input::-moz-placeholder {
    color: rgba(255,255,255,0.7);
}

.time-separator {
    color: #C0ff6b;
    font-weight: 700;
    font-size: 1rem;
    text-shadow: 0 0 4px rgba(192,255,107,0.3);
}

/* Disable time inputs when day is not selected */
.availability-day:not(.day-selected) .time-input {
    opacity: 0.5;
    pointer-events: none;
}

.availability-day.day-selected {
    border-color: rgba(192,255,107,0.4);
    background: rgba(192,255,107,0.05);
}

.quick-actions {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(192,255,107,0.2);
}

.quick-actions h3 {
    color: #C0ff6b;
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-outline {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    box-shadow: 0 2px 8px rgba(192,255,107,0.2);
}

.btn-outline:hover {
    background: rgba(192,255,107,0.15);
    border-color: #C0ff6b;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-admin {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: #ffffff;
    border: 2px solid #dc3545;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
}

.btn-admin:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    color: #ffffff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220,53,69,0.4);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #C0ff6b, #a0e066);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(192,255,107,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a0e066, #8fd455);
    box-shadow: 0 8px 25px rgba(192,255,107,0.4);
}

.btn-secondary {
    background: transparent;
    color: #C0ff6b;
    border: 2px solid rgba(192,255,107,0.6);
}

.btn-secondary:hover {
    background: rgba(192,255,107,0.1);
    border-color: #C0ff6b;
    box-shadow: 0 4px 15px rgba(192,255,107,0.2);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    box-shadow: 0 8px 25px rgba(40,167,69,0.4);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #1a1a1a;
    box-shadow: 0 4px 15px rgba(255,193,7,0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #fd7e14, #dc3545);
    box-shadow: 0 8px 25px rgba(255,193,7,0.4);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

.btn:hover {
    transform: translateY(-2px);
}

.earnings-section {
    grid-column: 1 / -1;
}

.earnings-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.monthly-earnings-highlight {
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(192,255,107,0.15) 0%, rgba(192,255,107,0.05) 100%);
    border-radius: 15px;
    border: 2px solid rgba(192,255,107,0.3);
    text-align: center;
}

.monthly-amount {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.amount-label {
    font-size: 1.2rem;
    color: #C0ff6b;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.amount-value {
    font-size: 3.5rem;
    font-weight: 900;
    color: #C0ff6b;
    text-shadow: 0 0 20px rgba(192,255,107,0.3);
    line-height: 1;
}

.earnings-summary {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

.earnings-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255,255,255,0.02);
    border-radius: 10px;
    border: 1px solid rgba(192,255,107,0.1);
}

.earnings-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
}

.earnings-value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #C0ff6b;
}

.earnings-actions {
    margin-bottom: 1.5rem;
    text-align: center;
}

.earnings-note {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background: rgba(192,255,107,0.15);
    border-radius: 8px;
    border: 1px solid rgba(192,255,107,0.3);
    position: relative;
    z-index: 10;
}

.earnings-note::before,
.earnings-note::after {
    display: none !important;
    content: none !important;
}

.earnings-note i {
    color: #C0ff6b;
    margin-top: 0.25rem;
    flex-shrink: 0;
    z-index: 11;
    position: relative;
}

.earnings-note i::before,
.earnings-note i::after {
    display: none !important;
    content: none !important;
}

.earnings-note p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #ffffff !important;
    font-weight: 500;
    position: relative;
    z-index: 11;
}

.earnings-note p::before,
.earnings-note p::after {
    display: none !important;
    content: none !important;
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }

    .quick-stats {
        flex-direction: column;
        width: 100%;
    }

    .stat-card {
        justify-content: center;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn-outline {
        justify-content: center;
    }

    .earnings-summary {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .monthly-earnings-highlight {
        padding: 1.5rem;
    }

    .amount-value {
        font-size: 2.5rem;
    }

    .amount-label {
        font-size: 1rem;
    }

    .session-details {
        gap: 0.25rem;
    }

    .session-details span {
        font-size: 0.8rem;
    }

    .availability-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .availability-day {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem;
        min-height: auto;
    }

    .day-label {
        min-width: auto;
        font-size: 0.9rem;
    }

    .time-inputs {
        width: 100%;
        justify-content: space-between;
        gap: 0.5rem;
    }

    .time-input {
        width: 70px;
        font-size: 0.85rem;
        padding: 0.4rem 0.5rem;
    }

    .time-separator {
        font-size: 0.9rem;
    }
}
</style>

<script>
// Profile form submission
document.getElementById('mentorProfileForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    // Collect availability data
    const availableSlots = [];
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

    days.forEach((day, index) => {
        const checkbox = document.getElementById(day);
        if (checkbox && checkbox.checked) {
            const startTime = formData.get(`${day}_start`);
            const endTime = formData.get(`${day}_end`);
            if (startTime && endTime) {
                availableSlots.push({
                    day: dayNames[index],
                    start: startTime,
                    end: endTime
                });
            }
        }
    });

    const data = {
        bio: formData.get('bio'),
        hourly_rate: formData.get('hourly_rate'),
        specializations: Array.from(document.getElementById('specializations').selectedOptions).map(option => option.value),
        is_active: formData.get('is_active') === 'true',
        profile_completed: document.getElementById('profileCompleted').checked,
        available_slots: availableSlots
    };

    try {
        const response = await fetch('{% url "mentorship:mentor_dashboard" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Profile updated successfully! Your changes are now live in the marketplace.');
            location.reload();
        } else {
            alert('Error: ' + result.error);
        }
    } catch (error) {
        alert('An error occurred. Please try again.');
        console.error('Error:', error);
    }
});

// Quick action functions
function openAvailabilityModal() {
    alert('Availability management feature coming soon! For now, you can add availability through the Django admin.');
}

function previewProfile() {
    // Open mentor detail page in new tab
    window.open('{% url "mentorship:mentor_detail" mentor_profile.id %}', '_blank');
}

function viewMarketplaceProfile() {
    // Open mentor detail page in new tab
    window.open('{% url "mentorship:mentor_detail" mentor_profile.id %}', '_blank');
}

function downloadEarningsReport() {
    alert('Earnings report download feature coming soon!');
}

// Form validation
document.getElementById('bio').addEventListener('input', function() {
    const bioLength = this.value.length;
    const minLength = 50;

    if (bioLength < minLength) {
        this.style.borderColor = '#dc3545';
    } else {
        this.style.borderColor = 'rgba(192,255,107,0.2)';
    }
});

document.getElementById('hourlyRate').addEventListener('input', function() {
    const rate = parseFloat(this.value);
    const commission = rate * 0.15;
    const netEarnings = rate - commission;

    // Update the help text to show commission calculation
    const helpText = this.nextElementSibling;
    helpText.textContent = `Your rate per hour (platform takes 15% = $${commission.toFixed(2)}, you earn $${netEarnings.toFixed(2)})`;
});

// Availability management
document.addEventListener('DOMContentLoaded', function() {
    // Initialize availability state
    initializeAvailability();

    // Add event listeners for day checkboxes
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    days.forEach(day => {
        const checkbox = document.getElementById(day);
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                toggleDayAvailability(day, this.checked);
            });

            // Initialize state
            toggleDayAvailability(day, checkbox.checked);
        }
    });

    // Add time validation listeners
    const timeInputs = document.querySelectorAll('.time-input');
    timeInputs.forEach(input => {
        input.addEventListener('change', function() {
            const day = this.name.split('_')[0];
            validateTimeRange(day);
        });
    });
});

function initializeAvailability() {
    // Load existing availability data if any
    const existingSlots = {{ mentor_profile.available_slots|safe }};

    if (existingSlots && existingSlots.length > 0) {
        existingSlots.forEach(slot => {
            const dayName = slot.day.toLowerCase();
            const checkbox = document.getElementById(dayName);
            const startInput = document.querySelector(`input[name="${dayName}_start"]`);
            const endInput = document.querySelector(`input[name="${dayName}_end"]`);

            if (checkbox) {
                checkbox.checked = true;
                toggleDayAvailability(dayName, true);
            }

            if (startInput) startInput.value = slot.start;
            if (endInput) endInput.value = slot.end;
        });
    }
}

function toggleDayAvailability(day, isEnabled) {
    const dayContainer = document.getElementById(day).closest('.availability-day');
    const timeInputs = dayContainer.querySelectorAll('.time-input');

    if (isEnabled) {
        dayContainer.classList.add('day-selected');
        timeInputs.forEach(input => {
            input.disabled = false;
            input.style.opacity = '1';
            input.style.pointerEvents = 'auto';
        });
    } else {
        dayContainer.classList.remove('day-selected');
        timeInputs.forEach(input => {
            input.disabled = true;
            input.style.opacity = '0.5';
            input.style.pointerEvents = 'none';
        });
    }
}

function validateTimeRange(day) {
    const startInput = document.querySelector(`input[name="${day}_start"]`);
    const endInput = document.querySelector(`input[name="${day}_end"]`);

    if (startInput && endInput && startInput.value && endInput.value) {
        const startTime = new Date(`2000-01-01T${startInput.value}`);
        const endTime = new Date(`2000-01-01T${endInput.value}`);

        if (startTime >= endTime) {
            alert('End time must be after start time');
            endInput.focus();
            return false;
        }

        // Check for minimum 1-hour availability
        const diffHours = (endTime - startTime) / (1000 * 60 * 60);
        if (diffHours < 1) {
            alert('Please provide at least 1 hour of availability');
            endInput.focus();
            return false;
        }
    }

    return true;
}
</script>
{% endblock %}
